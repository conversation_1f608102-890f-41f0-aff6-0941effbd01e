# Respector

Respector is a static analyzer who check the respect your conceptions rules with a simple unit test

[![Build Status](https://img.shields.io/travis/com/PHP-DI/PHP-DI/master.svg?style=flat-square)](https://travis-ci.com/PHP-DI/PHP-DI)

## Why?

A large number of tools exist to check the quality of your code (PhpStan, Psalm etc.). Sometimes we need to add our
own conception rules.
It is possible to customize the rules of these tools to add our own rules, but this implies that we must know the
tools well and can be more complicated to evolve or maintain.


## Installation

Describe how to install the project/library/framework/…

Make sure your installation instructions work by testing them!

## Usage

Describe how to use the project. A gif or a short code example is the best
way to show how it works. Also keep paragraphs short and sentences simple: not
everybody speaks english well.

For the sake of the example here is how you can use this project template
as a basis for your own repository:

```bash
git clone https://github.com/mnapoli/project-template.git my-project
cd my-project
# Remove the git repository metadata
rm -rf .git/
# Start a brand new repository
git init
git add .
```

Easy peasy! Now you just have to code.

Make sure your examples work by testing them! I didn't test mine and I should feel ashamed.
