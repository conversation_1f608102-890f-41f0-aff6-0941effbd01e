<?php

declare(strict_types=1);

namespace Telenco\Component\StaticPage\Domain\Port\Repository;

use Telenco\Component\Shared\Domain\Model\Region;
use Telenco\Component\StaticPage\Domain\Model\StaticPage;
use Telenco\Component\StaticPage\Domain\UseCase\CreateStaticPageUseCase\DTO\StaticPageSlugDTO;

interface StaticPageRepositoryInterface
{
    public function save(StaticPage $staticPage): void;
    public function findById(int $staticPageId): ?StaticPage;
    public function findByIdAndRegion(int $staticPageId, Region $region): ?StaticPage;
    public function findBySlug(string $staticPageSlug): ?StaticPage;
    public function update(StaticPage $staticPage): void;
    public function findAll(?string $sortBy = null, ?string $sortDirection = null): array;

    /**
     * @return StaticPageSlugDTO[]
     */
    public function findAllWithSlugName(): array;

    public function findByIdWithSlugName(int $id): ?StaticPageSlugDTO;

    /**
     * @return StaticPageSlugDTO[]
     */
    public function findChildrenOfStaticPageInRegion(int $staticPageId, string $region): array;


    /**
     * @return StaticPageSlugDTO[]
     */
    public function findAllFromRegionWithSlugNameAndNotId(string $region, ?int $id = null): array;
}
