<?php

declare(strict_types=1);

namespace Telenco\Component\StaticPage\Domain\UseCase\CreateStaticPageUseCase\DTO;

use Telenco\Component\Shared\Domain\Model\Region;

final class CreateStaticPageRequest extends StaticPageRequest
{
    /**
     * CreateStaticPageRequest constructor.
     * @param StaticPageContentRequest[] $staticPageContents
     * @param Region[] $staticPageRegions
     */
    public function __construct(
        public string $status,
        public ?string $slug = null,
        /** @var StaticPageContentRequest[] $staticPageContents */
        public array $staticPageContents = [],
        /** @var Region[] $staticPageRegions */
        public array $staticPageRegions = [],
        public ?StaticPageSlugDTO $parent = null
    ) {
        parent::__construct($status, $slug, $staticPageContents, $staticPageRegions, $parent);
    }
}
