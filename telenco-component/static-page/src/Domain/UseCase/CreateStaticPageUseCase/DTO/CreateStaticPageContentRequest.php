<?php

declare(strict_types=1);

namespace Telenco\Component\StaticPage\Domain\UseCase\CreateStaticPageUseCase\DTO;

final class CreateStaticPageContentRequest extends StaticPageContentRequest
{
    /**
     * CreateStaticPageContentRequest constructor.
     * @param string $locale
     * @param string|null $title
     * @param string|null $content
     * @param string|null $seoTitle
     * @param string|null $seoDescription
     */
    public function __construct(
        string $locale,
        ?string $title = null,
        ?string $content = null,
        ?string $seoTitle = null,
        ?string $seoDescription = null,
    ) {
        parent::__construct($locale, $title, $content, $seoTitle, $seoDescription);
    }
}
