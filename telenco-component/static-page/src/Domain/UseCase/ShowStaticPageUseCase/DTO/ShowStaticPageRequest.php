<?php

declare(strict_types=1);

namespace Telenco\Component\StaticPage\Domain\UseCase\ShowStaticPageUseCase\DTO;

use Telenco\Component\Shared\Domain\Model\Region;
use Telenco\Component\Shared\Domain\Request\SlugRequestInterface;

final class ShowStaticPageRequest implements SlugRequestInterface
{
    /**
     * UpdateStaticPageRequest constructor.
     * @param string $locale
     * @param string $slug
     * @param Region $region
     */
    public function __construct(
        public Region $region,
        public string $locale,
        public string $slug
    ) {
    }
}
