<?php

declare(strict_types=1);

namespace Telenco\Component\StaticPage\Domain\Assert;

use Assert\AssertionFailedException;
use Marketplace\Component\CleanArchiCore\Domain\Assert\Assert as CleanArchiCoreAssert;
use Telenco\Component\Shared\Domain\Assert\Assertion;
use Telenco\Component\Shared\Domain\Port\Repository\SlugRepositoryInterface;
use Telenco\Component\StaticPage\Domain\UseCase\CreateStaticPageUseCase\DTO\StaticPageRequest;
use Telenco\Component\StaticPage\Domain\UseCase\CreateStaticPageUseCase\DTO\StaticPageResponse;

class Assert extends CleanArchiCoreAssert
{
    public static function validateRequest(
        StaticPageRequest $request,
        StaticPageResponse $response,
        SlugRepositoryInterface $slugRepository,
        ?int $id = null
    ): bool {
        $requestValid = $request->validate($response);
        $requestContentValid = self::validateRequestContent($request, $response);
        $slugValid = self::validateUniqueSlug($request, $response, $slugRepository, $id);

        return $requestValid && $requestContentValid && $slugValid;
    }

    private static function validateRequestContent(StaticPageRequest $request, StaticPageResponse $response): bool
    {
        $isContentsValid = true;

        foreach ($request->staticPageContents as $staticPageContentRequest) {
            if ($staticPageContentRequest->validate($response, $request) === false) {
                $isContentsValid = false;
            }
        }

        return $isContentsValid;
    }

    private static function validateUniqueSlug(
        StaticPageRequest $request,
        StaticPageResponse $response,
        SlugRepositoryInterface $slugRepository,
        ?int $id = null
    ): bool {
        if ($request->slug !== null) {
            try {
                Assertion::nonUniqueSlug(
                    $request->slug,
                    $slugRepository,
                    'static_page.create.errors.slug_exists',
                    'slug',
                    $id
                );
                return true;
            } catch (AssertionFailedException $failedException) {
                $response->getNotification()->addError(
                    $failedException->getPropertyPath(),
                    $failedException->getMessage()
                );
                return false;
            }
        }

        return true;
    }
}
