<?php

declare(strict_types=1);

namespace Telenco\Component\StaticPage\Presentation\Presenter;

use Marketplace\Component\CleanArchiCore\Utils\Str;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Telenco\Component\StaticPage\Domain\Presenter\CreateStaticPagePresenterInterface;
use Telenco\Component\StaticPage\Domain\UseCase\CreateStaticPageUseCase\DTO\CreateStaticPageResponse;
use Telenco\Component\StaticPage\Presentation\ViewModel\CreateStaticPageViewModel;

class CreateStaticPagePresenter implements CreateStaticPagePresenterInterface
{
    private CreateStaticPageViewModel $viewModel;

    public function __construct(
        private TranslatorInterface $translator
    ) {
        $this->viewModel = new CreateStaticPageViewModel($translator);
    }

    public function present(CreateStaticPageResponse $response): void
    {
        $this->viewModel->error = $response->getNotification()->hasError();
        $this->viewModel->isSaved = $response->getStaticPage() !== null;
        $this->viewModel->staticPage = $response->getStaticPage();
        $this->viewModel->templateVariables = $response->getTemplateVariables();


        foreach ($response->getNotification()->getErrors() as $error) {
            $this->viewModel->addFormErrors(
                $error->getFieldName(),
                $this->translator->trans($error->getMessage(), [], 'translations')
            );
        }
    }

    public function viewModel(FormInterface $form): CreateStaticPageViewModel
    {
        if ($this->viewModel->error) {
            foreach ($this->viewModel->formErrors as $error) {
                if (str_contains($error['field'], '.')) {
                    $locale = Str::extractLeftPart(
                        Str::lastRightPart($error['field'], '.', 2),
                        '.'
                    );
                    $field = Str::lastRightPart($error['field'], '.');
                    $form->get('staticPageContents')->get($locale)->get($field)
                        ->addError(new FormError($error['message']));
                    continue;
                }
                $form->get($error['field'])->addError(new FormError($error['message']));
            }
        }
        $this->viewModel->form = $form->createView();
        return $this->viewModel;
    }
}
