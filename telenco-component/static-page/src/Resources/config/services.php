<?php

namespace Symfony\Component\DependencyInjection\Loader\Configurator;

return function (ContainerConfigurator $configurator) {
    $services = $configurator->services()
        ->defaults()
        ->autowire()
        ->autoconfigure()
    ;
    $services->load('Telenco\\Component\\StaticPage\\', '../../../src/*')
        ->exclude('../../../src/{DependencyInjection,Entity,Resources,TelencoStaticPageBundle.php}');
};
