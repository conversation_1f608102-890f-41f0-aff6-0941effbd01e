<?php

declare(strict_types=1);

namespace Telenco\Component\StaticPage\Infrastructure\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use DateTimeImmutable;
use Telenco\Component\StaticPage\Infrastructure\Adapter\Repository\StaticPageRepository;
use Telenco\Component\Shared\Infrastructure\Entity\Region;

#[ORM\Entity(repositoryClass: StaticPageRepository::class)]
class StaticPage
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: "string")]
    private string $status;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private DateTimeImmutable $createdAt;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private DateTimeImmutable $updatedAt;

    #[ORM\OneToMany(mappedBy: "staticPage", targetEntity: StaticPageContent::class)]
    private Collection $staticPageContents;

    #[ORM\ManyToMany(targetEntity: Region::class, cascade: ["persist"])]
    private Collection $staticPageRegions;

    #[ORM\ManyToOne(targetEntity: StaticPage::class, inversedBy: "children")]
    private ?StaticPage $parent = null;

    public function __construct()
    {
        $this->staticPageContents = new ArrayCollection();
        $this->createdAt = new DateTimeImmutable();
        $this->updatedAt = new DateTimeImmutable();
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return StaticPage
     */
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return StaticPage
     */
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return DateTimeImmutable
     */
    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    /**
     * @param DateTimeImmutable $createdAt
     * @return StaticPage
     */
    public function setCreatedAt(DateTimeImmutable $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    /**
     * @return DateTimeImmutable
     */
    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    /**
     * @param DateTimeImmutable $updatedAt
     * @return StaticPage
     */
    public function setUpdatedAt(DateTimeImmutable $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     * @return ArrayCollection|Collection
     */
    public function getStaticPageContents(): ArrayCollection|Collection
    {
        return $this->staticPageContents;
    }

    /**
     * @param ArrayCollection|Collection $staticPageContents
     * @return StaticPage
     */
    public function setStaticPageContents(ArrayCollection|Collection $staticPageContents): self
    {
        $this->staticPageContents = $staticPageContents;
        return $this;
    }

    public function addStaticPageContent(StaticPageContent $staticPageContent): self
    {
        if (!$this->staticPageContents->contains($staticPageContent)) {
            $this->staticPageContents->add($staticPageContent);
        }
        return $this;
    }

    public function getStaticPageRegions(): Collection
    {
        return $this->staticPageRegions;
    }

    public function setStaticPageRegions(Collection $staticPageRegions): self
    {
        $this->staticPageRegions = $staticPageRegions;
        return $this;
    }

    public function getParent(): ?StaticPage
    {
        return $this->parent;
    }

    public function setParent(?StaticPage $parent): self
    {
        $this->parent = $parent;
        return $this;
    }
}
