<?php

declare(strict_types=1);

namespace Telenco\Component\StaticPage\Infrastructure\Mapper;

use Exception;
use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use Telenco\Component\Shared\Domain\Model\Region;
use Telenco\Component\Shared\Infrastructure\Entity\Region as DoctrineRegion;
use Telenco\Component\StaticPage\Domain\Model\StaticPage;
use Telenco\Component\StaticPage\Domain\Model\StaticPageContent;
use Telenco\Component\StaticPage\Infrastructure\Entity\StaticPage as DoctrineStaticPage;

abstract class StaticPageMapper
{
    /**
     * @param DoctrineStaticPage $doctrineStaticPage
     * @return StaticPage
     * @throws Exception
     */
    public static function doctrineToDomain(DoctrineStaticPage $doctrineStaticPage): StaticPage
    {
        $staticPage = (new StaticPage())
            ->setId($doctrineStaticPage->getId())
            ->setStatus($doctrineStaticPage->getStatus())
            ->setStaticPageRegions(array_map(
                fn(DoctrineRegion $doctrineRegion) => (new Region())
                    ->setId($doctrineRegion->getId())
                    ->setName($doctrineRegion->getName()),
                $doctrineStaticPage->getStaticPageRegions()->toArray()
            ))
            ->setCreatedAt($doctrineStaticPage->getCreatedAt())
            ->setUpdatedAt($doctrineStaticPage->getUpdatedAt())
        ;
        $parent = $doctrineStaticPage->getParent();
        if ($parent === null) {
            $staticPage->setParent(null);
        } else {
            $staticPage->setParent(StaticPageMapper::doctrineToDomain($parent));
        }

        $staticPageContents = [];
        foreach ($doctrineStaticPage->getStaticPageContents() as $doctrineStaticPageContent) {
            $staticPageContents[] = (new StaticPageContent())
                ->setId($doctrineStaticPageContent->getId())
                ->setLocale(Locale::create($doctrineStaticPageContent->getLocale()))
                ->setTitle($doctrineStaticPageContent->getTitle())
                ->setText($doctrineStaticPageContent->getContent())
                ->setSeoTitle($doctrineStaticPageContent->getSeoTitle())
                ->setSeoMetaDescription($doctrineStaticPageContent->getSeoDescription())
            ;
        }
        $staticPage->setStaticPageContents($staticPageContents);

        return $staticPage;
    }
}
