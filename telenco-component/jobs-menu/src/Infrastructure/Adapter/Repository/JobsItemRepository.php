<?php

declare(strict_types=1);

namespace Telenco\Component\JobsMenu\Infrastructure\Adapter\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetRegionServiceInterface;
use Telenco\Component\JobsMenu\Domain\Model\JobsItem;
use Telenco\Component\JobsMenu\Domain\Port\Repository\JobsItemRepositoryInterface;
use Telenco\Component\JobsMenu\Infrastructure\Entity\JobsItem as DoctrineJobsItem;
use Telenco\Component\JobsMenu\Infrastructure\Entity\JobsItemContent as DoctrineJobsItemContent;
use Telenco\Component\JobsMenu\Infrastructure\Mapper\JobsItemMapper;
use Telenco\Component\Shared\Domain\Model\Region;
use Telenco\Component\Shared\Domain\Status\Status;
use Telenco\Component\Shared\Infrastructure\Entity\Region as DoctrineRegion;

class JobsItemRepository extends ServiceEntityRepository implements JobsItemRepositoryInterface
{
    public function __construct(
        ManagerRegistry $registry,
        private GetRegionServiceInterface $getRegion
    ) {
        parent::__construct($registry, DoctrineJobsItem::class);
    }

    /**
     * @param int $id
     * @return JobsItem|null
     */
    public function findById(int $id): ?JobsItem
    {
        $doctrineJobsItem = parent::find($id);

        if (!$doctrineJobsItem instanceof DoctrineJobsItem) {
            return null;
        }
        return JobsItemMapper::doctrineToDomain($doctrineJobsItem);
    }

    /**
     * @param JobsItem $jobsItem
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function create(JobsItem $jobsItem): void
    {
        $doctrineJobsItem = new DoctrineJobsItem();
        $doctrineJobsItem
            ->setStatus($jobsItem->getStatus())
            ->setName($jobsItem->getName())
            ->setParentId($jobsItem->getParentId())
        ;


        $this->getEntityManager()->persist($doctrineJobsItem);
        $this->getEntityManager()->flush();

        $jobsItem->setId($doctrineJobsItem->getId());
    }

    public function update(JobsItem $jobsItem): void
    {
        $doctrineJobsItem = $this->find($jobsItem->getId());
        if ($doctrineJobsItem instanceof DoctrineJobsItem) {
            $doctrineJobsItem = JobsItemMapper::domainToDoctrine($jobsItem, $doctrineJobsItem);
            $doctrineJobsItem
                ->setRegions(new ArrayCollection(
                    array_map(
                        function (Region $region) {
                            /** @var DoctrineRegion $doctrineRegion */
                            $doctrineRegion = $this->getEntityManager()
                                ->getRepository(DoctrineRegion::class)->find($region->getId());
                            return $doctrineRegion;
                        },
                        $jobsItem->getRegions()
                    )
                ))
            ;

            $doctrineJobsItemContents = [];
            foreach ($jobsItem->getJobsItemContents() as $jobsItemContent) {
                $doctrineJobsItemContent =
                    $this
                        ->getEntityManager()
                        ->getRepository(DoctrineJobsItemContent::class)
                        ->find($jobsItemContent->getId())
                ;
                // TODO : Utiliser le mapper
                if (!$doctrineJobsItemContent instanceof DoctrineJobsItemContent) {
                    $doctrineJobsItemContent = new DoctrineJobsItemContent();
                }
                $doctrineJobsItemContents[] = $doctrineJobsItemContent
                    ->setId($jobsItemContent->getId())
                    ->setLocale($jobsItemContent->getLocale()->getLocale())
                    ->setLabel($jobsItemContent->getLabel())
                ;
            }

            $doctrineJobsItem->setJobsItemContents(new ArrayCollection($doctrineJobsItemContents));
        }
        $this->getEntityManager()->persist($doctrineJobsItem);
        $this->getEntityManager()->flush();
    }

    /**
     * @return JobsItem[]
     */
    public function getAllJobsItems(): array
    {
        $doctrineJobsItem = parent::findAll();

        $jobsItems = [];
        foreach ($doctrineJobsItem as $jobsItem) {
            $jobsItems[] = JobsItemMapper::doctrineToDomain($jobsItem);
        }
        return $jobsItems;
    }

    /**
     * @param string $name
     * @return JobsItem|null
     * @throws NonUniqueResultException
     */
    public function findByName(string $name): ?JobsItem
    {
        $doctrineJobsItem = $this->createQueryBuilder('ji')
            ->where('ji.name = :name')
            ->andWhere('ji.status != :status')
            ->setParameter('name', $name)
            ->setParameter('status', Status::DELETED)
            ->getQuery()
            ->getOneOrNullResult();

        if ($doctrineJobsItem === null) {
            return null;
        }

        return JobsItemMapper::doctrineToDomain($doctrineJobsItem);
    }

    public function delete(int $id): bool
    {
        $jobsItem = $this->find($id);
        if (!$jobsItem instanceof DoctrineJobsItem) {
            return false;
        }
        $jobsItem->setStatus(Status::DELETED);
        $this->getEntityManager()->persist($jobsItem);
        $this->getEntityManager()->flush();

        return true;
    }

    public function getAllJobsItemsPositionOrdered(): array
    {
        $doctrineJobsItems = $this->createQueryBuilder('ji')
            ->where('ji.status != :status')
            ->setParameter('status', Status::DELETED)
            ->orderBy('ji.position')
            ->getQuery()
            ->getResult()
        ;

        $jobsItems = [];
        foreach ($doctrineJobsItems as $doctrineJobsItem) {
            $jobsItems[] = JobsItemMapper::doctrineToDomain($doctrineJobsItem);
        }

        return $jobsItems;
    }

    public function getPublishedJobsItemsPositionOrdered(): array
    {
        $doctrineJobsItems = $this->createQueryBuilder('ji')
            ->Leftjoin('ji.regions', 'region')
            ->addSelect('region')
            ->where('ji.status = :status')
            ->andWhere('region.name = :region')
            ->setParameters([
                'status' => Status::PUBLISHED,
                'region' => $this->getRegion
            ])
            ->orderBy('ji.position', 'ASC')
            ->getQuery()
            ->getResult()
        ;

        $jobsItems = [];
        foreach ($doctrineJobsItems as $doctrineJobsItem) {
            $jobsItems[] = JobsItemMapper::doctrineToDomain($doctrineJobsItem);
        }

        return $jobsItems;
    }
}
