<?php

declare(strict_types=1);

namespace Telenco\Component\JobsMenu\Presentation\View;

use Marketplace\Component\CleanArchiCore\Utils\Flash\FlashMessageInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Telenco\Component\JobsMenu\Presentation\ViewModel\UpdateJobsItemViewModel;
use Twig\Environment;

class UpdateJobsItemView
{
    public function __construct(
        private FlashMessageInterface $flashMessage,
        private UrlGeneratorInterface $urlGenerator,
        private Environment $twig,
    ) {
    }

    public function generateView(UpdateJobsItemViewModel $viewModel): Response
    {
        if ($viewModel->hasFlashMessage()) {
            foreach ($viewModel->flashMessages as ['type' => $type, 'message' => $message]) {
                $this->flashMessage->add($type, $message);
            }
        }

        if (!$viewModel->error && $viewModel->isSaved) {
            $this->flashMessage->add('success', 'jobs_menu.jobs_item.update.success');
            return new RedirectResponse(
                $this->urlGenerator->generate('jobs-menu.home')
            );
        }

        return new Response(
            $this->twig->render('jobs-menu/update-jobs-item.html.twig', [
                'viewModel' => $viewModel
            ])
        );
    }
}
