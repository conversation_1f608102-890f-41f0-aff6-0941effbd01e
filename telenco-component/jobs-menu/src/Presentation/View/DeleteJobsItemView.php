<?php

declare(strict_types=1);

namespace Telenco\Component\JobsMenu\Presentation\View;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Telenco\Component\JobsMenu\Presentation\ViewModel\DeleteJobsItemViewModel;

class DeleteJobsItemView
{
    public function __construct(
        private TranslatorInterface $translator,
        private SerializerInterface $serializer
    ) {
    }

    public function generateView(DeleteJobsItemViewModel $viewModel): JsonResponse
    {
        $type = 'success';
        $message = $this->translator->trans(
            'jobs_menu.jobs_item.delete.success',
            [],
            'translations'
        );

        // Jobs Item doesn't exist
        if ($viewModel->isDeleted() === false) {
            $message = $this->translator->trans(
                'jobs_menu.jobs_item.delete.error',
                [],
                'translations'
            );
            $type = 'error';
        }

        return JsonResponse::fromJsonString($this->serializer->serialize(
            ["type" => $type, "message" => $message],
            'json'
        ));
    }
}
