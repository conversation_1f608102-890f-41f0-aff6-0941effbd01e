<?php

declare(strict_types=1);

namespace Telenco\Component\JobsMenu\Presentation\ViewModel;

use Symfony\Contracts\Translation\TranslatorInterface;

class UpdateJobsItemViewModel extends CreateJobsItemViewModel
{
    public function __construct(private TranslatorInterface $translator)
    {
        parent::__construct($translator);
    }

    public function headerTitle(): string
    {
        return $this->translator->trans(id: 'jobs_menu.jobs_item.update.header_title', domain: 'translations');
    }

    public function pageTitle(): string
    {
        return $this->translator->trans(id: 'jobs_menu.jobs_item.update.title', domain: 'translations');
    }
}
