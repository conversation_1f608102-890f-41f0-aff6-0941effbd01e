<?php

declare(strict_types=1);

namespace Telenco\Component\JobsMenu\Presentation\ViewModel;

class OperatorJobsItemsShowViewModel
{
    /**
     * @param array $jobsItemsDrafted
     * @param array $jobsItemsCompleted
     * @param array $jobsItemsPublished
     */
    public function __construct(
        private readonly array $jobsItemsDrafted,
        private readonly array $jobsItemsCompleted,
        private readonly array $jobsItemsPublished
    ) {
    }

    /**
     * @return array
     */
    public function getJobsItemsDrafted(): array
    {
        return $this->jobsItemsDrafted;
    }

    /**
     * @return array
     */
    public function getJobsItemsCompleted(): array
    {
        return $this->jobsItemsCompleted;
    }

    /**
     * @return array
     */
    public function getJobsItemsPublished(): array
    {
        return $this->jobsItemsPublished;
    }
}
