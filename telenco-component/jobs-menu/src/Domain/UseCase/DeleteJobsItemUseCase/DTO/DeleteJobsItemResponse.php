<?php

declare(strict_types=1);

namespace Telenco\Component\JobsMenu\Domain\UseCase\DeleteJobsItemUseCase\DTO;

final class DeleteJobsItemResponse
{
    private bool $deleted;

    /**
     * @return bool
     */
    public function isDeleted(): bool
    {
        return $this->deleted;
    }

    /**
     * @param bool $deleted
     * @return DeleteJobsItemResponse
     */
    public function setDeleted(bool $deleted): DeleteJobsItemResponse
    {
        $this->deleted = $deleted;
        return $this;
    }
}
