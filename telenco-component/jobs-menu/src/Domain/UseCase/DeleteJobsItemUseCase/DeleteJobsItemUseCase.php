<?php

declare(strict_types=1);

namespace Telenco\Component\JobsMenu\Domain\UseCase\DeleteJobsItemUseCase;

use Telenco\Component\JobsMenu\Domain\Port\Repository\JobsItemRepositoryInterface;
use Telenco\Component\JobsMenu\Domain\Presenter\DeleteJobsItemPresenterInterface;
use Telenco\Component\JobsMenu\Domain\UseCase\DeleteJobsItemUseCase\DTO\DeleteJobsItemRequest;
use Telenco\Component\JobsMenu\Domain\UseCase\DeleteJobsItemUseCase\DTO\DeleteJobsItemResponse;

final class DeleteJobsItemUseCase
{
    public function __construct(private JobsItemRepositoryInterface $jobsItemRepository)
    {
    }

    public function execute(DeleteJobsItemRequest $request, DeleteJobsItemPresenterInterface $presenter): void
    {
        $response = new DeleteJobsItemResponse();

        $result = $this->jobsItemRepository->delete($request->getId());
        $response->setDeleted($result);

        $presenter->present($response);
    }
}
