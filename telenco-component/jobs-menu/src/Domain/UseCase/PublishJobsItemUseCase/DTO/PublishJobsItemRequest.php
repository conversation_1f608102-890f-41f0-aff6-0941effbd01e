<?php

declare(strict_types=1);

namespace Telenco\Component\JobsMenu\Domain\UseCase\PublishJobsItemUseCase\DTO;

use Telenco\Component\JobsMenu\Domain\Model\JobsItem;

final class PublishJobsItemRequest
{
    public function __construct(
        private array $jobsItemsToPublish,
    ) {
    }

    public static function create(mixed ...$params): self
    {
        return new self(...$params);
    }

    /**
     * @return JobsItem[]
     */
    public function getJobsItemsToPublish(): array
    {
        return $this->jobsItemsToPublish;
    }

    /**
     * @param JobsItem[] $jobsItemsToPublish
     * @return PublishJobsItemRequest
     */
    public function setJobsItemsToPublish(array $jobsItemsToPublish): PublishJobsItemRequest
    {
        $this->jobsItemsToPublish = $jobsItemsToPublish;
        return $this;
    }
}
