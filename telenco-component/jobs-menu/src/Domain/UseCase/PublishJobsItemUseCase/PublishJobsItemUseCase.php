<?php

declare(strict_types=1);

namespace Telenco\Component\JobsMenu\Domain\UseCase\PublishJobsItemUseCase;

use Telenco\Component\JobsMenu\Domain\Model\JobsItem;
use Telenco\Component\JobsMenu\Domain\Port\Repository\JobsItemRepositoryInterface;
use Telenco\Component\JobsMenu\Domain\Presenter\PublishJobsItemPresenterInterface;
use Telenco\Component\JobsMenu\Domain\UseCase\PublishJobsItemUseCase\DTO\PublishJobsItemRequest;
use Telenco\Component\JobsMenu\Domain\UseCase\PublishJobsItemUseCase\DTO\PublishJobsItemResponse;

final class PublishJobsItemUseCase
{
    public function __construct(
        private JobsItemRepositoryInterface $jobsItemRepository
    ) {
    }

    public function execute(
        PublishJobsItemRequest $request,
        PublishJobsItemPresenterInterface $presenter
    ): void {
        $response = new PublishJobsItemResponse();

        // Unpublish all jobs items
        $allJobsItems = $this->jobsItemRepository->getAllJobsItems();
        foreach ($allJobsItems as $jobsItem) {
            if ($jobsItem->unpublish()) {
                $this->jobsItemRepository->update($jobsItem);
            }
        }

        // Publish JobsItems from Request
        foreach ($request->getJobsItemsToPublish() as $jobsItem) {
            /** @var int $jobsItemId */
            $jobsItemId = $jobsItem->getId();
            if ($jobsItem->publish() === false) {
                $this->setUpPresenter(
                    $response,
                    $presenter,
                    strval($jobsItemId),
                    'jobs_menu.jobs_item.publish.error'
                );
                continue;
            }
            $this->jobsItemRepository->update($jobsItem);
        }

        // Default response
        $this->setUpPresenter($response, $presenter);
    }

    private function setUpPresenter(
        PublishJobsItemResponse $response,
        PublishJobsItemPresenterInterface $presenter,
        ?string $field = null,
        ?string $message = null
    ): void {
        if ($message !== null) {
            $response->getNotification()->addError($field, $message);
        }
        $presenter->present($response);
    }
}
