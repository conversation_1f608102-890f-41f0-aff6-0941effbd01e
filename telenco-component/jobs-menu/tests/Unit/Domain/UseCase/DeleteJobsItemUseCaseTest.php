<?php

declare(strict_types=1);

namespace Telenco\Component\JobsMenu\Tests\Unit\Domain\UseCase;

use Exception;
use Generator;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Telenco\Component\JobsMenu\Domain\Port\Repository\JobsItemRepositoryInterface;
use Telenco\Component\JobsMenu\Domain\Presenter\DeleteJobsItemPresenterInterface;
use Telenco\Component\JobsMenu\Domain\UseCase\DeleteJobsItemUseCase\DeleteJobsItemUseCase;
use Telenco\Component\JobsMenu\Domain\UseCase\DeleteJobsItemUseCase\DTO\DeleteJobsItemRequest;
use Telenco\Component\JobsMenu\Domain\UseCase\DeleteJobsItemUseCase\DTO\DeleteJobsItemResponse;

class DeleteJobsItemUseCaseTest extends TestCase
{
    private DeleteJobsItemUseCase $useCase;
    private DeleteJobsItemPresenterInterface $presenter;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements DeleteJobsItemPresenterInterface {
            public DeleteJobsItemResponse $response;

            public function present(DeleteJobsItemResponse $response): void
            {
                $this->response = $response;
            }
        };

        $prophecy = new Prophet();

        /** @var JobsItemRepositoryInterface|ObjectProphecy $jobsItemRepository */
        $jobsItemRepository = $prophecy->prophesize(JobsItemRepositoryInterface::class);


        $jobsItemRepository->delete(Argument::type('int'))
            ->will(function ($args) {
                $id = $args[0];
                if ($id ===  0) {
                    return false;
                }
                return true;
            })
        ;

        $this->useCase = new DeleteJobsItemUseCase(
            $jobsItemRepository->reveal()
        );
    }


    /**
     * @dataProvider provideDeleteJobsItem
     * @throws Exception
     */
    public function testExecute(DeleteJobsItemRequest $request, DeleteJobsItemResponse $expected): void
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($expected, $this->presenter->response);
    }

    /**
     * @throws Exception
     */
    public function provideDeleteJobsItem(): Generator
    {
        // Jobs Item Delete successful
        /** GIVEN: jobsItem exist */
        $request = new DeleteJobsItemRequest(1);
        /** WHEN: calling DeleteJobsItemUseCase */
        /** THEN: Response successful delete */
        $response = (new DeleteJobsItemResponse())->setDeleted(true);
        yield 'JobsItem delete successful' => [$request, $response];

        // Jobs Item doesn't exist
        /** GIVEN: jobsItem doesn't exist */
        $request = new DeleteJobsItemRequest(0);
        /** WHEN: calling DeleteJobsItemUseCase */
        /** THEN: Response not delete */
        $response = (new DeleteJobsItemResponse())->setDeleted(false);
        yield 'JobsItem does not exist' => [$request, $response];
    }
}
