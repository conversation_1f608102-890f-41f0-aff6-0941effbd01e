<?php

namespace Telenco\Component\Shared\Infrastructure\Strategy;

use RuntimeException;
use Symfony\Component\HttpFoundation\Response;
use Telenco\Component\Shared\Domain\Request\SlugRequestInterface;

final class ShowItemContext
{
    /** @var ShowSlugStrategyInterface[]  */
    private array $strategies = [];

    public function __construct(iterable $strategies)
    {
        foreach ($strategies as $strategy) {
            $this->addStrategy($strategy);
        }
    }

    private function addStrategy(ShowSlugStrategyInterface $strategy): void
    {
        $this->strategies[] = $strategy;
    }

    public function process(SlugRequestInterface $request): Response
    {
        foreach ($this->strategies as $strategy) {
            if ($strategy->canProcess($request)) {
                return $strategy->process($request);
            }
        }

        throw new RuntimeException('strategy error');
    }
}
