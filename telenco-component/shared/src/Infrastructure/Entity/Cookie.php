<?php

declare(strict_types=1);

namespace Telenco\Component\Shared\Infrastructure\Entity;

use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use Telenco\Component\Shared\Infrastructure\Adapter\Repository\CookieRepository;

#[ORM\Entity(repositoryClass: CookieRepository::class)]
class Cookie
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer', nullable: false)]
    private int $id;

    #[ORM\Column(type: 'boolean', options: ["default" => false])]
    private bool $accepted;

    #[ORM\Column(type: 'datetime_immutable', nullable: false)]
    private DateTimeImmutable $createdAt;

    public function __construct(bool $accepted)
    {
        $this->accepted = $accepted;
        $this->createdAt = new DateTimeImmutable();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function isAccepted(): bool
    {
        return $this->accepted;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }
}
