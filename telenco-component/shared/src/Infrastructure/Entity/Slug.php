<?php

declare(strict_types=1);

namespace Telenco\Component\Shared\Infrastructure\Entity;

use Doctrine\ORM\Mapping as ORM;
use Stringable;
use Telenco\Component\Shared\Infrastructure\Adapter\Repository\SlugRepository;

#[ORM\Entity(repositoryClass: SlugRepository::class)]
class Slug implements Stringable
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id;

    #[ORM\Column(type: 'string', length: 255)]
    private string $name;

    #[ORM\Column(type: 'string', length: 255)]
    private string $ref;

    #[ORM\Column(type: 'integer')]
    private int $refId;

    public function __toString(): string
    {
        return $this->name;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getRef(): string
    {
        return $this->ref;
    }

    public function setRef(string $ref): self
    {
        $this->ref = $ref;
        return $this;
    }

    public function getRefId(): int
    {
        return $this->refId;
    }

    public function setRefId(int $refId): self
    {
        $this->refId = $refId;
        return $this;
    }
}
