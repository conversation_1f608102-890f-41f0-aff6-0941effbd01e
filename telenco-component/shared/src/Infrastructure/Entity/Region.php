<?php

declare(strict_types=1);

namespace Telenco\Component\Shared\Infrastructure\Entity;

use DateTimeImmutable;
use DateTimeInterface;
use Telenco\Component\Shared\Domain\Model\Region as ModelRegion;
use Telenco\Component\Shared\Infrastructure\Adapter\Repository\RegionRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: RegionRepository::class), ORM\Table(name: 'regions')]
class Region
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 50, nullable: false)]
    private string $name;

    #[ORM\Column(type: 'string', length: 50, nullable: false)]
    private string $code;

    #[ORM\Column(type: 'boolean', options: ["default" => true])]
    private bool $enabled;

    #[ORM\Column(type: 'datetime_immutable')]
    private DateTimeInterface $createdAt;

    #[ORM\Column(type: 'simple_array')]
    private array $authorisedPaymentMethods;

    public function __construct()
    {
        $this->createdAt = new DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): static
    {
        $this->id = $id;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;
        return $this;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;
        return $this;
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;
        return $this;
    }

    public function getCreatedAt(): DateTimeImmutable|DateTimeInterface
    {
        return $this->createdAt;
    }

    public static function domainToDoctrine(ModelRegion $regionModel): self
    {
        return (new Region())
            ->setId($regionModel->getId())
            ->setName($regionModel->getName())
        ;
    }

    public function getAuthorisedPaymentMethods(): array
    {
        return $this->authorisedPaymentMethods;
    }

    public function setAuthorisedPaymentMethods(array $authorisedPaymentMethods): self
    {
        $this->authorisedPaymentMethods = $authorisedPaymentMethods;
        return $this;
    }
}
