<?php

declare(strict_types=1);

namespace Telenco\Component\Shared\Infrastructure\Adapter\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;
use Telenco\Component\Shared\Domain\Model\Slug;
use Telenco\Component\Shared\Domain\Port\Repository\SlugRepositoryInterface;
use Telenco\Component\Shared\Domain\Status\Status;
use Telenco\Component\Shared\Infrastructure\Entity\Slug as DoctrineSlug;
use Telenco\Component\Shared\Infrastructure\Mapper\SlugMapper;

class SlugRepository extends ServiceEntityRepository implements SlugRepositoryInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DoctrineSlug::class);
    }

    /**
     * @param int $id
     * @return Slug|null
     */
    public function findById(int $id): ?Slug
    {
        $doctrineSlug = parent::find($id);
        if (!$doctrineSlug instanceof DoctrineSlug) {
            return null;
        }
        return SlugMapper::doctrineToDomain($doctrineSlug);
    }

    /**
     * @param string $name
     * @return Slug|null
     * @throws NonUniqueResultException
     */
    public function findByName(string $name): ?Slug
    {
        $doctrineSlug = $this->createQueryBuilder('s')
            ->where('s.name = :name')
            ->setParameter('name', $name)
            ->getQuery()
            ->getOneOrNullResult()
        ;
        if ($doctrineSlug === null) {
            return null;
        }
        return SlugMapper::doctrineToDomain($doctrineSlug);
    }

    public function create(Slug $slug): void
    {
        $doctrineSlug = (new DoctrineSlug())
            ->setName($slug->getName())
            ->setRef($slug->getRef())
            ->setRefId($slug->getRefId())
        ;

        $this->getEntityManager()->persist($doctrineSlug);
        $this->getEntityManager()->flush();


        $slug->setId($doctrineSlug->getId());
    }

    public function update(int $slugId, string $slugName): void
    {
        /** @var DoctrineSlug $doctrineSlug */
        $doctrineSlug = $this->find($slugId);

        if (!$doctrineSlug instanceof DoctrineSlug) {
            return;
        }

        $doctrineSlug->setName($slugName);
        $this->getEntityManager()->persist($doctrineSlug);
        $this->getEntityManager()->flush();
    }

    public function findByRefAndRefId(string $ref, int $refId): ?Slug
    {
        $doctrineSlug = $this->createQueryBuilder('s')
            ->where('s.ref = :ref')
            ->andWhere('s.refId = :refId')
            ->setParameter('ref', $ref)
            ->setParameter('refId', $refId)
            ->getQuery()
            ->getOneOrNullResult()
        ;
        if ($doctrineSlug === null) {
            return null;
        }
        return SlugMapper::doctrineToDomain($doctrineSlug);
    }

    public function findPublishedStaticPagesSlugs(): array
    {
        $doctrineSlugQuery = $this->createQueryBuilder('slug')
            ->join('Telenco\Component\StaticPage\Infrastructure\Entity\StaticPage', 'page')
            ->where('page.status = :status')
            ->andWhere('page.id = slug.refId')
            ->setParameter('status', Status::PUBLISHED);

        $doctrineSlugs = $doctrineSlugQuery
            ->getQuery()
            ->getResult();

        return array_map(fn (DoctrineSlug $slug) =>  SlugMapper::doctrineToDomain($slug), $doctrineSlugs);
    }
}
