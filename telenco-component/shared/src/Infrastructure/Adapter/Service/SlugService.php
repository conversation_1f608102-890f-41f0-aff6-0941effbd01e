<?php

declare(strict_types=1);

namespace Telenco\Component\Shared\Infrastructure\Adapter\Service;

use Telenco\Component\Shared\Domain\Model\Slug;
use Telenco\Component\Shared\Domain\Port\Model\SluggableInterface;
use Telenco\Component\Shared\Domain\Port\Repository\SlugRepositoryInterface;
use Telenco\Component\Shared\Domain\Port\Service\SlugServiceInterface;

class SlugService implements SlugServiceInterface
{
    public function __construct(
        private SlugRepositoryInterface $slugRepository,
    ) {
    }

    public function getSlug(SluggableInterface $sluggable): ?string
    {
        $id = $sluggable->getId();
        if ($id === null) {
            return null;
        }
        $slug = $this->slugRepository->findByRefAndRefId($sluggable::class, $id);
        if (!$slug instanceof Slug) {
            return null;
        }
        return $slug->getName();
    }

    public function setSlug(SluggableInterface $sluggable): SluggableInterface
    {
        $id = $sluggable->getId();
        if ($id === null) {
            return $sluggable;
        }
        $slug = $this->slugRepository->findByRefAndRefId($sluggable::class, $id);
        if ($slug instanceof Slug) {
            $sluggable->setSlug($slug->getName());
        }

        return $sluggable;
    }
}
