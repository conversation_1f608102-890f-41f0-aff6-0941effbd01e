<?php

declare(strict_types=1);

namespace Telenco\Component\Shared\Infrastructure\Mapper;

use Telenco\Component\Shared\Domain\Model\Region;
use Telenco\Component\Shared\Domain\Port\Mapper\RegionMapperInterface;
use Telenco\Component\Shared\Infrastructure\Entity\Region as DoctrineRegion;

class RegionMapper implements RegionMapperInterface
{
    public static function doctrineToDomain(DoctrineRegion $doctrineRegion): Region
    {
        return (new Region())
            ->setId($doctrineRegion->getId())
            ->setName($doctrineRegion->getName())
            ->setCode($doctrineRegion->getCode())
            ->setEnabled($doctrineRegion->isEnabled())
        ;
    }
}
