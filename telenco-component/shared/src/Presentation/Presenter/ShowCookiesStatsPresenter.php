<?php

declare(strict_types=1);

namespace Telenco\Component\Shared\Presentation\Presenter;

use Telenco\Component\Shared\Domain\Presenter\ShowCookiesStatsPresenterInterface;
use Telenco\Component\Shared\Domain\UseCase\ShowCookiesStatsUseCase\DTO\ShowCookiesStatsResponse;
use Telenco\Component\Shared\Presentation\ViewModel\ShowCookiesStatsViewModel;

final class ShowCookiesStatsPresenter implements ShowCookiesStatsPresenterInterface
{
    private ShowCookiesStatsViewModel $viewModel;

    public function __construct()
    {
        $this->viewModel = new ShowCookiesStatsViewModel();
    }

    public function present(ShowCookiesStatsResponse $response): void
    {
        $this->viewModel->nbAccepted = $response->nbAccepted;
        $this->viewModel->nbRefused = $response->nbRefused;
    }

    public function viewModel(): ShowCookiesStatsViewModel
    {
        return $this->viewModel;
    }
}
