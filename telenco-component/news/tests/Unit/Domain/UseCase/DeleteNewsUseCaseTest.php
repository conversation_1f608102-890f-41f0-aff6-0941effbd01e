<?php

declare(strict_types=1);

namespace Telenco\Component\News\Tests\Unit\Domain\UseCase;

use Exception;
use Generator;
use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Telenco\Component\News\Domain\Model\News;
use Telenco\Component\News\Domain\Model\NewsContent;
use Telenco\Component\News\Domain\Port\Repository\NewsRepositoryInterface;
use Telenco\Component\News\Domain\Presenter\DeleteNewsPresenterInterface;
use Telenco\Component\News\Domain\UseCase\DeleteNewsUseCase\DeleteNewsUseCase;
use Telenco\Component\News\Domain\UseCase\DeleteNewsUseCase\DTO\DeleteNewsResponse;
use Telenco\Component\News\Domain\UseCase\DeleteNewsUseCase\DTO\DeleteNewsRequest;
use PHPUnit\Framework\TestCase;
use Telenco\Component\Shared\Domain\Model\Region;
use Telenco\Component\Shared\Domain\Status\Status;

final class DeleteNewsTest extends TestCase
{
    private DeleteNewsUseCase $useCase;
    private DeleteNewsPresenterInterface $presenter;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements DeleteNewsPresenterInterface {
            public DeleteNewsResponse $response;
            public function present(DeleteNewsResponse $response): void
            {
                $this->response = $response;
            }
        };

        $prophecy = new Prophet();
        /** @var ObjectProphecy|NewsRepositoryInterface $newsRepository */
        $newsRepository = $prophecy->prophesize(NewsRepositoryInterface::class);

        $newsPublished = $this->makeNewsModel(
            status: Status::PUBLISHED,
            regions: [(new Region())->setId(1)->setName('France')],
            contents: [
            $this->makeNewsContent(
                locale: 'FR',
                title: 'Label FR',
            ),
            $this->makeNewsContent(
                locale: 'EN',
                title: 'Label EN',
            ),
            $this->makeNewsContent(
                locale: 'DE',
                title: 'Label DE',
            ),
            ]
        );
        $newsRepository->findById(Argument::type('int'))->will(
            function ($args) use ($newsPublished) {
                $id = $args[0];
                if ($id > 0) {
                    return $newsPublished;
                }
                return null;
            }
        );

        $this->useCase = new DeleteNewsUseCase($newsRepository->reveal());
    }

    /**
     * @dataProvider provideExecute
     * @throws Exception
     */
    public function testExecute(DeleteNewsRequest $request, DeleteNewsResponse $expected): void
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($expected, $this->presenter->response);
    }

    public function provideExecute(): Generator
    {
        //Delete news: Success
        $request = new DeleteNewsRequest(1);

        $expected = new DeleteNewsResponse();
        $expected->deleted = true;

        yield 'Delete news: Success' => [$request, $expected];

        //Delete news: Not Found
        $request = new DeleteNewsRequest(0);

        $expected = new DeleteNewsResponse();
        $expected->deleted = false;
        $expected->newsExist = false;

        yield 'Delete news: Not Found' => [$request, $expected];
    }

    private function makeNewsContent(
        ?string $locale = null,
        ?string $title = null,
    ): NewsContent {
        return (new NewsContent())
            ->setId(null)
            ->setTitle($title)
            ->setLocale(Locale::create($locale))
            ->setText(null)
            ->setSeoTitle(null)
            ->setSeoMetaDescription(null);
    }

    /**
     * @throws Exception
     */
    private function makeNewsModel(
        ?string $status = null,
        ?array $regions = [],
        ?array $contents = [],
    ): News {
        return (new News())
            ->setId(1)
            ->setStatus($status)
            ->setRegions($regions)
            ->setNewsContents($contents);
    }
}
