<?php

declare(strict_types=1);

namespace Telenco\Component\News\Tests\Unit\Domain\UseCase;

use Exception;
use Generator;
use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Telenco\Component\News\Domain\Exception\NewsNotFoundException;
use Telenco\Component\News\Domain\Exception\NewsUnauthorizedException;
use Telenco\Component\News\Domain\Model\News;
use Telenco\Component\News\Domain\Model\NewsContent;
use Telenco\Component\News\Domain\Port\Repository\NewsRepositoryInterface;
use Telenco\Component\News\Domain\Presenter\ShowNewsPresenterInterface;
use Telenco\Component\News\Domain\UseCase\ShowNewsUseCase\DTO\ShowNewsResponse;
use Telenco\Component\News\Domain\UseCase\ShowNewsUseCase\DTO\ShowNewsRequest;
use PHPUnit\Framework\TestCase;
use Telenco\Component\News\Domain\UseCase\ShowNewsUseCase\ShowNewsUseCase;
use Telenco\Component\Shared\Domain\Exception\SlugNotFoundException;
use Telenco\Component\Shared\Domain\Model\Region;
use Telenco\Component\Shared\Domain\Model\Slug;
use Telenco\Component\Shared\Domain\Port\Repository\SlugRepositoryInterface;
use Telenco\Component\Shared\Domain\Status\Status;

final class ShowNewsUseCaseTest extends TestCase
{
    private ShowNewsUseCase $useCase;

    private ShowNewsPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements ShowNewsPresenterInterface {
            public ShowNewsResponse $response;
            public function present(ShowNewsResponse $response): void
            {
                $this->response = $response;
            }
        };

        $prophecy = new Prophet();
        /** @var ObjectProphecy|NewsRepositoryInterface $newsRepository */
        $newsRepository = $prophecy->prophesize(NewsRepositoryInterface::class);
        /** @var ObjectProphecy|SlugRepositoryInterface $slugRepository */
        $slugRepository = $prophecy->prophesize(SlugRepositoryInterface::class);

        $slugRepository->findByName(Argument::type('string'))->will(
            function ($args) {
                $slugName = $args[0];
                if ($slugName === 'SlugExist') {
                    return (new Slug())
                        ->setName('SlugExist')
                        ->setId(1)
                        ->setRefId(1)
                        ->setRef(News::class);
                } elseif ($slugName === 'PageNotFound') {
                    return (new Slug())
                        ->setName('PageNotFound')
                        ->setId(2)
                        ->setRefId(10)
                        ->setRef(News::class);
                } elseif ($slugName === 'PageNotPublished') {
                    return (new Slug())
                        ->setName('PageNotPublished')
                        ->setId(2)
                        ->setRefId(2)
                        ->setRef(News::class);
                }
                return null;
            }
        );

        $contents = [
            $this->makeNewsContent(
                locale: 'FR',
                title: 'Label FR',
                content: 'Content FR',
                seoTitle: 'SEO Title FR',
                seoDescription: 'SEO Description FR'
            ),
            $this->makeNewsContent(
                locale: 'EN',
                title: 'Label EN',
                content: 'Content EN',
                seoTitle: 'SEO Title EN',
                seoDescription: 'SEO Description EN'
            ),
            $this->makeNewsContent(
                locale: 'DE',
                title: 'Label DE',
                content: 'Content DE',
                seoTitle: 'SEO Title DE',
                seoDescription: 'SEO Description DE'
            ),
        ];

        $newsRepository->findByIdAndRegion(Argument::type('int'), Argument::type('object'))->will(
            /**
             * @throws Exception
             */
            function ($args) use ($contents) {
                $id = $args[0];
                if ($id === 1) {
                    return (new News())
                        ->setId($id)
                        ->setNewsContents($contents)
                        ->setRegions([])
                        ->setStatus(Status::PUBLISHED)
                        ;
                } elseif ($id === 2) {
                    return (new News())
                        ->setId($id)
                        ->setNewsContents($contents)
                        ->setRegions([])
                        ->setStatus(Status::INCOMPLETE)
                        ;
                }
                return null;
            }
        );

        $this->useCase = new ShowNewsUseCase(
            $newsRepository->reveal(),
            $slugRepository->reveal()
        );
    }

    /**
     * @throws NewsNotFoundException
     * @throws NewsUnauthorizedException
     * @throws SlugNotFoundException
     * @dataProvider provideExecute
     */
    public function testExecute(ShowNewsRequest $request, ShowNewsResponse $expected): void
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($expected, $this->presenter->response);
    }

    /**
     * @return Generator
     * @throws Exception
     */
    public function provideExecute(): Generator
    {
        //Show News: Success
        $request = $this->makeShowNewsRequest(
            region: $this->makeRegion(),
            slug: 'SlugExist'
        );

        $news = (new News())
            ->setId(1)
            ->setStatus(Status::PUBLISHED)
            ->setRegions([])
            ->setNewsContents([
                $this->makeNewsContent(
                    locale: 'FR',
                    title: 'Label FR',
                    content: 'Content FR',
                    seoTitle: 'SEO Title FR',
                    seoDescription: 'SEO Description FR'
                )
            ]);

        $expected = new ShowNewsResponse();
        $expected->setNews($news);
        yield 'Show News: Success' => [$request, $expected];
    }

    /**
     * @throws SlugNotFoundException
     * @throws NewsUnauthorizedException
     */
    public function testThrowPageNotFound()
    {
        $this->expectException(NewsNotFoundException::class);
        $this->expectExceptionMessage("News not found for slug PageNotFound");
        $request = $this->makeShowNewsRequest(
            region: $this->makeRegion(),
            slug: 'PageNotFound'
        );
        $this->useCase->execute($request, $this->presenter);
    }

    /**
     * @throws SlugNotFoundException
     * @throws NewsNotFoundException
     */
    public function testThrowPageUnauthorized()
    {
        $this->expectException(NewsUnauthorizedException::class);
        $this->expectExceptionMessage("Page unauthorized");
        $request = $this->makeShowNewsRequest(
            region: $this->makeRegion(),
            slug: 'PageNotPublished'
        );
        $this->useCase->execute($request, $this->presenter);
    }

    /**
     * @throws NewsUnauthorizedException
     * @throws NewsNotFoundException
     */
    public function testThrowSlugNotFound()
    {
        $this->expectException(SlugNotFoundException::class);
        $this->expectExceptionMessage("Slug not found");
        $request = $this->makeShowNewsRequest(
            region: $this->makeRegion(),
            slug: 'SlugNotFound'
        );
        $this->useCase->execute($request, $this->presenter);
    }


    private function makeNewsContent(
        ?string $locale = null,
        ?string $title = null,
        ?string $content = null,
        ?string $seoTitle = null,
        ?string $seoDescription = null,
    ): NewsContent {
        return (new NewsContent())
            ->setId(null)
            ->setTitle($title)
            ->setLocale(Locale::create($locale))
            ->setText($content)
            ->setSeoTitle($seoTitle)
            ->setSeoMetaDescription($seoDescription);
    }

    private function makeShowNewsRequest(
        Region $region,
        string $slug,
    ): ShowNewsRequest {
        return new ShowNewsRequest($region, 'FR', $slug);
    }

    private function makeRegion(): Region
    {
        return (new Region())
            ->setId(1)
            ->setName('France');
    }
}
