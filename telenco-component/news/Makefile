analyse:
	phpcs && psalm --no-cache

.PHONY: test
test: vendor/autoload.php
	php ./vendor/bin/phpunit --colors --testdox

.PHONY: testc
testc:
	php -dxdebug.mode=coverage ./vendor/bin/phpunit --colors --testdox --coverage-html=./coverage

.PHONY: tw
tw: vendor/autoload.php ## Lance le watcher phpunit
	php ./vendor/bin/phpunit-watcher watch --colors

vendor/autoload.php: composer.lock
	composer install
	touch ./vendor/autoload.php
