<?php

declare(strict_types=1);

namespace Telenco\Component\News\Domain\UseCase\ShowNewsUseCase;

use Telenco\Component\News\Domain\Exception\NewsNotFoundException;
use Telenco\Component\News\Domain\Exception\NewsUnauthorizedException;
use Telenco\Component\News\Domain\Model\News;
use Telenco\Component\News\Domain\Port\Repository\NewsRepositoryInterface;
use Telenco\Component\News\Domain\Presenter\ShowNewsPresenterInterface;
use Telenco\Component\News\Domain\UseCase\ShowNewsUseCase\DTO\ShowNewsRequest;
use Telenco\Component\News\Domain\UseCase\ShowNewsUseCase\DTO\ShowNewsResponse;
use Telenco\Component\Shared\Domain\Exception\SlugNotFoundException;
use Telenco\Component\Shared\Domain\Model\Slug;
use Telenco\Component\Shared\Domain\Port\Repository\SlugRepositoryInterface;
use Telenco\Component\Shared\Domain\Status\Status;

final class ShowNewsUseCase
{

    public function __construct(
        private NewsRepositoryInterface $newsRepository,
        private SlugRepositoryInterface $slugRepository
    ) {
    }

    /**
     * @throws NewsUnauthorizedException
     * @throws SlugNotFoundException
     * @throws NewsNotFoundException
     */
    public function execute(ShowNewsRequest $request, ShowNewsPresenterInterface $presenter): void
    {
        $response = new ShowNewsResponse();
        $slugName = $request->slug;
        $slug = $this->slugRepository->findByName($slugName);

        if (!$slug instanceof Slug) {
            throw new SlugNotFoundException();
        }
        $news = $this->newsRepository->findByIdAndRegion($slug->getRefId(), $request->region);

        if (!$news instanceof News) {
            throw new NewsNotFoundException($slugName);
        }

        if ($news->getStatus() !== Status::PUBLISHED) {
            throw new NewsUnauthorizedException();
        }
        $newsContent = $news->getContentByLocale(strtoupper($request->locale));
        if ($newsContent === null) {
            $news->setNewsContents([]);
        } else {
            $news->setNewsContents([$newsContent]);
        }
        $response->setNews($news);
        $presenter->present($response);
    }
}
