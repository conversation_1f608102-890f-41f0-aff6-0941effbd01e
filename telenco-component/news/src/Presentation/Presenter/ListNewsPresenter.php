<?php

namespace Telenco\Component\News\Presentation\Presenter;

use Symfony\Contracts\Translation\TranslatorInterface;
use Telenco\Component\News\Domain\Presenter\ListNewsPresenterInterface;
use Telenco\Component\News\Domain\UseCase\ListNewsUseCase\DTO\ListNewsResponse;
use Telenco\Component\News\Presentation\ViewDTO\NewsData;
use Telenco\Component\News\Presentation\ViewModel\ListNewsViewModel;

class ListNewsPresenter implements ListNewsPresenterInterface
{
    private ListNewsViewModel $viewModel;

    public function __construct(
        private TranslatorInterface $translator
    ) {
        $this->viewModel = new ListNewsViewModel($this->translator);
    }

    public function present(ListNewsResponse $response): void
    {
        $this->viewModel->allNews = NewsData::createFromNewsModel($response->getAllNews());
        $this->viewModel->lastNews = NewsData::createFromNewsModel($response->getLastNews());
        $this->viewModel->categories = $response->getCategories();
    }

    /**
     * @return ListNewsViewModel
     */
    public function viewModel(): ListNewsViewModel
    {
        return $this->viewModel;
    }
}
