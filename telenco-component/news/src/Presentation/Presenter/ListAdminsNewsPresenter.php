<?php

namespace Telenco\Component\News\Presentation\Presenter;

use Symfony\Contracts\Translation\TranslatorInterface;
use Telenco\Component\News\Domain\Presenter\ListAdminNewsPresenterInterface;
use Telenco\Component\News\Domain\UseCase\ListAdminNewsUseCase\DTO\ListAdminNewsResponse;
use Telenco\Component\News\Presentation\ViewModel\ListAdminNewsViewModel;

class ListAdminsNewsPresenter implements ListAdminNewsPresenterInterface
{
    private ListAdminNewsViewModel $viewModel;

    public function __construct(
        private TranslatorInterface $translator
    ) {
        $this->viewModel = new ListAdminNewsViewModel($this->translator);
    }

    public function present(ListAdminNewsResponse $response): void
    {
        $this->viewModel->news = $response->getNews();
    }

    public function viewModel(): ListAdminNewsViewModel
    {
        return $this->viewModel;
    }
}
