<?php

declare(strict_types=1);

namespace Telenco\Component\News\Presentation\View;

use Telenco\Component\News\Presentation\ViewModel\ListAdminNewsViewModel;
use Twig\Environment;
use Symfony\Component\HttpFoundation\Response;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;

class ListAdminNewsView
{
    public function __construct(private Environment $twig)
    {
    }

    /**
     * @throws SyntaxError
     * @throws RuntimeError
     * @throws LoaderError
     */
    public function generateView(ListAdminNewsViewModel $viewModel): Response
    {
        return new Response(
            $this->twig->render('news/list-admin-news.html.twig', [
                'viewModel' => $viewModel
            ])
        );
    }
}
