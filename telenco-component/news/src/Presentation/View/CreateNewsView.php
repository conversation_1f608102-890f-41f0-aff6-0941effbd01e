<?php

namespace Telenco\Component\News\Presentation\View;

use Marketplace\Component\CleanArchiCore\Utils\Flash\FlashMessageInterface;
use Telenco\Component\News\Presentation\ViewModel\CreateNewsViewModel;
use Twig\Environment;
use Symfony\Component\HttpFoundation\Response;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;

class CreateNewsView
{
    public function __construct(
        private FlashMessageInterface $flashMessage,
        private Environment $twig,
    ) {
    }

    /**
     * @throws SyntaxError
     * @throws RuntimeError
     * @throws LoaderError
     */
    public function generateView(CreateNewsViewModel $viewModel): Response
    {
        if ($viewModel->hasFlashMessage()) {
            foreach ($viewModel->flashMessages as ['type' => $type, 'message' => $message]) {
                $this->flashMessage->add($type, $message);
            }
        }

        if (!$viewModel->error && $viewModel->isSaved) {
            $this->flashMessage->add('success', 'news.create.success');
        }
        return new Response(
            $this->twig->render('news/create-news.html.twig', [
                'viewModel' => $viewModel
            ])
        );
    }
}
