<?php

declare(strict_types=1);

namespace Telenco\Component\SyncAfterSale\Domain\UseCase\SyncCreation;

use Telenco\Component\SyncAfterSale\Domain\Port\Service\SyncAfterSaleCreationApiInterface;
use Telenco\Component\SyncAfterSale\Domain\Presenter\SyncAfterSalePresenterInterface;
use Telenco\Component\SyncAfterSale\Domain\UseCase\SyncCreation\DTO\AfterSaleSyncRequest;
use Telenco\Component\SyncAfterSale\Domain\UseCase\SyncCreation\DTO\AfterSaleSyncResponse;

class SyncCreationUseCase
{
    public function __construct(
        private SyncAfterSaleCreationApiInterface $syncAfterSaleCreationApi
    ) {
    }

    public function execute(
        AfterSaleSyncRequest $request,
        SyncAfterSalePresenterInterface $presenter
    ): void {
        $result = $this->syncAfterSaleCreationApi->createClaim($request->getAfterSaleId(), $request->getMessage() ?? '');
        $response = new AfterSaleSyncResponse($result);
        $presenter->present($response);
    }
}
