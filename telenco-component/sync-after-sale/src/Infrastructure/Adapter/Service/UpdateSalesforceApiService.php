<?php

namespace Telenco\Component\SyncAfterSale\Infrastructure\Adapter\Service;

use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Telenco\Component\SyncAfterSale\Domain\Port\Service\SyncAfterSaleUpdateApiInterface;
use Telenco\Component\SyncAfterSale\Infrastructure\Exception\SalesforceException;

class UpdateSalesforceApiService extends AbstractSalesforceApiService implements SyncAfterSaleUpdateApiInterface
{

    public function updateClaim(int $claimId): bool
    {
        $bearerToken = $this->client->getBearerToken();
        $version = $this->client->getApiVersion();
        try {
            $response = $this->client->getClient()->request(
                'PATCH',
                '/services/data/v' . $version . '/sobjects/Case/Claim_ID__c/' . $claimId,
                [
                    'auth_bearer' => $bearerToken,
                    'json' => $this->salesforceApiBuilder->buildUpdateClaim($claimId)
                ]
            );
            $jsonArray = json_decode($response->getContent(), true);

            return $jsonArray['success'];
        } catch (
            ClientExceptionInterface |
            TransportExceptionInterface |
            ServerExceptionInterface |
            RedirectionExceptionInterface $exception
        ) {
            $this->logger->error($exception->getMessage());
            throw new SalesForceException("updateClaim : technical error:" . $exception->getMessage());
        }
    }

    public function sendMessageClaim(int $claimId, string $message): bool
    {
        $bearerToken = $this->client->getBearerToken();
        $version = $this->client->getApiVersion();
        try {
            $response = $this->client->getClient()->request(
                'POST',
                '/services/data/v' . $version . '/sobjects/CaseComment/',
                ['auth_bearer' => $bearerToken,
                    'json' => $this->salesforceApiBuilder->buildMessageClaim($claimId, $message)
                ]
            );
            $jsonArray = json_decode($response->getContent(), true);

            return $jsonArray['success'];
        } catch (
            ClientExceptionInterface |
            TransportExceptionInterface |
            ServerExceptionInterface |
            RedirectionExceptionInterface $exception
        ) {
            $this->logger->error($exception->getMessage());
            throw new SalesForceException("sendMessageClaim : technical error:" . $exception->getMessage());
        }
    }
}
