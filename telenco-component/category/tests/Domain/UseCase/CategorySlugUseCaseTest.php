<?php

namespace Telenco\Component\StaticPage\Tests\Unit\Domain\UseCase;

use Generator;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetLocaleInterface;
use Marketplace\Component\Offer\Domain\Model\SearchResult;
use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Marketplace\Component\Offer\Domain\Port\Service\CategoryAdapterInterface;
use Marketplace\Component\User\Domain\Port\Repository\MerchantRepositoryInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;
use Symfony\Contracts\Translation\TranslatorInterface;
use Telenco\Component\Category\Domain\Model\CategoryDescription;
use Telenco\Component\Category\Domain\Model\CategorySlug;
use Telenco\Component\Category\Domain\Port\Repository\CategoryDescriptionRepositoryInterface;
use Telenco\Component\Category\Domain\Port\Repository\CategorySlugRepositoryInterface;
use Telenco\Component\Category\Domain\Port\Service\CategoryServiceInterface;
use Telenco\Component\Category\Domain\Presenter\CategorySlugPresenterInterface;
use Telenco\Component\Category\Domain\UseCase\CategorySlugUseCase\CategorySlugUseCase;
use Telenco\Component\Category\Domain\UseCase\CategorySlugUseCase\DTO\CategorySlugRequest;
use Telenco\Component\Category\Domain\UseCase\CategorySlugUseCase\DTO\CategorySlugResponse;
use Telenco\Component\Widget\Domain\Port\Repository\WidgetRepositoryInterface;

final class CategorySlugUseCaseTest extends TestCase
{
    private CategorySlugUseCase $useCase;
    private CategorySlugPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements CategorySlugPresenterInterface {
            public CategorySlugResponse $response;
            public function present(CategorySlugResponse $response): void
            {
                $this->response = $response;
            }
        };

        $prophecy = new Prophet();
        $categorySlugRepository = $prophecy->prophesize(CategorySlugRepositoryInterface::class);
        $categorySlugRepository->findBySlugAndLanguage(Argument::type('string'), Argument::type('string'))->will(function ($args) {
            $slug = $args[0];
            $locale = $args[1];
            $categorySlug = new CategorySlug();
            $categorySlug->setId(1)
                ->setSlug($args[0]);
            if ($slug === "notfound") {
                return null;
            } elseif ($slug === "category3") {
                $categorySlug->setIzbergId(3);
                return $categorySlug;
            } elseif ($slug === "english" && $locale === "fr") {
                return null;
            } else {
                $categorySlug->setIzbergId(1);
                return $categorySlug;
            }
        });

        $categoryService = $prophecy->prophesize(CategoryServiceInterface::class);
        $categoryService->getCategoryName(Argument::type('int'))->will(function ($args) {
            $id = $args[0];
            if ($id === 1 || $id === 3) {
                return 'Category name';
            }

            return null;
        });
        $categoryService->getCategoryLevel(Argument::type('int'))->will(function ($args) {
            $id = $args[0];
            // TODO: faire un return de niveau 3
             return 2;
        });
        $categoryService->getCategoryParent(Argument::type('int'))->will(function ($args) {
            $id = $args[0];
            // TODO : faire un vrai return
            return 3;
        });

        $categoryRepository = $prophecy->prophesize(CategoryDescriptionRepositoryInterface::class);
        $categoryRepository->findByIzbergId(Argument::type('int'))->will(function ($args) {
            $id = $args[0];
            if ($id === 1) {
                $category = new CategoryDescription();
                $category->setIzbergId($id)
                    ->setName('Category name')
                    ->setDescription('category_description_key')
                    ->setId(1);

                return $category;
            }

            return null;
        });

        $translator = $prophecy->prophesize(TranslatorInterface::class);
        $translator->trans(Argument::type('string'), Argument::type('array'), Argument::type('string'))->willReturn('Category description');

        $offerRepository = $prophecy->prophesize(OfferRepositoryInterface::class);
        $offerRepository->findOffers(
            null,
            Argument::type('array'),
            Argument::type('int'),
            Argument::type('int'),
            null,
            null
        )->willReturn(new SearchResult());
        $merchantRepository = $prophecy->prophesize(MerchantRepositoryInterface::class);
        $categoryAdapter = $prophecy->prophesize(CategoryAdapterInterface::class);
        $categoryAdapter->getSameLevelCategories(Argument::type('int'))->willReturn([]);
        $widgetRepository = $prophecy->prophesize(WidgetRepositoryInterface::class);
        $getLocale = $prophecy->prophesize(GetLocaleInterface::class);
        $getLocale->getLocale()->willReturn('fr');

        $this->useCase = new CategorySlugUseCase(
            $categorySlugRepository->reveal(),
            $categoryService->reveal(),
            $categoryRepository->reveal(),
            $translator->reveal(),
            $offerRepository->reveal(),
            $merchantRepository->reveal(),
            $categoryAdapter->reveal(),
            $widgetRepository->reveal(),
            $getLocale->reveal(),
        );
    }

    /**
     * @param CategorySlugRequest $request
     * @param CategorySlugResponse $expected
     *
     * @dataProvider provideExecute
     */
    public function testExecute(CategorySlugRequest $request, CategorySlugResponse $expected): void
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($expected, $this->presenter->response);
    }

    /**
     * @return Generator
     */
    public function provideExecute(): Generator
    {
        $request = new CategorySlugRequest("category1");
        $expected = new CategorySlugResponse(1);
        $expected->title = 'Category nameCategory description1';
        $expected->name = 'Category name';
        $expected->description = 'Category description';
        $expected->slug = 'category1';
        $expected->page = 1;
        $expected->hitsPerPage = 51;
        $expected->offers = new SearchResult();
        $expected->errors = [];
        yield 'Get category description success' => [$request, $expected];

        $request = new CategorySlugRequest("category3");
        $expected = new CategorySlugResponse(3);
        $expected->title = 'Category nameCategory description1';
        $expected->name = 'Category name';
        $expected->description = null;
        $expected->slug = 'category3';
        $expected->page = 1;
        $expected->hitsPerPage = 51;
        $expected->offers = new SearchResult();
        $expected->errors = [];
        yield 'Category no description' => [$request, $expected];

        $request = new CategorySlugRequest("english");
        $expected = new CategorySlugResponse();
        $expected->errors = ['no category found'];
        yield "Category slug don't match locale" => [$request, $expected];
    }
}
