<?php

namespace Telenco\Component\Category\Infrastructure\Adapter\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Telenco\Component\Category\Domain\Model\CategorySlug;
use Telenco\Component\Category\Domain\Port\Repository\CategorySlugRepositoryInterface;
use Telenco\Component\Category\Infrastructure\Entity\CategorySlug as DoctrineCategorySlug;
use Telenco\Component\Category\Infrastructure\Mapper\CategorySlugMapper;

class CategorySlugRepository extends ServiceEntityRepository implements CategorySlugRepositoryInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DoctrineCategorySlug::class);
    }

    public function save(CategorySlug $categorySlug): void
    {
        $doctrineCategorySlug = new DoctrineCategorySlug();
        $doctrineCategorySlug->setIzbergId($categorySlug->getIzbergId())
            ->setSlug($categorySlug->getSlug())
            ->setLanguage($categorySlug->getLanguage());

        $this->getEntityManager()->persist($doctrineCategorySlug);
        $this->getEntityManager()->flush();
    }

    public function findBySlugAndLanguage(string $slug, string $locale): ?CategorySlug
    {
        $doctrineCategorySlug = $this->findOneBy(['slug' => $slug, 'language' => $locale]);
        if (!$doctrineCategorySlug instanceof DoctrineCategorySlug) {
            return null;
        }

        return CategorySlugMapper::mappingDoctrineToModel($doctrineCategorySlug);
    }

    public function truncate(): void
    {
        $this->createQueryBuilder('cs')
            ->delete(DoctrineCategorySlug::class, 'cs')
            ->getQuery()
            ->execute();
    }

    public function isTableNotEmpty(): bool
    {
        return count($this->findAll()) > 0;
    }

    public function isSlugExist(string $slug): bool
    {
        $doctrineCategorySlug = $this->createQueryBuilder('cs')
            ->where('cs.slug = :slug')
            ->setParameter('slug', $slug)
            ->getQuery()
            ->getOneOrNullResult();

        return $doctrineCategorySlug instanceof DoctrineCategorySlug;
    }

    public function findAllCategoryByLanguage(string $locale): array
    {
        return $this->findBy(['language' => $locale]);
    }

    public function findCategoryByIdAndLanguage(int $id, string $locale): ?CategorySlug
    {
        $doctrineCategorySlug = $this->findOneBy(['izbergId' => $id, 'language' => $locale]);
        if (!$doctrineCategorySlug instanceof DoctrineCategorySlug) {
            return null;
        }

        return CategorySlugMapper::mappingDoctrineToModel($doctrineCategorySlug);
    }
}
