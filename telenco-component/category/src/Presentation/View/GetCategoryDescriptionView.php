<?php

declare(strict_types=1);

namespace Telenco\Component\Category\Presentation\View;

use Marketplace\Component\CleanArchiCore\Presentation\View\AbstractView;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Telenco\Component\Category\Presentation\ViewModel\GetCategoryDescriptionViewModel;

class GetCategoryDescriptionView extends AbstractView
{
    public function __construct(
        private SerializerInterface $serializer
    ) {
    }

    public function generateJson(GetCategoryDescriptionViewModel $viewModel): Response
    {
        $status = 200;
        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $viewModel,
                'json'
            ),
            $status
        );
    }
}
