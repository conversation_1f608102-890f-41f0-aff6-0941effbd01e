<?php

namespace Telenco\Component\Category\Domain\Model;

class CategoryDescription
{
    private int $id;
    private string $izbergId;
    private string $name;
    private string $description;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getIzbergId(): string
    {
        return $this->izbergId;
    }

    public function setIzbergId(string $izbergId): self
    {
        $this->izbergId = $izbergId;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;
        return $this;
    }
}
