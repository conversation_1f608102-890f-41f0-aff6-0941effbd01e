<?php

declare(strict_types=1);

namespace Telenco\Component\ContactUs\Presentation\Presenter;

use Telenco\Component\ContactUs\Domain\Presenter\SendContactUsPresenterInterface;
use Telenco\Component\ContactUs\Domain\UseCase\SendContactUsUseCase\DTO\SendContactUsResponse;
use Telenco\Component\ContactUs\Presentation\ViewModel\SendContactUsViewModel;
use Symfony\Component\Form\FormInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

final class SendContactUsPresenter implements SendContactUsPresenterInterface
{
    private SendContactUsViewModel $viewModel;

    public function __construct(
        private readonly TranslatorInterface $translator
    ) {
        $this->viewModel = new SendContactUsViewModel($translator);
    }

    public function present(SendContactUsResponse $response): void
    {
        if ($response->isSucceed() === true) {
            $this->viewModel->setSuccessMessage($this->translator->trans(
                id: 'contact_us.succeed',
                domain: 'translations'
            ));
        }

        foreach ($response->getNotification()->getErrors() as $error) {
            $this->viewModel->addFormErrors(
                $error->getFieldName() ?? '',
                $this->translator->trans(id: $error->getTranslationKey(), domain: 'translations')
            );
        }
    }

    public function viewModel(FormInterface $form): SendContactUsViewModel
    {
        $this->viewModel->form = $form->createView();
        return $this->viewModel;
    }
}
