<?php

declare(strict_types=1);

namespace Telenco\Component\ContactUs\Domain\Model;

use DateTimeImmutable;
use Marketplace\Component\User\Domain\Model\User;

class ContactUsResponse
{
    private ?int $id = null;

    private ContactUs $contactUs;

    private User $operator;

    private string $content;

    private DateTimeImmutable $createdAt;

    private array $files = [];

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return ContactUsResponse
     */
    public function setId(?int $id): ContactUsResponse
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return User
     */
    public function getOperator(): User
    {
        return $this->operator;
    }

    /**
     * @param User $operator
     * @return ContactUsResponse
     */
    public function setOperator(User $operator): ContactUsResponse
    {
        $this->operator = $operator;
        return $this;
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * @param string $content
     * @return ContactUsResponse
     */
    public function setContent(string $content): ContactUsResponse
    {
        $this->content = $content;
        return $this;
    }

    /**
     * @return DateTimeImmutable
     */
    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    /**
     * @param DateTimeImmutable $createdAt
     * @return ContactUsResponse
     */
    public function setCreatedAt(DateTimeImmutable $createdAt): ContactUsResponse
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    /**
     * @return array
     */
    public function getFiles(): array
    {
        return $this->files;
    }

    /**
     * @param array $files
     * @return ContactUsResponse
     */
    public function setFiles(array $files): ContactUsResponse
    {
        $this->files = $files;
        return $this;
    }

    /**
     * @return ContactUs
     */
    public function getContactUs(): ContactUs
    {
        return $this->contactUs;
    }

    /**
     * @param ContactUs $contactUs
     * @return ContactUsResponse
     */
    public function setContactUs(ContactUs $contactUs): ContactUsResponse
    {
        $this->contactUs = $contactUs;
        return $this;
    }
}
