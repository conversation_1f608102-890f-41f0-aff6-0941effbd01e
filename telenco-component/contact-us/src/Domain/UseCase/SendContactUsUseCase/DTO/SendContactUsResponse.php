<?php

declare(strict_types=1);

namespace Telenco\Component\ContactUs\Domain\UseCase\SendContactUsUseCase\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Error\NotificationTrait;
use Telenco\Component\ContactUs\Domain\Model\ContactUs;

class SendContactUsResponse
{
    use NotificationTrait;

    private ?string $currentLocale = null;
    private ?int $contactUsMessageId = null;
    private bool $succeed = false;

    /**
     * @return string|null
     */
    public function getCurrentLocale(): ?string
    {
        return $this->currentLocale;
    }

    /**
     * @param string|null $currentLocale
     * @return SendContactUsResponse
     */
    public function setCurrentLocale(?string $currentLocale): SendContactUsResponse
    {
        $this->currentLocale = $currentLocale;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getContactUsMessageId(): ?int
    {
        return $this->contactUsMessageId;
    }

    /**
     * @param int|null $contactUsMessageId
     * @return SendContactUsResponse
     */
    public function setContactUsMessageId(?int $contactUsMessageId): SendContactUsResponse
    {
        $this->contactUsMessageId = $contactUsMessageId;
        return $this;
    }

    /**
     * @return bool
     */
    public function isSucceed(): bool
    {
        return $this->succeed;
    }

    /**
     * @param bool $succeed
     * @return SendContactUsResponse
     */
    public function setSucceed(bool $succeed): SendContactUsResponse
    {
        $this->succeed = $succeed;
        return $this;
    }
}
