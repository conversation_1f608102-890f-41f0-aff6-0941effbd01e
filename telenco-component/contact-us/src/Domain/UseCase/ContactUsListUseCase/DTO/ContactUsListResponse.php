<?php

declare(strict_types=1);

namespace Telenco\Component\ContactUs\Domain\UseCase\ContactUsListUseCase\DTO;

class ContactUsListResponse
{
    private array $contactUsMessages = [];

    public function __construct(
        private int $page,
        private int $itemPerPage,
    ) {
    }

    /**
     * @return array
     */
    public function getContactUsMessages(): array
    {
        return $this->contactUsMessages;
    }

    /**
     * @param array $contactUsMessages
     * @return ContactUsListResponse
     */
    public function setContactUsMessages(array $contactUsMessages): ContactUsListResponse
    {
        $this->contactUsMessages = $contactUsMessages;
        return $this;
    }

    /**
     * @return int
     */
    public function getPage(): int
    {
        return $this->page;
    }

    /**
     * @param int $page
     * @return ContactUsListResponse
     */
    public function setPage(int $page): ContactUsListResponse
    {
        $this->page = $page;
        return $this;
    }

    /**
     * @return int
     */
    public function getItemPerPage(): int
    {
        return $this->itemPerPage;
    }

    /**
     * @param int $itemPerPage
     * @return ContactUsListResponse
     */
    public function setItemPerPage(int $itemPerPage): ContactUsListResponse
    {
        $this->itemPerPage = $itemPerPage;
        return $this;
    }
}
