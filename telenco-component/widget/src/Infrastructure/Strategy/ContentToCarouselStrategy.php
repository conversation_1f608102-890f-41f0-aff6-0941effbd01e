<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Strategy;

use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Model\WidgetContent\Carousel as DomainCarousel;
use Telenco\Component\Widget\Domain\Model\WidgetContent\WidgetContent as DomainWidgetContent;
use Telenco\Component\Widget\Infrastructure\Entity\WidgetContent\Carousel;
use Telenco\Component\Widget\Infrastructure\Entity\WidgetContent\WidgetContent;

final class ContentToCarouselStrategy implements ContentToWidgetTypeStrategyInterface
{
    public function __construct(private OfferRepositoryInterface $offerRepositoryElasticsearch)
    {
    }

    /**
     * @inheritDoc
     */
    public function process(WidgetContent $content): DomainWidgetContent
    {
        $domainCarousel = new DomainCarousel();

        /** @var Carousel $content */
        $domainCarousel->setLocale(Locale::create($content->getLocale()));
        $regionDoctrine = $content->getRegion();
        $region = new Region();
        $region->setName($regionDoctrine->getName());
        $region->setId($regionDoctrine->getId());
        $domainCarousel->setRegion($region);
        $domainCarousel->setId($content->getId());
        $domainCarousel->setDiscountOfferId($content->getDiscountOfferId());
        $domainCarousel->setNewsOfferId($content->getNewsOfferId());
        $domainCarousel->setBestSellsOfferId($content->getBestSellsOfferId());
        $domainCarousel->setDiscountOffers($this->getOffers($content->getDiscountOfferId()));
        $domainCarousel->setNewsOffers($this->getOffers($content->getNewsOfferId()));
        $domainCarousel->setBestSellsOffers($this->getOffers($content->getBestSellsOfferId()));
        return $domainCarousel;
    }

    private function getOffers(array $offerIds): array
    {
        $offers = [];
        foreach ($offerIds as $key => $offerId) {
            $offer = $this->offerRepositoryElasticsearch->fetchOfferById((int)$offerId);
            if ($offer !== null) {
                $offers[] = $offer;
            }
        }
        return $offers;
    }

    /**
     * @inheritDoc
     */
    public function canProcess(WidgetContent $content): bool
    {
        return $content instanceof Carousel;
    }
}
