<?php

namespace Telenco\Component\Widget\Infrastructure\Strategy;

use Telenco\Component\Widget\Domain\Model\WidgetContent\WidgetContent as DomainWidgetContent;
use Telenco\Component\Widget\Infrastructure\Entity\WidgetContent\WidgetContent;

class ContentToWidgetTypeContext
{
    /**
     * @var ContentToWidgetTypeStrategyInterface[] $strategies
     */
    private array $strategies;


    /**
     * UnpublishedReasonPriorityContext constructor.
     *
     * @param iterable $strategies
     */
    public function __construct(iterable $strategies)
    {
        foreach ($strategies as $strategy) {
            $this->addStrategy($strategy);
        }
    }

    /**
     * This function will allow to add new strategy.
     *
     * @param ContentToWidgetTypeStrategyInterface $strategy
     */
    public function addStrategy(ContentToWidgetTypeStrategyInterface $strategy)
    {
        $this->strategies[] = $strategy;
    }

    /**
     * @param WidgetContent $content
     * @return DomainWidgetContent|null
     */
    public function process(WidgetContent $content): ?DomainWidgetContent
    {
        foreach ($this->strategies as $strategy) {
            if ($strategy->canProcess($content)) {
                return $strategy->process($content);
            }
        }

        return null;
    }
}
