<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Strategy;

use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Telenco\Component\Widget\Domain\Request\WidgetContentRequestInterface;
use Telenco\Component\Widget\Domain\UseCase\OperatorTriplePromosEdit\DTO\OperatorTriplePromosEditRequest;
use Telenco\Component\Widget\Domain\UseCase\OperatorTriplePromosEdit\OperatorTriplePromosEditUseCase;
use Telenco\Component\Widget\Infrastructure\Form\TriplePromosEditForm;
use Telenco\Component\Widget\Presentation\Presenter\OperatorTriplePromosEditPresenter;
use Telenco\Component\Widget\Presentation\View\OperatorTriplePromosEditView;

final class TriplePromosEditStrategy implements EditWidgetStrategyInterface
{
    public function __construct(
        private readonly OperatorTriplePromosEditUseCase $useCase,
        private readonly OperatorTriplePromosEditPresenter $presenter,
        private readonly OperatorTriplePromosEditView $homeBannerEditView,
        private readonly RequestStack $requestStack,
        private readonly FormFactoryInterface $formFactory,
    ) {
    }

    public function process(WidgetContentRequestInterface $request): Response
    {
        /** @var OperatorTriplePromosEditRequest $request */
        $currentRequest = $this->requestStack->getCurrentRequest();
        $form = $this->formFactory->create(TriplePromosEditForm::class, $request)->handleRequest($currentRequest);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->useCase->execute($request, $this->presenter);
        }

        return $this->homeBannerEditView->generateView($this->presenter->viewModel($form));
    }

    public function canProcess(WidgetContentRequestInterface $request): bool
    {
        return $request instanceof OperatorTriplePromosEditRequest;
    }
}
