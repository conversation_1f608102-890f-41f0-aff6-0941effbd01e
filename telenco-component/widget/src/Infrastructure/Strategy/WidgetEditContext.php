<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Strategy;

use Symfony\Component\HttpFoundation\Response;
use Telenco\Component\Widget\Domain\Request\WidgetContentRequestInterface;

class WidgetEditContext
{
    /**
     * @var EditWidgetStrategyInterface[]
     */
    private array $strategies = [];

    public function __construct(iterable $strategies)
    {
        foreach ($strategies as $strategy) {
            $this->addStrategy($strategy);
        }
    }

    private function addStrategy(EditWidgetStrategyInterface $strategy): void
    {
        $this->strategies[] = $strategy;
    }

    public function process(WidgetContentRequestInterface $request): Response
    {
        foreach ($this->strategies as $strategy) {
            if ($strategy->canProcess($request)) {
                return $strategy->process($request);
            }
        }

        return new Response('Bad strategy', Response::HTTP_NOT_FOUND);
    }
}
