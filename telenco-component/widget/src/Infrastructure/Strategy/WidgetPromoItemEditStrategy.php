<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Strategy;

use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Telenco\Component\Widget\Domain\Request\WidgetContentRequestInterface;
use Telenco\Component\Widget\Domain\UseCase\OperatorPromoItemEdit\DTO\OperatorPromoItemEditRequest;
use Telenco\Component\Widget\Domain\UseCase\OperatorPromoItemEdit\OperatorPromoItemEditUseCase;
use Telenco\Component\Widget\Infrastructure\Form\WidgetPromoItemEditForm;
use Telenco\Component\Widget\Presentation\Presenter\OperatorPromoItemEditPresenter;
use Telenco\Component\Widget\Presentation\View\OperatorPromoItemEditView;

final class WidgetPromoItemEditStrategy implements EditWidgetStrategyInterface
{
    public function __construct(
        private OperatorPromoItemEditUseCase $useCase,
        private OperatorPromoItemEditPresenter $presenter,
        private FormFactoryInterface $formFactory,
        private RequestStack $requestStack,
        private OperatorPromoItemEditView $view,
    ) {
    }

    public function process(WidgetContentRequestInterface $request): Response
    {
        /** @var OperatorPromoItemEditRequest $request */

        $currentRequest = $this->requestStack->getCurrentRequest();
        $form = $this
            ->formFactory
            ->create(WidgetPromoItemEditForm::class, $request)
            ->handleRequest($currentRequest);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->useCase->execute($request, $this->presenter);
        }

        return $this->view->generateView($this->presenter->viewModel($form));
    }

    public function canProcess(WidgetContentRequestInterface $request): bool
    {
        return $request instanceof OperatorPromoItemEditRequest;
    }
}
