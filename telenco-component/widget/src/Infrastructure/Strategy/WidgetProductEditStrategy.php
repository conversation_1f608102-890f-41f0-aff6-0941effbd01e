<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Strategy;

use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Telenco\Component\Widget\Domain\Request\WidgetContentRequestInterface;
use Telenco\Component\Widget\Domain\UseCase\WidgetSimpleProductEdit\DTO\WidgetSimpleProductEditRequest;
use Telenco\Component\Widget\Domain\UseCase\WidgetSimpleProductEdit\WidgetSimpleProductEditUseCase;
use Telenco\Component\Widget\Infrastructure\Form\WidgetSimpleProductEditForm;
use Telenco\Component\Widget\Presentation\Presenter\WidgetProductEditPresenter;
use Telenco\Component\Widget\Presentation\View\WidgetSimpleProductEditView;

class WidgetProductEditStrategy implements EditWidgetStrategyInterface
{
    public function __construct(
        private WidgetSimpleProductEditUseCase $useCase,
        private WidgetProductEditPresenter $presenter,
        private FormFactoryInterface $formFactory,
        private RequestStack $requestStack,
        private WidgetSimpleProductEditView $view,
    ) {
    }

    public function process(WidgetContentRequestInterface $request): Response
    {
        /** @var WidgetSimpleProductEditRequest $request */

        $currentRequest = $this->requestStack->getCurrentRequest();
        $form = $this
            ->formFactory
            ->create(WidgetSimpleProductEditForm::class, $request)
            ->handleRequest($currentRequest);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->useCase->execute($request, $this->presenter);
        }

        return $this->view->generateView($this->presenter->viewModel($form));
    }

    public function canProcess(WidgetContentRequestInterface $request): bool
    {
        return $request instanceof WidgetSimpleProductEditRequest;
    }
}
