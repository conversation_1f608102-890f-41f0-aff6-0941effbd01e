<?php

namespace Telenco\Component\Widget\Infrastructure\Strategy;

use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Infrastructure\Entity\WidgetContent\SoftHighlight;
use Telenco\Component\Widget\Infrastructure\Entity\WidgetContent\WidgetContent;
use Telenco\Component\Widget\Domain\Model\WidgetContent\WidgetContent as DomainWidgetContent;
use Telenco\Component\Widget\Domain\Model\WidgetContent\SoftHighlight as DomainSoftHighlight;
use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;

class ContentToSoftHighlightStrategy implements ContentToWidgetTypeStrategyInterface
{
    /**
     * @param WidgetContent $content
     * @return DomainWidgetContent
     */
    public function process(WidgetContent $content): DomainWidgetContent
    {
        $domainSoftHighlight = new DomainSoftHighlight();
        /**
         * @var SoftHighlight $content
         */
        $domainSoftHighlight->setLocale(Locale::create($content->getLocale()));
        $regionDoctrine = $content->getRegion();
        $region = new Region();
        $region->setName($regionDoctrine->getName());
        $region->setId($regionDoctrine->getId());
        $domainSoftHighlight->setRegion($region);
        $domainSoftHighlight->setId($content->getId());
        $domainSoftHighlight->setLink($content->getLink());
        $domainSoftHighlight->setImage($content->getImage());

        return $domainSoftHighlight;
    }

    /**
     * @param WidgetContent $content
     * @return bool
     */
    public function canProcess(WidgetContent $content): bool
    {
        return $content instanceof SoftHighlight;
    }
}
