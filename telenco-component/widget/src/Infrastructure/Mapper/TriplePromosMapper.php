<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Mapper;

use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\Model\WidgetContent\TriplePromos;
use Telenco\Component\Widget\Infrastructure\Entity\WidgetContent\TriplePromos as DoctrineTriplePromos;

abstract class TriplePromosMapper
{
    public static function domainToDoctrine(TriplePromos $triplePromos): DoctrineTriplePromos
    {
        return (new DoctrineTriplePromos())
            ->setLocale($triplePromos->getLocale()->getLocale())
            ->setTitle($triplePromos->getMainTitle())
            ->setSubTitle($triplePromos->getSubTitle())
        ;
    }

    public static function doctrineToDomain(DoctrineTriplePromos $doctrineTriplePromos): TriplePromos
    {
        $region = (new Region())
            ->setId($doctrineTriplePromos->getRegion()->getId())
            ->setName($doctrineTriplePromos->getRegion()->getName())
        ;
        $widget = (new Widget())
            ->setId($doctrineTriplePromos->getWidget()->getId())
            ->setName($doctrineTriplePromos->getWidget()->getName())
            ->setStatus($doctrineTriplePromos->getWidget()->getStatus())
        ;

        return (new TriplePromos())
            ->setId($doctrineTriplePromos->getId())
            ->setMainTitle($doctrineTriplePromos->getTitle())
            ->setRegion($region)
            ->setWidget($widget)
            ->setLocale(Locale::create($doctrineTriplePromos->getLocale()))
            ->setSubTitle($doctrineTriplePromos->getSubTitle())
        ;
    }
}
