<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Mapper;

use Telenco\Component\Widget\Domain\Model\WidgetContent\SelectionsHighlight as ModelSelectionsHighlight;
use Telenco\Component\Widget\Infrastructure\Entity\WidgetContent\SelectionsHighlight;

abstract class SelectionsHighlightMapper
{
    public static function domainToDoctrine(ModelSelectionsHighlight $highlight, ?SelectionsHighlight $doctrineHighlight): SelectionsHighlight
    {
        /** @var string $image */
        $image = $highlight->getImage();
        if (! $doctrineHighlight instanceof SelectionsHighlight) {
            $doctrineHighlight = new SelectionsHighlight();
        }
        $doctrineHighlight->setImage($image)
            ->setSlug($highlight->getSlug() ?? '');
        return $doctrineHighlight;
    }

    public static function doctrineToDomain(SelectionsHighlight $doctrineHighlight): ModelSelectionsHighlight
    {
        return (new ModelSelectionsHighlight())
            ->setId($doctrineHighlight->getId())
            ->setImage($doctrineHighlight->getImage())
            ->setSlug($doctrineHighlight->getSlug())
        ;
    }
}
