<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Entity\WidgetContent;

use DateTimeImmutable;
use DateTimeInterface;
use Telenco\Component\Widget\Infrastructure\Adapter\Repository\WidgetSimpleProductRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Table(name="widget_carousel")
 * @ORM\Entity(repositoryClass=WidgetCarouselRepository::class)
 */
class Carousel extends WidgetContent
{


    /**
     * @var int[]
     * @ORM\Column(type="simple_array")
     */
    private array $discountOfferId = [];

    /**
     * @var int[]
     * @ORM\Column(type="simple_array")
     */
    private array $newsOfferId = [];

    /**
     * @var int[]
     * @ORM\Column(type="simple_array")
     */
    private array $bestSellsOfferId = [];

    /**
     * @ORM\Column(type="datetime_immutable")
     */
    private DateTimeInterface $createdAt;

    public function __construct()
    {
        $this->createdAt = new DateTimeImmutable();
    }

    /**
     * @return array
     */
    public function getDiscountOfferId(): array
    {
        return $this->discountOfferId;
    }

    /**
     * @param array $discountOfferId
     * @return Carousel
     */
    public function setDiscountOfferId(array $discountOfferId): Carousel
    {
        $this->discountOfferId = $discountOfferId;
        return $this;
    }

    /**
     * @return array
     */
    public function getNewsOfferId(): array
    {
        return $this->newsOfferId;
    }

    /**
     * @param array $newsOfferId
     * @return Carousel
     */
    public function setNewsOfferId(array $newsOfferId): Carousel
    {
        $this->newsOfferId = $newsOfferId;
        return $this;
    }

    /**
     * @return array
     */
    public function getBestSellsOfferId(): array
    {
        return $this->bestSellsOfferId;
    }

    /**
     * @param array $bestSellsOfferId
     * @return Carousel
     */
    public function setBestSellsOfferId(array $bestSellsOfferId): Carousel
    {
        $this->bestSellsOfferId = $bestSellsOfferId;
        return $this;
    }

    public function getCreatedAt(): DateTimeImmutable|DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTimeImmutable|DateTimeInterface $createdAt): Carousel
    {
        $this->createdAt = $createdAt;
        return $this;
    }
}
