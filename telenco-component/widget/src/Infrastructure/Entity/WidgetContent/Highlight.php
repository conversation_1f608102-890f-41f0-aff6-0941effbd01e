<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Entity\WidgetContent;

use Telenco\Component\Widget\Infrastructure\Adapter\Repository\HighlightRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=HighlightRepository::class)
 */
class Highlight extends WidgetContent
{
    /**
     * @ORM\Column(type="string", length=255, nullable=false)
     */
    private string $image;

    /**
     * @ORM\Column
     */
    private string $hat;

    /**
     * @ORM\Column(type="text")
     */
    private string $description;

    /**
     * @ORM\Column(type="array")
     */
    private array $links = [];

    public function getImage(): string
    {
        return $this->image;
    }

    public function setImage(string $image): self
    {
        $this->image = $image;
        return $this;
    }

    public function getHat(): string
    {
        return $this->hat;
    }

    public function setHat(string $hat): self
    {
        $this->hat = $hat;
        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;
        return $this;
    }

    public function getLinks(): array
    {
        return $this->links;
    }

    public function setLinks(array $links): self
    {
        $this->links = $links;
        return $this;
    }
}
