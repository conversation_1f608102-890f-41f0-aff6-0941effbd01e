<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Entity\WidgetContent;

use Doctrine\ORM\Mapping as ORM;
use Telenco\Component\Shared\Infrastructure\Entity\Region;
use Telenco\Component\Widget\Infrastructure\Entity\Widget;

/**
 * @ORM\Entity
 * @ORM\Table(name="widget_contents")
 * @ORM\InheritanceType("JOINED")
 * @ORM\DiscriminatorColumn(name="discr", type="string")
 * @ORM\DiscriminatorMap({
 *      "highlight"=Highlight::class,
 *      "topBrand"=TopBrand::class,
 *      "product"=Product::class,
 *      "topBrand"=TopBrand::class,
 *      "newsWidget"=NewsWidget::class,
 *      "selections"=Selections::class,
 *      "softHighlight"=SoftHighlight::class,
 *      "carousel_v2"=Carousel::class,
 *      "homeBanner"=HomeBanner::class,
 *      "promoItem"=PromoItem::class,
 *      "triplePromos"=TriplePromos::class
 * })
 */
abstract class WidgetContent
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    protected ?int $id = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    protected ?string $name;

    /**
     * @ORM\Column(type="string", length=50)
     */
    protected string $locale;

    /**
     * @ORM\ManyToOne(targetEntity=Widget::class, inversedBy="widgetContents", cascade={"persist"})
     * @ORM\JoinColumn(name="widget_id", referencedColumnName="id")
     * @var Widget
     */
    protected Widget $widget;

    /**
     * @ORM\ManyToOne(targetEntity=Region::class, cascade={"persist"})
     * @var Region
     */
    protected Region $region;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): WidgetContent
    {
        $this->id = $id;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): static
    {
        $this->name = $name;
        return $this;
    }

    public function getLocale(): string
    {
        return $this->locale;
    }

    public function setLocale(string $locale): static
    {
        $this->locale = $locale;
        return $this;
    }

    public function getWidget(): Widget
    {
        return $this->widget;
    }

    public function setWidget(Widget $widget): static
    {
        $this->widget = $widget;
        return $this;
    }

    public function getRegion(): Region
    {
        return $this->region;
    }

    public function setRegion(Region $region): static
    {
        $this->region = $region;
        return $this;
    }
}
