<?php

namespace Telenco\Component\Widget\Infrastructure\Entity;

use Doctrine\ORM\PersistentCollection;
use Telenco\Component\Widget\Infrastructure\Adapter\Repository\WidgetRepository;
use Doctrine\ORM\Mapping as ORM;
use Telenco\Component\Widget\Infrastructure\Entity\WidgetContent\WidgetContent;

/**
 * @ORM\Table(name="widgets")
 * @ORM\Entity(repositoryClass=WidgetRepository::class)
 */
class Widget extends SlotableContent
{
    /**
     * @ORM\Column(name="type", type="string", length=50, nullable=false)
     */
    private string $type;

    /**
     * @ORM\Column(name="status", type="string", length=50)
     */
    private string $status;

    /**
     * @ORM\OneToMany(targetEntity="Telenco\Component\Widget\Infrastructure\Entity\WidgetContent\WidgetContent", mappedBy="widget")
     */
    private PersistentCollection $widgetContents;

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return PersistentCollection
     */
    public function getWidgetContents(): PersistentCollection
    {
        return $this->widgetContents;
    }

    /**
     * @param PersistentCollection $widgetContents
     */
    public function setWidgetContents(PersistentCollection $widgetContents): void
    {
        $this->widgetContents = $widgetContents;
    }
}
