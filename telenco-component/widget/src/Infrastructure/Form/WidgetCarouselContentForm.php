<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Telenco\Component\Widget\Domain\UseCase\OperatorCarouselEdit\DTO\OperatorCarouselContentRequest;
use Telenco\Component\Widget\Infrastructure\Form\DataTransformer\OffersIdTransformer;

final class WidgetCarouselContentForm extends AbstractType
{

    public function __construct(
        private OffersIdTransformer $offersIdTransformer,
    ) {
    }

    /**
     * @inheritDoc
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('locale', HiddenType::class)
            ->add('discountOfferId', TextType::class, [
                'label' => 'widget.carousel.discount_offer_id.label',
                'attr' => [
                    'class' => 'select-offers',
                    'autocomplete' => 'off',
                    'placeholder' => 'widget.carousel.discount_offer_id.placeholder'
                ]
            ])
            ->add('newsOfferId', TextType::class, [
                'label' => 'widget.carousel.news_offer_id.label',
                'attr' => [
                    'class' => 'select-offers',
                    'autocomplete' => 'off',
                    'placeholder' => 'widget.carousel.news_offer_id.placeholder'
                ]
            ])
            ->add('bestSellsOfferId', TextType::class, [
                'label' => 'widget.carousel.best_sells_offer_id.label',
                'attr' => [
                    'class' => 'select-offers',
                    'autocomplete' => 'off',
                    'placeholder' => 'widget.carousel.best_sells_offer_id.placeholder'
                ]
            ])
            ;

        $builder->get('discountOfferId')->addModelTransformer($this->offersIdTransformer);
        $builder->get('newsOfferId')->addModelTransformer($this->offersIdTransformer);
        $builder->get('bestSellsOfferId')->addModelTransformer($this->offersIdTransformer);
    }

    /**
     * @inheritDoc
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => OperatorCarouselContentRequest::class,
            'translation_domain' => 'translations',
        ]);
    }
}
