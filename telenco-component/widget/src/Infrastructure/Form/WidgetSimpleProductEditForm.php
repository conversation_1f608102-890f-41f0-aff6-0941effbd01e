<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Telenco\Component\Widget\Domain\Model\WidgetContent\Product;
use Telenco\Component\Widget\Domain\UseCase\WidgetSimpleProductEdit\DTO\WidgetSimpleProductEditRequest;
use Telenco\Component\Widget\Infrastructure\Form\DataTransformer\RegionNameToModelTransformer;

final class WidgetSimpleProductEditForm extends AbstractType
{
    public function __construct(
        private RegionNameToModelTransformer $transformer
    ) {
    }

    /**
     * @inheritDoc
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('region', TextType::class, [
                'label' => 'widget.simple_product.region.label',
                'attr' => ['readonly' => true],
            ])
            ->add('type', ChoiceType::class, [
                'label' => 'widget.simple_product.type.label',
                'choices' => [
                    Product::SIMPLE_TYPE => Product::SIMPLE_TYPE,
                    Product::DIVERS_TYPE => Product::DIVERS_TYPE,
                    Product::PROMOTION_TYPE => Product::PROMOTION_TYPE
                ]
            ])
            ->add(
                'contents',
                CollectionType::class,
                array(
                    'entry_type' => WidgetSimpleProductContentForm::class,
                    'by_reference' => false
                )
            )
            ->add('submit', SubmitType::class, [
                'label' => 'form.button.save',
                'attr' => [
                    'class' => 'btn btn-primary my-3',
                ]
            ])
        ;

        $builder->get('region')->addModelTransformer($this->transformer);
    }

    /**
     * @inheritDoc
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => WidgetSimpleProductEditRequest::class,
            'translation_domain' => 'translations',
        ]);
    }
}
