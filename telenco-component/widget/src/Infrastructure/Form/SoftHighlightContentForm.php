<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Telenco\Component\Widget\Domain\UseCase\OperatorSoftHighlightEdit\DTO\SoftHighlightContentRequest;

final class SoftHighlightContentForm extends AbstractType
{
    /**
     * @inheritDoc
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('locale', HiddenType::class)
            ->add('link', TextType::class, [
                'label' => 'widget.highlight.label.link',
            ])
        ;
    }

    /**
     * @inheritDoc
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => SoftHighlightContentRequest::class,
            'translation_domain' => 'translations',
        ]);
    }
}
