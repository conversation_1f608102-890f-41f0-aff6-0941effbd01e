<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Telenco\Component\Widget\Domain\UseCase\OperatorTopBrandEdit\DTO\OperatorTopBrandEditRequest;
use Telenco\Component\Widget\Infrastructure\Form\DataTransformer\RegionNameToModelTransformer;

final class TopBrandEditForm extends AbstractType
{
    public function __construct(private RegionNameToModelTransformer $transformer)
    {
    }

    /**
     * @inheritDoc
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('region', TextType::class, [
                'label' => 'widget.top_brand.region.label',
                'attr' => ['readonly' => true],
            ])
            ->add('contents', CollectionType::class, [
                'entry_type' => TopBrandContentForm::class,
                'by_reference' => false
            ])
            ->add('submit', SubmitType::class, [
                'label' => 'form.button.save',
                'attr' => [
                    'class' => 'btn-primary my-3',
                ]
            ])
        ;

        $builder->get('region')->addModelTransformer($this->transformer);
    }

    /**
     * @inheritDoc
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => OperatorTopBrandEditRequest::class,
            'translation_domain' => 'translations',
        ]);
    }
}
