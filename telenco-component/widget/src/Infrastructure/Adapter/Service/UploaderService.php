<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Adapter\Service;

use SplFileInfo;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\String\Slugger\SluggerInterface;
use Telenco\Component\Widget\Domain\Port\Service\UploaderServiceInterface;

class UploaderService implements UploaderServiceInterface
{
    public function __construct(
        private SluggerInterface $slugger,
        private string $uploadsAbsoluteDir,
        private string $uploadsRelativeDir,
        private string $siteUrl,
        private Filesystem $filesystem
    ) {
    }

    /**
     * @inheritDoc
     */
    public function save(SplFileInfo $image): string
    {
        if (!$image instanceof UploadedFile) {
            throw new \RuntimeException(sprintf('The image class must be instance of %s', UploadedFile::class));
        }
        /** @var string $slug */
        $slug = $this->slugger->slug($image->getClientOriginalName());
        $filename = sprintf(
            "%s_%s.%s",
            $slug,
            uniqid(),
            $image->getClientOriginalExtension()
        );
        try {
            $image->move($this->uploadsAbsoluteDir, $filename);
        } catch (FileException $exception) {
        }


        return $this->siteUrl . $this->uploadsRelativeDir . '/' . $filename;
    }

    public function delete(string $imageUrl): void
    {
        $this->filesystem->remove(str_replace($this->siteUrl, '', $imageUrl));
    }
}
