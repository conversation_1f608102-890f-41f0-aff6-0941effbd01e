<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Adapter\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Port\Repository\RegionRepositoryInterface;
use Telenco\Component\Shared\Infrastructure\Entity\Region as DoctrineRegion;

/**
 * @deprecated
 */
class RegionRepository extends ServiceEntityRepository implements RegionRepositoryInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DoctrineRegion::class);
    }

    public function getRegionById(int $regionId): ?Region
    {
        $doctrineRegion = $this->find($regionId);
        if (!$doctrineRegion instanceof DoctrineRegion) {
            return null;
        }

        return (new Region())
            ->setId($doctrineRegion->getId())
            ->setName($doctrineRegion->getName())
        ;
    }

    /**
     * @return array
     */
    public function findAllRegions(): array
    {
        $regions = $this->findAll();
        return array_map(function (DoctrineRegion $doctrineRegion) {
            return $this->buildRegionFromDoctrine($doctrineRegion);
        }, $regions);
    }

    /**
     * @param DoctrineRegion $doctrineRegion
     * @return Region
     */
    private function buildRegionFromDoctrine(DoctrineRegion $doctrineRegion): Region
    {
        return (new Region())
            ->setId($doctrineRegion->getId())
            ->setName($doctrineRegion->getName())
            ;
    }
}
