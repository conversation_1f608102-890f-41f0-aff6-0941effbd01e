<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Adapter\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\PersistentCollection;
use Doctrine\Persistence\ManagerRegistry;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetRegionServiceInterface;
use Marketplace\Component\User\Domain\Interface\RegionAwareInterface;
use Telenco\Component\Shared\Domain\Status\Status;
use Telenco\Component\Widget\Infrastructure\Entity\Widget as DoctrineWidget;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\Port\Repository\WidgetRepositoryInterface;
use Telenco\Component\Widget\Infrastructure\Entity\WidgetContent\WidgetContent;
use Telenco\Component\Widget\Infrastructure\Strategy\ContentToWidgetTypeContext;

class WidgetRepository extends ServiceEntityRepository implements WidgetRepositoryInterface, RegionAwareInterface
{
    public function __construct(
        ManagerRegistry $registry,
        private ContentToWidgetTypeContext $contentToWidgetTypeContext,
        private GetRegionServiceInterface $getRegionService
    ) {
        parent::__construct($registry, DoctrineWidget::class);
    }

    public function save(Widget $widget): bool
    {
        $id = $widget->getId();
        $doctrineWidget = null;
        if ($id !== null) {
            $doctrineWidget = parent::find($id);
        }

        if (!$doctrineWidget instanceof DoctrineWidget) {
            $doctrineWidget = new DoctrineWidget();
        }
        $doctrineWidget ->setStatus($widget->getStatus());
        $doctrineWidget->setName($widget->getName());
        $doctrineWidget->setType($widget->getType());

        $this->getEntityManager()->persist($doctrineWidget);
        $this->getEntityManager()->flush();

        $widget->setId($doctrineWidget->getId());

        return true;
    }

    public function findByName(string $name): ?Widget
    {
        $doctrineWidget = $this->createQueryBuilder('w')
            ->where('w.name = :name')
            ->andWhere('w.status != :status')
            ->setParameter('name', $name)
            ->setParameter('status', Status::DELETED)
            ->getQuery()
            ->getOneOrNullResult()
        ;

        if ($doctrineWidget === null) {
            return null;
        }

        return $this->createDomain($doctrineWidget);
    }

    private function createDomain(DoctrineWidget $doctrineWidget): Widget
    {
        $domainContents = [];
        $contents = $doctrineWidget->getWidgetContents()->toArray();
        /**
         * @var WidgetContent $c
         */
        foreach ($contents as $c) {
            $domainContents[] = $this->contentToWidgetTypeContext->process($c);
        }
        return (new Widget())
            ->setId($doctrineWidget->getId())
            ->setType($doctrineWidget->getType())
            ->setName($doctrineWidget->getName())
            ->setStatus($doctrineWidget->getStatus())
            ->setContents($domainContents);
    }

    /**
     * @return Widget[]
     */
    public function findAllWidget(): array
    {
        $widgets = $this->findAll();
        return array_map(function (DoctrineWidget $widgetDoc) {
            return $this->mapWidgetDocToWidget($widgetDoc);
        }, $widgets);
    }

    /**
     * @return Widget[]
     */
    public function findPublishedWidget(string $locale): array
    {
        $excludedWidget = [Widget::TYPE_SELECTIONS, Widget::TYPE_HIGHLIGHT_LISTING, Widget::TYPE_SOFT_HIGHLIGHT, Widget::TYPE_PROMO_ITEM];
        $widgets = $this->createQueryBuilder('w')
            ->where('w.status = :status')
            ->join('w.slot', 's')
            ->join('w.widgetContents', 'wc')
            ->join('wc.region', 'r')
            ->andWhere('r.name = :region')
            ->andWhere('w.type NOT IN (:excludedWidget)')
            ->setParameters(['status' => Status::PUBLISHED,
                'region' => $this->getRegionService->getRegion(),
                'excludedWidget' => $excludedWidget])
            ->orderBy('s.number', 'ASC')
            ->getQuery()
            ->getResult()
        ;

        /**
         * @var PersistentCollection $collection
         */
        return array_map(
            fn (DoctrineWidget $widgetDoc) => $this->mapWidgetDocToWidget($widgetDoc, $locale),
            $widgets
        );
    }

    public function findById(int $id): ?Widget
    {
        $doctrineWidget = parent::find($id);

        if (!$doctrineWidget instanceof DoctrineWidget) {
            return null;
        }

        return $this->createDomain($doctrineWidget);
    }

    /**
     * @param DoctrineWidget $widgetDoc
     * @param string $locale
     * @return Widget
     * @throws \Exception
     */
    private function mapWidgetDocToWidget(DoctrineWidget $widgetDoc, string $locale = 'FR'): widget
    {
        $slot = $widgetDoc->getSlot();
        $contents = $widgetDoc->getWidgetContents()->toArray();
        $content = null;

        /**  @var WidgetContent $c */
        foreach ($contents as $c) {
            if ($c->getLocale() === strtoupper($locale) && $c->getRegion()->getName() === $this->getRegionService->getRegion()) {
                $content = $this->contentToWidgetTypeContext->process($c);
            }
        }
        return (new Widget())
            ->setId($widgetDoc->getId())
            ->setStatus($widgetDoc->getStatus())
            ->setName($widgetDoc->getName())
            ->setType($widgetDoc->getType())
            ->setSlotNumber($slot?->getNumber())
            ->setContent($content);
    }

    public function findPublishedSelectionWidget(string $locale): ?Widget
    {
        return $this->findPublishedWidgetWithType($locale, Widget::TYPE_SELECTIONS);
    }

    public function findPublishedHighlightListingWidget(string $locale): ?Widget
    {
        return $this->findPublishedWidgetWithType($locale, Widget::TYPE_HIGHLIGHT_LISTING);
    }

    public function findPublishedSoftHighlightWidget(string $locale): ?Widget
    {
        return $this->findPublishedWidgetWithType($locale, Widget::TYPE_SOFT_HIGHLIGHT);
    }

    public function findPublishedWidgetWithType(string $locale, string $type): ?Widget
    {
        $widget = $this->createQueryBuilder('w')
            ->where('w.status = :status')
            ->join('w.slot', 's')
            ->join('w.widgetContents', 'wc')
            ->join('wc.region', 'r')
            ->andWhere('r.name = :region')
            ->andWhere('w.type = :type')
            ->setParameters(['status' => Status::PUBLISHED,
                'region' => $this->getRegionService->getRegion(),
                'type' => $type])
            ->orderBy('s.number', 'ASC')
            ->getQuery()
            ->getOneOrNullResult()
        ;
        /**
         * @var PersistentCollection $collection
         */
        return $widget ? $this->mapWidgetDocToWidget($widget, $locale) : null;
    }

    public function findCompletedWidgetWithType(string $locale, string $type): array
    {
        $widgets = $this->createQueryBuilder('w')
            ->where('w.status = :status')
            ->join('w.widgetContents', 'wc')
            ->join('wc.region', 'r')
            ->andWhere('r.name = :region')
            ->andWhere('w.type = :type')
            ->setParameters(
                [
                    'region' => $this->getRegionService->getRegion(),
                    'status' => Status::COMPLETE,
                    'type' => $type
                ]
            )
            ->getQuery()
            ->getResult()
        ;

        return array_map(
            fn (DoctrineWidget $widget) =>
            $this->mapWidgetDocToWidget($widget, $locale),
            $widgets
        );
    }
}
