<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Adapter\Repository;

use Doctrine\ORM\EntityNotFoundException;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;
use Telenco\Component\Widget\Domain\Model\WidgetContent\Product;
use Telenco\Component\Widget\Domain\Model\WidgetContent\WidgetContent;
use Telenco\Component\Widget\Domain\Port\Repository\WidgetProductRepositoryInterface;
use Telenco\Component\Shared\Infrastructure\Entity\Region as DoctrineRegion;
use Telenco\Component\Widget\Infrastructure\Entity\Widget as DoctrineWidget;
use Telenco\Component\Widget\Infrastructure\Entity\WidgetContent\Product as DoctrineProduct;
use Telenco\Component\Widget\Infrastructure\Mapper\ProductMapper;

class WidgetSimpleProductRepository extends WidgetContentRepository implements WidgetProductRepositoryInterface
{
    public function __construct(
        ManagerRegistry $registry,
        protected RegionRepository $regionRepository,
        protected WidgetRepository $widgetRepository,
    ) {
        parent::__construct($registry, DoctrineProduct::class);
    }

    /**
     * @param WidgetContent $widgetContent
     * @throws EntityNotFoundException
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function save(WidgetContent $widgetContent): void
    {
        if (!$widgetContent instanceof Product) {
            throw new EntityNotFoundException(
                sprintf('The widget content model is not a %s instance', Product::class)
            );
        }

        /** @var DoctrineRegion $doctrineRegion */
        $doctrineRegion = $this->regionRepository->find($widgetContent->getRegion()->getId());

        /** @var DoctrineWidget $doctrineWidget */
        $doctrineWidget = $this->widgetRepository->find($widgetContent->getWidget()->getId());

        $doctrineProduct = ProductMapper::domainToDoctrine($widgetContent);
        $doctrineProduct->setRegion($doctrineRegion);
        $doctrineProduct->setWidget($doctrineWidget);

        $this->getEntityManager()->persist($doctrineProduct);
        $this->getEntityManager()->flush();
    }

    /**
     * @param WidgetContent $widgetContent
     * @throws EntityNotFoundException
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function update(WidgetContent $widgetContent): void
    {
        /** @var Product $topBrandContent */
        $topBrandContent = $widgetContent;

        /** @var DoctrineProduct $doctrineContent */
        $doctrineContent = $this->find($widgetContent->getId());

        if (!$doctrineContent instanceof DoctrineProduct) {
            throw new EntityNotFoundException(
                sprintf('The topBrand content does not exist for edit in %s', __METHOD__)
            );
        }
        $doctrineContent->setType($topBrandContent->getType());
        $doctrineContent->setName($topBrandContent->getTitle());
        $doctrineContent->setOffersId($topBrandContent->getOffersId());
        $doctrineContent->setLink($topBrandContent->getLink());

        $this->getEntityManager()->persist($doctrineContent);
        $this->getEntityManager()->flush();
    }

    /**
     * @param int $regionId
     * @return WidgetContent|null
     * @throws NonUniqueResultException
     */
    public function findWidgetContentByRegionId(int $regionId): ?WidgetContent
    {
        $doctrineProduct = $this->getQueryOdRegionAndWidget()
            ->where('wc.region = :region')
            ->setParameter('region', $regionId)
            ->getQuery()
            ->getOneOrNullResult()
        ;

        if (!$doctrineProduct instanceof DoctrineProduct) {
            return null;
        }

        return ProductMapper::doctrineToDomain($doctrineProduct);
    }

    /**
     * @param int $regionId
     * @param int $widgetId
     * @return WidgetContent|null
     * @throws NonUniqueResultException
     */
    public function getContent(int $regionId, int $widgetId): ?WidgetContent
    {
        $doctrineProduct = $this->getQueryOdRegionAndWidget()
            ->where('wc.region = :regionId')
            ->andWhere('wc.widget = :widgetId')
            ->setParameters([
                'regionId' => $regionId,
                'widgetId' => $widgetId
            ])
            ->getQuery()
            ->getOneOrNullResult()
        ;

        if (!$doctrineProduct instanceof DoctrineProduct) {
            return null;
        }

        return ProductMapper::doctrineToDomain($doctrineProduct);
    }

    /**
     * @param int $contentId
     * @return WidgetContent|null
     * @throws NonUniqueResultException
     */
    public function getById(int $contentId): ?WidgetContent
    {
        $doctrineProduct = $this->getQueryOdRegionAndWidget()
            ->where('wc.id = :contentId')
            ->setParameter('contentId', $contentId)
            ->getQuery()
            ->getOneOrNullResult()
        ;

        if (!$doctrineProduct instanceof DoctrineProduct) {
            return null;
        }

        return ProductMapper::doctrineToDomain($doctrineProduct);
    }

    public function findByLocale(string $locale): ?WidgetContent
    {
        // TODO: Implement findByLocale() method.
        return null;
    }

    public function getContents(int $regionId, int $widgetId): array
    {
        /** @var DoctrineProduct[] $widgetContents */
        $widgetContents = $this->findContents($regionId, $widgetId);
        return array_map(
            fn (DoctrineProduct $doctrineSimpleProductWidget) =>
            ProductMapper::doctrineToDomain($doctrineSimpleProductWidget),
            $widgetContents
        );
    }
}
