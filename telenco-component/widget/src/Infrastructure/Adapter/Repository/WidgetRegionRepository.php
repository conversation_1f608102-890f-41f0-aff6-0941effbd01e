<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Infrastructure\Adapter\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Telenco\Component\Widget\Infrastructure\Entity\WidgetContent\WidgetContent as WidgetContentDoctrine;

class WidgetRegionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, WidgetContentDoctrine::class);
    }
}
