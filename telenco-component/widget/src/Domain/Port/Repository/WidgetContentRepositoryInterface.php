<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Domain\Port\Repository;

use Telenco\Component\Widget\Domain\Model\WidgetContent\WidgetContent;

interface WidgetContentRepositoryInterface
{
    public function findWidgetContentByRegionId(int $regionId): ?WidgetContent;

    public function save(WidgetContent $widgetContent): void;

    public function update(WidgetContent $widgetContent): void;

    public function findByLocale(string $locale): ?WidgetContent;

    public function getContent(int $regionId, int $widgetId): ?WidgetContent;

    /**
     * @param int $regionId
     * @param int $widgetId
     * @return WidgetContent[]
     */
    public function getContents(int $regionId, int $widgetId): array;

    public function getById(int $contentId): ?WidgetContent;
}
