<?php

namespace Telenco\Component\Widget\Domain\UseCase\OperatorSelectionsEdit;

use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use SplFileInfo;
use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\Model\WidgetContent\Selections;
use Telenco\Component\Widget\Domain\Model\WidgetContent\SelectionsHighlight;
use Telenco\Component\Widget\Domain\Model\WidgetContent\SelectionsOffers;
use Telenco\Component\Widget\Domain\Port\Repository\RegionRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Repository\SelectionsHighlightRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Repository\SelectionsOffersRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Repository\SelectionsWidgetRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Repository\WidgetRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Service\UploaderServiceInterface;
use Telenco\Component\Widget\Domain\Presenter\OperatorSelectionsEditPresenterInterface;
use Telenco\Component\Widget\Domain\UseCase\OperatorSelectionsEdit\DTO\OperatorSelectionsEditRequest;
use Telenco\Component\Widget\Domain\UseCase\OperatorSelectionsEdit\DTO\OperatorSelectionsEditResponse;
use Telenco\Component\Widget\Domain\UseCase\OperatorSelectionsEdit\DTO\SelectionContentRequest;
use Telenco\Component\Widget\Infrastructure\Adapter\Repository\SelectionsOffersRepository;

class OperatorSelectionsEditUseCase
{
    public function __construct(
        private SelectionsWidgetRepositoryInterface $selectionsWidgetRepository,
        private RegionRepositoryInterface $regionRepository,
        private WidgetRepositoryInterface $widgetRepository,
        private SelectionsHighlightRepositoryInterface $highlightRepository,
        private SelectionsOffersRepositoryInterface $selectionOffersRepository,
        private UploaderServiceInterface $uploaderService,
    ) {
    }

    public function execute(
        OperatorSelectionsEditRequest $request,
        OperatorSelectionsEditPresenterInterface $presenter
    ): void {
        $response = new OperatorSelectionsEditResponse();

        /**  @var int $regionId */
        $regionId = $request->regionId;
        $widgetId = $request->widgetId;

        $region = $this->regionRepository->getRegionById($regionId);
        $widget = $this->widgetRepository->findById($widgetId);
        $contents = [];
        if (
            !$region instanceof Region ||
            !$widget instanceof Widget
        ) {
            $response->getNotification()->addError('contents', 'widget.invalid');
            $presenter->present($response);
            return;
        }
        $highlightModels = [];
        foreach ($request->getHighlights() as $highlight) {
            $highlightModel = new SelectionsHighlight();
            $id = $highlight->getId();
            if ($id !== null) {
                $highlightModel = $this->highlightRepository->findSelectionsHighlight($id);
                if (!$highlightModel instanceof SelectionsHighlight) {
                    $response->getNotification()->addError('contents', 'widget.invalid');
                    $presenter->present($response);
                    return;
                }
            }
            $imagePath = $highlight->getImageUrl();
            $image = $highlight->getImage();
            if ($image instanceof SplFileInfo) {
                $imagePath = $this->uploaderService->save($image);
            }
            $highlightModel->setImage($imagePath);
            $highlightModel->setSlug($highlight->getSlug());
            $highlightModel = $this->highlightRepository->save($highlightModel);
            $highlightModels[] = $highlightModel;
        }
        /**
         * @var SelectionContentRequest $requestContent
         */
        foreach ($request->getContents() as $requestContent) {
            $locale = $requestContent->getLocale();

            $contentId = $requestContent->getId();
            /**
             * @var Selections $selectionsWidgetContent
             */
            $selectionsWidgetContent = $contentId
                ? $this->selectionsWidgetRepository->getById($contentId)
                : new Selections();
            $offerSelections = [];
            foreach ($requestContent->getOfferSelections() as $offerSelectionRequest) {
                $selectionsOfferContentId = $offerSelectionRequest->getId();
                $selectionsOfferContent = $selectionsOfferContentId
                    ? $this->selectionOffersRepository->getById($selectionsOfferContentId)
                    : new SelectionsOffers();
                /**
                 * @var SelectionsOffers $selectionsOfferContent
                 */
                $imagePath = $offerSelectionRequest->getImageUrl();
                $image = $offerSelectionRequest->getImage();
                if ($image instanceof SplFileInfo) {
                    $imagePath = $this->uploaderService->save($image);
                }

                $selectionsOfferContent
                    ->setTitle($offerSelectionRequest->getTitle())
                    ->setImage($imagePath)
                    ->setOfferIds(explode(',', $offerSelectionRequest->getOfferIds() ?? ''));
                $offerSelections[] = $selectionsOfferContent;
            }
            $selectionsWidgetContent->setSelectionsOffers($offerSelections)
                ->setLocale(Locale::create($locale))
                ->setWidget($widget)
                ->setRegion($region)
                ->setSelectionsHighlights($highlightModels);
            ;
            $contents [] = $selectionsWidgetContent;
            $widget->activate($selectionsWidgetContent);

            if ($selectionsWidgetContent->getId() === null) {
                $this->selectionsWidgetRepository->save($selectionsWidgetContent);
            } else {
                $this->selectionsWidgetRepository->update($selectionsWidgetContent);
            }
        }
        $widget->setContents($contents);
        $this->widgetRepository->save($widget);

        $response->setWidget($widget);
        $response->setContents($contents);
        $response->setHighlights($highlightModels);
        $presenter->present($response);
    }
}
