<?php

namespace Telenco\Component\Widget\Domain\UseCase\OperatorSelectionsEdit\DTO;

use Assert\LazyAssertionException;
use Telenco\Component\Widget\Domain\Assert\Assert;
use SplFileInfo;

class SelectionHighlightRequest
{
    /**
     * NewsContentRequest constructor.
     * @param string|null $slug
     * @param SplFileInfo|null $image
     */
    public function __construct(
        private ?int $id,
        private ?string $slug = null,
        private ?SplFileInfo $image = null,
        private ?string $imageUrl = null
    ) {
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSlug(): ?string
    {
        return $this->slug;
    }

    /**
     * @param string|null $slug
     * @return $this
     */
    public function setSlug(?string $slug): self
    {
        $this->slug = $slug;
        return $this;
    }

    /**
     * @return SplFileInfo|null
     */
    public function getImage(): ?SplFileInfo
    {
        return $this->image;
    }

    /**
     * @param SplFileInfo|null $image
     * @return $this
     */
    public function setImage(?SplFileInfo $image): self
    {
        $this->image = $image;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getImageUrl(): ?string
    {
        return $this->imageUrl;
    }

    /**
     * @param string|null $imageUrl
     * @return $this
     */
    public function setImageUrl(?string $imageUrl): self
    {
        $this->imageUrl = $imageUrl;
        return $this;
    }

    public function validate(OperatorSelectionsEditResponse $response): bool
    {
        try {
            Assert::lazy()
                ->that($this->slug, 'slug')->notBlank('highlight.validate.required')
                ->that($this->image, 'image')->notBlank('highlight.validate.required')
                ->verifyNow();

            return true;
        } catch (LazyAssertionException $exception) {
            foreach ($exception->getErrorExceptions() as $errorException) {
                $response
                    ->getNotification()
                    ->addError($errorException->getPropertyPath(), $errorException->getMessage())
                ;
            }
            return false;
        }
    }
}
