<?php

namespace Telenco\Component\Widget\Domain\UseCase\OperatorSelectionsEdit\DTO;

use Assert\LazyAssertionException;
use Telenco\Component\Widget\Domain\Assert\Assert;
use SplFileInfo;

class SelectionRequest
{
    private ?int $id = null;

    /**
     * SelectionRequest constructor.
     * @param int|null $id
     * @param string|null $title
     * @param SplFileInfo|null $image
     * @param string|null $offerIds
     */
    public function __construct(
        ?int $id,
        private ?string $title = null,
        private ?SplFileInfo $image = null,
        private ?string $offerIds = null,
        private ?string $imageUrl = null
    ) {
        $this->id = $id;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getTitle(): ?string
    {
        return $this->title;
    }

    /**
     * @param string|null $title
     * @return $this
     */
    public function setTitle(?string $title): self
    {
        $this->title = $title;
        return $this;
    }

    /**
     * @return SplFileInfo|null
     */
    public function getImage(): ?SplFileInfo
    {
        return $this->image;
    }

    /**
     * @param SplFileInfo|null $image
     * @return $this
     */
    public function setImage(?SplFileInfo $image): self
    {
        $this->image = $image;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getOfferIds(): ?string
    {
        return $this->offerIds;
    }

    /**
     * @param string|null $offerIds
     * @return $this
     */
    public function setOfferIds(?string $offerIds): self
    {
        $this->offerIds = $offerIds;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getImageUrl(): ?string
    {
        return $this->imageUrl;
    }

    /**
     * @param string|null $imageUrl
     * @return $this
     */
    public function setImageUrl(?string $imageUrl): self
    {
        $this->imageUrl = $imageUrl;
        return $this;
    }

    public function validate(OperatorSelectionsEditResponse $response): bool
    {
        try {
            Assert::lazy()
                ->that($this->title, 'title')->notBlank('highlight.validate.required')
                ->that($this->image, 'image')->notBlank('highlight.validate.required')
                ->that($this->offerIds, 'offerIds')->notEmpty('highlight.validate.required')
                ->verifyNow();

            return true;
        } catch (LazyAssertionException $exception) {
            foreach ($exception->getErrorExceptions() as $errorException) {
                $response
                    ->getNotification()
                    ->addError($errorException->getPropertyPath(), $errorException->getMessage())
                ;
            }
            return false;
        }
    }
}
