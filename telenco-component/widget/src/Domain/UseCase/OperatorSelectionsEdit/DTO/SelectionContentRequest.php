<?php

namespace Telenco\Component\Widget\Domain\UseCase\OperatorSelectionsEdit\DTO;

use Assert\LazyAssertionException;
use Telenco\Component\Widget\Domain\Assert\Assert;
use SplFileInfo;

class SelectionContentRequest
{
    private ?int $id = null;

    private string $locale;

    /**
     * SelectionContentRequest constructor.
     * @param int|null $id
     * @param string $locale
     * @param SelectionRequest[] $offerSelections
     */
    public function __construct(
        ?int $id,
        string $locale,
        private array $offerSelections,
    ) {
        $this->id = $id;
        $this->locale = $locale;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string
     */
    public function getLocale(): string
    {
        return $this->locale;
    }

    /**
     * @param string $locale
     * @return $this
     */
    public function setLocale(string $locale): self
    {
        $this->locale = $locale;
        return $this;
    }

    /**
     * @return SelectionRequest[]
     */
    public function getOfferSelections(): array
    {
        return $this->offerSelections;
    }

    /**
     * @param SelectionRequest[] $offerSelections
     * @return $this
     */
    public function setOfferSelections(array $offerSelections): self
    {
        $this->offerSelections = $offerSelections;
        return $this;
    }

    public function validate(OperatorSelectionsEditResponse $response): bool
    {
        try {
            Assert::lazy()
                ->that($this->offerSelections, 'selection')->notEmpty('highlight.validate.required')
                ->verifyNow();

            return true;
        } catch (LazyAssertionException $exception) {
            foreach ($exception->getErrorExceptions() as $errorException) {
                $response
                    ->getNotification()
                    ->addError($errorException->getPropertyPath(), $errorException->getMessage())
                ;
            }
            return false;
        }
    }
}
