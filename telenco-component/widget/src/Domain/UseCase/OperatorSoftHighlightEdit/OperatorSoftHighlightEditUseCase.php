<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Domain\UseCase\OperatorSoftHighlightEdit;

use SplFileInfo;
use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\Model\WidgetContent\SoftHighlight;
use Telenco\Component\Widget\Domain\Port\Repository\RegionRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Repository\SoftHighlightRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Repository\WidgetRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Service\UploaderServiceInterface;
use Telenco\Component\Widget\Domain\Presenter\OperatorSoftHighlightEditPresenterInterface;
use Telenco\Component\Widget\Domain\UseCase\OperatorSoftHighlightEdit\DTO\SoftHighlightContentRequest;
use Telenco\Component\Widget\Domain\UseCase\OperatorSoftHighlightEdit\DTO\OperatorSoftHighlightEditRequest;
use Telenco\Component\Widget\Domain\UseCase\OperatorSoftHighlightEdit\DTO\OperatorSoftHighlightEditResponse;
use Telenco\Component\Widget\Exception\HighlightNotFoundException;

class OperatorSoftHighlightEditUseCase
{
    public function __construct(
        private SoftHighlightRepositoryInterface $highlightRepository,
        private RegionRepositoryInterface $regionRepository,
        private UploaderServiceInterface $uploaderService,
        private WidgetRepositoryInterface $widgetRepository,
    ) {
    }

    public function execute(
        OperatorSoftHighlightEditRequest $request,
        OperatorSoftHighlightEditPresenterInterface $presenter
    ): void {
        $response = new OperatorSoftHighlightEditResponse();

        /** @var int $regionId */
        $regionId = $request->regionId;
        $widgetId = $request->widgetId;

        $region = $this->regionRepository->getRegionById($regionId);
        $widget = $this->widgetRepository->findById($widgetId);
        $contents = [];
        if (
            !$region instanceof Region ||
            !$widget instanceof Widget
        ) {
            $response->getNotification()->addError('contents', 'widget.invalid');
            $presenter->present($response);
            return;
        }

        $image = $request->image;
        $imagePath = '';
        if ($image instanceof SplFileInfo) {
            $imagePath = $this->uploaderService->save($image);
        }
        /** @var SoftHighlightContentRequest $requestContent */
        foreach ($request->getContents() as $requestContent) {
            if ($requestContent->validate($response) === false) {
                $presenter->present($response);
                return;
            }

            $contentId = $requestContent->getId();
            $highlightWidgetContent = $contentId ? $this->highlightRepository->getById($contentId) : new SoftHighlight();
            if (!$highlightWidgetContent instanceof SoftHighlight) {
                throw new HighlightNotFoundException();
            }

            $currentImg = $highlightWidgetContent->getImage();
            if ($currentImg === null && $request->getImage() === null) {
                $response->getNotification()->addError('image', 'highlight.validate.required');
                $presenter->present($response);
                return;
            }

            if ($image instanceof SplFileInfo) {
                if ($currentImg !== null) {
                    $this->uploaderService->delete($currentImg);
                }
            } else {
                $imagePath = $currentImg;
            }
            /** @var string $contentLink */
            $contentLink = $requestContent->getLink();

            /** @var SoftHighlight $highlightWidgetContent */
            $highlightWidgetContent
                ->setImage($imagePath)
                ->setLink($contentLink)
                ->setLocale(Locale::create($requestContent->getLocale()))
                ->setRegion($region)
                ->setWidget($widget)
            ;
            $contents[] = $highlightWidgetContent;
            $widget->activate($highlightWidgetContent);

            if ($highlightWidgetContent->getId() === null) {
                $this->highlightRepository->save($highlightWidgetContent);
            } else {
                $this->highlightRepository->update($highlightWidgetContent);
            }
        }

        $widget->setContents($contents);
        $this->widgetRepository->save($widget);

        $response->setWidget($widget);
        $response->setContentsSoftHighlight($contents);
        $presenter->present($response);
    }
}
