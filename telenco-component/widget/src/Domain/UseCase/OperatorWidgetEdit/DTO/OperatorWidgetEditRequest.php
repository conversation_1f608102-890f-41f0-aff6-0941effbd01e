<?php

namespace Telenco\Component\Widget\Domain\UseCase\OperatorWidgetEdit\DTO;

use Assert\LazyAssertionException;
use Telenco\Component\Widget\Domain\Assert\Assert;

class OperatorWidgetEditRequest
{
    public function __construct(
        public ?int $id = null,
        public ?string $name = null
    ) {
    }

    public function validate(OperatorWidgetEditResponse $response): bool
    {
        try {
            Assert::lazy()
                ->that($this->name, 'name')->notBlank('The widget name is invalid')
                ->verifyNow()
            ;

            return true;
        } catch (LazyAssertionException $exception) {
            foreach ($exception->getErrorExceptions() as $errorException) {
                $response
                    ->getNotification()
                    ->addError($errorException->getPropertyPath(), $errorException->getMessage())
                ;
            }

            return false;
        }
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }
}
