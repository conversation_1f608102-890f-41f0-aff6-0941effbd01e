<?php

namespace Telenco\Component\Widget\Domain\UseCase\OperatorNewsEdit\DTO;

class NewsContentRequest
{
    private ?int $id = null;

    private string $locale;

    /**
     * @var int|null
     */
    private ?int $mainNewsId =  null;

    /**
     * @var array|null
     */
    private ?array $minorNewsId = null;

    /**
     * NewsContentRequest constructor.
     * @param int|null $id
     * @param string $locale
     * @param int|null $mainNewsId
     * @param array|null $minorNewsId
     */
    public function __construct(?int $id, string $locale, ?int $mainNewsId, ?array $minorNewsId)
    {
        $this->id = $id;
        $this->locale = $locale;
        $this->mainNewsId = $mainNewsId;
        $this->minorNewsId = $minorNewsId;
    }


    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string
     */
    public function getLocale(): string
    {
        return $this->locale;
    }

    /**
     * @param string $locale
     * @return $this
     */
    public function setLocale(string $locale): self
    {
        $this->locale = $locale;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getMainNewsId(): ?int
    {
        return $this->mainNewsId;
    }

    /**
     * @param int|null $mainNewsId
     * @return $this
     */
    public function setMainNewsId(?int $mainNewsId): self
    {
        $this->mainNewsId = $mainNewsId;
        return $this;
    }

    /**
     * @return array|null
     */
    public function getMinorNewsId(): ?array
    {
        return $this->minorNewsId;
    }

    /**
     * @param array|null $minorNewsId
     * @return $this
     */
    public function setMinorNewsId(?array $minorNewsId): self
    {
        $this->minorNewsId = $minorNewsId;
        return $this;
    }
}
