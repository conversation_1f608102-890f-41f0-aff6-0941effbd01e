<?php

namespace Telenco\Component\Widget\Domain\UseCase\OperatorNewsEdit\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Error\NotificationTrait;
use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\Model\WidgetContent\WidgetContent;

class OperatorNewsEditResponse
{
    use NotificationTrait;

    private ?array $contentsNews = null;

    private ?Widget $widget = null;

    private ?Region $region = null;

    /**
     * @return WidgetContent[]|null
     */
    public function getContentsNews(): ?array
    {
        return $this->contentsNews;
    }

    /**
     * @param WidgetContent[]|null $contentsNews
     */
    public function setContentsNews(?array $contentsNews): void
    {
        $this->contentsNews = $contentsNews;
    }

    /**
     * @return Widget|null
     */
    public function getWidget(): ?Widget
    {
        return $this->widget;
    }

    /**
     * @param Widget|null $widget
     */
    public function setWidget(?Widget $widget): void
    {
        $this->widget = $widget;
    }

    /**
     * @return Region|null
     */
    public function getRegion(): ?Region
    {
        return $this->region;
    }

    /**
     * @param Region|null $region
     */
    public function setRegion(?Region $region): void
    {
        $this->region = $region;
    }
}
