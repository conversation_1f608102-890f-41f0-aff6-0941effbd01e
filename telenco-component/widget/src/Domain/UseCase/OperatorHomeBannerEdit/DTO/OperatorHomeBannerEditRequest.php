<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Domain\UseCase\OperatorHomeBannerEdit\DTO;

use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Request\WidgetContentRequestInterface;

final class OperatorHomeBannerEditRequest implements WidgetContentRequestInterface
{
    /** @var OperatorHomeBannerContentRequest[] $contents*/
    public array $contents = [];

    public function __construct(
        public ?int $regionId,
        public int $widgetId,
        public ?Region $region = null,
    ) {
    }

    public static function create(
        int $regionId,
        int $widgetId,
        array $contents,
        ?Region $region = null,
    ): self {
        $request = new self($regionId, $widgetId, $region);
        $request->contents = $contents;

        return $request;
    }

    /**
     * @return OperatorHomeBannerContentRequest[]
     */
    public function getContents(): array
    {
        return $this->contents;
    }
}
