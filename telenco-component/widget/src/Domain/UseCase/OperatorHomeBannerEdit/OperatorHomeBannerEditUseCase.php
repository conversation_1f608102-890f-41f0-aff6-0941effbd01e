<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Domain\UseCase\OperatorHomeBannerEdit;

use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use SplFileInfo;
use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\Model\WidgetContent\HomeBanner;
use Telenco\Component\Widget\Domain\Port\Repository\HomeBannerRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Repository\RegionRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Repository\WidgetRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Service\UploaderServiceInterface;
use Telenco\Component\Widget\Domain\Presenter\OperatorHomeBannerEditPresenterInterface;
use Telenco\Component\Widget\Domain\UseCase\OperatorHomeBannerEdit\DTO\OperatorHomeBannerEditRequest;
use Telenco\Component\Widget\Domain\UseCase\OperatorHomeBannerEdit\DTO\OperatorHomeBannerEditResponse;

final class OperatorHomeBannerEditUseCase
{
    public function __construct(
        private readonly HomeBannerRepositoryInterface $homeBannerRepository,
        private readonly RegionRepositoryInterface $regionRepository,
        private readonly UploaderServiceInterface $uploaderService,
        private readonly WidgetRepositoryInterface $widgetRepository,
    ) {
    }

    public function execute(
        OperatorHomeBannerEditRequest $request,
        OperatorHomeBannerEditPresenterInterface $presenter
    ): void {
        $response = new OperatorHomeBannerEditResponse();

        /** @var int $regionId */
        $regionId = $request->regionId;
        $widgetId = $request->widgetId;

        $region = $this->regionRepository->getRegionById($regionId);
        $widget = $this->widgetRepository->findById($widgetId);
        $contents = [];
        if (
            !$region instanceof Region ||
            !$widget instanceof Widget
        ) {
            $response->getNotification()->addError('contents', 'widget.invalid');
            $presenter->present($response);
            return;
        }

        foreach ($request->getContents() as $requestContent) {
            if ($requestContent->validate($response) === false) {
                $presenter->present($response);
                return;
            }
            $contentId  = $requestContent->getId();

            $homeBannerContent = $contentId ? $this->homeBannerRepository->getById($contentId) : new HomeBanner();
            if (!$homeBannerContent instanceof HomeBanner) {
                $homeBannerContent = new HomeBanner();
            }

            // IMAGE01 and URL01
            $currentImage01 = $homeBannerContent->getImage01();
            $image01 = $requestContent->getImage01();
            $image01Path = '';

            if ($currentImage01 === null && $image01 === null) {
                $response->getNotification()->addError(sprintf('contents.%s.image01', $requestContent->getLocale()), 'widget.home_banner.error.image');
            } elseif ($image01 instanceof SplFileInfo) {
                if ($currentImage01 !== null) {
                    $this->uploaderService->delete($currentImage01);
                }
                $image01Path = $this->uploaderService->save($image01);
            } else {
                $image01Path = $currentImage01;
            }
            /** @var string $url01 */
            $url01 = $requestContent->getUrl01();

            // IMAGE02 and URL02
            $currentImage02 = $homeBannerContent->getImage02();
            $image02 = $requestContent->getImage02();
            $image02Path = '';

            if ($currentImage02 === null && $image02 === null) {
                $response->getNotification()->addError(sprintf('contents.%s.image02', $requestContent->getLocale()), 'widget.home_banner.error.image');
            } elseif ($image02 instanceof SplFileInfo) {
                if ($currentImage02 !== null) {
                    $this->uploaderService->delete($currentImage02);
                }
                $image02Path = $this->uploaderService->save($image02);
            } else {
                $image02Path = $currentImage02;
            }
            /** @var string $url02 */
            $url02 = $requestContent->getUrl02();

            // IMAGE03 and URL03
            $currentImage03 = $homeBannerContent->getImage03();
            $image03 = $requestContent->getImage03();
            $image03Path = '';

            if ($currentImage03 === null && $image03 === null) {
                $response->getNotification()->addError(sprintf('contents.%s.image03', $requestContent->getLocale()), 'widget.home_banner.error.image');
            } elseif ($image03 instanceof SplFileInfo) {
                if ($currentImage03 !== null) {
                    $this->uploaderService->delete($currentImage03);
                }
                $image03Path = $this->uploaderService->save($image03);
            } else {
                $image03Path = $currentImage03;
            }
            /** @var string $url03 */
            $url03 = $requestContent->getUrl03();

            // IMAGE04 and URL04
            $currentImage04 = $homeBannerContent->getImage04();
            $image04 = $requestContent->getImage04();
            $image04Path = '';

            if ($currentImage04 === null && $image04 === null) {
                $response->getNotification()->addError(sprintf('contents.%s.image04', $requestContent->getLocale()), 'widget.home_banner.error.image');
            } elseif ($image04 instanceof SplFileInfo) {
                if ($currentImage04 !== null) {
                    $this->uploaderService->delete($currentImage04);
                }
                $image04Path = $this->uploaderService->save($image04);
            } else {
                $image04Path = $currentImage04;
            }
            /** @var string $url04 */
            $url04 = $requestContent->getUrl04();

            $locale = Locale::create($requestContent->getLocale());

            $homeBannerContent
                ->setImage01($image01Path)
                ->setUrl01($url01)
                ->setImage02($image02Path)
                ->setUrl02($url02)
                ->setImage03($image03Path)
                ->setUrl03($url03)
                ->setImage04($image04Path)
                ->setUrl04($url04)
                ->setLocale($locale)
                ->setRegion($region)
                ->setWidget($widget)
            ;

            $contents[] = $homeBannerContent;
        }

        if ($response->getNotification()->hasError()) {
            $presenter->present($response);
            return;
        }

        foreach ($contents as $content) {
            $widget->activate($content);
            if ($content->getId() === null) {
                $this->homeBannerRepository->save($content);
            } else {
                $this->homeBannerRepository->update($content);
            }
        }

        $widget->setContents($contents);
        $this->widgetRepository->save($widget);

        $response->setWidget($widget);
        $response->setHomeBannerContents($contents);
        $presenter->present($response);
    }
}
