<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Domain\UseCase\OperatorPromoItemEdit\DTO;

use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Request\WidgetContentRequestInterface;

final class OperatorPromoItemEditRequest implements WidgetContentRequestInterface
{
    /** @var PromoItemContentRequest[]  */
    public array $contents = [];

    public function __construct(
        public int $regionId,
        public int $widgetId,
        public ?Region $region = null
    ) {
    }

    public static function create(
        int $regionId,
        int $widgetId,
        array $contents,
        ?Region $region = null,
    ): self {
        $request = new self($regionId, $widgetId, $region);
        $request->contents = $contents;

        return $request;
    }

    /**
     * @return PromoItemContentRequest[]
     */
    public function getContents(): array
    {
        return $this->contents;
    }

    public static function validateRequestContent(OperatorPromoItemEditRequest $request, OperatorPromoItemEditResponse $response): bool
    {
        $isContentsValid = true;

        foreach ($request->contents as $promoItemContentRequest) {
            if ($promoItemContentRequest->validate($response) === false) {
                $isContentsValid = false;
            }
        }

        return $isContentsValid;
    }
}
