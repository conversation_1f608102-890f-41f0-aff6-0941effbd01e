<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Domain\UseCase\OperatorWidgetDelete\DTO;

use Telenco\Component\Widget\Domain\Model\Widget;

class OperatorWidgetDeleteResponse
{
    public const STATUS_NOT_FOUND = 'not_found';
    public const STATUS_UNAUTHORIZED = 'unauthorized';
    public const STATUS_DELETED = 'deleted';

    public function __construct(
        private Widget $widget,
        private string $status
    ) {
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }


    /**
     * @return Widget
     */
    public function getWidget(): Widget
    {
        return $this->widget;
    }
}
