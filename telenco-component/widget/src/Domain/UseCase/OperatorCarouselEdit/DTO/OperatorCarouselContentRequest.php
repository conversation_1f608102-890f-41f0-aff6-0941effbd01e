<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Domain\UseCase\OperatorCarouselEdit\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Assert\Assert;
use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Model\WidgetContent\Product;
use Telenco\Component\Widget\Domain\UseCase\WidgetSimpleProductEdit\DTO\WidgetSimpleProductEditResponse;

final class OperatorCarouselContentRequest
{
    public function __construct(
        private string $locale,
        private ?int $id = null,
        private array $discountOfferId = [],
        private array $newsOfferId = [],
        private array $bestSellsOfferId = []
    ) {
    }

    /**
     * @return string
     */
    public function getLocale(): string
    {
        return $this->locale;
    }

    /**
     * @param string $locale
     * @return OperatorCarouselContentRequest
     */
    public function setLocale(string $locale): OperatorCarouselContentRequest
    {
        $this->locale = $locale;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return OperatorCarouselContentRequest
     */
    public function setId(?int $id): OperatorCarouselContentRequest
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return array
     */
    public function getDiscountOfferId(): array
    {
        return $this->discountOfferId;
    }

    /**
     * @param array $discountOfferId
     * @return OperatorCarouselContentRequest
     */
    public function setDiscountOfferId(array $discountOfferId): OperatorCarouselContentRequest
    {
        $this->discountOfferId = $discountOfferId;
        return $this;
    }

    /**
     * @return array
     */
    public function getNewsOfferId(): array
    {
        return $this->newsOfferId;
    }

    /**
     * @param array $newsOfferId
     * @return OperatorCarouselContentRequest
     */
    public function setNewsOfferId(array $newsOfferId): OperatorCarouselContentRequest
    {
        $this->newsOfferId = $newsOfferId;
        return $this;
    }

    /**
     * @return array
     */
    public function getBestSellsOfferId(): array
    {
        return $this->bestSellsOfferId;
    }

    /**
     * @param array $bestSellsOfferId
     * @return OperatorCarouselContentRequest
     */
    public function setBestSellsOfferId(array $bestSellsOfferId): OperatorCarouselContentRequest
    {
        $this->bestSellsOfferId = $bestSellsOfferId;
        return $this;
    }

    public function validateOffers(OperatorCarouselEditResponse $response): bool
    {
        $valid = $this->validateOffersId($this->discountOfferId, '', $response);
        $valid = $valid && $this->validateOffersId($this->newsOfferId, '', $response);
        return $valid && $this->validateOffersId($this->bestSellsOfferId, '', $response);
    }

    /**
     * Return true if all array offers id is numeric
     *
     * @param array $offerIds
     * @param string $fieldName
     * @param OperatorCarouselEditResponse $response
     * @return bool
     */
    private function validateOffersId(array $offerIds, string $fieldName, OperatorCarouselEditResponse $response): bool
    {
        foreach ($offerIds as $offerId) {
            if (!is_integer($offerId)) {
                $response->getNotification()->addError($fieldName, 'widgetCarousel.validate.offersId.invalid');
                return false;
            }
        }
        return true;
    }
}
