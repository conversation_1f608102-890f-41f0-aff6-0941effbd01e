<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Domain\Model\WidgetContent;

use Marketplace\Component\Offer\Domain\Model\Offer;

class Product extends WidgetContent
{
    public const DIVERS_TYPE = 'divers';

    public const SIMPLE_TYPE = 'simple';

    public const PROMOTION_TYPE = 'promotion';

    /** @var array<string, string>  */
    public const TYPES = [
        self::SIMPLE_TYPE => self::SIMPLE_TYPE,
        self::DIVERS_TYPE => self::DIVERS_TYPE,
        self::PROMOTION_TYPE => self::PROMOTION_TYPE
    ];

    private array $link = [];

    private array $offersId = [];

    private string $type = self::SIMPLE_TYPE;

    /**
     * @var array<array-key,Offer|null>
     */
    private array $offers = [];

    /**
     * @return array
     */
    public function getLink(): array
    {
        return $this->link;
    }

    public function setLink(array $link): self
    {
        $this->link = $link;
        return $this;
    }

    public function getOffersId(): array
    {
        return $this->offersId;
    }

    public function setOffersId(array $offersId): self
    {
        $this->offersId = $offersId;
        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function isContentIsCompleted(): bool
    {
        return $this->offersId !== [];
    }

    /**
     * @return array<array-key,Offer|null>
     */
    public function getOffers(): array
    {
        return $this->offers;
    }

    /**
     * @param array<array-key,Offer|null> $offers
     */
    public function setOffers(array $offers): void
    {
        $this->offers = $offers;
    }

    public function hasTitle(): bool
    {
        return $this->title !== '';
    }

    public function hasLink(): bool
    {
        return !empty($this->link);
    }

    public function isPromotionType(): bool
    {
        if (!isset(self::TYPES[$this->type])) {
            throw new \Exception('The type does not exist');
        }

        return self::TYPES[$this->type] === self::PROMOTION_TYPE;
    }
}
