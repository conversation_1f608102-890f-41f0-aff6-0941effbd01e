<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Domain\Model\WidgetContent;

class Selections extends WidgetContent
{
    /**
     * @var SelectionsOffers[] $selectionsOffers
     */
    private array $selectionsOffers = [];

    /**
     * @var SelectionsHighlight[] $selectionsHighlights
     */
    private array $selectionsHighlights = [];

    /**
     * @return SelectionsOffers[]
     */
    public function getSelectionsOffers(): array
    {
        return $this->selectionsOffers;
    }

    /**
     * @param SelectionsOffers[] $selectionsOffers
     * @return $this
     */
    public function setSelectionsOffers(array $selectionsOffers): self
    {
        $this->selectionsOffers = $selectionsOffers;
        return $this;
    }

    /**
     * @return SelectionsHighlight[]
     */
    public function getSelectionsHighlights(): array
    {
        return $this->selectionsHighlights;
    }

    /**
     * @param SelectionsHighlight[] $selectionsHighlights
     * @return $this
     */
    public function setSelectionsHighlights(array $selectionsHighlights): self
    {
        $this->selectionsHighlights = $selectionsHighlights;
        return $this;
    }

    public function isContentIsCompleted(): bool
    {
        $selectionsOffersCompleted = !empty($this->selectionsOffers);

        foreach ($this->selectionsOffers as $selectionsOffer) {
            if (!$selectionsOffer->isContentIsCompleted()) {
                $selectionsOffersCompleted = false;
                break;
            }
        }
        $highlightCompleted = !empty($this->getSelectionsHighlights());
        foreach ($this->selectionsHighlights as $selectionsHighlight) {
            if (!$selectionsHighlight->isContentIsCompleted()) {
                $highlightCompleted = false;
                break;
            }
        }
        //TODO check content completed for all locale
        return $selectionsOffersCompleted &&
            $highlightCompleted;
    }
}
