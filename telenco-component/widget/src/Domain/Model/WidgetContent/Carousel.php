<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Domain\Model\WidgetContent;

use Marketplace\Component\Offer\Domain\Model\Offer;

class Carousel extends WidgetContent
{
    private array $discountOfferId = [];
    private array $newsOfferId = [];
    private array $bestSellsOfferId = [];


    /**
     * @var array<array-key,Offer|null>
     */
    private array $discountOffers = [];

    /**
     * @var array<array-key,Offer|null>
     */
    private array $newsOffers = [];


    /**
     * @var array<array-key,Offer|null>
     */
    private array $bestSellsOffers = [];

    /**
     * @return array
     */
    public function getDiscountOfferId(): array
    {
        return $this->discountOfferId;
    }

    /**
     * @param array $discountOfferId
     * @return Carousel
     */
    public function setDiscountOfferId(array $discountOfferId): Carousel
    {
        $this->discountOfferId = $discountOfferId;
        return $this;
    }

    /**
     * @return array
     */
    public function getNewsOfferId(): array
    {
        return $this->newsOfferId;
    }

    /**
     * @param array $newsOfferId
     * @return Carousel
     */
    public function setNewsOfferId(array $newsOfferId): Carousel
    {
        $this->newsOfferId = $newsOfferId;
        return $this;
    }

    /**
     * @return array
     */
    public function getBestSellsOfferId(): array
    {
        return $this->bestSellsOfferId;
    }

    /**
     * @param array $bestSellsOfferId
     * @return Carousel
     */
    public function setBestSellsOfferId(array $bestSellsOfferId): Carousel
    {
        $this->bestSellsOfferId = $bestSellsOfferId;
        return $this;
    }

    /**
     * @return array<array-key,Offer|null>
     */
    public function getDiscountOffers(): array
    {
        return $this->discountOffers;
    }

    /**
     * @param array<array-key,Offer|null> $discountOffers
     * @return Carousel
     */
    public function setDiscountOffers(array $discountOffers): Carousel
    {
        $this->discountOffers = $discountOffers;
        return $this;
    }

    /**
     * @return array<array-key,Offer|null>
     */
    public function getNewsOffers(): array
    {
        return $this->newsOffers;
    }

    /**
     * @param array<array-key,Offer|null> $newsOffers
     * @return Carousel
     */
    public function setNewsOffers(array $newsOffers): Carousel
    {
        $this->newsOffers = $newsOffers;
        return $this;
    }

    /**
     * @return array<array-key,Offer|null>
     */
    public function getBestSellsOffers(): array
    {
        return $this->bestSellsOffers;
    }

    /**
     * @param array<array-key,Offer|null> $bestSellsOffers
     * @return Carousel
     */
    public function setBestSellsOffers(array $bestSellsOffers): Carousel
    {
        $this->bestSellsOffers = $bestSellsOffers;
        return $this;
    }

    public function isContentIsCompleted(): bool
    {
        return $this->discountOfferId !== [] || $this->newsOfferId !== [] || $this->bestSellsOfferId !== [];
    }


    public function hasTitle(): bool
    {
        return $this->title !== '';
    }
}
