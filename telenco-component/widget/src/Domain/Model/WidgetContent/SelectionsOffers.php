<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Domain\Model\WidgetContent;

class SelectionsOffers
{
    private ?int $id = null;

    private ?string $title = null;

    private ?string $image = null;

    private array $offerIds = [];



    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(?string $image): self
    {
        $this->image = $image;
        return $this;
    }

    /**
     * @return array
     */
    public function getOfferIds(): array
    {
        return $this->offerIds;
    }

    /**
     * @param array $offerIds
     * @return $this
     */
    public function setOfferIds(array $offerIds): self
    {
        $this->offerIds = $offerIds;
        return $this;
    }

    public function hasOfferIds(): bool
    {
        return !empty($this->offerIds);
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getTitle(): ?string
    {
        return $this->title;
    }

    /**
     * @param string|null $title
     * @return $this
     */
    public function setTitle(?string $title): self
    {
        $this->title = $title;
        return $this;
    }

    public function isContentIsCompleted(): bool
    {
        //TODO check content completed for all locale
        return $this->getImage() !== '' &&
            $this->getTitle() !== '' &&
            $this->hasOfferIds();
    }
}
