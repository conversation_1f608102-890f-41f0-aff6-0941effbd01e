<?php

namespace Telenco\Component\Widget\Domain\Model;

use JsonSerializable;
use Telenco\Component\Widget\Domain\Model\WidgetContent\WidgetContent;
use Telenco\Component\Widget\Domain\Status\Status;
use Telenco\Component\Widget\Domain\Status\StatusFieldTrait;
use Telenco\Component\Widget\Domain\Traits\SlotNumberTrait;
use Telenco\Component\Widget\Domain\UseCase\OperatorWidgetCreation\DTO\OperatorWidgetCreationRequest;
use Telenco\Component\Widget\Exception\InvalidArgumentException;

class Widget implements JsonSerializable
{
    use StatusFieldTrait;
    use SlotNumberTrait;

    public const TYPE_HOME_BANNER = 'home_banner';
    public const TYPE_HIGHLIGHT = 'highlight';
    public const TYPE_CAROUSEL = 'carousel_v2';
    public const TYPE_PRODUCT_CAROUSEL = 'product_carousel';
    public const TYPE_TOP_BRAND = 'top_brand';
    public const TYPE_NEWS_WIDGET = 'news_widget';
    public const TYPE_SELECTIONS = 'selections_widget';
    public const TYPE_HIGHLIGHT_LISTING = 'highlight_listing';
    public const TYPE_SOFT_HIGHLIGHT = 'soft_highlight';
    public const TYPE_PROMO_ITEM = 'promo_item';

    public const TYPE_TRIPLE_PROMOS = 'triple_promos';

    public const MAX_SLOT_NUMBER = 50;
    public const MIN_SLOT_NUMBER = 1;

    private ?int $id = null;

    private string $type;

    private string $name;

    private ?WidgetContent $content;

    private array $contents;

    public static function fromOperatorWidgetCreationRequest(OperatorWidgetCreationRequest $request): Widget
    {
        if (null === $request->name || null === $request->type) {
            throw new InvalidArgumentException();
        }

        $widget = new Widget();
        $widget->setName($request->name);
        $widget->setType($request->type);
        $widget->setStatus(Status::INCOMPLETE);

        return $widget;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return WidgetContent|null
     */
    public function getContent(): ?WidgetContent
    {
        return $this->content;
    }

    /**
     * @param WidgetContent|null $content
     * @return Widget
     */
    public function setContent(?WidgetContent $content): self
    {
        $this->content = $content;
        return $this;
    }

    public function activate(WidgetContent $widgetContent): bool
    {
        if ($widgetContent->isContentIsCompleted() && $this->isDrafted()) {
            $this->setStatus(Status::COMPLETE);
            return true;
        }

        return false;
    }

    /**
     * @return array
     */
    public function getContents(): array
    {
        return $this->contents;
    }

    /**
     * @param array $contents
     * @return $this
     */
    public function setContents(array $contents): self
    {
        $this->contents = $contents;
        return $this;
    }

    public function jsonSerialize(): array
    {
        return get_object_vars($this);
    }
}
