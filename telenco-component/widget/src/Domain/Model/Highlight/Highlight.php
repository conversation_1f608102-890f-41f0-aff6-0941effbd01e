<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Domain\Model\Highlight;

use Telenco\Component\Widget\Domain\Status\StatusFieldTrait;
use Telenco\Component\Widget\Domain\Traits\SlotNumberTrait;

class Highlight
{
    use StatusFieldTrait;
    use SlotNumberTrait;

    public const LARGE_TYPE = 1;
    public const ONLY_IMAGE_TYPE = 2;

    public const LARGE_TYPE_LABEL = 'large';
    public const ONLY_IMAGE_TYPE_LABEL = 'only_image';

    public const TYPES = [
        null,
        self::LARGE_TYPE => self::LARGE_TYPE_LABEL,
        self::ONLY_IMAGE_TYPE => self::ONLY_IMAGE_TYPE_LABEL
    ];

    private ?int $id = null;

    private int $type;

    private ?string $title = null;

    private ?string $description = null;

    private ?string $buttonLabel = null;

    private string $image;

    private string $url;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): self
    {
        $this->title = $title;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;
        return $this;
    }

    public function getButtonLabel(): ?string
    {
        return $this->buttonLabel;
    }

    public function setButtonLabel(?string $buttonLabel): self
    {
        $this->buttonLabel = $buttonLabel;
        return $this;
    }

    public function getImage(): string
    {
        return $this->image;
    }

    public function setImage(string $image): self
    {
        $this->image = $image;
        return $this;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function setUrl(string $url): self
    {
        $this->url = $url;
        return $this;
    }

    public function getType(): int
    {
        return $this->type;
    }

    public function setType(int $type): self
    {
        $this->type = $type;
        return $this;
    }
}
