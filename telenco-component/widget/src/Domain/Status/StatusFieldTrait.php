<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Domain\Status;

use ReflectionClass;

/**
 * @deprecated Use the Status class of the shared component @see Telenco\Component\Shared\Domain\Status\StatusFieldTrait
 */
trait StatusFieldTrait
{
    /**
     * @see Status
     */
    private string $status;

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $reflectionClass = new ReflectionClass(Status::class);
        $statusSupported = $reflectionClass->getConstants();
        $statusOfConstant = $reflectionClass->getConstant(strtoupper($status));

        if ($statusOfConstant === false) {
            throw new \Exception(
                sprintf("The %s status is not supported in status list: %s.", $status, join(", ", $statusSupported))
            );
        }
        $this->status = $statusOfConstant;

        return $this;
    }

    public function isCompleted(): bool
    {
        return $this->status === Status::COMPLETE;
    }

    public function isDrafted(): bool
    {
        return $this->status === Status::INCOMPLETE;
    }

    public function isPublished(): bool
    {
        return $this->status === Status::PUBLISHED;
    }

    public function isDeleted(): bool
    {
        return $this->status === Status::DELETED;
    }
}
