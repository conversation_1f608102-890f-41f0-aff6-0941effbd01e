<?php

namespace Telenco\Component\Widget\Presentation\ViewModel;

use Marketplace\Component\CleanArchiCore\Presentation\ViewModel\AbstractViewModel;
use Symfony\Component\Form\FormView;
use Symfony\Contracts\Translation\TranslatorInterface;
use Telenco\Component\Widget\Domain\Model\Region;

class OperatorCarouselEditViewModel extends AbstractViewModel
{
    public bool $error = false;

    public ?FormView $form = null;

    public array $formErrors = [];

    public ?Region $region = null;

    public bool $submit = false;

    public bool $notFound = false;

    public array $discountOffers = [];

    public array $newsOffers = [];

    public array $bestSellsOffers = [];


    public function __construct(private TranslatorInterface $translator)
    {
    }

    public function addFormErrors(?string $field, string $message): void
    {
        $this->formErrors[] = ['field' => $field, 'message' => $message];
    }

    public function headerTitle(): string
    {
        return $this->translator->trans(
            'widget.carousel.header_title.region',
            ['%region%' => $this->region?->getName()],
            'translations'
        );
    }

    public function pageTitle(): string
    {
        return $this->translator->trans(
            'widget.carousel.page_title.region',
            ['%region%' => $this->region?->getName()],
            'translations'
        );
    }
}
