<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Presentation\View;

use Marketplace\Component\CleanArchiCore\Utils\Flash\FlashMessageInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Telenco\Component\Widget\Presentation\ViewModel\OperatorHighlightEditViewModel;
use Telenco\Component\Widget\Presentation\ViewModel\OperatorSoftHighlightEditViewModel;
use Twig\Environment;

class OperatorSoftHighlightEditView
{
    public function __construct(
        private Environment $twig,
        private FlashMessageInterface $flashMessage,
        private TranslatorInterface $translator,
        private UrlGeneratorInterface $urlGenerator,
    ) {
    }

    public function generateView(OperatorSoftHighlightEditViewModel $viewModel): Response
    {
        if ($viewModel->notFound === true) {
            $this->flashMessage->add(
                'error',
                $this->translator->trans('soft_highlight.message.notFound', domain: 'translations')
            );
            return new RedirectResponse($this->urlGenerator->generate('home.widgets'));
        } elseif ($viewModel->error === false && $viewModel->submit === true) {
            $this->flashMessage->add(
                'success',
                $this->translator->trans('soft_highlight.message.success', domain: 'translations')
            );
            return new RedirectResponse($this->urlGenerator->generate('home.widgets'));
        }
        return new Response($this->twig->render('widget/edit-soft-highlight.html.twig', compact('viewModel')));
    }
}
