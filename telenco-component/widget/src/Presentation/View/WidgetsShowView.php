<?php

namespace Telenco\Component\Widget\Presentation\View;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Telenco\Component\Widget\Presentation\ViewModel\WidgetsShowViewModel;
use Twig\Environment;
use Symfony\Component\HttpFoundation\Response;

class WidgetsShowView
{
    private static string $renderPage = 'widget/home-widgets.html.twig';
    public function __construct(
        private SerializerInterface $serializer
    ) {
    }

    public function generateView(WidgetsShowViewModel $viewModel): JsonResponse
    {
        return JsonResponse::fromJsonString($this->serializer->serialize(
            $viewModel,
            'json'
        ));
    }
}
