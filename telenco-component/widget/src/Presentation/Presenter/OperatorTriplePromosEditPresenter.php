<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Presentation\Presenter;

use Marketplace\Component\CleanArchiCore\Utils\Str;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\Presenter\OperatorTriplePromosEditPresenterInterface;
use Telenco\Component\Widget\Domain\UseCase\OperatorTriplePromosEdit\DTO\OperatorTriplePromosEditResponse;
use Telenco\Component\Widget\Presentation\ViewModel\OperatorTriplePromosEditViewModel;

final class OperatorTriplePromosEditPresenter implements OperatorTriplePromosEditPresenterInterface
{
    private OperatorTriplePromosEditViewModel $viewModel;

    public function __construct(private TranslatorInterface $translator)
    {
        $this->viewModel = new OperatorTriplePromosEditViewModel($this->translator);
    }
    public function present(OperatorTriplePromosEditResponse $response): void
    {
        $this->viewModel->error = $response->getNotification()->hasError();
        $this->viewModel->submit = true;
        $this->viewModel->notFound = !$response->getWidget() instanceof Widget;

        foreach ($response->getNotification()->getErrors() as $error) {
            $this->viewModel->addFormErrors(
                $error->getFieldName(),
                $this->translator->trans($error->getTranslationKey(), domain: 'translations')
            );
        }
    }

    public function viewModel(FormInterface $form): OperatorTriplePromosEditViewModel
    {
        if ($this->viewModel->submit === true && $this->viewModel->error === true) {
            foreach ($this->viewModel->formErrors as $error) {
                if (str_contains($error['field'], '.')) {
                    $locale = Str::extractLeftPart(
                        Str::lastRightPart($error['field'], '.', 2),
                        '.'
                    );
                    $field = Str::lastRightPart($error['field'], '.');
                    $form->get('contents')->get($locale)->get($field)
                        ->addError(new FormError($error['message']));
                    continue;
                }
                $form->get($error['field'])->addError(new FormError($error['message']));
            }
        }

        $this->viewModel->form = $form->createView();
        return $this->viewModel;
    }
}
