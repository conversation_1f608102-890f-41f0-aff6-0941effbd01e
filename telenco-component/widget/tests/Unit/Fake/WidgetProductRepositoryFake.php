<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Tests\Unit\Fake;

use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\Model\WidgetContent\Product;
use Telenco\Component\Widget\Domain\Model\WidgetContent\WidgetContent;
use Telenco\Component\Widget\Domain\Port\Repository\WidgetProductRepositoryInterface;
use Telenco\Component\Widget\Domain\Status\Status;

class WidgetProductRepositoryFake implements WidgetProductRepositoryInterface
{
    public function findWidgetContentByRegionId(int $regionId): ?WidgetContent
    {
        if ($regionId === 2) {
            return null;
        }

        return (new Product())
            ->setId(1)
            ->setRegion((new Region())->setId($regionId)->setName('Region+1'))
            ->setWidget(
                (new Widget())->setName('widget+1')->setType(Widget::TYPE_PRODUCT_CAROUSEL)->setStatus(Status::COMPLETE)
            )
            ->setTitle('Top brand content')
            ->setLocale(Locale::create('FR'))
            ->setLink(['website' => 'https://website.com'])
            ->setOffersId([1,2,3])
        ;
    }

    public function save(WidgetContent $widgetContent): void
    {
        if ($widgetContent->getTitle() !== '') {
            $widgetContent->setTitle('Save title');
        }
    }

    public function update(WidgetContent $widgetContent): void
    {
        if ($widgetContent->getTitle() !== '') {
            $widgetContent->setTitle('Update title');
        }
    }

    public function findByLocale(string $locale): ?WidgetContent
    {
        return null;
    }

    public function getContent(int $regionId, int $widgetId): ?WidgetContent
    {
        if ($regionId !== 5) {
            return null;
        }

        return (new Product())
            ->setId(1)
            ->setRegion((new Region())->setId($regionId)->setName('Region+1'))
            ->setWidget((new Widget())->setId(33)->setName('widget+1')
                ->setType(Widget::TYPE_PRODUCT_CAROUSEL)
                ->setStatus(Status::COMPLETE))
            ->setTitle('Top brand content')
            ->setLocale(Locale::create('FR'))
            ->setLink(['website' => 'https://website.com'])
            ->setOffersId([1,2,3])
        ;
    }

    public function getById(int $contentId): ?WidgetContent
    {
        if ($contentId !== 5) {
            return null;
        }

        return (new Product())
            ->setId(1)
            ->setRegion((new Region())->setId(2)->setName('Region+1'))
            ->setWidget((new Widget())->setId(33)->setName('widget+1')
                ->setType(Widget::TYPE_PRODUCT_CAROUSEL)
                ->setStatus(Status::COMPLETE))
            ->setTitle('Top brand content')
            ->setLocale(Locale::create('FR'))
            ->setLink(['website' => 'https://website.com'])
            ->setOffersId([1,2,3])
        ;
    }

    public function getContents(int $regionId, int $widgetId): array
    {
        return [];
    }
}
