<?php

namespace Telenco\Component\Widget\Tests\Unit\Domain\UseCase\OperatorWidgetCreation;

use InvalidArgumentException;
use Marketplace\Component\CleanArchiCore\Domain\Error\Error;
use PHPUnit\Framework\TestCase;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\Presenter\OperatorWidgetCreationPresenterInterface;
use Telenco\Component\Widget\Domain\UseCase\OperatorWidgetCreation\DTO\OperatorWidgetCreationRequest;
use Telenco\Component\Widget\Domain\UseCase\OperatorWidgetCreation\DTO\OperatorWidgetCreationResponse;
use Telenco\Component\Widget\Domain\UseCase\OperatorWidgetCreation\OperatorWidgetCreationUseCase;
use Telenco\Component\Widget\Tests\Unit\Fake\WidgetRepositoryFake;

class OperatorWidgetCreationTest extends TestCase
{
    private OperatorWidgetCreationUseCase $useCase;

    private OperatorWidgetCreationPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements OperatorWidgetCreationPresenterInterface {
            public OperatorWidgetCreationResponse $response;

            public function present(OperatorWidgetCreationResponse $response): void
            {
                $this->response = $response;
            }
        };

        $this->useCase = new OperatorWidgetCreationUseCase(
            new WidgetRepositoryFake()
        );
    }

    /**
     * @param OperatorWidgetCreationRequest $request
     * @param OperatorWidgetCreationResponse $expected
     * @dataProvider provideExecute
     */
    public function testExecute(OperatorWidgetCreationRequest $request, OperatorWidgetCreationResponse $expected)
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($expected, $this->presenter->response);
    }

    public function provideExecute()
    {
        //widgetCreationSuccessful
        $request = new OperatorWidgetCreationRequest('widget A', Widget::TYPE_HIGHLIGHT);
        $expected = new OperatorWidgetCreationResponse();
        $widget = new Widget();
        $widget->setName('widget A');
        $widget->setId(1);
        $widget->setType(Widget::TYPE_HIGHLIGHT);
        $widget->setStatus('incomplete');
        $expected->setWidget($widget);
        yield [$request, $expected];

        //invalid name
        $request = new OperatorWidgetCreationRequest('', Widget::TYPE_HIGHLIGHT);
        $expected = new OperatorWidgetCreationResponse();
        $expected->getNotification()->addError('name', 'The widget name is invalid', null);
        yield [$request, $expected];

        //invalid Type
        $request = new OperatorWidgetCreationRequest('widget B', null);
        $expected = new OperatorWidgetCreationResponse();
        $expected->getNotification()->addError('type', 'The widget type is invalid', null);
        yield [$request, $expected];

        //existing name
        $request = new OperatorWidgetCreationRequest('existing widget foo', Widget::TYPE_HIGHLIGHT);
        $expected = new OperatorWidgetCreationResponse();
        $widget = new Widget();
        $widget->setName('existing widget foo');
        $widget->setId(1);
        $widget->setType(Widget::TYPE_HIGHLIGHT);
        $widget->setStatus('incomplete');
        $expected->setWidget($widget);
        yield [$request, $expected];

        //name exceed 50 characters
        $request = new OperatorWidgetCreationRequest('Jesuisunnomdewidgetquifaitplusdecinquantecaracteres', Widget::TYPE_HIGHLIGHT);
        $expected = new OperatorWidgetCreationResponse();
        $expected->getNotification()->addError('name', 'Widget name must not exceed 50 characters');
        yield [$request, $expected];
    }
}
