<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Tests\Unit\Domain\UseCase;

use Generator;
use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\Model\WidgetContent\Carousel;
use Telenco\Component\Widget\Domain\Model\WidgetContent\WidgetContent;
use Telenco\Component\Widget\Domain\Port\Repository\RegionRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Repository\WidgetCarouselRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Repository\WidgetRepositoryInterface;
use Telenco\Component\Widget\Domain\Presenter\OperatorCarouselEditPresenterInterface;
use Telenco\Component\Widget\Domain\Status\Status;
use Telenco\Component\Widget\Domain\UseCase\OperatorCarouselEdit\DTO\OperatorCarouselContentRequest;
use Telenco\Component\Widget\Domain\UseCase\OperatorCarouselEdit\DTO\OperatorCarouselEditRequest;
use Telenco\Component\Widget\Domain\UseCase\OperatorCarouselEdit\DTO\OperatorCarouselEditResponse;
use Telenco\Component\Widget\Domain\UseCase\OperatorCarouselEdit\OperatorCarouselEditUseCase;

final class OperatorCarouselEditUseCaseTest extends TestCase
{
    private OperatorCarouselEditUseCase $useCase;

    private OperatorCarouselEditPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements OperatorCarouselEditPresenterInterface {
            public OperatorCarouselEditResponse $response;
            public function present(OperatorCarouselEditResponse $response): void
            {
                $this->response = $response;
            }
        };

        $this->useCase = new OperatorCarouselEditUseCase(
            regionRepository: $this->makeRegionRepository(),
            widgetRepository: $this->makeWidgetRepository(),
            widgetCarouselRepository: $this->makeWidgetCarouselRepo()
        );
    }

    public function testIfWidgetContentIsSuccessfullyCompletedForActivated(): void
    {
        $request = new OperatorCarouselEditRequest(regionId: 2, widgetId: 22);
        $request->region = new Region();
        $request->contents = [$this->makeSimpleCarouselModel()];

        $this->useCase->execute($request, $this->presenter);
        $this->assertContainsOnlyInstancesOf(
            Carousel::class,
            $this->presenter->response->getContentsCarousel()
        );

        $this->assertEquals([[7,8,9]], array_map(
            fn(Carousel $carousel) => $carousel->getBestSellsOfferId(),
            $this->presenter->response->getContentsCarousel()
        ));
    }

    /**
     * @dataProvider carouselEditDataProvider
     */
    public function testExecute(
        OperatorCarouselEditRequest $request,
        OperatorCarouselEditResponse $expected
    ): void {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($expected, $this->presenter->response);
    }

    public function carouselEditDataProvider(): Generator
    {
        // Given an operator want created a new carousel widget #0
        $response = new OperatorCarouselEditResponse();
        $widget = (new Widget())
            ->setId(33)
            ->setName('Widget 1')
            ->setType(Widget::TYPE_CAROUSEL)
            ->setStatus(Status::COMPLETE);
        $content = (new Carousel())
            ->setRegion((new Region())->setId(2)->setName('region+1'))
            ->setWidget($widget)
            ->setLocale(Locale::create('FR'))
            ->setDiscountOfferId([1,2,3])
            ->setNewsOfferId([4,5,6])
            ->setBestSellsOfferId([7,8,9])
        ;

        $request = OperatorCarouselEditRequest::create(
            2,
            33,
            [$this->makeSimpleCarouselModel()],
            new Region()
        );

        $widget->setContents([$content]);
        $response->setWidget($widget);
        $response->setContentsCarousel([$content]);
        yield 'Create new carousel widget' => [$request, $response];

        // Given an operator want update a carousel widget #1
        $response = new OperatorCarouselEditResponse();
        $widget = (new Widget())
            ->setId(33)
            ->setName('Widget 1')
            ->setType(Widget::TYPE_CAROUSEL)
            ->setStatus(Status::COMPLETE);
        $content = (new Carousel())
            ->setId(5)
            ->setRegion((new Region())->setId(5)->setName('region+1'))
            ->setWidget($widget)
            ->setLocale(Locale::create('FR'))
            ->setDiscountOfferId([1,2,3])
            ->setNewsOfferId([4,5,6])
            ->setBestSellsOfferId([7,8,9]);
        ;

        $request = OperatorCarouselEditRequest::create(
            5,
            33,
            [$this->makeSimpleCarouselModel(id: 5)],
            new Region()
        );

        $widget->setContents([$content]);
        $response->setWidget($widget);
        $response->setContentsCarousel([$content]);
        yield 'Update a product widget' => [$request, $response];

        // Given an operator want a created a new product widget but the region does not exist #2
        $request = OperatorCarouselEditRequest::create(
            1,
            33,
            [$this->makeSimpleCarouselModel()],
            new Region()
        );

        $response = new OperatorCarouselEditResponse();
        $response->getNotification()->addError('contents', 'widget.invalid');
        yield 'region does not exist' => [$request, $response];

        // Given an operator want a created a new product widget but the widget does not exist #3
        $request = OperatorCarouselEditRequest::create(
            5,
            0,
            [$this->makeSimpleCarouselModel()],
            new Region()
        );

        $response = new OperatorCarouselEditResponse();
        $response->getNotification()->addError('contents', 'widget.invalid');
        yield 'the widget does not exist' => [$request, $response];
    }

    private function makeSimpleCarouselModel(
        ?int $id = null,
        ?string $locale = null,
        ?string $title = 'simple widget',
        array $discountOfferId = [1,2,3],
        array $newsOfferId = [4,5,6],
        array $bestSellOfferId = [7,8,9]
    ) {
        return new OperatorCarouselContentRequest(
            locale: $locale ?? 'FR',
            id: $id,
            discountOfferId: $discountOfferId,
            newsOfferId: $newsOfferId,
            bestSellsOfferId: $bestSellOfferId
        );
    }

    private function makeRegionRepository(): RegionRepositoryInterface
    {
        $prophecy = new Prophet();
        /** @var RegionRepositoryInterface|ObjectProphecy $regionService */
        $regionRepo = $prophecy->prophesize(RegionRepositoryInterface::class);
        $regionRepo->getRegionById(Argument::type('integer'))->will(function (array $args) {
            $regionId = $args[0];
            if ($regionId === 1) {
                return null;
            }
            return (new Region())->setName('region+1')->setId($regionId);
        });
        return $regionRepo->reveal();
    }

    private function makeWidgetRepository(): WidgetRepositoryInterface
    {
        $prophecy = new Prophet();
        /** @var WidgetRepositoryInterface|ObjectProphecy $regionService */
        $widgetRepo = $prophecy->prophesize(WidgetRepositoryInterface::class);

        $widgetRepo->save(Argument::type(Widget::class))->will(function (array $args) {
            $widget = $args[0];
            if ($widget->getId() === null) {
                $widget->setId(1);
            }
            return true;
        });

        $widgetRepo->findByName(Argument::type('string'))->will(function (array $args) {

            return null;
        });

        $widgetRepo->findById(Argument::type('int'))->will(function (array $args) {
            $id = $args[0];
            if ($id < 1) {
                return null;
            }

            if ($id === 22) {
                return (new Widget())->setId(22)->setName('Widget 1')->setStatus(Status::INCOMPLETE)->setType('highlight');
            }

            if ($id === 33) {
                return (new Widget())
                    ->setId(33)
                    ->setName('Widget 1')
                    ->setType(Widget::TYPE_CAROUSEL)
                    ->setStatus(Status::COMPLETE);
            }

            return (new Widget())->setId($id)->setName('Widget 1')->setStatus(Status::COMPLETE);
        });

        return $widgetRepo->reveal();
    }

    private function makeWidgetCarouselRepo(): WidgetCarouselRepositoryInterface
    {
        $prophecy = new Prophet();
        /** @var WidgetCarouselRepositoryInterface|ObjectProphecy $regionService */
        $repo = $prophecy->prophesize(WidgetCarouselRepositoryInterface::class);

        $repo->getById(Argument::type('integer'))->will(function (array $args) {
            $id = $args[0];

            return (new Carousel())
                ->setId($id)
                ->setRegion((new Region())->setId(1)->setName('Region+1'))
                ->setWidget(
                    (new Widget())->setName('widget+1')->setType(Widget::TYPE_CAROUSEL)->setStatus(Status::COMPLETE)
                )
                ->setLocale(Locale::create('FR'))
                ->setDiscountOfferId([1,2,3])
                ->setNewsOfferId([4,5,6])
                ->setBestSellsOfferId([7,8,9])
                ;
        });

        $repo->save(Argument::type(WidgetContent::class))->will(function (array $args) {
        });

        $repo->update(Argument::type(WidgetContent::class))->will(function (array $args) {
        });

        return $repo->reveal();
    }


    public function save(WidgetContent $widgetContent): void
    {
    }

    public function update(WidgetContent $widgetContent): void
    {
    }
}
