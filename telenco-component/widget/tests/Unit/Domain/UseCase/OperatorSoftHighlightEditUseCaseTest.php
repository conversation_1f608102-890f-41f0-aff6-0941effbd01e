<?php

namespace Telenco\Component\Widget\Domain\UseCase\OperatorSoftHighlightEdit;

use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;
use SplFileInfo;
use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\Model\WidgetContent\SoftHighlight;
use Telenco\Component\Widget\Domain\Port\Repository\RegionRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Repository\SoftHighlightRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Repository\WidgetRepositoryInterface;
use Telenco\Component\Widget\Domain\Port\Service\UploaderServiceInterface;
use Telenco\Component\Widget\Domain\Presenter\OperatorSoftHighlightEditPresenterInterface;
use Telenco\Component\Widget\Domain\Status\Status;
use Telenco\Component\Widget\Domain\UseCase\OperatorSoftHighlightEdit\DTO\OperatorSoftHighlightEditRequest;
use Telenco\Component\Widget\Domain\UseCase\OperatorSoftHighlightEdit\DTO\OperatorSoftHighlightEditResponse;
use Telenco\Component\Widget\Domain\UseCase\OperatorSoftHighlightEdit\DTO\SoftHighlightContentRequest;
use Telenco\Component\Widget\Exception\HighlightNotFoundException;

class OperatorSoftHighlightEditUseCaseTest extends TestCase
{
    private OperatorSoftHighlightEditUseCase $useCase;

    private OperatorSoftHighlightEditPresenterInterface $presenter;
    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements OperatorSoftHighlightEditPresenterInterface {
            public OperatorSoftHighlightEditResponse $response;

            public function present(OperatorSoftHighlightEditResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        $highlightRepository = $prophet->prophesize(SoftHighlightRepositoryInterface::class);
        $highlightRepository->getById(Argument::type('int'))->will(function ($args) {
            $contentId = $args[0];
            if ($contentId === 5) {
                return null;
            }
            $highlight = (new SoftHighlight())->setId($contentId);
            if ($contentId === 2) {
                $highlight->setImage('toto');
            }
            return $highlight;
        });
        $regionRepository =  $prophet->prophesize(RegionRepositoryInterface::class);
        $regionRepository->getRegionById(Argument::type('int'))->will(function ($args) {
            $regionId = $args[0];
            if ($regionId === 0) {
                return null;
            }
            return new Region();
        });
        $uploaderService = $prophet->prophesize(UploaderServiceInterface::class);
        $uploaderService->save(Argument::type(SplFileInfo::class))->willReturn('path');

        $widgetRepository = $prophet->prophesize(WidgetRepositoryInterface::class);
        $widgetRepository->findById(Argument::type('int'))->will(function ($args) {
            $widgetId = $args[0];
            if ($widgetId === 0) {
                return null;
            }
            return (new Widget())->setStatus(Status::INCOMPLETE);
        });
        $widgetRepository->save(Argument::type(Widget::class))->willReturn(true);
        $this->useCase = new OperatorSoftHighlightEditUseCase(
            $highlightRepository->reveal(),
            $regionRepository->reveal(),
            $uploaderService->reveal(),
            $widgetRepository->reveal(),
        );
    }

    /**
     * @param OperatorSoftHighlightEditRequest $request
     * @param OperatorSoftHighlightEditResponse $response
     * @dataProvider provideExecute
     */
    public function testExecute(OperatorSoftHighlightEditRequest $request, OperatorSoftHighlightEditResponse $response)
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($response, $this->presenter->response);
    }

    public function testExecuteException()
    {
        $request = new OperatorSoftHighlightEditRequest(1, 1);
        $request->contents = [new SoftHighlightContentRequest(5, 'FR', 'toto')];
        $this->expectException(HighlightNotFoundException::class);
        $this->useCase->execute($request, $this->presenter);
    }

    public function provideExecute()
    {
        //given : I want to create a soft highlight widget with unexisting region
        $request = new OperatorSoftHighlightEditRequest(0, 0);
        //then : I've got an error
        $response = new OperatorSoftHighlightEditResponse();
        $response->getNotification()->addError('contents', 'widget.invalid');
        yield "wrong region " => [$request, $response];

        //given : I want to create a soft highlight widget with invalid request
        $request = new OperatorSoftHighlightEditRequest(1, 1);
        $request->contents = [new SoftHighlightContentRequest(1, 'FR', null)];
        //then : I've got an error
        $response = new OperatorSoftHighlightEditResponse();
        $response->getNotification()
            ->addError("link", "highlight.validate.required");
        yield "invalid request " => [$request, $response];

        //given : I want to create a soft highlight widget with no image
        $request = new OperatorSoftHighlightEditRequest(1, 1);
        $request->contents = [new SoftHighlightContentRequest(1, 'FR', 'toto')];
        //then : I've got an error
        $response = new OperatorSoftHighlightEditResponse();
        $response->getNotification()->addError('image', 'highlight.validate.required');
        yield "no image " => [$request, $response];
        //given : I want to create a soft highlight widget with no image
        $request = new OperatorSoftHighlightEditRequest(1, 1, new SplFileInfo('toto'));
        $request->contents = [new SoftHighlightContentRequest(1, 'FR', 'toto')];
        //then : I've got a widget and the contents
        $response = new OperatorSoftHighlightEditResponse();
        $widget = new Widget();
        $content = new SoftHighlight();
        $content->setId(1);
        $content->setImage('path');
        $content->setLink('toto');
        $content->setWidget($widget);
        $content->setRegion(new Region());
        $content->setLocale(Locale::create('FR'));
        $response->setWidget($widget);
        $widget->setContents([$content]);
        $widget->setStatus(Status::COMPLETE);
        $response->setContentsSoftHighlight([$content]);
        yield "save" => [$request, $response];
        //given : I want to create a soft highlight widget with no image
        $request = new OperatorSoftHighlightEditRequest(1, 1, new SplFileInfo('toto'));
        $request->contents = [new SoftHighlightContentRequest(null, 'FR', 'toto')];
        //then : I've got a widget and the contents
        $response = new OperatorSoftHighlightEditResponse();
        $widget = new Widget();
        $content = new SoftHighlight();
        $content->setImage('path');
        $content->setLink('toto');
        $content->setWidget($widget);
        $content->setRegion(new Region());
        $content->setLocale(Locale::create('FR'));
        $response->setWidget($widget);
        $widget->setContents([$content]);
        $widget->setStatus(Status::COMPLETE);
        $response->setWidget($widget);
        $response->setContentsSoftHighlight([$content]);
        yield "create" => [$request, $response];
    }
}
