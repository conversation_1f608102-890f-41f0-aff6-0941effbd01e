<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Tests\Unit\Domain\UseCase\OperatorWidgetCreation;

use Generator;
use PHPUnit\Framework\TestCase;
use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\Model\WidgetContent\Product;
use Telenco\Component\Widget\Domain\Presenter\WidgetSimpleProductEditPresenterInterface;
use Telenco\Component\Widget\Domain\Status\Status;
use Telenco\Component\Widget\Domain\UseCase\WidgetSimpleProductEdit\DTO\WidgetSimpleProductContentRequest;
use Telenco\Component\Widget\Domain\UseCase\WidgetSimpleProductEdit\DTO\WidgetSimpleProductEditRequest;
use Telenco\Component\Widget\Domain\UseCase\WidgetSimpleProductEdit\DTO\WidgetSimpleProductEditResponse;
use Telenco\Component\Widget\Domain\UseCase\WidgetSimpleProductEdit\WidgetSimpleProductEditUseCase;
use Telenco\Component\Widget\Tests\Unit\Fake\RegionRepository;
use Telenco\Component\Widget\Tests\Unit\Fake\WidgetProductRepositoryFake;
use Telenco\Component\Widget\Tests\Unit\Fake\WidgetRepositoryFake;

final class WidgetSimpleProductEditUseCaseTest extends TestCase
{
    private WidgetSimpleProductEditUseCase $useCase;

    private WidgetSimpleProductEditPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements WidgetSimpleProductEditPresenterInterface {
            public WidgetSimpleProductEditResponse $response;

            public function present(WidgetSimpleProductEditResponse $response): void
            {
                $this->response = $response;
            }
        };

        $this->useCase = new WidgetSimpleProductEditUseCase(
            regionRepository: new RegionRepository(),
            widgetRepository: new WidgetRepositoryFake(),
            widgetProductRepository: new WidgetProductRepositoryFake()
        );
    }

    public function testIfWidgetContentIsSuccessfullyCompletedForActivated(): void
    {
        $request = new WidgetSimpleProductEditRequest(regionId: 2, widgetId: 22);
        $request->type = Product::SIMPLE_TYPE;
        $request->region = new Region();
        $request->contents = [$this->makeSimpleProductModel()];

        $this->useCase->execute($request, $this->presenter);
        $this->assertContainsOnlyInstancesOf(
            Product::class,
            $this->presenter->response->getContentsSimpleProduct()
        );

        $this->assertEquals(['Save title'], array_map(
            fn(Product $product) => $product->getTitle(),
            $this->presenter->response->getContentsSimpleProduct()
        ));
    }

    /**
     * @dataProvider productEditDataProvider
     */
    public function testExecute(
        WidgetSimpleProductEditRequest $request,
        WidgetSimpleProductEditResponse $expected
    ): void {
        $this->useCase->execute($request, $this->presenter);

        $this->assertEquals($expected, $this->presenter->response);
    }

    public function productEditDataProvider(): Generator
    {
        // Given an operator want created a new product widget #0
        $response = new WidgetSimpleProductEditResponse();
        $widget = (new Widget())
            ->setId(33)
            ->setName('Widget 1')
            ->setType(Widget::TYPE_PRODUCT_CAROUSEL)
            ->setStatus(Status::COMPLETE);
        $content = (new Product())
            ->setRegion((new Region())->setId(2)->setName('region+1'))
            ->setWidget($widget)
            ->setTitle('Save title')
            ->setLocale(Locale::create('FR'))
            ->setLink(['website' => 'https://website.com'])
            ->setType(Product::SIMPLE_TYPE)
            ->setOffersId([1,2,3])
        ;

        $request = WidgetSimpleProductEditRequest::create(
            2,
            33,
            [$this->makeSimpleProductModel()],
            Product::SIMPLE_TYPE,
            new Region()
        );

        $widget->setContents([$content]);
        $response->setWidget($widget);
        $response->setContentsSimpleProduct([$content]);
        yield 'Create new product widget' => [$request, $response];

        // Given an operator want update a product widget #1
        $response = new WidgetSimpleProductEditResponse();
        $widget = (new Widget())
            ->setId(33)
            ->setName('Widget 1')
            ->setType('product_carousel')
            ->setStatus(Status::COMPLETE);
        $content = (new Product())
            ->setId(1)
            ->setRegion((new Region())->setId(5)->setName('region+1'))
            ->setWidget($widget)
            ->setTitle('Update title')
            ->setLocale(Locale::create('FR'))
            ->setLink(['website' => 'https://website.com'])
            ->setType(Product::SIMPLE_TYPE)
            ->setOffersId([1,2,3])
        ;

        $request = WidgetSimpleProductEditRequest::create(
            5,
            33,
            [$this->makeSimpleProductModel(id: 5)],
            Product::SIMPLE_TYPE,
            new Region()
        );

        $widget->setContents([$content]);
        $response->setWidget($widget);
        $response->setContentsSimpleProduct([$content]);
        yield 'Update a product widget' => [$request, $response];

        // Given an operator want a created a new product widget but the region does not exist #2
        $request = WidgetSimpleProductEditRequest::create(
            1,
            33,
            [$this->makeSimpleProductModel()],
            Product::SIMPLE_TYPE,
            new Region()
        );

        $response = new WidgetSimpleProductEditResponse();
        $response->getNotification()->addError('contents', 'widget.invalid');
        yield 'region does not exist' => [$request, $response];

        // Given an operator want a created a new product widget but the widget does not exist #3
        $request = WidgetSimpleProductEditRequest::create(
            5,
            0,
            [$this->makeSimpleProductModel()],
            Product::SIMPLE_TYPE,
            new Region()
        );

        $response = new WidgetSimpleProductEditResponse();
        $response->getNotification()->addError('contents', 'widget.invalid');
        yield 'the widget does not exist' => [$request, $response];

        // Given an operator want a created a new product widget with not valid widget type #6
        $request = WidgetSimpleProductEditRequest::create(
            5,
            3,
            [$this->makeSimpleProductModel()],
            'Autre',
            new Region()
        );

        $response = new WidgetSimpleProductEditResponse();
        $response->getNotification()->addError('type', 'widgetProduct.validate.type.invalid');
        yield 'not valid widget type' => [$request, $response];


        // Given an operator want a created a new product widget with a content of links array only integer values #7
        $request = WidgetSimpleProductEditRequest::create(
            5,
            33,
            [$this->makeSimpleProductModel(offerId: [1, '2', null, ''])],
            Product::SIMPLE_TYPE,
            new Region()
        );

        $response = new WidgetSimpleProductEditResponse();
        $response->getNotification()->addError('offersId', 'widgetProduct.validate.offersId.invalid');
        yield 'content of links array only integer values' => [$request, $response];


        // Given an operator want a created a new product widget with a content link is empty #8
        $request = WidgetSimpleProductEditRequest::create(
            5,
            33,
            [$this->makeSimpleProductModel(link: [])],
            'promotion',
            new Region()
        );

        $response = new WidgetSimpleProductEditResponse();
        $response->getNotification()->addError('link', 'widgetProduct.validate.required');
        yield 'content links is empty' => [$request, $response];


        // Given an operator want create a new product widget without title
        $request = WidgetSimpleProductEditRequest::create(
            5,
            33,
            [$this->makeSimpleProductModel(title: '')],
            'promotion',
            new Region()
        );

        $response = new WidgetSimpleProductEditResponse();
        $response->getNotification()->addError('title', 'widgetProduct.validate.required');
        yield 'product widget without title' => [$request, $response];
    }

    private function makeSimpleProductModel(
        ?int $id = null,
        ?string $locale = null,
        ?string $title = 'simple widget',
        array $link = ['website' => 'https://website.com'],
        array $offerId = [1,2,3],
    ) {
        return new WidgetSimpleProductContentRequest(
            locale: $locale ?? 'FR',
            id: $id,
            title: $title,
            link: $link,
            offerId: $offerId
        );
    }
}
