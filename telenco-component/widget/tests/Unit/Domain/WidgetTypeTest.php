<?php

declare(strict_types=1);

namespace Telenco\Component\Widget\Tests\Domain\WidgetType;

use Generator;
use PHPUnit\Framework\TestCase;
use Telenco\Component\Widget\Domain\Exception\WidgetTypeNotSupportedException;
use Telenco\Component\Widget\Domain\UseCase\OperatorHighlightEdit\DTO\OperatorHighlightEditRequest;
use Telenco\Component\Widget\Domain\UseCase\OperatorTopBrandEdit\DTO\OperatorTopBrandEditRequest;
use Telenco\Component\Widget\Domain\ValueObject\WidgetType;

final class WidgetTypeTest extends TestCase
{
    /**
     * @dataProvider widgetTypeDataProvider
     * @param string $type
     */
    public function testCreateWidgetTypeObjectSuccessful(string $type): void
    {
        $widgetType = WidgetType::create($type);

        $this->assertEquals($type, $widgetType->getType());
    }

    public function testCreateFailWidgetTypeObject(): void
    {
        $this->expectException(WidgetTypeNotSupportedException::class);
        $this->expectExceptionMessage('This unknown widget type is not supported');

        WidgetType::create('unknown');
    }

    /**
     * @dataProvider widgetContentRequestProvider
     */
    public function testLoadWidgetEditRequestSuccessful(string $type, string $expected): void
    {
        $widgetType = WidgetType::create($type);

        $this->assertEquals($expected, $widgetType->loadWidgetContentEditRequest());
    }

    public function widgetTypeDataProvider(): Generator
    {
        yield ['highlight'];
        yield ['top_brand'];
        yield ['product_carousel'];
    }

    public function widgetContentRequestProvider(): Generator
    {
        yield ['highlight', OperatorHighlightEditRequest::class];
        yield ['top_brand', OperatorTopBrandEditRequest::class];
    }
}
