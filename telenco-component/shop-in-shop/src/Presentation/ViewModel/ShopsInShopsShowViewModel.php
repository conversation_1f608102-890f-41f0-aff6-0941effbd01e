<?php

namespace Telenco\Component\ShopInShop\Presentation\ViewModel;

use Telenco\Component\ShopInShop\Domain\Model\ShopInShop;

class ShopsInShopsShowViewModel
{
    /**
     * ShopsInShopsShowViewModel constructor.
     * @param ShopInShop[] $shopsInShopsDraft
     * @param ShopInShop[] $shopsInShopsUsable
     * @param ShopInShop[] $shopsInShopsPublished
     */
    public function __construct(
        private readonly array $shopsInShopsDraft,
        private readonly array $shopsInShopsUsable,
        private readonly array $shopsInShopsPublished
    ) {
    }

    /**
     * @return ShopInShop[]
     */
    public function getShopsInShopsDraft(): array
    {
        return $this->shopsInShopsDraft;
    }

    /**
     * @return ShopInShop[]
     */
    public function getShopsInShopsUsable(): array
    {
        return $this->shopsInShopsUsable;
    }

    /**
     * @return ShopInShop[]
     */
    public function getShopsInShopsPublished(): array
    {
        return $this->shopsInShopsPublished;
    }
}
