<?php

declare(strict_types=1);

namespace Telenco\Component\ShopInShop\Presentation\ViewModel;

use Telenco\Component\ShopInShop\Domain\Model\ShopInShop;

final class DeleteShopInShopViewModel
{
    private ShopInShop $shopInShop;
    private string $status;

    /**
     * @return ShopInShop
     */
    public function getShopInShop(): ShopInShop
    {
        return $this->shopInShop;
    }

    /**
     * @param ShopInShop $shopInShop
     * @return DeleteShopInShopViewModel
     */
    public function setShopInShop(ShopInShop $shopInShop): self
    {
        $this->shopInShop = $shopInShop;
        return $this;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return DeleteShopInShopViewModel
     */
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }
}
