<?php

declare(strict_types=1);

namespace Telenco\Component\ShopInShop\Presentation\Presenter;

use Symfony\Contracts\Translation\TranslatorInterface;
use Telenco\Component\ShopInShop\Domain\Presenter\ShowListShopInShopApiPresenterInterface;
use Telenco\Component\ShopInShop\Domain\UseCase\ShowListShopInShop\DTO\ShowListShopInShopResponse;
use Telenco\Component\ShopInShop\Presentation\ViewModel\ShowListShopInShopApiViewModel;

class ShowListShopInShopApiPresenter implements ShowListShopInShopApiPresenterInterface
{
    private ShowListShopInShopApiViewModel $viewModel;

    public function __construct(private TranslatorInterface $translator)
    {
        $this->viewModel = new ShowListShopInShopApiViewModel($this->translator);
    }
    public function present(ShowListShopInShopResponse $response): void
    {
        $this->viewModel->setShopInShops($response->getShopInShops());
    }

    public function viewModel(): ShowListShopInShopApiViewModel
    {
        return $this->viewModel;
    }
}
