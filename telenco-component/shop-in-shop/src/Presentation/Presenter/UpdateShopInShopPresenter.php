<?php

namespace Telenco\Component\ShopInShop\Presentation\Presenter;

use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Telenco\Component\Shared\Domain\Model\Region;
use Telenco\Component\Shared\Domain\Port\Repository\RegionRepositoryInterface;
use Telenco\Component\ShopInShop\Domain\Presenter\UpdateShopInShopPresenterInterface;
use Telenco\Component\ShopInShop\Domain\UseCase\UpdateShopInShopUseCase\DTO\UpdateShopInShopResponse;
use Telenco\Component\ShopInShop\Presentation\ViewModel\UpdateShopInShopViewModel;

class UpdateShopInShopPresenter implements UpdateShopInShopPresenterInterface
{
    private UpdateShopInShopViewModel $viewModel;

    public function __construct(
        private TranslatorInterface $translator
    ) {
        $this->viewModel = new UpdateShopInShopViewModel($translator);
    }

    public function present(UpdateShopInShopResponse $response): void
    {
        $this->viewModel->error = $response->getNotification()->hasError();
        $this->viewModel->isSaved = $response->getShopInShop() !== null;

        foreach ($response->getNotification()->getErrors() as $error) {
            $this->viewModel->addFlashMessages('error', $this->translator->trans($error->getTranslationKey()));

            $this->viewModel->addFormErrors(
                $error->getFieldName(),
                $this->translator->trans($error->getTranslationKey())
            );
        }
    }

    /**
     * @param FormInterface $form
     * @return UpdateShopInShopViewModel
     */
    public function viewModel(FormInterface $form): UpdateShopInShopViewModel
    {
        if ($this->viewModel->error) {
            foreach ($this->viewModel->formErrors as $error) {
                $form->get($error['field'])->addError(new FormError($error['message']));
            }
        }

        $this->viewModel->form = $form->createView();
        return $this->viewModel;
    }
}
