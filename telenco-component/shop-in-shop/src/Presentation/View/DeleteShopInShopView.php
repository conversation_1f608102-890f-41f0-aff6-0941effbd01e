<?php

declare(strict_types=1);

namespace Telenco\Component\ShopInShop\Presentation\View;

use Marketplace\Component\CleanArchiCore\Presentation\View\AbstractView;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Telenco\Component\ShopInShop\Domain\UseCase\DeleteShopInShop\DTO\DeleteShopInShopResponse;
use Telenco\Component\ShopInShop\Presentation\ViewModel\DeleteShopInShopViewModel;

class DeleteShopInShopView extends AbstractView
{
    public function __construct(
        private TranslatorInterface $translator,
        private SerializerInterface $serializer
    ) {
    }

    public function generateView(DeleteShopInShopViewModel $viewModel): JsonResponse
    {
        $type = 'success';
        $message = $this->translator->trans(
            'shopinshop.delete.success',
            ['%name%' => $viewModel->getShopInShop()->getName()],
            'translations'
        );

        // Shop in shop not found
        if ($viewModel->getStatus() === DeleteShopInShopResponse::STATUS_NOT_FOUND) {
            $message = $this->translator->trans(
                'shopinshop.delete.not_found',
                ['%id%' => $viewModel->getShopInShop()->getId()],
                'translations'
            );
            $type = 'error';
        }

        // Widget delete unauthorized because published
        if ($viewModel->getStatus() === DeleteShopInShopResponse::STATUS_UNAUTHORIZED) {
            $message = $this->translator->trans(
                'shopinshop.delete.unauthorized',
                ['%name%' => $viewModel->getShopInShop()->getName()],
                'translations'
            );
            $type = 'error';
        }

        return JsonResponse::fromJsonString($this->serializer->serialize(
            ["type" => $type, "message" => $message],
            'json'
        ));
    }
}
