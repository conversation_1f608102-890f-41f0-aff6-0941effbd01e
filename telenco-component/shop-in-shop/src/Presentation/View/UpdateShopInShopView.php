<?php

namespace Telenco\Component\ShopInShop\Presentation\View;

use Marketplace\Component\CleanArchiCore\Utils\Flash\FlashMessageInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Telenco\Component\ShopInShop\Presentation\ViewModel\UpdateShopInShopViewModel;
use Twig\Environment;
use Symfony\Component\HttpFoundation\Response;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;

class UpdateShopInShopView
{
    public function __construct(
        private FlashMessageInterface $flashMessage,
        private UrlGeneratorInterface $urlGenerator,
        private Environment $twig,
    ) {
    }

    /**
     * @throws SyntaxError
     * @throws RuntimeError
     * @throws LoaderError
     */
    public function generateView(UpdateShopInShopViewModel $viewModel): Response
    {
        if ($viewModel->hasFlashMessage()) {
            foreach ($viewModel->flashMessages as ['type' => $type, 'message' => $message]) {
                $this->flashMessage->add($type, $message);
            }
        }

        if (!$viewModel->error && $viewModel->isSaved) {
            $this->flashMessage->add('success', 'shopinshop.update.success');
            return new RedirectResponse(
                $this->urlGenerator->generate('home.shops-in-shops')
            );
        }
        return new Response(
            $this->twig->render('shop-in-shop/update-shop-in-shop.html.twig', [
                'viewModel' => $viewModel
            ])
        );
    }
}
