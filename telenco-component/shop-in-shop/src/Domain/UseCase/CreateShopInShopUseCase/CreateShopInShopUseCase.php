<?php

declare(strict_types=1);

namespace Telenco\Component\ShopInShop\Domain\UseCase\CreateShopInShopUseCase;

use Exception;
use Telenco\Component\Shared\Domain\Status\Status;
use Telenco\Component\ShopInShop\Domain\Model\ShopInShop;
use Telenco\Component\ShopInShop\Domain\Port\Repository\ShopInShopRepositoryInterface;
use Telenco\Component\ShopInShop\Domain\Presenter\CreateShopInShopPresenterInterface;
use Telenco\Component\ShopInShop\Domain\UseCase\CreateShopInShopUseCase\DTO\CreateShopInShopRequest;
use Telenco\Component\ShopInShop\Domain\UseCase\CreateShopInShopUseCase\DTO\CreateShopInShopResponse;

final class CreateShopInShopUseCase
{
    public function __construct(private ShopInShopRepositoryInterface $shopInShopRepository)
    {
    }

    /**
     * @throws Exception
     */
    public function execute(
        CreateShopInShopRequest $request,
        CreateShopInShopPresenterInterface $presenter
    ): void {
        $response = new CreateShopInShopResponse();
        $isValid = $request->validate($response);
        $isValid = $isValid && $this->validateNameNotExist($request, $response);
        if ($isValid) {
            /** @var string $request->name */
            $shopInShop = ShopInShop::create($request->name);

            $this->shopInShopRepository->create($shopInShop);

            $response->setShopInShop($shopInShop);
        }
        $presenter->present($response);
    }

    private function validateNameNotExist(
        CreateShopInShopRequest $request,
        CreateShopInShopResponse $response
    ): bool {
        /** @var string $request->name */
        if ($this->shopInShopRepository->findByName($request->name) === null) {
            return true;
        }

        $response->getNotification()->addError('name', 'shopinshop.create.name.exists');
        return false;
    }
}
