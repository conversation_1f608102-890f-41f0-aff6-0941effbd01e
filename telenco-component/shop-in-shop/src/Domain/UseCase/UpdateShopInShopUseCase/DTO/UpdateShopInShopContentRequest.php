<?php

declare(strict_types=1);

namespace Telenco\Component\ShopInShop\Domain\UseCase\UpdateShopInShopUseCase\DTO;

use Assert\LazyAssertionException;
use Telenco\Component\ShopInShop\Domain\Assert\Assert;

class UpdateShopInShopContentRequest
{
    public function __construct(
        private string $locale,
        private ?int $id = null,
        private ?string $description = null
    ) {
    }

    public function validate(UpdateShopInShopResponse $response): bool
    {
        try {
            Assert::lazy()
                ->that($this->description, 'contents')
                ->notBlank('shopinshop.update.description.invalid')
                ->verifyNow();

            return true;
        } catch (LazyAssertionException $exception) {
            foreach ($exception->getErrorExceptions() as $errorException) {
                $response
                    ->getNotification()
                    ->addError($errorException->getPropertyPath(), $errorException->getMessage())
                ;
            }
            return false;
        }
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getLocale(): string
    {
        return $this->locale;
    }

    /**
     * @return string|null
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * @param string|null $description
     */
    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }
}
