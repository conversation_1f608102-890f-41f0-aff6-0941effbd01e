<?php

declare(strict_types=1);

namespace Telenco\Component\ShopInShop\Domain\Port\Repository;

use Telenco\Component\ShopInShop\Domain\Model\ShopInShop;

/**
 * @template T
 */
interface ShopInShopRepositoryInterface
{
    public function findById(int $shopInShopId): ?ShopInShop;
    public function findByName(string $name): ?ShopInShop;
    public function create(ShopInShop $shopInShop): void;
    public function update(ShopInShop $shopInShop): void;
    public function findByIdAndLocale(int $shopInShopId, string $locale): ?ShopInShop;
    public function getShopInShops(string $filteredBrand = ""): array;
    public function getPublishedShopInShops(): array;
    public function getPublishedShopInShopsByRegion(): array;
    public function findAllShopsInShop(): array;
    public function findAllShopsInShopOrderByPosition(): array;
}
