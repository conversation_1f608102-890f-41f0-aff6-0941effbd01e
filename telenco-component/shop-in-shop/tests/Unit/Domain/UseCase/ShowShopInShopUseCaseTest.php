<?php

declare(strict_types=1);

namespace Telenco\Component\ShopInShop\Tests\Unit\Domain\UseCase;

use Closure;
use Generator;
use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use Marketplace\Component\Offer\Domain\Model\Merchant;
use Marketplace\Component\Offer\Domain\Model\OfferSearchResult;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Telenco\Component\Shared\Domain\Model\Region;
use Telenco\Component\ShopInShop\Domain\Model\ShopInShop;
use Telenco\Component\ShopInShop\Domain\Model\ShopInShopContent;
use Telenco\Component\ShopInShop\Domain\Port\Repository\ShopInShopRepositoryInterface;
use Telenco\Component\ShopInShop\Domain\Presenter\ShowShopInShopPresenterInterface;
use Telenco\Component\ShopInShop\Domain\UseCase\ShowShopInShop\DTO\ShowShopInShopResponse;
use Telenco\Component\ShopInShop\Domain\UseCase\ShowShopInShop\DTO\ShowShopInShopRequest;
use Telenco\Component\ShopInShop\Domain\UseCase\ShowShopInShop\ShowShopInShopUseCase;
use PHPUnit\Framework\TestCase;

final class ShowShopInShopUseCaseTest extends TestCase
{
    private ShowShopInShopUseCase $useCase;

    private ShowShopInShopPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements ShowShopInShopPresenterInterface {
            public ShowShopInShopResponse $response;
            public function present(ShowShopInShopResponse $response): void
            {
                $this->response = $response;
            }
        };

        $prophecy = new Prophet();
        /** @var ShopInShopRepositoryInterface|ObjectProphecy $shopInShopRepositoryMock */
        $shopInShopRepositoryMock = $prophecy->prophesize(ShopInShopRepositoryInterface::class);
        $shopInShopModel = $this->makeShopInShopModel();
        $shopInShopRepositoryMock->findByIdAndLocale(Argument::type('int'), Argument::type('string'))
            ->shouldBeCalledTimes(1)
            ->will(function (array $args) use ($shopInShopModel): ?ShopInShop {
                [$shopInShopId, $locale] = $args;
                if ($shopInShopId < 1) {
                    return null;
                }
                if ($locale === 'withoutMerchantId') {
                    return $shopInShopModel('FR', null);
                }
                if ($locale === 'withoutBrandId') {
                    return $shopInShopModel('FR', 2, null);
                }
                return $shopInShopModel($locale);
            })
        ;

        $this->useCase = new ShowShopInShopUseCase($shopInShopRepositoryMock->reveal());
    }

    /**
     * @dataProvider provideShowShopInShop
     */
    public function testExecute(ShowShopInShopRequest $request, ShowShopInShopResponse $expected): void
    {
        $this->useCase->execute($request, $this->presenter);

        $this->assertEquals($expected, $this->presenter->response);
    }

    public function provideShowShopInShop(): Generator
    {
        // Given: I want to show merchant and brand offers of current shopInShop
        // When: I send an shop in shop valid id and locale in the request
        // Then: I get a shop in shop object and Offers in the response
        $response = new ShowShopInShopResponse();
        $response->shopInShop = $this->makeShopInShopModel()();

        yield 'Successful response' => [new ShowShopInShopRequest(2, 'FR'), $response];

        // Given: I want to show shop in shop for a english merchant
        // When: I send an shop in shop valid id and merchant locale in the request
        // Then: I get a shop in shop object with EN locale in the response
        $response = new ShowShopInShopResponse();
        $response->shopInShop = $this->makeShopInShopModel()('EN');

        yield 'Successful response with good locale' => [new ShowShopInShopRequest(2, 'EN'), $response];

        // Given: I want to show shop in shop for a merchant
        // When: I send an shop in shop invalid id in the request
        // Then: I get a null shop in shop object in the response
        $response = new ShowShopInShopResponse();
        $response->getNotification()->addError('shopInShop', 'shopinshop.search.notFound');

        yield 'Shop in shop not found' => [new ShowShopInShopRequest(0, 'FR'), $response];

        // Given: I want to show shop in shop for a merchant
        // When: I send an shop in shop valid id and blank locale in the request
        // Then: I get a null shop in shop object and notification message in the response
        $response = new ShowShopInShopResponse();
        $response->getNotification()->addError('locale', 'shopinshop.search.locale.required');

        yield 'Show shop in shop with blank locale' => [new ShowShopInShopRequest(2, ''), $response];

        // Given: I want to show shop in shop for a merchant
        // When: I send an shop in shop valid id and locale in the request
        // Then: I get a shop in shop object but without merchantDistantId and notification message in the response
        $response = new ShowShopInShopResponse();
        $response->getNotification()->addError('merchantDistantId', 'shopinshop.search.merchantDistantId');

        yield 'Show shop in shop without merchantDistantId' =>
            [new ShowShopInShopRequest(2, 'withoutMerchantId'), $response];

        // Given: I want to show shop in shop for a merchant
        // When: I send an shop in shop valid id and locale in the request
        // Then: I get a shop in shop object but without brandDistantId and notification message in the response
        $response = new ShowShopInShopResponse();
        $response->getNotification()->addError('brandDistantId', 'shopinshop.search.brandDistantId');

        yield 'Show shop in shop without brandDistantId' =>
            [new ShowShopInShopRequest(2, 'withoutBrandId'), $response];
    }

    private function makeShopInShopModel(): Closure
    {
        return static function (
            string $locale = 'FR',
            ?int $merchantDistantId = 2,
            ?int $brandDistantID = 2
        ): ShopInShop {
            $shopInShop = new ShopInShop();
            $shopInShop->setName(('shopInShopTest'));
            $shopInShop->setId(2);
            $shopInShop->setStatus('published');
            $shopInShop->setMerchantDistantId($merchantDistantId);
            $shopInShop->setBrandDistantId($brandDistantID);
            $shopInShop->setPathImageBrand('/path/to/brand');
            $shopInShop->setPathImagePageBrand('/path/to/page_brand');
            $shopInShop->setPathImageTop('/path/to/top');
            $shopInShop->setRegions([
                (new Region())->setId(1)->setName('region+1')
            ]);

            $shopInShop->setShopInShopContents([
                (new ShopInShopContent())
                    ->setLocale(Locale::create($locale))
            ]);

            return $shopInShop;
        };
    }
}
