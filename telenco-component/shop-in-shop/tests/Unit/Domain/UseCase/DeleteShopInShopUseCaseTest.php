<?php

declare(strict_types=1);

namespace Telenco\Component\ShopInShop\Tests\Unit\Domain\UseCase;

use Exception;
use Generator;
use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Telenco\Component\Shared\Domain\Model\Region;
use Telenco\Component\Shared\Domain\Status\Status;
use Telenco\Component\ShopInShop\Domain\Model\ShopInShop;
use Telenco\Component\ShopInShop\Domain\Model\ShopInShopContent;
use Telenco\Component\ShopInShop\Domain\Port\Repository\ShopInShopRepositoryInterface;
use Telenco\Component\ShopInShop\Domain\Presenter\DeleteShopInShopPresenterInterface;
use Telenco\Component\ShopInShop\Domain\UseCase\DeleteShopInShop\DeleteShopInShopUseCase;
use Telenco\Component\ShopInShop\Domain\UseCase\DeleteShopInShop\DTO\DeleteShopInShopRequest;
use Telenco\Component\ShopInShop\Domain\UseCase\DeleteShopInShop\DTO\DeleteShopInShopResponse;

class DeleteShopInShopUseCaseTest extends TestCase
{
    private DeleteShopInShopUseCase $useCase;

    private DeleteShopInShopPresenterInterface $presenter;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements DeleteShopInShopPresenterInterface {
            public DeleteShopInShopResponse $response;
            public function present(DeleteShopInShopResponse $response): void
            {
                $this->response = $response;
            }
        };

        $prophecy = new Prophet();
        /** @var ShopInShopRepositoryInterface|ObjectProphecy $shopInShopRepositoryMock */
        $shopInShopRepositoryMock = $prophecy->prophesize(ShopInShopRepositoryInterface::class);
        $shopInShopModel = $this->makeShopInShopModel();

        $shopInShopRepositoryMock->findById(Argument::type('int'))
            ->will(function (array $args) use ($shopInShopModel) {
                [$id] = $args;
                if ($id ===  0) {
                    return null;
                }
                return $shopInShopModel;
            })
        ;
        $shopInShopRepositoryMock->update(Argument::type(ShopInShop::class));

        $this->useCase = new DeleteShopInShopUseCase($shopInShopRepositoryMock->reveal());
    }

    /**
     * @dataProvider provideDeleteShopInShop
     * @throws Exception
     */
    public function testExecute(DeleteShopInShopRequest $request, DeleteShopInShopResponse $expected): void
    {
        $this->useCase->execute($request, $this->presenter);

        $this->assertEquals($expected, $this->presenter->response);
    }

    /**
     * @throws Exception
     */
    public function provideDeleteShopInShop(): Generator
    {
        // Given: I want to delete a non-existent shop in shop
        $request = new DeleteShopInShopRequest(0);
        // When: I call the useCase with a bad id
        $shopInShop = (new ShopInShop())
            ->setId(0)
            ->setName('inconnu');
        $response = new DeleteShopInShopResponse($shopInShop, DeleteShopInShopResponse::STATUS_NOT_FOUND);
        // Then: I get the error Shop in shop not found
        yield 'Shop in shop not found' => [$request, $response];

        // Given: I want to delete the shop in shop with id = 1
        $request = new DeleteShopInShopRequest(1);
        // When: I call the useCase with id = 1
        $shopInShop = $this->makeShopInShopModel();
        $shopInShop->setStatus(Status::DELETED);
        $response = new DeleteShopInShopResponse($shopInShop, DeleteShopInShopResponse::STATUS_DELETED);
        // Then: The shop in shop status is deleted
        yield 'Successful response' => [$request, $response];
    }

    /**
     * @throws Exception
     */
    private function makeShopInShopModel(int $id = 1): ShopInShop
    {
        return (new ShopInShop())
            ->setName('shopInShopTest')
            ->setId($id)
            ->setStatus('complete')
            ->setMerchantDistantId(2)
            ->setBrandDistantId(2)
            ->setPathImageBrand('/path/to/brand')
            ->setPathImagePageBrand('/path/to/page_brand')
            ->setPathImageTop('/path/to/top')
            ->setBrandName('brand')
            ->setRegions([(new Region())->setId(1)->setName('region+1')])
            ->setShopInShopContents([
                (new ShopInShopContent())
                    ->setLocale(Locale::create('FR'))
            ]);
    }
}
