.marketplace-component-cart:
    variables:
        WORKING_DIRECTORY: marketplace-component/cart

############################
# Build
############################

marketplace-component-cart-build:
    extends: [ ".build", ".marketplace-component-cart" ]

############################
# Code quality analysis
############################

marketplace-component-cart-analysis:
    extends: [ ".analysis", ".marketplace-component-cart" ]
    needs: [ marketplace-component-cart-build ]

############################
# TESTING
############################

marketplace-component-cart-test:
    extends: [ ".test", ".marketplace-component-cart" ]
    needs: [ marketplace-component-cart-build ]

############################
# Security
############################

marketplace-component-cart-security:
    extends: [ ".security", ".marketplace-component-cart" ]
