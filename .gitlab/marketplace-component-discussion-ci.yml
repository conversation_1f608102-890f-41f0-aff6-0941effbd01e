.marketplace-component-discussion:
    variables:
        WORKING_DIRECTORY: marketplace-component/discussion

############################
# Build
############################

marketplace-component-discussion-build:
    extends: [ ".build", ".marketplace-component-discussion" ]


############################
# Code quality analysis
############################

marketplace-component-discussion-analysis:
    extends: [ ".analysis", ".marketplace-component-discussion" ]
    needs: [ marketplace-component-discussion-build ]

############################
# TESTING
############################

marketplace-component-discussion-test:
    extends: [ ".test", ".marketplace-component-discussion" ]
    needs: [ marketplace-component-discussion-build ]

############################
# Security
############################

marketplace-component-discussion-security:
    extends: [ ".security", ".marketplace-component-discussion" ]
