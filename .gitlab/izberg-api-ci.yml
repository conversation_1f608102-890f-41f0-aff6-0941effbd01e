.izberg-api:
    variables:
        WORKING_DIRECTORY: izberg-api

############################
# Build
############################

izberg-api-build:
    extends: [ ".build", ".izberg-api" ]

############################
# Code quality analysis
############################

izberg-api-analysis:
    extends: [ ".analysis", ".izberg-api" ]
    needs: [ izberg-api-build ]

############################
# TESTING
############################

izberg-api-test:
    extends: [ ".test", ".izberg-api" ]
    needs: [ izberg-api-build ]

############################
# Security
############################

izberg-api-security:
    extends: [ ".security", ".izberg-api" ]
