<?php

declare(strict_types=1);

namespace Marketplace\Component\Mail\Domain\Port\Service;

use Marketplace\Component\CleanArchiCore\Domain\ValueObject\File\FileStream;
use Marketplace\Component\Mail\Infrastructure\Exception\TemplateException;
use Marketplace\Component\User\Domain\Model\User;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;

interface EmailServiceInterface
{
    public const NO_REPLAY_EMAIL = "<EMAIL>";
    public const NO_REPLAY_NAME = "no reply";

    /**
     * @param array $from
     * @param array $to
     * @param string $slug
     * @param string $locale
     * @param array $context an array of key|value containing variable for the email
     * @param array $attachments an array of string or ressources for attachments
     * @param FileStream|null $fileStream
     * @return bool
     * @throws TemplateException
     */
    public function send(
        array $from,
        array $to,
        string $slug,
        string $locale,
        array $context,
        array $attachments = [],
        ?FileStream $fileStream = null
    ): bool;

    /**
     * @param array $from
     * @param array $to
     * @param string $subject
     * @param string $content
     * @param array $context an array of key|value containing variable for the email
     * @param array $attachments an array of string or ressources for attachments
     * @param FileStream|null $fileStream
     * @return bool
     * @throws TemplateException
     * @throws TransportExceptionInterface
     */
    public function sendHtml(
        array $from,
        array $to,
        string $subject,
        string $content,
        array $context,
        array $attachments = [],
        ?FileStream $fileStream = null
    ): bool;

    /**
     * @return array
     */
    public function getNoReplyUser(): array;

    /**
     * @param User[] $users
     * @return array
     */
    public function buildEmailUserList(array $users): array;

    public function generateUrl($routeName, $parameters);

    public function getOperatorsEmails(): array;
}
