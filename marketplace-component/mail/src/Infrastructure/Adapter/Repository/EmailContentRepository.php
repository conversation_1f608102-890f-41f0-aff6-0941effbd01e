<?php

namespace Marketplace\Component\Mail\Infrastructure\Adapter\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Marketplace\Component\Mail\Domain\Model\Email;
use Marketplace\Component\Mail\Domain\Port\Repository\EmailContentRepositoryInterface;
use Marketplace\Component\Mail\Infrastructure\Entity\EmailContent as DoctrineEmailContent;
use Marketplace\Component\Mail\Infrastructure\Mapper\EmailMapper;

class EmailContentRepository extends ServiceEntityRepository implements EmailContentRepositoryInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DoctrineEmailContent::class);
    }

    public function getContentBySlugAndLocale(string $slug, string $locale): ?DoctrineEmailContent
    {
        return $this->createQueryBuilder('eco')
            ->leftJoin('eco.email', 'e')
            ->where('e.slug = :slug')
            ->andWhere('eco.locale = :locale')
            ->getQuery()
            ->setParameters([
                'slug' => $slug,
                'locale' => $locale,
            ])->getOneOrNullResult();
    }
}
