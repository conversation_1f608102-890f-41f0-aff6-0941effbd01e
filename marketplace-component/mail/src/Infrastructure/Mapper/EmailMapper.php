<?php

namespace Marketplace\Component\Mail\Infrastructure\Mapper;

use Doctrine\Common\Collections\ArrayCollection;
use Marketplace\Component\Mail\Domain\Model\Email;
use Marketplace\Component\Mail\Infrastructure\Entity\Email as DoctrineEmail;

class EmailMapper
{
    /**
     * @param Email $email
     * @param DoctrineEmail|null $doctrineEmail
     * @return DoctrineEmail
     */
    public static function domainToDoctrine(Email $email, ?DoctrineEmail $doctrineEmail = null): DoctrineEmail
    {
        if (!$doctrineEmail instanceof DoctrineEmail) {
            $doctrineEmail = new DoctrineEmail();
        }
        $doctrineEmail->setSlug($email->getSlug());
        $doctrineEmail->setUpdatedAt(new \DateTimeImmutable());
        $contents = new ArrayCollection();

        foreach ($email->getContents() as $content) {
            $doctrineContent = EmailContentMapper::domainToDoctrine($content);
            $doctrineContent->setEmail($doctrineEmail);
            /**
             * @psalm-suppress InvalidArgument
             */
            $contents->add($doctrineContent);
        }
        $doctrineEmail->setContents($contents);
        return $doctrineEmail;
    }

    /**
     * @param DoctrineEmail $doctrineEmail
     * @return Email
     */
    public static function doctrineToDomain(DoctrineEmail $doctrineEmail): Email
    {
        $email = new Email();
        $email->setId($doctrineEmail->getId());
        $email->setSlug($doctrineEmail->getSlug());
        $contents = [];
        foreach ($doctrineEmail->getContents() as $content) {
            $contents[] = EmailContentMapper::doctrineToDomain($content);
        }
        $email->setContents($contents);

        return $email;
    }
}
