<?php

namespace Marketplace\Component\Mail\Presentation\ViewModel;

use Knp\Component\Pager\Pagination\PaginationInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class ShowEmailsViewModel
{
    private PaginationInterface $paginatedResult;

    public function __construct(private TranslatorInterface $translator)
    {
    }
    /**
     * @return PaginationInterface
     */
    public function getPaginatedResult(): PaginationInterface
    {
        return $this->paginatedResult;
    }

    /**
     * @param PaginationInterface $paginatedResult
     */
    public function setPaginatedResult(PaginationInterface $paginatedResult): void
    {
        $this->paginatedResult = $paginatedResult;
    }

    public function headerTitle(): string
    {
        return $this->translator->trans('back.mail.show.headerTitle', domain: 'translations');
    }

    public function pageTitle(): string
    {
        return $this->translator->trans('back.mail.show.pageTitle', domain: 'translations');
    }
}
