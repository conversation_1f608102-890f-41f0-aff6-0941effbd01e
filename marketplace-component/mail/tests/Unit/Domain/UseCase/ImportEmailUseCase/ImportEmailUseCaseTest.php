<?php

namespace Marketplace\Component\Mail\Domain\UseCase\ImportEmailUseCase;

use Marketplace\Component\Mail\Domain\Model\Email;
use Marketplace\Component\Mail\Domain\Model\EmailTemplate;
use Marketplace\Component\Mail\Domain\Model\EmailTemplateSlug;
use Marketplace\Component\Mail\Domain\Port\Repository\EmailRepositoryInterface;
use Marketplace\Component\Mail\Domain\Port\Service\EmailTemplateServiceInterface;
use Marketplace\Component\Mail\Domain\Port\Service\OutputServiceInterface;
use Marketplace\Component\Mail\Domain\Presenter\ImportEmailPresenterInterface;
use Marketplace\Component\Mail\Domain\UseCase\ImportEmailUseCase\DTO\ImportEmailRequest;
use Marketplace\Component\Mail\Domain\UseCase\ImportEmailUseCase\DTO\ImportEmailResponse;
use Marketplace\Component\Mail\Exception\InvalidEmailException;
use PhpParser\Node\Arg;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;

class ImportEmailUseCaseTest extends TestCase
{
    private ImportEmailPresenterInterface $presenter;
    private ImportEmailUseCase $importEmailUseCase;
    private ObjectProphecy $emailTemplateService;
    private Prophet $prophet;
    protected function setUp(): void
    {
        parent::setUp();
        $this->presenter = new class implements ImportEmailPresenterInterface {
            public ImportEmailResponse $response;
            public function present(ImportEmailResponse $response): void
            {
                $this->response = $response;
            }
        };
        $this->prophet = new Prophet();
        $this->emailTemplateService = $this->prophet->prophesize(EmailTemplateServiceInterface::class);
        $this->emailTemplateService->fetchAllTemplateNames()->willReturn([EmailTemplateSlug::RESET_PASSWORD_TO_USER]);
        $this->initUseCase();
    }

    private function initUseCase()
    {
        $emailRepository = $this->prophet->prophesize(EmailRepositoryInterface::class);
        $emailRepository->findEmailBySlug(Argument::type('string'))->willReturn(null);
        $emailRepository->deleteEmail(Argument::type('int'));
        $emailRepository->save(Argument::type(Email::class));
        $this->emailTemplateService->getTemplate(Argument::type('string'))->will(function ($args) {
            $slug = $args[0];
            $emailTemplate = new EmailTemplate();
            $emailTemplate->setContent('content');
            $emailTemplate->setSubject($slug);
            $emailTemplate->setTemplateName($slug);
            $emailTemplate->setVariables([]);
            return $emailTemplate;
        });
        $this->importEmailUseCase = new ImportEmailUseCase(
            $emailRepository->reveal(),
            $this->emailTemplateService->reveal()
        );
    }

    /**
 * @param ImportEmailRequest $request
 * @param ImportEmailResponse $expected
 * @dataProvider provideExecute
 */
    public function testExecute(ImportEmailRequest $request, ImportEmailResponse $expected)
    {
        $this->importEmailUseCase->execute($request, $this->presenter);
        $this->assertEquals($expected, $this->presenter->response);
    }

    /**
     */
    public function testExecuteException()
    {
        $this->emailTemplateService->fetchAllTemplateNames()->willReturn(['toto']);
        $this->initUseCase();
        $request = new ImportEmailRequest(false);
        $this->expectException(InvalidEmailException::class);
        $this->importEmailUseCase->execute($request, $this->presenter);
    }

    /**
     * @return \Generator
     */
    public function provideExecute()
    {
        $request = new ImportEmailRequest(true);
        $expected = new ImportEmailResponse(1);
        yield [$request, $expected];
    }
}
