<?php

namespace Marketplace\Component\Mail\Domain\UseCase\ShowEmailsUseCase;

use Marketplace\Component\Mail\Domain\Model\Email;
use Marketplace\Component\Mail\Domain\Port\Repository\EmailRepositoryInterface;
use Marketplace\Component\Mail\Domain\Presenter\ShowEmailsPresenterInterface;
use Marketplace\Component\Mail\Domain\UseCase\ShowEmailsUseCase\DTO\ShowEmailsRequest;
use Marketplace\Component\Mail\Domain\UseCase\ShowEmailsUseCase\DTO\ShowEmailsResponse;
use PHPUnit\Framework\TestCase;
use Prophecy\Prophet;

class ShowEmailsUseCaseTest extends TestCase
{
    private ShowEmailsUseCase $useCase;

    private ShowEmailsPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class () implements ShowEmailsPresenterInterface {
            public ShowEmailsResponse $response;
            public function present(ShowEmailsResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        $emailRepository = $prophet->prophesize(EmailRepositoryInterface::class);
        $emailRepository->findAllEmails()->willReturn([new Email()]);
        $this->useCase = new ShowEmailsUseCase($emailRepository->reveal());
    }

    public function testExecute()
    {
        $request = new ShowEmailsRequest(1, 1);
        $this->useCase->execute($request, $this->presenter);
        $response = new ShowEmailsResponse([new Email()], 1, 1);
        $this->assertEquals($response, $this->presenter->response);
    }
}
