<?php

namespace Marketplace\Component\Mail\Infrastructure\Adapter\Builder;

use Marketplace\Component\Mail\Exception\InvalidEmailException;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;
use Psr\Log\LoggerInterface;
use Twig\Environment;
use Twig\Template;
use Twig\TemplateWrapper;

class EmailContentBuilderTest extends TestCase
{
    private EmailContentBuilder $emailContentBuilder;
    protected function setUp(): void
    {
        parent::setUp();
        $prophet = new Prophet();

        $this->logger = $prophet->prophesize(LoggerInterface::class);
        $this->twig = $prophet->prophesize(Environment::class);
        $twig = $this->twig;
        $this->twig->getExtensions()->willReturn([]);
        $this->twig->createTemplate(Argument::type('string'))->will(function ($args) use ($twig) {
            if ($args[0] === 'exception') {
                throw new InvalidEmailException();
            }
            /**
             * @var Environment $twig
             */
            $twig = $twig->reveal();
            $template = new Class ($twig, $args[0]) extends Template{
                public function __construct(Environment $env, private string $content)
                {
                    parent::__construct($env);
                }

                public function getTemplateName()
                {
                }

                public function getDebugInfo()
                {
                }

                public function getSourceContext()
                {
                }

                protected function doDisplay(array $context, array $blocks = [])
                {
                }

                public function render(array $context)
                {
                    return $this->content;
                }
            };
            return new TemplateWrapper($twig, $template);
        });
        $this->emailContentBuilder = new EmailContentBuilder(
            $this->twig->reveal()
        );
        $this->emailContentBuilder->setLogger($this->logger->reveal());
    }


    /**
     * @param string $content
     * @param array $variables
     * @param string|null $expected
     * @dataProvider provideBuildContentFromTwigTemplate
     */
    public function testBuildContentFromTwigTemplate(string $content, array $variables, ?string $expected)
    {
        $result = $this->emailContentBuilder->buildContentFromTwigTemplate($content, $variables);
        $this->assertEquals($expected, $result);
    }

    public function provideBuildContentFromTwigTemplate()
    {
        //ok
        yield ['test {{content}}',[],'test {{content|raw}}'];
        //exception thrown
        yield ['exception',[],null];
    }

    public function testBuildContentFromTwigTemplateException()
    {
        $this->expectException(InvalidEmailException::class);
        $this->emailContentBuilder->buildContentFromTwigTemplate('exception', [], true);
    }
}
