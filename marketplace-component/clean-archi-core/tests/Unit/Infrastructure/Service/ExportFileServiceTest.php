<?php

namespace Marketplace\Component\CleanArchiCore\Infrastructure\Service\ExportFile;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CsvReaderInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;
use Psr\Log\LoggerInterface;
use Symfony\Component\Filesystem\Filesystem;

class ExportFileServiceTest extends TestCase
{
    private ExportFileService $exportFileService;

    protected function setUp(): void
    {
        $fileSystem = new Filesystem();
        $fileSystem->remove(__DIR__ . '/../csv-fake/current/');
        $fileSystem->remove(__DIR__ . '/../csv-fake/archive/');
        parent::setUp();
        $prophet = new Prophet();
        $csvReader = $prophet->prophesize(CsvReaderInterface::class);
        $csvReader
            ->write(Argument::type('string'), Argument::type('array'), Argument::type('array'))
            ->willReturn('toto');
        $this->exportFileService = new ExportFileService($csvReader->reveal());
        $logger = $prophet->prophesize(LoggerInterface::class);
        $this->exportFileService->setLogger($logger->reveal());
    }


    public function testExport()
    {
        $this->exportFileService->export(
            ['toto'],
            [0 => ['toto' => 'tata']],
            'toto',
            __DIR__ . '/../csv-fake/current/',
            __DIR__ . '/../csv-fake/archive/'
        );
        $this->assertTrue(true);
    }

    public function testGetUniqueFilename()
    {
        $fileName = $this->exportFileService->getUniqueFilename('toto');
        $this->assertStringContainsString('toto.csv', $fileName);
    }
}
