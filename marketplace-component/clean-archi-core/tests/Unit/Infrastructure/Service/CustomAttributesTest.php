<?php

declare(strict_types=1);

namespace Marketplace\Component\CleanArchiCore\Tests\Unit\Infrastructure\Service;

use Marketplace\Component\CleanArchiCore\Infrastructure\Service\CustomAttributes;
use PHPUnit\Framework\TestCase;

final class CustomAttributesTest extends TestCase
{
    public function testShouldHaveReceivedTheSinglePackageValue(): void
    {
        $customAttributes = new CustomAttributes([
            'seo_offer_title' => '',
            'seo_offer_metadescription' => '',
            'tva_group_name' => '',
            'availability' => '',
            'warehouse' => '',
            'delivery' => '',
            'additional_products' => '',
            'product_advantages' => '',
            'click_n_collect' => '',
            'guarantee_month' => '',
            'conditioning' => '',
            'psp_merchant_id' => '',
            'moq' => '',
            'threshold_1' => '',
            'threshold_2' => '',
            'threshold_3' => '',
            'threshold_4' => '',
            'threshold_5' => '',
            'threshold_1_price' => '',
            'threshold_2_price' => '',
            'threshold_3_price' => '',
            'threshold_4_price' => '',
            'threshold_5_price' => '',
            'top_vendor' => '',
            'partner_vendor' => '',
            'single_package' => 'CUSTOM_NAME',
            'short_description' => 'PRDC00_title_level2',
            'price_category_05' => 'PRX050_price_category_05',
            'video' => 'AAA080_video',
            'AAA_recipient' => 'AAA_recipient',
            'AAB_recipient_phone' => 'AAB_recipient_phone',
            'AAC_client_order_number' => 'AAC_client_order_number',
            'AAD_shipping_comment' => 'AAD_shipping_comment',
            'flocking' => 'PRD045_flocking',
            'return_product' => 'PRD050_returned_product',
            'dangerous_goods' => 'PRD060_dangerous_goods',
            'dg_classification' => 'PRD061_dg_classification',
            'PRDD20_replenishment_lead_time' => 'PRDD20_replenishment_lead_time',
            'PRDD40_infos_types' => 'PRDD40_infos_types',
            'variation_description' => 'variation_description',
            'battery_included' => 'CBE010_battery_included',
            'game' => 'EBB010_game',
            'visor' => 'EBF010_visor',
            'equipment_fall_arrest' => 'EAY010_equipment_fall_arrest',
            'AAE_early_delivery' => 'AAE_early_delivery',
            'no_price' => "no_price"
        ]);

        $this->assertSame('CUSTOM_NAME', $customAttributes->singlePackage());
    }
}
