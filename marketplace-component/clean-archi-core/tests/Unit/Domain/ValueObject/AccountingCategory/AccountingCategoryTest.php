<?php

declare(strict_types=1);

namespace Marketplace\Component\CleanArchiCore\Tests\Unit\Domain\ValueObject\AccountingCategory;

use Marketplace\Component\CleanArchiCore\Domain\Exception\AccountingCategoryNoSupportedCountry;
use Marketplace\Component\CleanArchiCore\Domain\ValueObject\AccountingCategory\AccountingCategory;
use PHPUnit\Framework\TestCase;

final class AccountingCategoryTest extends TestCase
{
    /**
     * @dataProvider provideLocale
     */
    public function testCreateAccountingCategoryFromCountry(string $country, string $expected): void
    {
        $accountingCategory = AccountingCategory::createFromCountry($country);

        $this->assertEquals($expected, (string) $accountingCategory);
    }

    public function testThrowExceptionWithCountryNotSupported(): void
    {
        $this->expectException(AccountingCategoryNoSupportedCountry::class);
        $this->expectExceptionMessage('The FAIL country is no supported');

        AccountingCategory::createFromCountry('fail');
    }

    public function provideLocale(): iterable
    {
        yield ['FRANCE', 'LOCAL'];
        yield ['MARTINIQUE', 'DOM'];
        yield ['GUADELOUPE', 'DOM'];
        yield ['GUYANE', 'DOM'];
        yield ['SAINT_BARTHELEMY', 'DOM'];
        yield ['SAINT_MARTIN', 'DOM'];
        yield ['REUNION', 'DOM'];
        yield ['MAYOTTE', 'DOM'];
        yield ['BELGIUM', 'CEE'];
        yield ['LUXEMBOURG', 'CEE'];
        yield ['GERMANY', 'CEE'];
        yield ['AUSTRIA', 'CEE'];
        yield ['IRELAND', 'CEE'];
        yield ['SWITZERLAND', 'HORS CEE'];
        yield ['UNITED_KINGDOM', 'HORS CEE'];
    }
}
