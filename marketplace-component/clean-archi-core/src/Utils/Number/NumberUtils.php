<?php

namespace Marketplace\Component\CleanArchiCore\Utils\Number;

class NumberUtils
{
    public static function getFloatValue(string $value): ?float
    {
        $val = str_replace(",", ".", $value);
        $val = preg_replace('/\.(?=.*\.)/', '', $val);
        return floatval($val);
    }

    public static function isInt(?string $value): bool
    {
        if ($value === null) {
            return false;
        }
        return (ctype_digit($value) && (string)(int)$value === $value);
    }
}
