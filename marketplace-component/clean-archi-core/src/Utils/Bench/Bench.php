<?php

namespace Marketplace\Component\CleanArchiCore\Utils\Bench;

class Bench
{
    private float $startTime;
    private int $stepNumber = 0;

    public function __construct(private string $name)
    {
        $this->startTime = microtime(true);
    }

    /**
     * return Millisecond since bench creation
     * @return float
     */
    public function getStep(): float
    {
        $endTime = microtime(true);
        $this->stepNumber++;
        return ($endTime - $this->startTime) * 1000;
    }

    public function dump(?string $comment = null): void
    {
        $message = sprintf("%s:%d : %d", $this->name, $this->stepNumber, $this->getStep());
        if ($comment != null) {
            $message = sprintf("%s, %s", $message, $comment);
        }
        dump($message);
    }
}
