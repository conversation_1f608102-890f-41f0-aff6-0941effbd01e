<?php

declare(strict_types=1);

namespace Marketplace\Component\CleanArchiCore\Infrastructure\Service;

use Marketplace\Component\CleanArchiCore\Domain\Enum\RegionEnum;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CurrencyServiceInterface;

final class CurrencyService implements CurrencyServiceInterface
{
    public const SUPPORTED_CURRENCY = [
        self::EUR,
        self::GBP
    ];

    public const EUR = 'EUR';
    public const GBP = 'GBP';

    public function getCurrencyFromRegion(string $region): string
    {
        return match ($region) {
            RegionEnum::ANTILLES->value,
            RegionEnum::AUSTRIA->value,
            RegionEnum::BELGIUM->value,
            RegionEnum::FRANCE->value,
            RegionEnum::GERMANY->value,
            RegionEnum::IRELAND->value,
            RegionEnum::LUXEMBOURG->value,
            RegionEnum::REUNION->value => self::EUR,
            RegionEnum::UNITED_KINGDOM->value => self::GBP,
            default => self::EUR
        };
    }
}
