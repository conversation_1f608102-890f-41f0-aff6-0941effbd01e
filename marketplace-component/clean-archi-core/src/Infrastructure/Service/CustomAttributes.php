<?php

namespace Marketplace\Component\CleanArchiCore\Infrastructure\Service;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CustomAttributesInterface;

class CustomAttributes implements CustomAttributesInterface
{
    private string $seoOfferTitle;

    private string $seoOfferMetadescription;

    private string $tvaGroupName;

    private string $availability;

    private string $warehouse;

    private string $delivery;

    private string $additionalProducts;

    private array $ignoredAttributes = [];

    private array $documentationAttributes = [];

    private string $productAdvantages;

    private string $clickNCollectKey;

    private string $guaranteeMonth;

    private string $conditioning;

    private string $pspMerchantId;

    private array $facetingIgnoredAttributes = [];

    private string $moq;

    private string $threshold1;
    private string $threshold1Price;
    private string $threshold2;
    private string $threshold2Price;
    private string $threshold3;
    private string $threshold3Price;
    private string $threshold4;
    private string $threshold4Price;
    private string $threshold5;
    private string $threshold5Price;

    private string $topVendor;
    private string $partnerVendor;
    private string $singlePackage;
    private string $video;

    private string $shortDescription;

    private string $priceCategory05;

    private string $recipient;
    private string $recipientPhone;
    private string $clientOrderNumber;
    private string $shippingComment;
    private string $flocking;
    private string $returnProduct;
    private string $dangerousGood;
    private string $dgClassification;
    private string $offerInfosTypesLabels;
    private string $replenishmentLeadTime;
    private string $variationDescription;
    private string $batteryIncluded;
    private string $game;
    private string $visor;
    private string $equipmentFallArrest;
    private string $earlyDelivery;
    private string $noPrice;

    public function __construct(array $customAttributes)
    {
        $this->seoOfferTitle = $customAttributes['seo_offer_title'];
        $this->seoOfferMetadescription = $customAttributes['seo_offer_metadescription'];
        $this->tvaGroupName = $customAttributes['tva_group_name'];
        $this->availability = $customAttributes['availability'];
        $this->warehouse = $customAttributes['warehouse'];
        $this->delivery = $customAttributes['delivery'];
        $this->additionalProducts = $customAttributes['additional_products'];
        $this->productAdvantages = $customAttributes['product_advantages'];
        $this->clickNCollectKey = $customAttributes['click_n_collect'];
        $this->guaranteeMonth = $customAttributes['guarantee_month'];
        $this->conditioning = $customAttributes['conditioning'];
        $this->pspMerchantId = $customAttributes['psp_merchant_id'];
        $this->moq = $customAttributes['moq'];
        $this->threshold1 = $customAttributes['threshold_1'];
        $this->threshold2 = $customAttributes['threshold_2'];
        $this->threshold3 = $customAttributes['threshold_3'];
        $this->threshold4 = $customAttributes['threshold_4'];
        $this->threshold5 = $customAttributes['threshold_5'];
        $this->threshold1Price = $customAttributes['threshold_1_price'];
        $this->threshold2Price = $customAttributes['threshold_2_price'];
        $this->threshold3Price = $customAttributes['threshold_3_price'];
        $this->threshold4Price = $customAttributes['threshold_4_price'];
        $this->threshold5Price = $customAttributes['threshold_5_price'];
        $this->topVendor = $customAttributes['top_vendor'];
        $this->partnerVendor = $customAttributes['partner_vendor'];
        $this->singlePackage = $customAttributes['single_package'];
        $this->shortDescription = $customAttributes['short_description'];
        $this->priceCategory05 = $customAttributes['price_category_05'];
        $this->video = $customAttributes['video'];
        $this->recipient = $customAttributes['AAA_recipient'];
        $this->recipientPhone = $customAttributes['AAB_recipient_phone'];
        $this->clientOrderNumber = $customAttributes['AAC_client_order_number'];
        $this->shippingComment = $customAttributes['AAD_shipping_comment'];
        $this->flocking = $customAttributes['flocking'];
        $this->returnProduct = $customAttributes['return_product'];
        $this->dangerousGood = $customAttributes['dangerous_goods'];
        $this->dgClassification = $customAttributes['dg_classification'];
        $this->offerInfosTypesLabels = $customAttributes['PRDD40_infos_types'];
        $this->replenishmentLeadTime = $customAttributes['PRDD20_replenishment_lead_time'];
        $this->variationDescription = $customAttributes['variation_description'];
        $this->batteryIncluded = $customAttributes['battery_included'];
        $this->game = $customAttributes['game'];
        $this->visor = $customAttributes['visor'];
        $this->equipmentFallArrest = $customAttributes['equipment_fall_arrest'];
        $this->earlyDelivery = $customAttributes['AAE_early_delivery'];
        $this->noPrice = $customAttributes['no_price'];
    }

    /**
     * @return array
     */
    public function getIgnoredAttributes(): array
    {
        return $this->ignoredAttributes;
    }


    public function setIgnoredAttributes(array $ignoredAttributes)
    {
        $this->ignoredAttributes = $ignoredAttributes;
    }

    public function fetchIgnoredAttributes()
    {
        return $this->ignoredAttributes;
    }

    public static function createFullAttributeName($attributeName): string
    {
        return sprintf('attributes.%s', $attributeName);
    }

    public function fetchAllAttributes(): array
    {
        $attributes = [];

        $properties = get_object_vars($this);
        foreach ($properties as $attribute) {
            if (is_array($attribute)) {
                $attributes = array_merge($attributes, $attribute);
            }

            if (is_string($attribute)) {
                $attributes[] = $attribute;
            }
        }

        return $attributes;
    }

    public function getSeoOfferTitle(): string
    {
        return $this->seoOfferTitle;
    }

    public function getSeoOfferMetadescription(): string
    {
        return $this->seoOfferMetadescription;
    }

    public function getTvaGroupName(): string
    {
        return $this->tvaGroupName;
    }

    public function getAvailability(): string
    {
        return $this->availability;
    }

    public function getWarehouse(): string
    {
        return $this->warehouse;
    }

    /**
     * @return string
     */
    public function getDelivery(): string
    {
        return $this->delivery;
    }

    /**
     * @return mixed|string
     */
    public function getAdditionalProducts(): mixed
    {
        return $this->additionalProducts;
    }

    /**
     * @return array
     */
    public function getDocumentationAttributes(): array
    {
        return $this->documentationAttributes;
    }

    /**
     * @param array $documentationAttributes
     * @return $this
     */
    public function setDocumentationAttributes(array $documentationAttributes): self
    {
        $this->documentationAttributes = $documentationAttributes;
        return $this;
    }

    /**
     * @return mixed|string
     */
    public function getProductAdvantages(): mixed
    {
        return $this->productAdvantages;
    }

    /**
     * @param mixed|string $productAdvantages
     * @return $this
     */
    public function setProductAdvantages(mixed $productAdvantages): self
    {
        $this->productAdvantages = $productAdvantages;
        return $this;
    }

    /**
     * @return string
     */
    public function getGuaranteeMonth(): string
    {
        return $this->guaranteeMonth;
    }

    public function excludeAttributes(array $attributes, array $excludedAttributes): array
    {
        return array_filter($attributes, function ($key) use ($excludedAttributes) {
            return !in_array($key, $excludedAttributes);
        }, ARRAY_FILTER_USE_KEY);
    }

    public function getClickNCollectKey(): string
    {
        return $this->clickNCollectKey;
    }

    public function getConditioning(): string
    {
        return $this->conditioning;
    }

    public function setConditioning(string $conditioning): self
    {
        $this->conditioning = $conditioning;
        return $this;
    }

    /**
     * @return string
     */
    public function getPspMerchantId(): string
    {
        return $this->pspMerchantId;
    }

    /**
     * @param string $pspMerchantId
     * @return CustomAttributes
     */
    public function setPspMerchantId(string $pspMerchantId): CustomAttributes
    {
        $this->pspMerchantId = $pspMerchantId;
        return $this;
    }

    /**
     * @return array
     */
    public function getFacetingIgnoredAttributes(): array
    {
        return $this->facetingIgnoredAttributes;
    }


    public function setFacetingIgnoredAttributes(array $facetingIgnoredAttributes)
    {
        $this->facetingIgnoredAttributes = $facetingIgnoredAttributes;
    }

    /**
     * @return mixed|string
     */
    public function getMoq(): mixed
    {
        return $this->moq;
    }

    /**
     * @param mixed|string $moq
     * @return CustomAttributes
     */
    public function setMoq(mixed $moq): CustomAttributes
    {
        $this->moq = $moq;
        return $this;
    }

    /**
     * @return string
     */
    public function getThreshold1(): string
    {
        return $this->threshold1;
    }

    /**
     * @return string
     */
    public function getThreshold1Price(): string
    {
        return $this->threshold1Price;
    }

    /**
     * @return string
     */
    public function getThreshold2(): string
    {
        return $this->threshold2;
    }

    /**
     * @return string
     */
    public function getThreshold2Price(): string
    {
        return $this->threshold2Price;
    }

    /**
     * @return string
     */
    public function getThreshold3(): string
    {
        return $this->threshold3;
    }

    /**
     * @return string
     */
    public function getThreshold3Price(): string
    {
        return $this->threshold3Price;
    }

    /**
     * @return string
     */
    public function getThreshold4(): string
    {
        return $this->threshold4;
    }

    /**
     * @return string
     */
    public function getThreshold4Price(): string
    {
        return $this->threshold4Price;
    }

    /**
     * @return string
     */
    public function getThreshold5(): string
    {
        return $this->threshold5;
    }

    /**
     * @return string
     */
    public function getThreshold5Price(): string
    {
        return $this->threshold5Price;
    }

    /**
     * @return mixed|string
     */
    public function getTopVendor(): mixed
    {
        return $this->topVendor;
    }

    /**
     * @param mixed|string $topVendor
     * @return $this
     */
    public function setTopVendor(mixed $topVendor): self
    {
        $this->topVendor = $topVendor;
        return $this;
    }

    /**
     * @return mixed|string
     */
    public function getPartnerVendor(): mixed
    {
        return $this->partnerVendor;
    }

    /**
     * @param mixed|string $partnerVendor
     * @return $this
     */
    public function setPartnerVendor(mixed $partnerVendor): self
    {
        $this->partnerVendor = $partnerVendor;
        return $this;
    }

    public function singlePackage(): string
    {
        return $this->singlePackage;
    }

    public function getVideo(): string
    {
        return $this->video;
    }

    /**
     * @return mixed|string
     */
    public function getShortDescription(): mixed
    {
        return $this->shortDescription;
    }

    /**
     * @param mixed|string $shortDescription
     * @return $this
     */
    public function setShortDescription(mixed $shortDescription): self
    {
        $this->shortDescription = $shortDescription;
        return $this;
    }

    /**
     * @return string
     */
    public function getPriceCategory05(): string
    {
        return $this->priceCategory05;
    }

    /**
     * @param mixed $priceCategory05
     * @return $this
     */
    public function setPriceCategory05(mixed $priceCategory05): self
    {
        $this->priceCategory05 = $priceCategory05;
        return $this;
    }

    /**
     * @return string
     */
    public function getRecipient(): string
    {
        return $this->recipient;
    }

    /**
     * @return string
     */
    public function getRecipientPhone(): string
    {
        return $this->recipientPhone;
    }

    /**
     * @return string
     */
    public function getClientOrderNumber(): string
    {
        return $this->clientOrderNumber;
    }

    /**
     * @return string
     */
    public function getShippingComment(): string
    {
        return $this->shippingComment;
    }

    /**
     * @return string
     */
    public function getFlocking(): string
    {
        return $this->flocking;
    }

    /**
     * @param string $flocking
     * @return CustomAttributes
     */
    public function setFlocking(string $flocking): CustomAttributes
    {
        $this->flocking = $flocking;
        return $this;
    }

    /**
     * @return string
     */
    public function getReturnProduct(): string
    {
        return $this->returnProduct;
    }

    /**
     * @param string $returnProduct
     * @return CustomAttributes
     */
    public function setReturnProduct(string $returnProduct): CustomAttributes
    {
        $this->returnProduct = $returnProduct;
        return $this;
    }

    /**
     * @return string
     */
    public function getDangerousGood(): string
    {
        return $this->dangerousGood;
    }

    /**
     * @param string $dangerousGood
     * @return CustomAttributes
     */
    public function setDangerousGood(string $dangerousGood): CustomAttributes
    {
        $this->dangerousGood = $dangerousGood;
        return $this;
    }

    /**
     * @return string
     */
    public function getDgClassification(): string
    {
        return $this->dgClassification;
    }

    /**
     * @param string $dgClassification
     * @return CustomAttributes
     */
    public function setDgClassification(string $dgClassification): CustomAttributes
    {
        $this->dgClassification = $dgClassification;
        return $this;
    }

    /**
     * @return mixed|string
     */
    public function getOfferInfosTypesLabels(): mixed
    {
        return $this->offerInfosTypesLabels;
    }

    /**
     * @param mixed|string $offerInfosTypesLabels
     * @return CustomAttributes
     */
    public function setOfferInfosTypesLabels(mixed $offerInfosTypesLabels): CustomAttributes
    {
        $this->offerInfosTypesLabels = $offerInfosTypesLabels;
        return $this;
    }

    public function getReplenishmentLeadTime(): string
    {
        return $this->replenishmentLeadTime;
    }

    /**
     * @return mixed|string
     */
    public function getVariationDescription(): mixed
    {
        return $this->variationDescription;
    }

    public function getBatteryIncluded(): string
    {
        return $this->batteryIncluded;
    }

    public function getGame(): string
    {
        return $this->game;
    }

    public function getVisor(): string
    {
        return $this->visor;
    }

    public function getEquipmentFallArrest(): string
    {
        return $this->equipmentFallArrest;
    }

    public function getEarlyDelivery(): string
    {
        return $this->earlyDelivery;
    }

    /**
     * @return string
     */
    public function getNoPrice(): string
    {
        return $this->noPrice;
    }

    public static function getBoolean(array $attributes, string $key, bool $default = false): bool
    {
        if (array_key_exists($key, $attributes)) {
            return $attributes[$key];
        }
        return $default;
    }
}
