<?php

namespace Marketplace\Component\CleanArchiCore\Infrastructure\Service\ReadFile;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CsvReaderInterface;
use Marketplace\Component\CleanArchiCore\Infrastructure\Exception\BadPathException;
use Marketplace\Component\CleanArchiCore\Infrastructure\Service\ExportFile\ExportFileServiceInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Finder\Finder;

abstract class AbstractReadFileService implements ReadFileInterface
{
    protected Filesystem $filesystem;
    protected string $archiveFileName;

    public function __construct(
        protected CsvReaderInterface $csvReader,
        protected ExportFileServiceInterface $exportFileService,
        protected string $currentPath,
        protected string $archivePath
    ) {
        $this->filesystem = new Filesystem();
        if (!$this->filesystem->exists($this->currentPath)) {
            throw new BadPathException($this->currentPath);
        }

        if (!$this->filesystem->exists($this->archivePath)) {
            throw new BadPathException($this->archivePath);
        }
    }

    public function read(): array
    {
        return [];
    }

    public function moveToArchive(): void
    {
        $files = $this->findFiles();
        foreach ($files as $file) {
            $fullFilePath = $this->currentPath . '/' . $file;
            $this->filesystem->copy($fullFilePath, $this->archiveFilename(), true);
            $this->filesystem->remove($fullFilePath);
        }
    }

    protected function findFiles(): array
    {
        $files = (new Finder())->in($this->currentPath)->name('*.csv')->files();
        if ($files->count() === 0) {
            return [];
        }
        $deliveryFile = [];
        foreach ($files as $file) {
            $deliveryFile[] = $file->getFilename();
        }

        return $deliveryFile;
    }

    protected function archiveFilename(): string
    {
        return sprintf(
            '%s/%s',
            $this->archivePath,
            $this->exportFileService->getUniqueFilename($this->archiveFileName),
        );
    }
}
