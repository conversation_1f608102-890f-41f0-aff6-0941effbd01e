<?php

namespace Marketplace\Component\CleanArchiCore\Infrastructure\Service;

use Exception;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\DownloadServiceInterface;
use Marketplace\Component\CleanArchiCore\Domain\ValueObject\File\FileStream;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\Response\StreamableInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class DownloadService implements DownloadServiceInterface
{
    public function __construct(private HttpClientInterface $httpClient, private LoggerInterface $logger)
    {
    }

    /**
     *
     * @return null|resource
     */
    public function downloadFile(string $url)
    {

        try {
            $response = $this->httpClient->request('GET', $url);
            if ($response instanceof StreamableInterface) {
                return $response->toStream();
            } else {
                $this->logger->error("cant download file, unknown stream", ["url" => $url]);
            }
        } catch (TransportExceptionInterface | ClientExceptionInterface | RedirectionExceptionInterface | ServerExceptionInterface | Exception $ex) {
            $this->logger->error("cant download file", ["url" => $url, "error" => $ex->getMessage(), "trace" => $ex->getTraceAsString()]);
        }
        return null;
    }

    public function getFileStream(?string $url, ?string $fileName): ?FileStream
    {
        if ($url === null || $fileName === null) {
            $this->logger->error("url or filename is null", ["fileName" => $fileName, "url" => $url]);
            return null;
        }
        $attachment = $this->downloadFile($url);
        if ($attachment === null) {
            return null;
        }
        return new FileStream($fileName, $attachment);
    }
}
