<?php

namespace Marketplace\Component\CleanArchiCore\Infrastructure\Service;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\TranslationInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class TranslationService implements TranslationInterface
{


    /**
     * TranslationService constructor.
     * @param TranslatorInterface $translator
     */
    public function __construct(private TranslatorInterface $translator)
    {
    }

    public function trans(string $key, string $locale = null)
    {
        return $this->translator->trans($key, [], 'translations', $locale);
    }
}
