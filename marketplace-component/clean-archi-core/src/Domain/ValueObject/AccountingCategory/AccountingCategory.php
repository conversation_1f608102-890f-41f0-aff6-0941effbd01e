<?php

declare(strict_types=1);

namespace Marketplace\Component\CleanArchiCore\Domain\ValueObject\AccountingCategory;

use Marketplace\Component\CleanArchiCore\Domain\Exception\AccountingCategoryNoSupportedCountry;
use Stringable;

final class AccountingCategory implements Stringable
{
    private const SUPPORTED_COUNTRIES = [
        self::FRANCE,
        self::MARTINIQUE,
        self::GUADELOUPE,
        self::GUYANA,
        self::ST_BARTHELEMY,
        self::ST_MARTIN,
        self::REUNION,
        self::MAYOTTE,
        self::BELGIQUE,
        self::LUXEMBOURG,
        self::ALLEMAGNE,
        self::AUTRICHE,
        self::SUISSE,
        self::ROYAUME_UNI,
        self::IRLANDE,
    ];

    private const FRANCE = 'FRANCE';
    private const MARTINIQUE = 'MARTINIQUE';
    private const GUADELOUPE = 'GUADELOUPE';
    private const GUYANA = 'GUYANE';
    private const ST_BARTHELEMY = 'SAINT_BARTHELEMY';
    private const ST_MARTIN = 'SAINT_MARTIN';
    private const REUNION = 'REUNION';
    private const MAYOTTE = 'MAYOTTE';
    private const BELGIQUE = 'BELGIUM';
    private const LUXEMBOURG = 'LUXEMBOURG';
    private const ALLEMAGNE = 'GERMANY';
    private const AUTRICHE = 'AUSTRIA';
    private const SUISSE = 'SWITZERLAND';
    private const ROYAUME_UNI = 'UNITED_KINGDOM';
    private const IRLANDE = 'IRELAND';

    private const ERP_LOCALE = 'LOCAL';
    private const ERP_DOM = 'DOM';
    private const ERP_CEE = 'CEE';
    private const ERP_HORS_CEE = 'HORS CEE';

    private function __construct(private string $accountingCategory)
    {
    }

    public static function createFromString(string $accounting): self
    {
        if ($accounting === "DOM" || $accounting === "HORS CEE") {
            return new self($accounting);
        }
        throw new AccountingCategoryNoSupportedCountry(sprintf('The %s accounting is no supported', $accounting));
    }

    public static function createFromCountry(string $country): self
    {
        $country = strtoupper($country);
        if (!in_array($country, self::SUPPORTED_COUNTRIES)) {
            throw new AccountingCategoryNoSupportedCountry(sprintf('The %s country is no supported', $country));
        }

        $accountingCategory = match ($country) {
            self::FRANCE => self::ERP_LOCALE,
            self::MARTINIQUE,
            self::GUADELOUPE,
            self::GUYANA,
            self::ST_BARTHELEMY,
            self::ST_MARTIN,
            self::REUNION,
            self::MAYOTTE => self::ERP_DOM,
            self::BELGIQUE, self::LUXEMBOURG, self::ALLEMAGNE, self::AUTRICHE, self::IRLANDE => self::ERP_CEE,
            self::SUISSE, self::ROYAUME_UNI => self::ERP_HORS_CEE
        };

        return new self($accountingCategory);
    }

    /**
     * @inheritDoc
     */
    public function __toString(): string
    {
        return $this->accountingCategory;
    }
}
