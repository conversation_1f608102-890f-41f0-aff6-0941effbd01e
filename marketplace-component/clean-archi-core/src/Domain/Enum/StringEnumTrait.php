<?php

declare(strict_types=1);

namespace Marketplace\Component\CleanArchiCore\Domain\Enum;

trait StringEnumTrait
{
    /**
     * TODO Delete this override method when psalm will supported enum feature
     *
     * @param int|string $scalar
     * @return static
     */
    public static function from(int|string $scalar): static
    {
        return self::from($scalar);
    }

    /**
     * TODO Delete this override method when psalm will supported enum feature
     *
     * @param int|string $scalar
     * @return static
     */
    public static function tryFrom(int|string $scalar): static
    {
        return self::tryFrom($scalar);
    }
}
