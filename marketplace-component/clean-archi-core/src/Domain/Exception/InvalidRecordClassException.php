<?php

declare(strict_types=1);

namespace Marketplace\Component\CleanArchiCore\Domain\Exception;

use Marketplace\Component\CleanArchiCore\Domain\Model\RecordInterface;

final class InvalidRecordClassException extends \RuntimeException
{
    public function __construct(string $recordClassName)
    {
        parent::__construct(
            sprintf('The %s record class must implement %s interface', $recordClassName, RecordInterface::class)
        );
    }
}
