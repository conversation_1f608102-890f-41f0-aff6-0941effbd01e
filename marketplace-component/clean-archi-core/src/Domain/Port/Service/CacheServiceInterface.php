<?php

namespace Marketplace\Component\CleanArchiCore\Domain\Port\Service;

interface CacheServiceInterface
{
    /**
     * @param string $key
     * @return mixed
     */
    public function getItem(string $key);

    /**
     * one day default value (1440 min)
     * @param string $key
     * @param $value
     * @param int $expiration
     */
    public function saveItem(string $key, $value, int $expiration = 1440): void;

    /**
     * @param string $key
     */
    public function removeItem(string $key): void;
}
