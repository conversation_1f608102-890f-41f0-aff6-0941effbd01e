<?php

namespace Symfony\Component\DependencyInjection\Loader\Configurator;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CsvReaderInterface;
use Marketplace\Component\CleanArchiCore\Infrastructure\CSV\LeagueCsvReader;

return function (ContainerConfigurator $configurator) {
    // default configuration for services in *this* file
    $services = $configurator->services()
        ->defaults()
        ->autowire()      // Automatically injects dependencies in your services.
        ->autoconfigure() // Automatically registers your services as commands, event subscribers, etc.
    ;

    // makes classes in src/ available to be used as services
    // this creates a service per class whose id is the fully-qualified class name
    $services->load('Marketplace\\Component\\CleanArchiCore\\', '../../../src/*')
        ->exclude('../../../src/{DependencyInjection,Entity,Resources,MarketplaceCleanArchiCoreBundle.php}');

    $services->alias(CsvReaderInterface::class, LeagueCsvReader::class);
    //$services->set(BatchHandlerInterface::class)->tag('batch.handler');
};
