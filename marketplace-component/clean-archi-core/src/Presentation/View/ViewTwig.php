<?php

declare(strict_types=1);

namespace Marketplace\Component\CleanArchiCore\Presentation\View;

use Marketplace\Component\CleanArchiCore\Presentation\ViewModel\ViewModelInterface;
use Symfony\Component\HttpFoundation\Response;
use Twig\Environment;

final class ViewTwig implements ViewInterface
{
    public function __construct(
        private Environment $twig,
    ) {
    }

    public function send(string $template, ViewModelInterface $viewModel): Response
    {
        return new Response($this->twig->render($template, ['viewModel' => $viewModel]));
    }
}
