<?php

declare(strict_types=1);

namespace Marketplace\Component\CleanArchiCore\Presentation\View;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Service\Attribute\Required;

abstract class AbstractView
{
    #[Required]
    public UrlGeneratorInterface $urlGenerator;

    #[Required]
    public RequestStack $requestStack;

    public function redirectBack(string $route, array $params = []): RedirectResponse
    {
        $stack = $this->requestStack;
        $request = $stack->getCurrentRequest();
        if ($request && $request->server->get('HTTP_REFERER')) {
            return new RedirectResponse($request->server->get('HTTP_REFERER'));
        }

        return new RedirectResponse($this->urlGenerator->generate($route, $params));
    }
}
