<?php

namespace Symfony\Component\DependencyInjection\Loader\Configurator;

use Marketplace\Component\Newsletter\Domain\Port\Repository\NewsletterRepositoryInterface;
use Marketplace\Component\Newsletter\Infrastructure\Adapter\Repository\NewsletterRepository;

return function (ContainerConfigurator $configurator) {
    // default configuration for services in *this* file
    $services = $configurator->services()
        ->defaults()
        ->autowire()      // Automatically injects dependencies in your services.
        ->autoconfigure() // Automatically registers your services as commands, event subscribers, etc.
    ;

    // makes classes in src/ available to be used as services
    // this creates a service per class whose id is the fully-qualified class name
    $services->load('Marketplace\\Component\\Newsletter\\', '../../../src/*')
        ->exclude('../../../src/{DependencyInjection,Entity,Resources,MarketplaceNewsletterBundle.php}');
    $services->alias(NewsletterRepositoryInterface::class, NewsletterRepository::class);
};
