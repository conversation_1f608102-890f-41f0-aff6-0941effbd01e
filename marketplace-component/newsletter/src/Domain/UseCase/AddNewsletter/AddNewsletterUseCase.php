<?php

declare(strict_types=1);

namespace Marketplace\Component\Newsletter\Domain\UseCase\AddNewsletter;

use Marketplace\Component\Newsletter\Domain\Port\Repository\NewsletterRepositoryInterface;
use Marketplace\Component\Newsletter\Domain\Presenter\AddNewsletterPresenterInterface;
use Marketplace\Component\Newsletter\Domain\UseCase\AddNewsletter\DTO\AddNewsletterRequest;
use Marketplace\Component\Newsletter\Domain\UseCase\AddNewsletter\DTO\AddNewsletterResponse;
use Marketplace\Component\Newsletter\Domain\Model\Newsletter;

class AddNewsletterUseCase
{
    public function __construct(
        private NewsletterRepositoryInterface $newsletterRepository,
        private string $secretKey,
    ) {
    }

    public function execute(AddNewsletterRequest $request, AddNewsletterPresenterInterface $presenter)
    {
        $response = new AddNewsletterResponse();

        // Check Captcha only if user is not connected
        if ($request->userConnected === false) {
            //get verify response data
            $verifyResponse = file_get_contents('https://www.google.com/recaptcha/api/siteverify?secret='.$this->secretKey.'&response='.$request->reCaptcha);
            $responseData = json_decode($verifyResponse);
            if ($responseData->success === false) {
                $response->addError('captcha', 'footer.newsletter.error.captcha');
            }
        }

        // Check Email Form
        $email = $request->email;
        $pattern_format = "/^([a-z]+([\.\-_]?[a-z0-9]+)*)@([a-z]+([\-]?[a-z0-9]+)*)\.([a-z]+([\.\-]?[a-z0-9]+)*)$/i";
        $pattern_length = "/^.{2,50}@.{2,150}\..{2,50}$/i";
        if (preg_match($pattern_format, $email) !== 1 || preg_match($pattern_length, $email) !== 1) {
            $response->addError('email', 'footer.newsletter.error.invalid_email');
        }

        $emailAlreadyExist = $this->newsletterRepository->findByEmail($email);
        if ($emailAlreadyExist instanceof Newsletter) {
            $response->addError('email', 'footer.newsletter.error.already_exist');
        }

        if ($response->getNotification()->hasError()) {
            $response->success = false;
            $presenter->present($response);
            return;
        }

        // Insert Email only if no error
        $this->newsletterRepository->insertNewsletter($email);

        $presenter->present($response);
    }
}
