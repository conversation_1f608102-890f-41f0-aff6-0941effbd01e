<?php

declare(strict_types=1);

namespace Marketplace\Component\Newsletter\Domain\UseCase\AddNewsletter\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Error\NotificationTrait;

class AddNewsletterResponse
{
    use NotificationTrait;

    public bool $success = true;

    public function addError(string $fieldName, string $message): void
    {
        $this->getNotification()->addError($fieldName, $message);
    }
}
