<?php

namespace Marketplace\Component\Newsletter\Domain\Port\Repository;

use Marketplace\Component\Newsletter\Domain\Model\Newsletter;

interface NewsletterRepositoryInterface
{

    public function findNewsletter(int $newsletterId): ?Newsletter;

    public function insertNewsletter(string $email): ?Newsletter;

    /**
     * @return array<int,Newsletter>|null
     */
    public function fetchAllNewsletter(int $limit = Newsletter::DEFAULT_LIMIT, int $offset = 0): ?array;

    public function findByEmail(string $email): ?Newsletter;
}
