<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Tests\Unit\Presentation\Presenter;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CustomAttributesInterface;
use Marketplace\Component\CleanArchiCore\Infrastructure\Service\GetRegionService;
use Marketplace\Component\Offer\Domain\Model\Offer;
use Marketplace\Component\Offer\Domain\UseCase\ShowOffer\DTO\ShowOfferResponse;
use Marketplace\Component\Offer\Presentation\Presenter\ShowOfferPresenter;
use Marketplace\Component\Offer\Presentation\Presenter\TechnicalDataRenderer;
use Marketplace\Component\Offer\Presentation\ViewModel\ShowOfferViewModel;
use PHPUnit\Framework\TestCase;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Symfony\Contracts\Translation\TranslatorInterface;

final class ShowOfferPresenterTest extends TestCase
{
    private ShowOfferPresenter $presenter;

    protected function setUp(): void
    {
        $prophecy = new Prophet();
        /** @var TranslatorInterface|ObjectProphecy $translatorMock */
        $translatorMock = $prophecy->prophesize(TranslatorInterface::class);

        /** @var CustomAttributesInterface|ObjectProphecy $customAttributesMock */
        $customAttributesMock = $prophecy->prophesize(CustomAttributesInterface::class);
        $customAttributesMock->getAvailability()->willReturn('availability_name');

        /** @var GetRegionService|ObjectProphecy $getRegionService */
        $getRegionService = $prophecy->prophesize(GetRegionService::class);
        $getRegionService->isReunionOrAntilles()->willReturn(false);

        $this->presenter = new ShowOfferPresenter(
            translator: $translatorMock->reveal(),
            customAttributes: $customAttributesMock->reveal(),
            technicalDataRenderer: $this->createMock(TechnicalDataRenderer::class),
            getRegionService: $getRegionService->reveal()
        );
    }

    public function testInitializeViewModel(): void
    {
        $this->presenter->present(new ShowOfferResponse(offer: $this->makeOffer()));

        $this->assertInstanceOf(ShowOfferViewModel::class, $this->presenter->getViewModel());
        $this->assertTrue($this->presenter->getViewModel()->offerIsAvailability());
        $this->assertSame('stock', $this->presenter->getViewModel()->offerAvailability());
    }

    public function testWithoutAvailabilityAttributeInOffer(): void
    {
        $this->presenter->present(new ShowOfferResponse(offer: $this->makeOffer([])));

        $this->assertInstanceOf(ShowOfferViewModel::class, $this->presenter->getViewModel());
        $this->assertFalse($this->presenter->getViewModel()->offerIsAvailability());
    }

    private function makeOffer(array $attributes = ['availability_name' => 'stock']): Offer
    {
        return (new Offer())
            ->setId(2)
            ->setTitle('Product+1')
            ->setPrice(20.0)
            ->setAttributes($attributes)
            ->setId(2)
        ;
    }
}
