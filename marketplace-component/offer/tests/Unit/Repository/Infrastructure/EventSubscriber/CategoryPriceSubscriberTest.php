<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Tests\Unit\Infrastructure\EventSubscriber;

use InvalidArgumentException;
use Marketplace\Component\Offer\Domain\Model\SpecificPrice\CategoryPrice;
use Marketplace\Component\Offer\Domain\Model\SpecificPrice\SpecificPrice;
use Marketplace\Component\Offer\Domain\Port\Repository\CategoryPriceRepositoryInterface;
use Marketplace\Component\Offer\Domain\Port\Repository\SpecificPriceRepositoryInterface;
use Marketplace\Component\Offer\Infrastructure\EventSubscriber\CategoryPriceSubscriber;
use Marketplace\Component\User\Domain\Event\PriceCategory;
use Marketplace\Component\User\Domain\Model\Merchant;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;

final class CategoryPriceSubscriberTest extends TestCase
{
    private CategoryPriceSubscriber $eventSubscriber;

    protected function setUp(): void
    {
        parent::setUp();

        $prophecy = new Prophet();

        /** @var SpecificPriceRepositoryInterface|ObjectProphecy $specificPriceRepositoryMock */
        $specificPriceRepositoryMock = $prophecy->prophesize(SpecificPriceRepositoryInterface::class);
        $specificPriceRepositoryMock->findAllByVatNumber(Argument::type('string'))->will(function (array $args): array {
            $vatNumber = $args[0];
            if ($vatNumber === 'failSpecificPrice') {
                return [];
            }
            $merchantDistantId = 2;
            if ($vatNumber === 'failCategoryPrice') {
                $merchantDistantId = 0;
            }
            if ($vatNumber === 'failMerchantId') {
                $merchantDistantId = null;
            }
            return [
                (new SpecificPrice())
                    ->setId(1)
                    ->setVatNumber('vatNumber')
                    ->setMerchant((new Merchant())->setVatNumber('vatNumber')->setDistantId($merchantDistantId))
            ];
        });
        /** @var CategoryPriceRepositoryInterface|ObjectProphecy $categoryPriceRepositoryMock */
        $categoryPriceRepositoryMock = $prophecy->prophesize(CategoryPriceRepositoryInterface::class);
        $categoryPriceRepositoryMock->findByMerchant(Argument::type('int'))->will(function (array $args): array {
            $merchantId = $args[0];
            if ($merchantId < 1) {
                return [];
            }

            return [new CategoryPrice(label: 'categoryPrice', merchantId: $merchantId, vatNumber: 'vatNumber')];
        });

        $this->eventSubscriber = new CategoryPriceSubscriber(
            $categoryPriceRepositoryMock->reveal(),
            $specificPriceRepositoryMock->reveal()
        );
    }

    public function testComputeCategoryPriceOfCompany(): void
    {
        $priceCategory = new PriceCategory('vatNumber');
        $this->eventSubscriber->onComputeCategoryPrice($priceCategory);

        $this->assertNotNull($priceCategory->value);
        $this->assertSame('categoryPrice', $priceCategory->value);
    }

    public function testComputeCategoryPriceOfCompanyWithSpecificPriceAtNull(): void
    {
        $priceCategory = new PriceCategory('failSpecificPrice');
        $this->eventSubscriber->onComputeCategoryPrice($priceCategory);

        $this->assertNull($priceCategory->value);
    }

    public function testComputeCategoryPriceOfCompanyWithCategoryPriceAtNull(): void
    {
        $priceCategory = new PriceCategory('failCategoryPrice');
        $this->eventSubscriber->onComputeCategoryPrice($priceCategory);

        $this->assertNull($priceCategory->value);
    }

    public function testWithNoMerchantDistantId(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('The merchant of the specific price vatNumber has no merchant distant id');

        $priceCategory = new PriceCategory('failMerchantId');
        $this->eventSubscriber->onComputeCategoryPrice($priceCategory);
    }
}
