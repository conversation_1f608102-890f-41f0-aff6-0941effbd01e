<?php

namespace Marketplace\Component\Offer\Infrastructure\Adapter\Service;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetLocaleInterface;
use Open\Izberg\Model\Category;
use Marketplace\Component\Offer\Domain\Model\Category as ModelCategory;
use Open\Izberg\Service\CategoryServiceInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

class CategoryAdapterTest extends TestCase
{
    private CategoryAdapter $categoryAdapter;

    protected function setUp(): void
    {
        parent::setUp();
        $prophet = new Prophet();

        $categoryService = $prophet->prophesize(CategoryServiceInterface::class);
        $categoryService->getSameLevelCategories(Argument::type('int'))->willReturn([(new Category())->setId(1)]);
        $categoryService->find(Argument::type('int'))->willReturn((new Category())->setId(1)->setName('category'));

        $getLocaleService =  $prophet->prophesize(GetLocaleInterface::class);
        $getLocaleService->getLocale()->willReturn('fr');
        $this->categoryAdapter = new CategoryAdapter($categoryService->reveal(), $getLocaleService->reveal());
    }

    public function testGetSameLevelCategories()
    {
        $expected = [new ModelCategory(1, 'category', 'category')];
        $result = $this->categoryAdapter->getSameLevelCategories(1);
        $this->assertEquals($expected, $result);
    }
}
