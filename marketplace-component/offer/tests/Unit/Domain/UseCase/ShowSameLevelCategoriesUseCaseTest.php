<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Tests\Unit\Domain\UseCase;

use Marketplace\Component\Offer\Domain\Model\Category;
use Marketplace\Component\Offer\Domain\Model\Offer;
use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Marketplace\Component\Offer\Domain\Presenter\ShowSameLevelCategoriesPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\ShowSameLevelCategoriesUseCase\DTO\ShowSameLevelCategoriesResponse;
use Marketplace\Component\Offer\Domain\UseCase\ShowSameLevelCategoriesUseCase\DTO\ShowSameLevelCategoriesRequest;
use Marketplace\Component\Offer\Domain\UseCase\ShowSameLevelCategoriesUseCase\ShowSameLevelCategoriesUseCase;
use Marketplace\Component\Offer\Domain\Port\Service\CategoryAdapterInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

final class ShowSameLevelCategoriesUseCaseTest extends TestCase
{
    private ShowSameLevelCategoriesUseCase $useCase;

    private ShowSameLevelCategoriesPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements ShowSameLevelCategoriesPresenterInterface {
            public ShowSameLevelCategoriesResponse $response;
            public function present(ShowSameLevelCategoriesResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        $offerRepository = $prophet->prophesize(OfferRepositoryInterface::class);
        $offerRepository->find(Argument::type('int'))->will(function ($args) {
            $offerId = $args[0];
            if ($offerId === 0) {
                return null;
            }
            $offer = new Offer();
            $offer->setCategories([new Category(1, "", "")]);
            return $offer;
        });
        $categoryService = $prophet->prophesize(CategoryAdapterInterface::class);
        $categoryService->getSameLevelCategories(Argument::type('int'))->willReturn([new Category(1, "", "")]);

        $this->useCase = new ShowSameLevelCategoriesUseCase(
            $offerRepository->reveal(),
            $categoryService->reveal()
        );
    }

    /**
     * @param ShowSameLevelCategoriesRequest $request
     * @param ShowSameLevelCategoriesResponse $response
     * @dataProvider  provideExecute
     */
    public function testExecute(
        ShowSameLevelCategoriesRequest $request,
        ShowSameLevelCategoriesResponse $response
    ): void {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($response, $this->presenter->response);
    }

    public function provideExecute()
    {
        //given: I want to get same level categories of an unexisting offer
        $request = new ShowSameLevelCategoriesRequest(0);
        //when: I submit the request
        $response = new ShowSameLevelCategoriesResponse();
        $response->getNotification()->addError('offerId', 'this offer does not exist. id :0');
        //then: I've got an error
        yield "unexisting offer" => [$request, $response];
        //given: I want to get same level categories of an offer
        $request = new ShowSameLevelCategoriesRequest(1);
        //when: I submit the request
        $response = new ShowSameLevelCategoriesResponse();
        $response->setCategories([new Category(1, "", "")]);
        //then: I've got all the same level categories
        yield "success" => [$request, $response];
    }
}
