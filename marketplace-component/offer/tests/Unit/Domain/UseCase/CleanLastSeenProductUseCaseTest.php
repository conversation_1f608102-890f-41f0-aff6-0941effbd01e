<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Tests\Unit\Domain\UseCase;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CustomAttributesInterface;
use Marketplace\Component\Offer\Domain\Model\Category;
use Marketplace\Component\Offer\Domain\Model\LastSeenProduct;
use Marketplace\Component\Offer\Domain\Model\Offer;
use Marketplace\Component\Offer\Domain\Port\Repository\LastSeenProductRepositoryInterface;
use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Marketplace\Component\Offer\Domain\Presenter\CleanLastSeenProductPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\LastSeenProduct\CleanLastSeenProductUseCase\CleanLastSeenProductUseCase;
use Marketplace\Component\Offer\Domain\UseCase\LastSeenProduct\CleanLastSeenProductUseCase\DTO\CleanLastSeenProductResponse;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

final class CleanLastSeenProductUseCaseTest extends TestCase
{
    private CleanLastSeenProductUseCase $useCase;

    private CleanLastSeenProductPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements CleanLastSeenProductPresenterInterface {
            public CleanLastSeenProductResponse $response;
            public function present(CleanLastSeenProductResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        $offerRepository = $prophet->prophesize(OfferRepositoryInterface::class);
        $offerRepository->find(Argument::type('int'))->will(function ($args) {
            $offerId = $args[0];
            $offer = new Offer();
            $offer->setPrice(12);
            $offer->setCategories([new Category(1, [])]);
            $offer->setAttributes(['delivery' => ['france']]);
            if ($offerId === 2) {
                return $offer;
            }
            $offer->setPreviousPrice(25);
            if ($offerId === 3) {
                $offer->setAttributes(['delivery' => ['allemagne']]);
                return $offer;
            }
            if ($offerId === 4) {
                $offer->setCategories([new Category(2, [])]);
                return $offer;
            }
            return null;
        });
        $lastSeenProductRepository = $prophet->prophesize(LastSeenProductRepositoryInterface::class);
        $lastSeenProducts = $this->buildLastSeenOffers();
        $lastSeenProductRepository->getAllLastSeenOffers()->willReturn($lastSeenProducts);
        $customAttributes = $prophet->prophesize(CustomAttributesInterface::class);
        $customAttributes->getDelivery()->willReturn('delivery');
        $this->useCase = new CleanLastSeenProductUseCase(
            $offerRepository->reveal(),
            $lastSeenProductRepository->reveal(),
            $customAttributes->reveal()
        );
    }

    private function buildLastSeenOffers(): array
    {
        $lastSeenOffers = [];
        //offer does not exist anymore
        $lastSeenOffers[] = new LastSeenProduct(1, 1, 'france');
        //offer is not in discount
        $lastSeenOffers[] = new LastSeenProduct(2, 1, 'france');
        //offer is not in the region anymore
        $lastSeenOffers[] = new LastSeenProduct(3, 1, 'france');
        //offer is not in this category anymore
        $lastSeenOffers[] = new LastSeenProduct(4, 1, 'france');
        return $lastSeenOffers;
    }

    public function testExecute(): void
    {
        $this->useCase->execute($this->presenter);
        $response = new CleanLastSeenProductResponse();
        $response->setSuccess(true);
        $this->assertEquals($response, $this->presenter->response);
    }
}
