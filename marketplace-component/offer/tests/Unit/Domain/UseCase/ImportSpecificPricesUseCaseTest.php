<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Tests\Domain\UseCase;

use DateTimeImmutable;
use Exception;
use Generator;
use Marketplace\Component\Offer\Domain\Model\SpecificPrice\CategoryPrice;
use Marketplace\Component\Offer\Domain\Model\SpecificPrice\ImportTask;
use Marketplace\Component\Offer\Domain\Model\SpecificPrice\Record;
use Marketplace\Component\Offer\Domain\Model\SpecificPrice\Records;
use Marketplace\Component\Offer\Domain\Model\SpecificPrice\SpecificPrice;
use Marketplace\Component\Offer\Domain\Port\Repository\CategoryPriceRepositoryInterface;
use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Marketplace\Component\Offer\Domain\Port\Repository\SpecificPriceRepositoryInterface;
use Marketplace\Component\Offer\Domain\Port\Service\SpecificPriceServiceInterface;
use Marketplace\Component\Offer\Domain\Presenter\ImportSpecificPricesPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\ImportSpecificPrices\DTO\ImportSpecificPricesRequest;
use Marketplace\Component\Offer\Domain\UseCase\ImportSpecificPrices\DTO\ImportSpecificPricesResponse;
use Marketplace\Component\Offer\Domain\UseCase\ImportSpecificPrices\ImportSpecificPricesUseCase;
use Marketplace\Component\Offer\Domain\UseCase\ImportSpecificPricesRead\DTO\ImportSpecificPricesReadResponse;
use Marketplace\Component\Offer\Infrastructure\Adapter\Elasticsearch\OfferRepositoryElasticsearch;
use Marketplace\Component\User\Domain\Model\Merchant;
use Marketplace\Component\User\Domain\Port\Repository\MerchantRepositoryInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Psr\Log\LoggerInterface;
use SplFileInfo;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

final class ImportSpecificPricesUseCaseTest extends TestCase
{
    private ImportSpecificPricesUseCase $useCase;

    private ImportSpecificPricesPresenterInterface $presenter;

    private array $scenarioData = [];

    protected function setUp(): void
    {
        parent::setUp();

        $this->makeScenarioData();
        $this->presenter = new class implements ImportSpecificPricesPresenterInterface {
            public ImportSpecificPricesResponse $response;

            public function present(ImportSpecificPricesResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        $messageBus = $prophet->prophesize(MessageBusInterface::class);
        $messageBus->dispatch(Argument::any())->will(function ($args) {
            return new Envelope(new ImportSpecificPricesRequest([], 123));
        });

        $this->useCase = new ImportSpecificPricesUseCase(
            $this->makeSpecificPriceRepository(),
            $this->makeMerchantRepository(),
            $this->makeCategoryPriceRepository(),
            $this->makeOfferRepository()->reveal(),
            $this->createMock(LoggerInterface::class)
        );
    }

    /**
     * @dataProvider importSpecificPricesProvider
     * @param ImportSpecificPricesRequest $request
     * @param ImportSpecificPricesResponse $expected
     * @throws Exception
     */
    public function testExecute(
        ImportSpecificPricesRequest $request,
        ImportSpecificPricesResponse $expected
    ): void {
        $this->useCase->execute($request, $this->presenter);

        $this->assertEquals($expected, $this->presenter->response);
    }

    public function importSpecificPricesProvider(): Generator
    {
        $this->makeScenarioData();

        // Successful create and update test
        $response = new ImportSpecificPricesResponse();
        //$response->getNotification()->addError('csvFile', 'import.specific_prices.warning.line_error');

        $requestData = $this->scenarioData['fullValid.csv'];
        $request = new ImportSpecificPricesRequest($requestData, 1);
        yield 'Successful create and update' => [$request, $response];

        // Fail if merchant not found
        $response = new ImportSpecificPricesResponse();
        $response->success = false;
        $response->getNotification()->addError('merchant', 'import.specific_prices.error.merchant');
        $requestData = $this->scenarioData['fullValid.csv'];
        $request = new ImportSpecificPricesRequest($requestData, 0);
        yield 'Fail if merchant not found' => [$request, $response];

        // blank sku
        $response = new ImportSpecificPricesResponse();
        $response->getNotification()->addError('csvFile', 'import.specific_prices.warning.line_error');
        $requestData = $this->scenarioData['with_blank_sku.csv'];
        $request = new ImportSpecificPricesRequest($requestData, 1);

        yield 'Blank SKU' => [$request, $response];

        // Invalid format date end
        $response = new ImportSpecificPricesResponse();
        $response->getNotification()->addError('csvFile', 'import.specific_prices.warning.line_error');
        $requestData = $this->scenarioData['invalid_date_end.csv'];
        $request = new ImportSpecificPricesRequest($requestData, 1);
        yield 'Non unique SKU' => [$request, $response];

        // Delete once specific price
        $response = new ImportSpecificPricesResponse();
        $requestData = $this->scenarioData['delete_once_price.csv'];
        $request = new ImportSpecificPricesRequest($requestData, 1);
        yield 'Delete once specific price' => [$request, $response];

        // With category price
        $response = new ImportSpecificPricesResponse();
        $requestData = $this->scenarioData['with_category.csv'];
        $request = new ImportSpecificPricesRequest($requestData, 1);

        yield 'With category price' => [$request, $response];

        // Delete all specific prices of buyer id and categories
        $response = new ImportSpecificPricesResponse();
        $requestData = $this->scenarioData['delete_all_and_category.csv'];
        $request = new ImportSpecificPricesRequest($requestData, 1);

        yield 'Delete all specific prices of buyer id and categories' => [$request, $response];
    }

    private function makeSpecificPricesService(): SpecificPriceServiceInterface
    {
        return new class ([]) implements SpecificPriceServiceInterface {

            public function __construct(private array $scenarioData)
            {
            }

            public function import(SplFileInfo $csvFile): ImportTask
            {
                $importTask = new ImportTask();
                $importTask->setToProcess($this->scenarioData[$csvFile->getFilename()] ?? []);
                return $importTask;
            }

            public function countLines(SplFileInfo $csvFile): int
            {
                $fileName = $csvFile->getFilename();
                if ($fileName === 'lines.csv') {
                    return 2001;
                }

                return 150;
            }

            public function export(Records $records): string
            {
                return '';
            }

            public function expireSpecificPrice(SpecificPrice $specificPrice): void
            {
            }

            public function importBulk(SplFileInfo $csvFile): Generator
            {
                yield $this->scenarioData[$csvFile->getFilename()] ?? [];
            }
        };
    }

    private function makeScenarioData(): void
    {
        $this->scenarioData['with_blank_sku.csv'] = [
            Record::create('newSku', 'FR60488650921', 20, '12/12/2020'),
            Record::create('', 'FR60488650921', 20, '12/12/2020'),
        ];

        $this->scenarioData['fullValid.csv'] = [
            Record::create('newSku', 'FR60488650921', 20, '12/12/2020'),
            Record::create('withoutPriceSku', 'vatNumber', null, '12/12/2020'),
            Record::create('newSku2', 'FR60488650921', null, null, 'categoryA'),
            Record::create('uniqueSku2', 'unknown', null, null, 'categoryA'), // Error
            Record::create('updateSku2', 'FR60488650921', 20, '12/12/2020'),
            Record::create('updateSku3', 'FR60488650921', null, null, 'categoryA'),
        ];

        $this->scenarioData['delete_once_price.csv'] = [
            Record::create('deleteOnceSku', companyIdentification: 'FR60488650921'),
            Record::create('unknown', companyIdentification: 'unknownVatNumber'),
            Record::create('newSku', 'FR60488650921', 20, '12/12/2020'),
            Record::create('newSku2', 'FR60488650921', 200, '12/12/2020'),
        ];

        $this->scenarioData['invalid_date_end.csv'] = [
            Record::create('newSku', 'FR60488650921', 20, '12/12/2020'),
            Record::create('newSku', 'FR60488650921', 20, ''),
            Record::create('newSku', 'FR60488650921', 20, '2022--12'),
        ];

        $this->scenarioData['buyer_not_exist.csv'] = [
            Record::create('newSku', 'FR60488650921', 20, '12/12/2020'),
            Record::create('newSku', 'unknown', 20, '12/12/2020'),
        ];

        $this->scenarioData['with_category.csv'] = [
            Record::create('newSku', 'FR60488650921', null, null, 'categoryA'),
        ];

        $this->scenarioData['delete_all_and_category.csv'] = [
            Record::create(companyIdentification: 'FR60488650921'),
        ];
    }

    private function makeSpecificPriceRepository(): SpecificPriceRepositoryInterface
    {
        return new class implements SpecificPriceRepositoryInterface {
            public function save(SpecificPrice $specificPrice): ?int
            {
                return 0;
            }

            public function findBySkuAndVatNumber(
                string $sku,
                string $vatNumber,
                int $merchantDistantId
            ): ?SpecificPrice {
                if (str_contains($sku, 'newSku') === true || $vatNumber === 'newVatNumber') {
                    return null;
                }

                return (new SpecificPrice())
                    ->setId(2)
                    ->setSku($sku)
                    ->setVatNumber($vatNumber)
                    ->setPrice(20)
                    ->setMerchant((new Merchant())->setId(2)->setDistantId(2)->setFirstname('merchant_name'))
                    ->setDateEnd(DateTimeImmutable::createFromFormat('!d/m/Y', '12/12/2020'));
            }

            public function delete(int $specificPriceId): void
            {
            }

            /**
             * @inheritDoc
             */
            public function findAllByVatNumber(string $vatNumber): array
            {
                return [];
            }

            /**
             * @inheritDoc
             */
            public function deleteAll(array $specificPrices): void
            {
            }

            public function findAllByMerchantDistantId(int $merchantDistantId): array
            {
                return [];
            }

            public function findAllByVatNumberAndMerchantDistantId(string $vatNumber, int $merchantDistantId): array
            {
                return [];
            }

            public function getExpiredOffers(): array
            {
            }
        };
    }

    private function makeSpecificPrice(
        string $sku = 'newSku',
        ?float $price = 20,
        string $vatNumber = 'FR60488650921'
    ): SpecificPrice {
        return (new SpecificPrice())
            ->setSku($sku)
            ->setPrice($price)
            ->setVatNumber($vatNumber)
            ->setDateEnd(DateTimeImmutable::createFromFormat('!d/m/Y', '12/12/2020'))
            ->setMerchant((new Merchant())->setId(2)->setDistantId(2)->setFirstname('merchant_name'));
    }

    private function makeMerchantRepository(): MerchantRepositoryInterface
    {
        return new class implements MerchantRepositoryInterface {
            public function register(Merchant $merchant): Merchant
            {
                return $merchant;
            }

            public function findByEmail(string $email): ?Merchant
            {
                return null;
            }

            public function findById(int $id): ?Merchant
            {
                if ($id < 1) {
                    return null;
                }

                return (new Merchant())->setId(2)->setDistantId(2)->setFirstname('merchant_name');
            }

            public function update(Merchant $merchant): void
            {
            }

            public function findByVatNumber(string $vatNumber): ?Merchant
            {
                return null;
            }

            public function acceptMerchant(int $id, int $distantId): void
            {
            }

            public function isEmailUnique(string $email): bool
            {
                return false;
            }

            public function findMerchants(
                string $filteredName,
                string $filteredVat,
                string $filteredEmail,
                string $filteredStatus,
                string $filteredCreationDate
            ): array {
                return [];
            }

            public function findByDistantId(int $merchantDistantId): ?Merchant
            {
                if ($merchantDistantId < 1) {
                    return null;
                }

                return (new Merchant())->setId(2)->setDistantId(2)->setFirstname('merchant_name');
            }

            public function findAllActiveMerchants(): array
            {
                return [];
            }

            public function updateReview(Merchant $merchant): void
            {
            }
        };
    }

    private function makeCategoryPriceRepository(): CategoryPriceRepositoryInterface
    {
        return new class implements CategoryPriceRepositoryInterface {

            public function save(CategoryPrice $categoryPrice): int
            {
                return 0;
            }

            public function findByMerchantAndVatNumber(int $merchantId, string $vatNumber): ?CategoryPrice
            {
                return null;
            }

            public function delete(CategoryPrice $categoryPrice): void
            {
            }

            public function findByMerchant(int $merchantId): array
            {
                return [];
            }

            public function deleteByCompanyId(string $companyIdentification, int $merchantId): void
            {
                // TODO: Implement deleteByCompanyId() method.
            }

            public function findByVat(string $vatNumber): array
            {
                return [];
            }
        };
    }

    private function makeOfferRepository(): ObjectProphecy
    {
        $prophet = new Prophet();
        /** @var OfferRepositoryInterface|ObjectProphecy $offerRepo */
        $offerRepo = $prophet->prophesize(OfferRepositoryInterface::class);
        $offerRepo->updateSpecificPrice(
            Argument::type("string"),
            Argument::type("string"),
            Argument::type("float"),
            Argument::type("int")
        )->willReturn(true);
        $offerRepo->deleteSpecificPrice(
            Argument::type("string"),
            Argument::type("string"),
            Argument::type("int")
        )->willReturn(true);

        return $offerRepo;
    }
}
