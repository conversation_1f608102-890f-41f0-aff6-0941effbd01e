<?php

namespace Marketplace\Component\Offer\Domain\Model;

use JsonSerializable;

class Threshold implements JsonSerializable
{

    private ?int $max = null;

    public function __construct(private int $qty, private float $price, private int $min)
    {
    }

    /**
     * @return int
     */
    public function getQty(): int
    {
        return $this->qty;
    }

    /**
     * @return float
     */
    public function getPrice(): float
    {
        return $this->price;
    }

    /**
     * @return int
     */
    public function getMin(): int
    {
        return $this->min;
    }

    /**
     * @return int|null
     */
    public function getMax(): ?int
    {
        return $this->max;
    }

    /**
     * @param int|null $max
     * @return Threshold
     */
    public function setMax(?int $max): Threshold
    {
        $this->max = $max;
        return $this;
    }

    public function jsonSerialize(): mixed
    {
        return get_object_vars($this);
    }
}
