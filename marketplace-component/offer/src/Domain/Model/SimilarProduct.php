<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\Model;

final class SimilarProduct
{
    public function __construct(
        private int $offerId,
        private string $merchantName,
        private float $price,
        private string $currency,
        private string $productLink,
        private bool $cheapest
    ) {
    }

    public function getMerchantName(): string
    {
        return $this->merchantName;
    }

    public function setMerchantName(string $merchantName): self
    {
        $this->merchantName = $merchantName;
        return $this;
    }

    /**
     * @return float
     */
    public function getPrice(): float
    {
        return $this->price;
    }

    /**
     * @param float $price
     * @return SimilarProduct
     */
    public function setPrice(float $price): self
    {
        $this->price = $price;
        return $this;
    }

    /**
     * @return string
     */
    public function getCurrency(): string
    {
        return $this->currency;
    }

    /**
     * @param string $currency
     * @return SimilarProduct
     */
    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    /**
     * @return string
     */
    public function getProductLink(): string
    {
        return $this->productLink;
    }

    /**
     * @param string $productLink
     * @return SimilarProduct
     */
    public function setProductLink(string $productLink): self
    {
        $this->productLink = $productLink;
        return $this;
    }

    public function getOfferId(): int
    {
        return $this->offerId;
    }

    public function setOfferId(int $offerId): self
    {
        $this->offerId = $offerId;
        return $this;
    }

    /**
     * @return bool
     */
    public function isCheapest(): bool
    {
        return $this->cheapest;
    }

    /**
     * @param bool $cheapest
     * @return SimilarProduct
     */
    public function setCheapest(bool $cheapest): self
    {
        $this->cheapest = $cheapest;
        return $this;
    }

    public function computeCheapestPrice(float $smallerPrice): void
    {
        if ($this->price === $smallerPrice) {
            $this->cheapest = true;
        }
    }
}
