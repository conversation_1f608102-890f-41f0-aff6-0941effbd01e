<?php

namespace Marketplace\Component\Offer\Domain\Model;

class LastSeenProduct
{
    public const MAX_LAST_SEEN_PRODUCT = 10;
    private int $offerId;

    private int $categoryId;

    private string $regionCode;

    /**
     * @param int $offerId
     * @param int $categoryId
     * @param string $regionCode
     */
    public function __construct(int $offerId, int $categoryId, string $regionCode)
    {
        $this->offerId = $offerId;
        $this->categoryId = $categoryId;
        $this->regionCode = $regionCode;
    }

    /**
     * @return int
     */
    public function getOfferId(): int
    {
        return $this->offerId;
    }

    /**
     * @return int
     */
    public function getCategoryId(): int
    {
        return $this->categoryId;
    }

    /**
     * @return string
     */
    public function getRegionCode(): string
    {
        return $this->regionCode;
    }
}
