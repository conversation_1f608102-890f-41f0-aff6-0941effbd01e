<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\Model\SpecificPrice;

use DateTimeImmutable;

class CategoryPrice
{
    private ?int $id = null;
    private DateTimeImmutable $updatedAt;

    public function __construct(
        private string $label,
        private int $merchantId,
        private string $vatNumber
    ) {
        $this->updatedAt = new DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;
        return $this;
    }

    public function getMerchantId(): int
    {
        return $this->merchantId;
    }

    public function setMerchantId(int $merchantId): self
    {
        $this->merchantId = $merchantId;
        return $this;
    }

    public function getVatNumber(): string
    {
        return $this->vatNumber;
    }

    public function setVatNumber(string $vatNumber): self
    {
        $this->vatNumber = $vatNumber;
        return $this;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    /**
     * @param DateTimeImmutable $updatedAt
     * @return CategoryPrice
     */
    public function setUpdatedAt(DateTimeImmutable $updatedAt): CategoryPrice
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }
}
