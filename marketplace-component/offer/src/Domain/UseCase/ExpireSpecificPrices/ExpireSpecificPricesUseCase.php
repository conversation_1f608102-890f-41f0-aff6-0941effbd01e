<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\UseCase\ExpireSpecificPrices;

use Marketplace\Component\Offer\Domain\Port\Repository\SpecificPriceRepositoryInterface;
use Marketplace\Component\Offer\Domain\Port\Service\SpecificPriceServiceInterface;
use Marketplace\Component\Offer\Domain\Presenter\ExpireSpecificPricesPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\ExpireSpecificPrices\DTO\ExpireSpecificPricesResponse;

final class ExpireSpecificPricesUseCase
{

    public function __construct(
        private SpecificPriceRepositoryInterface $specificPriceRepository,
        private SpecificPriceServiceInterface $specificPriceService
    ) {
    }

    public function execute(ExpireSpecificPricesPresenterInterface $presenter): void
    {
        $specificPrices = $this->specificPriceRepository->getExpiredOffers();

        foreach ($specificPrices as $specificPrice) {
            $this->specificPriceService->expireSpecificPrice($specificPrice);
        }

        $response = new ExpireSpecificPricesResponse($specificPrices);
        $presenter->present($response);
    }
}
