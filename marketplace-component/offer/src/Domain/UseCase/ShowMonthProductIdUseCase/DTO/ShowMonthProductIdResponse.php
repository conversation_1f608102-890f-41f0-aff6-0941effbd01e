<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\UseCase\ShowMonthProductIdUseCase\DTO;

final class ShowMonthProductIdResponse
{
    private array $monthProductIds;

    /**
     * @return array
     */
    public function getMonthProductIds(): array
    {
        return $this->monthProductIds;
    }

    /**
     * @param array $monthProductIds
     * @return $this
     */
    public function setMonthProductIds(array $monthProductIds): self
    {
        $this->monthProductIds = $monthProductIds;
        return $this;
    }
}
