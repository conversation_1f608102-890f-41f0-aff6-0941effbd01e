<?php

namespace Marketplace\Component\Offer\Domain\UseCase\SearchSuggestions\DTO;

class SearchSuggestionsRequest
{

    /**
     * @param string|null $query
     * @param array $filterQuery
     */
    public function __construct(
        private ?string $query = null,
        private array $filterQuery = []
    ) {
    }

    /**
     * @return string|null
     */
    public function getQuery(): ?string
    {
        return $this->query;
    }

    /**
     * @return array
     */
    public function getFilterQuery(): array
    {
        return $this->filterQuery;
    }
}
