<?php

namespace Marketplace\Component\Offer\Domain\UseCase\SearchSuggestions\DTO;

use Marketplace\Component\Offer\Domain\Model\SuggestionSearchResult;

class SearchSuggestionsResponse
{
    /**
     * SearchOffersResponse constructor.
     * @param SuggestionSearchResult $searchResult
     */
    public function __construct(
        private SuggestionSearchResult $searchResult
    ) {
    }

    /**
     * @return SuggestionSearchResult
     */
    public function getSuggestions(): SuggestionSearchResult
    {
        return $this->searchResult;
    }
}
