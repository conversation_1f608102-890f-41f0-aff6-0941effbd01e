<?php

namespace Marketplace\Component\Offer\Domain\UseCase\SearchSuggestions;

use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Marketplace\Component\Offer\Domain\Presenter\SearchSuggestionsPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\SearchSuggestions\DTO\SearchSuggestionsRequest;
use Marketplace\Component\Offer\Domain\UseCase\SearchSuggestions\DTO\SearchSuggestionsResponse;

class SearchSuggestionsUseCase
{
    /**
     * @param OfferRepositoryInterface $offerRepository
     */
    public function __construct(private OfferRepositoryInterface $offerRepository)
    {
    }

    /**
     * @param SearchSuggestionsRequest $request
     * @param SearchSuggestionsPresenterInterface $presenter
     */
    public function execute(
        SearchSuggestionsRequest $request,
        SearchSuggestionsPresenterInterface $presenter,
    ): void {


        $offersResult = $this->offerRepository->findSuggestions(
            $request->getQuery(),
            $request->getFilterQuery()
        );
        $response = new SearchSuggestionsResponse(
            $offersResult
        );

        $presenter->present($response);
    }
}
