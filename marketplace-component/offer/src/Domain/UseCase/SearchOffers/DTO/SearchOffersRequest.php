<?php

namespace Marketplace\Component\Offer\Domain\UseCase\SearchOffers\DTO;

use Marketplace\Component\Offer\Domain\Model\SearchFilterQuery;

class SearchOffersRequest
{
    public const DEFAULT_PAGE = 1;
    public const DEFAULT_OFFER_PER_PAGE = 20;

    /**
     * SearchOffersRequest constructor.
     * @param string|null $query
     * @param array $filterQuery
     * @param int $hitsPerPage
     * @param int $page
     * @param string|null $sortField
     * @param string|null $sortDir
     */
    public function __construct(
        private ?string $query = null,
        private array $filterQuery = [],
        private int $hitsPerPage = self::DEFAULT_OFFER_PER_PAGE,
        private int $page = self::DEFAULT_PAGE,
        private ?string $sortField = null,
        private ?string $sortDir = null
    ) {
        if ($sortDir !== null) {
            $this->sortDir = $this->checkSortDirValue($sortDir);
        }
    }

    /**
     * @return string|null
     */
    public function getQuery(): ?string
    {
        return $this->query;
    }

    /**
     * @return array
     */
    public function getFilterQuery(): array
    {
        return $this->filterQuery;
    }

    /**
     * @return int
     */
    public function getHitsPerPage(): int
    {
        return $this->hitsPerPage;
    }

    /**
     * @return int
     */
    public function getPage(): int
    {
        return $this->page;
    }

    /**
     * @return string|null
     */
    public function getSortField(): ?string
    {
        return $this->sortField;
    }

    /**
     * @return string|null
     */
    public function getSortDir(): ?string
    {
        return $this->sortDir;
    }

    /**
     * @param string $sortOrder
     * @return string
     */
    private function checkSortDirValue(string $sortOrder): string
    {
        $sortOrder = strtolower($sortOrder);
        if ($sortOrder === 'asc' || $sortOrder === 'desc') {
            return $sortOrder;
        }
        return 'asc';
    }
}
