<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\UseCase\ShowSameLevelCategoriesUseCase;

use Marketplace\Component\Offer\Domain\Model\Offer;
use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Marketplace\Component\Offer\Domain\Presenter\ShowSameLevelCategoriesPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\ShowSameLevelCategoriesUseCase\DTO\ShowSameLevelCategoriesRequest;
use Marketplace\Component\Offer\Domain\UseCase\ShowSameLevelCategoriesUseCase\DTO\ShowSameLevelCategoriesResponse;
use Marketplace\Component\Offer\Domain\Port\Service\CategoryAdapterInterface;

final class ShowSameLevelCategoriesUseCase
{

    public function __construct(private OfferRepositoryInterface $offerRepository, private CategoryAdapterInterface $categoryAdapter)
    {
    }

    public function execute(ShowSameLevelCategoriesRequest $request, ShowSameLevelCategoriesPresenterInterface $presenter): void
    {
        $response = new ShowSameLevelCategoriesResponse();
        $offerId = $request->getOfferId();
        $offer = $this->offerRepository->find($offerId);
        if (!$offer instanceof Offer) {
            $response->getNotification()->addError('offerId', 'this offer does not exist. id :' . $offerId);
            $presenter->present($response);
            return;
        }
        $categories = $offer->getCategories();
        $sameLevelCategories = $this->categoryAdapter->getSameLevelCategories(end($categories)->getId());
        $response->setCategories($sameLevelCategories);
        $presenter->present($response);
    }
}
