<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\UseCase\ImportSpecificPrices;

use Exception;
use Marketplace\Component\Offer\Domain\Model\SpecificPrice\CategoryPrice;
use Marketplace\Component\Offer\Domain\Model\SpecificPrice\ImportTask;
use Marketplace\Component\Offer\Domain\Model\SpecificPrice\Record;
use Marketplace\Component\Offer\Domain\Model\SpecificPrice\SpecificPrice;
use Marketplace\Component\Offer\Domain\Port\Repository\CategoryPriceRepositoryInterface;
use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Marketplace\Component\Offer\Domain\Port\Repository\SpecificPriceRepositoryInterface;
use Marketplace\Component\Offer\Domain\Presenter\ImportSpecificPricesPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\ImportSpecificPrices\DTO\ImportSpecificPricesRequest;
use Marketplace\Component\Offer\Domain\UseCase\ImportSpecificPrices\DTO\ImportSpecificPricesResponse;
use Marketplace\Component\User\Domain\Model\Merchant;
use Marketplace\Component\User\Domain\Port\Repository\MerchantRepositoryInterface;
use Psr\Log\LoggerInterface;

class ImportSpecificPricesUseCase
{
    private const CASE_DELETE = "DELETE";
    private const CASE_DELETE_ALL = "DELETE_ALL";
    private const CASE_UPDATE_SPECIFIC = "UPDATE_SPECIFIC";
    private const CASE_UPDATE_CATEGORY = "UPDATE_CATEGORY";

    public function __construct(
        private SpecificPriceRepositoryInterface $specificPriceRepository,
        private MerchantRepositoryInterface $merchantRepository,
        private CategoryPriceRepositoryInterface $categoryPriceRepository,
        private OfferRepositoryInterface $offerRepository,
        private LoggerInterface $logger
    ) {
    }

    /**
     * @param ImportSpecificPricesRequest $request
     * @param ImportSpecificPricesPresenterInterface $presenter
     * @throws Exception
     */
    public function execute(
        ImportSpecificPricesRequest $request,
        ImportSpecificPricesPresenterInterface $presenter
    ): void {
        $response = new ImportSpecificPricesResponse();
        $merchant = $this->merchantRepository->findByDistantId($request->merchantId);

        if (!$merchant instanceof Merchant) {
            $this->setUpPresenter($response, $presenter, 'merchant', 'import.specific_prices.error.merchant');
            return;
        }
        $this->logger->info(
            sprintf(
                "[SPECIFIC_PRICE] received one bulk lines: %d/%d",
                $request->bulk[0]->line,
                $request->bulk[count($request->bulk) - 1]->line
            )
        );
        $this->buildSpecificPrices($request->bulk, $merchant, $response);

        $presenter->present($response);
    }

    private function addNotification(ImportTask $importTask, ImportSpecificPricesResponse $response)
    {
        $notifications = $response->getNotification();
    }

    private function setUpPresenter(
        ImportSpecificPricesResponse $response,
        ImportSpecificPricesPresenterInterface $presenter,
        ?string $field = null,
        ?string $message = null,
    ) {
        $response->success = $message === null;
        if ($message !== null) {
            $response->getNotification()->addError($field, $message);
        }
        $presenter->present($response);
    }

    /**
     * @param Record[] $records
     * @return SpecificPrice[]
     * @throws Exception
     */
    private function buildSpecificPrices(
        array $records,
        Merchant $merchant,
        ImportSpecificPricesResponse $response
    ): array {
        $specificPrices = [];
        $allLinesSuccess = true;
        foreach ($records as $record) {
            $lineSuccess = $this->processRecord($record, $merchant, $specificPrices);
            if ($lineSuccess === false && $allLinesSuccess) {
                $allLinesSuccess = false;
                $response->getNotification()
                    ->addError('csvFile', 'import.specific_prices.warning.line_error');
            }
        }
        return $specificPrices;
    }

    private function processRecord(Record $record, Merchant $merchant, array &$specificPrices): bool
    {
        $case = $this->getImportCase($record);
        $this->logger->debug(
            sprintf("[SPECIFIC_PRICE] case: %s", $case ?? 'unknown'),
            ["record" => json_encode($record->toArray())]
        );
        switch ($case) {
            case self::CASE_DELETE:
                $this->deleteSpecificPrice($record, $merchant, $specificPrices);
                break;
            case self::CASE_DELETE_ALL:
                $this->deleteCategory($record, $merchant, $specificPrices);
                break;
            case self::CASE_UPDATE_CATEGORY:
                $this->updateCategoryPrice(
                    $record->categoryName,
                    $merchant->getDistantId(),
                    $record->companyIdentification
                );
                break;
            case self::CASE_UPDATE_SPECIFIC:
                $this->updateSpecificPrice($record, $merchant, $specificPrices);
                break;
            default:
                break;
        }
        return $case !== null;
    }

    private function getImportCase(Record $record): ?string
    {
        if ($record->isEmpty()) {
            //DELETE
            if ($record->canBeDeleted()) {
                return self::CASE_DELETE;
            }
            if ($record->canAllDelete()) {
                return self::CASE_DELETE_ALL;
            }
        } else {
            //UPDATE
            if ($record->hasCategoryPrice()) {
                return self::CASE_UPDATE_CATEGORY;
            }

            if ($record->isInvalid() || $record->isDateEndInvalid()) {
                return null;
            }
            return self::CASE_UPDATE_SPECIFIC;
        }
        return null;
    }

    private function updateCategoryPrice(string $categoryName, ?int $merchantId, string $vatNumber): void
    {
        if (is_null($merchantId)) {
            return;
        }
        $this->logger->debug(
            sprintf("updateCategoryPrice: company:%s, merchant:%d", $vatNumber, $merchantId)
        );


        $categoryPrice = $this->categoryPriceRepository
            ->findByMerchantAndVatNumber($merchantId, $vatNumber);

        if ($categoryPrice instanceof CategoryPrice) {
            $categoryPrice->setLabel($categoryName);
        } else {
            $categoryPrice = new CategoryPrice(
                label: $categoryName,
                merchantId: $merchantId,
                vatNumber: $vatNumber
            );
        }

        $this->categoryPriceRepository->save($categoryPrice);
    }

    private function deleteSpecificPrice(Record $record, Merchant $merchant, array &$specificPrices): void
    {
        $merchantDistantId = $merchant->getDistantId();
        if ($merchantDistantId === null) {
            return;
        }

        $this->offerRepository->deleteSpecificPrice($record->sku, $record->companyIdentification, $merchantDistantId);

        $specificPrice = $this->specificPriceRepository
            ->findBySkuAndVatNumber($record->sku, $record->companyIdentification, $merchantDistantId);

        if (!$specificPrice instanceof SpecificPrice) {
            return;
        }
        /** @var int $id */
        $id = $specificPrice->getId();
        $this->specificPriceRepository->delete($id);


        $this->deleteSpecificPriceOfArray($this->specificPriceUniqueKey($record), $specificPrices);
    }

    private function deleteCategory(Record $record, Merchant $merchant, array &$specificPrices): void
    {
        $merchantId = $merchant->getDistantId();

        if (is_null($merchantId)) {
            return;
        }

        $this->logger->debug(
            sprintf("deleteCategory: company:%s, merchant:%d", $record->companyIdentification, $merchantId)
        );
        //delete db
        $this->categoryPriceRepository->deleteByCompanyId($record->companyIdentification, $merchantId);
    }

    private function updateSpecificPrice(Record $record, Merchant $merchant, array &$specificPrices): void
    {
        $merchantDistantId = $merchant->getDistantId();
        if ($merchantDistantId === null) {
            return;
        }

        $this->logger->debug(
            sprintf("updateSpecificPrice: company:%s, merchant:%d", $record->companyIdentification, $record->sku)
        );

        $price = $record->price;
        if ($price === null) {
            return;
        }
        $success = $this->offerRepository->updateSpecificPrice(
            $record->companyIdentification,
            $record->sku,
            $price,
            $merchantDistantId
        );

        if ($success) {
            $specificPrice = $this->specificPriceRepository
                ->findBySkuAndVatNumber($record->sku, $record->companyIdentification, $merchantDistantId);

            if ($specificPrice !== null) {
                $specificPrice->setDateEnd($record->getDateEnd());
            } else {
                $specificPrice = (new SpecificPrice())
                    ->setSku($record->sku)
                    ->setDateEnd($record->getDateEnd())
                    ->setMerchant($merchant)
                    ->setVatNumber($record->companyIdentification);
            }

            $specificPrice->setPrice($record->price);

            $this->specificPriceRepository->save($specificPrice);
            $specificPrices[$this->specificPriceUniqueKey($record)] = $specificPrice;
        }
    }


    private function specificPriceUniqueKey(Record $record): string
    {
        return $record->sku . '#' . $record->companyIdentification;
    }

    private function deleteSpecificPriceOfArray(string $uniqueKey, array &$specificPrices): void
    {
        if (array_key_exists($uniqueKey, $specificPrices)) {
            unset($specificPrices[$uniqueKey]);
        }
    }
}
