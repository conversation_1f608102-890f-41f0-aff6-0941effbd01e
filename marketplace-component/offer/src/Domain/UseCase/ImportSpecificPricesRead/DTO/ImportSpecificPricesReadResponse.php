<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\UseCase\ImportSpecificPricesRead\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Error\NotificationTrait;
use Marketplace\Component\Offer\Domain\Model\SpecificPrice\SpecificPrice;

class ImportSpecificPricesReadResponse
{
    use NotificationTrait;

    public bool $success = true;

    /**
     * @var SpecificPrice[]
     */
    public array $specificPrices = [];
}
