<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\UseCase\LastSeenProduct\ShowLastSeenProductsUseCase;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetRegionServiceInterface;
use Marketplace\Component\Offer\Domain\Model\LastSeenProduct;
use Marketplace\Component\Offer\Domain\Model\Offer;
use Marketplace\Component\Offer\Domain\Port\Repository\LastSeenProductRepositoryInterface;
use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Marketplace\Component\Offer\Domain\Presenter\ShowLastSeenProductsPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\LastSeenProduct\ShowLastSeenProductsUseCase\DTO\ShowLastSeenProductsRequest;
use Marketplace\Component\Offer\Domain\UseCase\LastSeenProduct\ShowLastSeenProductsUseCase\DTO\ShowLastSeenProductsResponse;

final class ShowLastSeenProductsUseCase
{

    public function __construct(
        private OfferRepositoryInterface $offerRepository,
        private LastSeenProductRepositoryInterface $lastSeenProductRepository,
        private GetRegionServiceInterface $getRegionService
    ) {
    }

    public function execute(ShowLastSeenProductsRequest $request, ShowLastSeenProductsPresenterInterface $presenter): void
    {
        $response = new ShowLastSeenProductsResponse();
        $offerId = $request->getOfferId();
        $offer = $this->offerRepository->find($offerId);
        if (!$offer instanceof Offer) {
            $response->getNotification()->addError('offerId', 'This offer does not exist. id :' . $offerId);
            $presenter->present($response);
            return;
        }
        $category = $offer->getLowLevelCategory();
        $regionCode = $this->getRegionService->getRegion();
        $lastSeenProducts = $this->lastSeenProductRepository->getLastSeenOffers($category->getId(), $regionCode);
        $lastSeenOffers = $this->offerRepository->searchOffersByIds(array_map(function (LastSeenProduct $lastSeenProduct) {
            return $lastSeenProduct->getOfferId();
        }, $lastSeenProducts));
        $response->setLastSeenProduct($lastSeenOffers);
        $presenter->present($response);
    }
}
