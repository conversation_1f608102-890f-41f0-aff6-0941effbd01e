<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\UseCase\LastSeenProduct\CleanLastSeenProductUseCase\DTO;

final class CleanLastSeenProductResponse
{
    private bool $success = false;

    /**
     * @return bool
     */
    public function isSuccess(): bool
    {
        return $this->success;
    }

    /**
     * @param bool $success
     * @return $this
     */
    public function setSuccess(bool $success): self
    {
        $this->success = $success;
        return $this;
    }
}
