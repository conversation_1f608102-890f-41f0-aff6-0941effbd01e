<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\UseCase\ShowMonthProductUseCase\DTO;

use Marketplace\Component\Offer\Domain\Model\OfferSearchResult;

final class ShowMonthProductResponse
{
    private array $result;

    /**
     * @return OfferSearchResult[]
     */
    public function getResult(): array
    {
        return $this->result;
    }

    /**
     * @param OfferSearchResult[] $result
     * @return $this
     */
    public function setResult(array $result): self
    {
        $this->result = $result;
        return $this;
    }
}
