<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\UseCase\ShowMonthProductUseCase;

use Marketplace\Component\Offer\Domain\Model\Merchant;
use Marketplace\Component\Offer\Domain\Model\MonthProduct;
use Marketplace\Component\Offer\Domain\Port\Repository\MonthProductRepositoryInterface;
use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Marketplace\Component\Offer\Domain\Presenter\ShowMonthProductPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\ShowMonthProductUseCase\DTO\ShowMonthProductRequest;
use Marketplace\Component\Offer\Domain\UseCase\ShowMonthProductUseCase\DTO\ShowMonthProductResponse;

final class ShowMonthProductUseCase
{

    public function __construct(
        private MonthProductRepositoryInterface $monthProductRepository,
        private OfferRepositoryInterface $offerRepository
    ) {
    }

    public function execute(ShowMonthProductRequest $request, ShowMonthProductPresenterInterface $presenter): void
    {
        $response = new ShowMonthProductResponse();
        //get offer ID month's product
        $ids = $this->monthProductRepository->getMonthProductIds($request->getRegionCode(), MonthProduct::OFFER_LIMIT);
        //get offer
        $offers = $this->offerRepository->searchOffersByIds($ids, MonthProduct::OFFER_FETCH_LIMIT);
        $offers = array_filter(
            $offers,
            function ($offer) {
                return $offer->getPrice() > 0;
            }
        );

		    $offers = array_map(function ($offer) {
				    if($offer->getMerchant() instanceof Merchant) {
						    $offer->setCountVotes($offer->getMerchant()->getReviewCount());
						    $offer->setRating($offer->getMerchant()->getReviewScore());
				    }

				    return $offer;
		    }, $offers);

        $response->setResult($offers);
        $presenter->present($response);
    }
}
