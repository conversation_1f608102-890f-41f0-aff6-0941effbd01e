<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\UseCase\SearchMerchant;

use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Marketplace\Component\Offer\Domain\Presenter\SearchMerchantPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\SearchMerchant\DTO\SearchMerchantResponse;

final class SearchMerchantUseCase
{
    /**
     * @param OfferRepositoryInterface $offerRepository
     */
    public function __construct(private OfferRepositoryInterface $offerRepository)
    {
    }

    public function execute(SearchMerchantPresenterInterface $presenter): void
    {
        $merchantResult = $this->offerRepository->findmerchants();
        $response = new SearchMerchantResponse($merchantResult);
        $presenter->present($response);
    }
}
