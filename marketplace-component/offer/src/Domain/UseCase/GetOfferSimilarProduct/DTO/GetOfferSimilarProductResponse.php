<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\UseCase\GetOfferSimilarProduct\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Error\NotificationTrait;
use Marketplace\Component\Offer\Domain\Model\SimilarProduct;

final class GetOfferSimilarProductResponse
{
    use NotificationTrait;

    /**
     * @var SimilarProduct[]
     */
    public array $similarProducts = [];
}
