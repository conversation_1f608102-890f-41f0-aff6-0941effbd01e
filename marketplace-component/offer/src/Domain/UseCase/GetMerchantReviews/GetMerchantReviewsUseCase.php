<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\UseCase\GetMerchantReviews;

use Marketplace\Component\Offer\Domain\Model\MerchantScore;
use Marketplace\Component\Offer\Domain\Port\Service\GetMerchantReviewsServiceInterface;
use Marketplace\Component\Offer\Domain\Presenter\GetMerchantReviewsPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\GetMerchantReviews\DTO\GetMerchantReviewsRequest;
use Marketplace\Component\Offer\Domain\UseCase\GetMerchantReviews\DTO\GetMerchantReviewsResponse;

class GetMerchantReviewsUseCase
{
    public function __construct(private GetMerchantReviewsServiceInterface $getMerchantReviewsService)
    {
    }

    public function execute(GetMerchantReviewsRequest $request, GetMerchantReviewsPresenterInterface $presenter)
    {
        $response = new GetMerchantReviewsResponse();

        $merchantScore = new MerchantScore();
        $averageScore = $this->getMerchantReviewsService->getMerchantAverageScore($request->merchantId);
        $reviews = $this->getMerchantReviewsService->getMerchantReviews($request->merchantId);

        $merchantScore->setAverageScore($averageScore);
        $merchantScore->setMerchantReviews($reviews);

        $response->setMerchantScore($merchantScore);

        $presenter->present($response);
    }
}
