<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Domain\UseCase\DeleteCategoryPrice;

use Marketplace\Component\Offer\Domain\Model\SpecificPrice\CategoryPrice;
use Marketplace\Component\Offer\Domain\Port\Repository\CategoryPriceRepositoryInterface;
use Marketplace\Component\Offer\Domain\Presenter\DeleteCategoryPricePresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\DeleteCategoryPrice\DTO\DeleteCategoryPriceRequest;
use Marketplace\Component\Offer\Domain\UseCase\DeleteCategoryPrice\DTO\DeleteCategoryPriceResponse;
use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\Merchant;
use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\MerchantRepositoryInterface;

class DeleteCategoryPriceUseCase
{
    public function __construct(
        private CompanyRepositoryInterface $companyRepository,
        private MerchantRepositoryInterface $merchantRepository,
        private CategoryPriceRepositoryInterface $categoryPriceRepository
    ) {
    }

    public function execute(DeleteCategoryPriceRequest $request, DeleteCategoryPricePresenterInterface $presenter)
    {
        $response = new DeleteCategoryPriceResponse();

        $company = $this->companyRepository->findByVatNumber($request->vatNumber);
        if (!$company instanceof Company) {
            $this->setUpPresenter($response, $presenter, 'company', 'delete.category_price.error.company');
            return;
        }

        $merchant = $this->merchantRepository->findByDistantId($request->merchantId);
        if (!$merchant instanceof Merchant) {
            $this->setUpPresenter($response, $presenter, 'merchant', 'delete.category_price.error.merchant');
            return;
        }

        $categoryPrice = $this->categoryPriceRepository->findByMerchantAndVatNumber(
            $request->merchantId,
            $request->vatNumber
        );
        if (!$categoryPrice instanceof CategoryPrice) {
            $this->setUpPresenter(
                $response,
                $presenter,
                'category_price',
                'delete.category_price.error.category_price'
            );
            return;
        }
        $this->categoryPriceRepository->delete($categoryPrice);
        $this->setUpPresenter($response, $presenter);
    }

    private function setUpPresenter(
        DeleteCategoryPriceResponse $response,
        DeleteCategoryPricePresenterInterface $presenter,
        ?string $field = null,
        ?string $message = null
    ) {
        $response->success = $message === null;
        if ($message !== null) {
            $response->getNotification()->addError($field, $message);
        }
        $presenter->present($response);
    }
}
