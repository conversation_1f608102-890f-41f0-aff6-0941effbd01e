<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Presentation\ViewModel;

use Marketplace\Component\Offer\Domain\Model\Facet\Facet;
use Marketplace\Component\Offer\Domain\Model\MerchantSearchResult;
use Marketplace\Component\Offer\Domain\Model\SearchResult;
use Symfony\Contracts\Translation\TranslatorInterface;

final class SearchMerchantViewModel
{
    private MerchantSearchResult $searchResult;

    /**
     * @return MerchantSearchResult
     */
    public function getSearchResult(): MerchantSearchResult
    {
        return $this->searchResult;
    }

    /**
     * @param MerchantSearchResult $searchResult
     * @return SearchMerchantViewModel
     */
    public function setSearchResult(MerchantSearchResult $searchResult): SearchMerchantViewModel
    {
        $this->searchResult = $searchResult;
        return $this;
    }
}
