<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Presentation\Presenter;

use Marketplace\Component\CleanArchiCore\Domain\Error\Error;
use Marketplace\Component\Offer\Domain\Presenter\ImportSpecificPricesPresenterInterface;
use Marketplace\Component\Offer\Domain\Presenter\ImportSpecificPricesReadPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\ImportSpecificPrices\DTO\ImportSpecificPricesResponse;
use Marketplace\Component\Offer\Domain\UseCase\ImportSpecificPricesRead\DTO\ImportSpecificPricesReadResponse;
use Marketplace\Component\Offer\Presentation\ViewModel\ImportSpecificPriceViewModel;

class ImportSpecificPriceReadPresenter implements ImportSpecificPricesReadPresenterInterface
{
    private ImportSpecificPriceViewModel $viewModel;

    public function present(ImportSpecificPricesReadResponse $response): void
    {
        $this->viewModel = new ImportSpecificPriceViewModel();
        $this->viewModel->specificPrices = $response->specificPrices;
        $this->viewModel->success = $response->success;
        $this->viewModel->errors = array_map(
            fn (Error $error) => $error->getMessage(),
            $response->getNotification()->getErrors()
        );
    }

    public function viewModel(): ImportSpecificPriceViewModel
    {
        return $this->viewModel;
    }
}
