<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Presentation\Presenter;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CustomAttributesInterface;
use Marketplace\Component\CleanArchiCore\Infrastructure\Service\GetRegionService;
use Marketplace\Component\Offer\Domain\Presenter\ShowOfferPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\ShowOffer\DTO\ShowOfferResponse;
use Marketplace\Component\Offer\Presentation\ViewModel\ShowOfferViewModel;
use Symfony\Contracts\Translation\TranslatorInterface;

class ShowOfferPresenter implements ShowOfferPresenterInterface
{
    private ShowOfferViewModel $viewModel;

    public function __construct(
        private TranslatorInterface $translator,
        private readonly CustomAttributesInterface $customAttributes,
        private TechnicalDataRenderer $technicalDataRenderer,
        private GetRegionService $getRegionService
    ) {
        $this->viewModel = new ShowOfferViewModel($this->translator);
    }

    public function present(ShowOfferResponse $response): void
    {
        $this->viewModel->notFound = $response->getOffer() === null;
        $this->technicalDataRenderer->render($response->getOffer());
        $this->viewModel->setOffer($response->getOffer());
        $this->viewModel->setReviews($response->getReviews());
        $this->viewModel->setScoreAverageReviews($response->getAverageScore());
        $this->viewModel->setCountReviews($response->getCountReviews());
        $this->viewModel->customAttributeAvailability = $this->customAttributes->getAvailability();
        $this->viewModel->isUserConnectedInDomTom = $this->getRegionService->isReunionOrAntilles();
        $this->viewModel->setRedirectToCategory($response->getRedirectToCategory());
    }

    public function getViewModel(): ShowOfferViewModel
    {
        return $this->viewModel;
    }
}
