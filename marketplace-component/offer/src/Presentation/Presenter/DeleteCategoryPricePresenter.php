<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Presentation\Presenter;

use Marketplace\Component\Offer\Domain\Presenter\DeleteCategoryPricePresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\DeleteCategoryPrice\DTO\DeleteCategoryPriceResponse;
use Marketplace\Component\Offer\Presentation\ViewModel\DeleteCategoryPriceViewModel;

class DeleteCategoryPricePresenter implements DeleteCategoryPricePresenterInterface
{
    private DeleteCategoryPriceViewModel $viewModel;

    public function present(DeleteCategoryPriceResponse $response): void
    {
        $this->viewModel = new DeleteCategoryPriceViewModel();
        $this->viewModel->success = $response->success;
    }

    public function viewModel(): DeleteCategoryPriceViewModel
    {
        return $this->viewModel;
    }
}
