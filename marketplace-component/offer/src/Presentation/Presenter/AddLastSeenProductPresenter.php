<?php

namespace Marketplace\Component\Offer\Presentation\Presenter;

use Marketplace\Component\CleanArchiCore\Domain\Error\Error;
use Marketplace\Component\Offer\Domain\Presenter\AddLastSeenProductPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\LastSeenProduct\AddLastSeenProduct\DTO\AddLastSeenProductResponse;
use Marketplace\Component\Offer\Presentation\ViewModel\AddLastSeenProductViewModel;

class AddLastSeenProductPresenter implements AddLastSeenProductPresenterInterface
{
    private AddLastSeenProductViewModel $viewModel;

    public function __construct()
    {
        $this->viewModel = new AddLastSeenProductViewModel();
    }


    public function present(AddLastSeenProductResponse $response): void
    {
        $this->viewModel->success = $response->isSuccess();
        $this->viewModel->errors = array_map(
            fn (Error $error) => $error->getMessage(),
            $response->getNotification()->getErrors()
        );
    }

    public function viewModel(): AddLastSeenProductViewModel
    {
        return $this->viewModel;
    }
}
