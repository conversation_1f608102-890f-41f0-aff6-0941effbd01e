<?php

namespace Marketplace\Component\Offer\Presentation\Presenter;

use Marketplace\Component\Offer\Domain\Presenter\SearchOffersPresenterInterface;
use Marketplace\Component\Offer\Presentation\ViewModel\SearchOffersViewModel;
use Marketplace\Component\Offer\Domain\UseCase\SearchOffers\DTO\SearchOffersResponse;

class SearchOffersPresenter implements SearchOffersPresenterInterface
{
    private SearchOffersViewModel $viewModel;

    /**
     * SearchOffersPresenter constructor.
     */
    public function __construct()
    {
    }

    /**
     * @param SearchOffersResponse $response
     */
    public function present(SearchOffersResponse $response): void
    {
        $this->viewModel = new SearchOffersViewModel();
        $this->viewModel->setSearchResult($response->getOffers());
    }

    /**
     * @return SearchOffersViewModel
     */
    public function viewModel(): SearchOffersViewModel
    {
        return $this->viewModel;
    }
}
