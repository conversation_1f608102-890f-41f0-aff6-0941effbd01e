<?php

namespace Marketplace\Component\Offer\Presentation\Presenter;

use Marketplace\Component\Offer\Domain\Presenter\OfferUrlListPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\OfferUrlList\DTO\OfferUrlListResponse;
use Marketplace\Component\Offer\Presentation\ViewModel\OfferUrlListViewModel;

class OfferUrlListPresenter implements OfferUrlListPresenterInterface
{
    private OfferUrlListViewModel $viewModel;

    public function __construct(private string $siteUrl)
    {
    }

    public function present(OfferUrlListResponse $response): void
    {
        $this->viewModel = new OfferUrlListViewModel($response->getOffer(), $this->siteUrl);
    }

    /**
     * @return OfferUrlListViewModel
     */
    public function getViewModel(): OfferUrlListViewModel
    {
        return $this->viewModel;
    }
}
