<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Presentation\View;

use Marketplace\Component\CleanArchiCore\Presentation\ViewModel\ViewModelInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Serializer\SerializerInterface;

class View<PERSON>son implements ViewJsonInterface
{
    public function __construct(private SerializerInterface $serializer)
    {
    }

    public function send(ViewModelInterface $viewModel): JsonResponse
    {
        return JsonResponse::fromJsonString(
            $this->serializer->serialize($viewModel, 'json'),
        );
    }
}
