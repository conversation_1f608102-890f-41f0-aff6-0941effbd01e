<?php

namespace Marketplace\Component\Offer\Presentation\View;

use Marketplace\Component\Offer\Presentation\ViewModel\GetMerchantReviewsViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class GetMerchantReviewsView
{
    public function __construct(private SerializerInterface $serializer)
    {
    }

    public function generateJson(GetMerchantReviewsViewModel $viewModel): Response
    {
        return JsonResponse::fromJsonString(
            $this->serializer->serialize($viewModel, 'json'),
        );
    }
}
