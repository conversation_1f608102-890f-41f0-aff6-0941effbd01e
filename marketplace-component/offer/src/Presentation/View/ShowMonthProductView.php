<?php

namespace Marketplace\Component\Offer\Presentation\View;

use Marketplace\Component\Offer\Presentation\ViewModel\ShowMonthProductViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Serializer\SerializerInterface;

class ShowMonthProductView
{
    /**
     * AcceptMerchantView constructor.
     * @param SerializerInterface $serializer
     */
    public function __construct(private SerializerInterface $serializer)
    {
    }
    /**
     * @param ShowMonthProductViewModel $viewModel
     * @return JsonResponse
     */
    public function generateView(ShowMonthProductViewModel $viewModel): JsonResponse
    {
        return JsonResponse::fromJsonString($this->serializer->serialize(
            $viewModel,
            'json'
        ));
    }
}
