<?php

namespace Marketplace\Component\Offer\Infrastructure\Adapter\Service;

use Marketplace\Component\CleanArchiCore\Utils\Str;
use Open\Izberg\Service\CategoryServiceInterface;
use Marketplace\Component\Offer\Domain\Port\Service\CategoryAdapterInterface;
use Marketplace\Component\Offer\Domain\Model\Category;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetLocaleInterface;
use Open\Izberg\Model\Category as IzbergCategory;

class CategoryAdapter implements CategoryAdapterInterface
{
    public function __construct(
        private CategoryServiceInterface $categoryService,
        private GetLocaleInterface $getLocaleService,
    ) {
    }
    public function getSameLevelCategories(int $catId): array
    {
        $izbergCategories = $this->categoryService->getSameLevelCategories($catId);

        $result = [];
        foreach ($izbergCategories as $cat) {
            $category = $this->categoryService->find($cat->getId());
            if ($category instanceof IzbergCategory) {
                $result[] =   new Category($category->getId(), $category->getName(), Str::slugify($category->getName()));
            }
        }
        return $result;
    }
}
