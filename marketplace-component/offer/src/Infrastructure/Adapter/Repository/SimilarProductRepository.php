<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Infrastructure\Adapter\Repository;

use Marketplace\Component\Offer\Domain\Model\Offer;
use Marketplace\Component\Offer\Domain\Model\OfferSearchResult;
use Marketplace\Component\Offer\Domain\Model\SimilarProduct;
use Marketplace\Component\Offer\Domain\Port\Repository\SimilarProductRepositoryInterface;
use Marketplace\Component\Offer\Infrastructure\Adapter\Elasticsearch\OfferRepositoryElasticsearch;
use Psr\Log\LoggerInterface;

class SimilarProductRepository implements SimilarProductRepositoryInterface
{
    private const NB_MAX_SIMILAR_OFFERS = 11;

    public function __construct(
        private OfferRepositoryElasticsearch $offerRepository,
        private LoggerInterface $logger
    ) {
    }

    /**
     * {@inheritDoc}
     */
    public function fetchAllByOffer(Offer $offer): array
    {
        $gtin = $offer->getGtin();
        if (null === $gtin) {
            $this->logger->info(sprintf('Get similar offers - No GTIN for offer id %d', $offer->getId()));
            return [];
        }
        $offers = $this->offerRepository->findByGtin($gtin, self::NB_MAX_SIMILAR_OFFERS);

        return array_map(
            fn (OfferSearchResult $offer) => new SimilarProduct(
                offerId: $offer->getId(),
                merchantName: $offer->getMerchant()->getName(),
                price: $offer->getPrice(),
                currency: $offer->getCurrency(),
                productLink: $offer->getUrl(),
                cheapest: false,
            ),
            $offers
        );
    }
}
