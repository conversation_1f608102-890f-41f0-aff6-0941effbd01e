<?php

namespace Marketplace\Component\Offer\Infrastructure\Elasticsearch\Model;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CurrencyServiceInterface;
use Marketplace\Component\CleanArchiCore\Infrastructure\Service\CustomAttributes;
use Marketplace\Component\Offer\Domain\Presenter\RegionServiceInterface;
use Marketplace\Component\Offer\Infrastructure\Adapter\Service\FacetService;

class SearchFilterQuery implements SearchFilterInterface
{
    /**
     * @var SearchFilterInterface[]
     */
    protected array $must = [];

    /**
     * @var SearchFilterInterface[]
     */
    protected array $mustNot = [];

    /**
     * @var SearchFilterInterface[]
     */
    protected array $should = [];


    public function __construct(private CustomAttributes $customAttributes, private CurrencyServiceInterface $currencyService)
    {
    }


    public function build(array $filterQuery, string $region): SearchFilterInterface
    {
        $this->must = [];
        $this->mustNot = [];
        $this->should = [];

        //filter par défaut

        $activeOfferFilters = [
            (new SearchFilter('currency', $this->currencyService->getCurrencyFromRegion($region))),
            (new SearchFilter('status', 'active')),
            (new SearchFilter('product.status', 'active')),
            (new GreaterFilter('price', 0)),
            (new SearchFilter('merchant.status', '10')),
            (new SearchFilter('attributes.' . $this->customAttributes->getDelivery(), $region)),
        ];

        $this->addMust(...$activeOfferFilters);
        $this->addMustNot(...[new SearchFilter('product.keywords', 'Devis')]);

        foreach ($filterQuery as $filterName => $filterValue) {
            $filterName = str_replace(':', '.', $filterName);
            if ($filterName === FacetService::FIELD_PRICE) {
                $this->addMust(self::getRangeFilter($filterName, $filterValue));
                continue;
            }
            //$this->addMust(self::getFacetFilter($filterName, $filterValue, $this->customAttributes));
        }
        return $this;
    }

    public function buildPostFilter(array $filterQuery): SearchFilterInterface
    {
        $this->must = [];
        $this->mustNot = [];
        $this->should = [];
        foreach ($filterQuery as $filterName => $filterValue) {
            $filterName = str_replace(':', '.', $filterName);
            if ($filterName === FacetService::FIELD_PRICE) {
                continue;
            }
            $this->addMust(self::getFacetFilter($filterName, $filterValue, $this->customAttributes, $this->currencyService));
        }
        return $this;
    }

    /**
     * @param SearchFilterInterface ...$mustArray
     * @return $this
     */
    public function addMust(SearchFilterInterface ...$mustArray): self
    {
        $this->must = $this->addClause($this->must, ...$mustArray);
        return $this;
    }

    /**
     * @param SearchFilterInterface ...$mustNotArray
     * @return $this
     */
    public function addMustNot(SearchFilterInterface ...$mustNotArray): self
    {
        $this->mustNot = $this->addClause($this->mustNot, ...$mustNotArray);
        return $this;
    }

    /**
     * @param SearchFilterInterface ...$shouldArray
     * @return $this
     */
    public function addShould(SearchFilterInterface ...$shouldArray): self
    {
        $this->should = $this->addClause($this->should, ...$shouldArray);
        return $this;
    }

    public function isEmpty(): bool
    {
        return empty($this->must) && empty($this->should) && empty($this->mustNot);
    }

    /**
     * @return array[]
     */
    public function query(): array
    {
        $buildFilter = function (array $filters, SearchFilterInterface $filter) {
            $filters[] = $filter->query();
            return $filters;
        };
        $must = array_reduce($this->must, $buildFilter, []);
        $should = array_reduce($this->should, $buildFilter, []);
        $mustNot = array_reduce($this->mustNot, $buildFilter, []);

        $bool = [];
        if (count($must) > 0) {
            $bool['must'] = $must;
        }
        if (count($mustNot) > 0) {
            $bool['must_not'] = $mustNot;
        }
        if (count($should) > 0) {
            $bool['should'] = $should;
            // when there is no "must" element, default value is 1, but otherwise it's 0.
            $bool['minimum_should_match'] = 1;
        }
        return [
            'bool' => $bool
        ];
    }

    /**
     * @param array $operator
     * @param SearchFilterInterface ...$clauseArray
     * @return mixed
     */
    private function addClause(array $operator, SearchFilterInterface ...$clauseArray): array
    {
        return array_merge(
            $operator,
            $clauseArray
        );
    }

    private static function getFacetFilter(
        string $key,
        array $values,
        CustomAttributes $customAttributes,
        CurrencyServiceInterface $currencyService
    ): SearchFilterQuery {
        $facetQuery = new SearchFilterQuery($customAttributes, $currencyService);
        foreach ($values as $value) {
            $facetQuery->addShould(new SearchFilter($key, $value));
        }
        return $facetQuery;
    }

    private static function getRangeFilter(string $key, array $values): RangeFilter
    {
        $facetQuery = new RangeFilter($key, $values);
        return $facetQuery;
    }
}
