<?php

namespace Marketplace\Component\Offer\Infrastructure\Entity;

use Doctrine\ORM\Mapping as ORM;
use Marketplace\Component\Offer\Infrastructure\Adapter\Repository\MonthProductRepository;

#[ORM\Entity(repositoryClass: MonthProductRepository::class)]
class MonthProduct
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'integer')]
    private ?int $offerId = null;

    #[ORM\Column(name: 'region_code', type:"string", length:50, nullable:false)]
    private string $regionCode;

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getOfferId(): ?int
    {
        return $this->offerId;
    }

    /**
     * @param int|null $offerId
     * @return $this
     */
    public function setOfferId(?int $offerId): self
    {
        $this->offerId = $offerId;
        return $this;
    }

    /**
     * @return string
     */
    public function getRegionCode(): string
    {
        return $this->regionCode;
    }

    /**
     * @param string $regionCode
     * @return $this
     */
    public function setRegionCode(string $regionCode): self
    {
        $this->regionCode = $regionCode;
        return $this;
    }
}
