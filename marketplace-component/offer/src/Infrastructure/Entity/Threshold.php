<?php

declare(strict_types=1);

namespace Marketplace\Component\Offer\Infrastructure\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'specific_price_threshold')]
class Threshold
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'integer')]
    private int $number;

    #[ORM\Column(type: 'float')]
    private float $price;

    #[ORM\ManyToOne(targetEntity: SpecificPrice::class, inversedBy: 'thresholds')]
    private ?SpecificPrice $specificPrice = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getNumber(): int
    {
        return $this->number;
    }

    public function setNumber(int $number): self
    {
        $this->number = $number;
        return $this;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function setPrice(float $price): self
    {
        $this->price = $price;
        return $this;
    }

    public function getSpecificPrice(): ?SpecificPrice
    {
        return $this->specificPrice;
    }

    public function setSpecificPrice(?SpecificPrice $specificPrice): self
    {
        $this->specificPrice = $specificPrice;

        return $this;
    }
}
