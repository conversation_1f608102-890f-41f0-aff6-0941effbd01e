<?php

declare(strict_types=1);

namespace Marketplace\Component\TermPayment\Infrastructure\Adapter\Service;

use Marketplace\Component\Invoice\Domain\Port\Repository\InvoiceRepositoryInterface;
use Marketplace\Component\Order\Domain\Model\MerchantOrder;
use Marketplace\Component\Order\Domain\Model\Order;
use Marketplace\Component\Order\Domain\Port\Repository\OrderRepositoryInterface;
use Marketplace\Component\TermPayment\Domain\Port\Service\CalculateAmountServiceInterface;

final class CalculateAmountService implements CalculateAmountServiceInterface
{
    private const TERM_PAYMENT = 'TERM_PAYMENT';

    public function __construct(
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly InvoiceRepositoryInterface $invoiceRepository
    ) {
    }

    public function calculateInProgressAmount(int $companyId, ?float $orderAmountTTC = null): float
    {
        $ordersTermPayment = $this->orderRepository->findInProgressOrders($companyId, self::TERM_PAYMENT);

        $inProgressAmount = 0;
        if ($orderAmountTTC) {
            $inProgressAmount = $orderAmountTTC;
        }
        $merchantOrderIds = [];
        /** @var Order $order */
        foreach ($ordersTermPayment as $order) {
            $inProgressAmount += $order->getAmountTTC();
            $merchantOrders = $order->getMerchantOrders();
            /** @var MerchantOrder $merchantOrder */
            foreach ($merchantOrders as $merchantOrder) {
                $merchantOrderIds[] = $merchantOrder->getMerchantOrderDistantId();
            }
        }
        $totalPaid = $this->invoiceRepository->getTotalPaidInvoicesFromMerchantOrders($merchantOrderIds);
        $inProgressAmount -= $totalPaid;

        return $inProgressAmount;
    }
}
