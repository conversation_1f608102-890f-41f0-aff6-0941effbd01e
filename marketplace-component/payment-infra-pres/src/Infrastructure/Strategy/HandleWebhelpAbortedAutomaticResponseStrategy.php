<?php

namespace Marketplace\Component\Payment\Infrastructure\Strategy;

use Marketplace\Component\Payment\Infrastructure\Adapter\Service\AutomaticResponseService;
use Open\Webhelp\Model\AutomaticResponse;

class HandleWebhelpAbortedAutomaticResponseStrategy implements WebhelpAutomaticResponseStrategyInterface
{
    public function __construct(private AutomaticResponseService $automaticResponseService)
    {
    }

    public function process(AutomaticResponse $automaticResponse): void
    {
        $this->automaticResponseService->manageRefusedAutomaticResponse($automaticResponse);
    }

    public function canProcess(AutomaticResponse $automaticResponse): bool
    {
        return $automaticResponse->getStatus() === AutomaticResponse::STATUS_ABORTED;
    }
}
