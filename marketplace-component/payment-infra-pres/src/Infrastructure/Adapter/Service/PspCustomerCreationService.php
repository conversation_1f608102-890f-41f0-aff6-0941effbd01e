<?php

namespace Marketplace\Component\Payment\Infrastructure\Adapter\Service;

use Exception;
use Marketplace\Component\Payment\Domain\Model\PspCustomer;
use Marketplace\Component\Payment\Domain\Port\Service\PspCustomerCreationServiceInterface;
use Marketplace\Component\Payment\Domain\Port\Repository\PspCustomerRepositoryInterface;
use Marketplace\Component\Payment\Infrastructure\Exception\PspCustomerException;
use Marketplace\Component\User\Domain\Model\Address;
use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\Country;
use Marketplace\Component\User\Domain\Port\Repository\AddressRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\CountryRepositoryInterface;
use Marketplace\Component\User\Infrastructure\Adapter\Repository\CompanyAccessTokenRepository;
use Open\Izberg\Client\OperatorClient;
use Open\Izberg\Exception\NotFoundException;
use Open\Izberg\Model\CustomerAttribute;
use Open\Webhelp\WebhelpClient;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\Translation\LocaleAwareInterface;

class PspCustomerCreationService implements PspCustomerCreationServiceInterface, LocaleAwareInterface, LoggerAwareInterface
{
    private string $locale;
    private LoggerInterface $logger;

    public function __construct(
        private WebhelpClient $webhelpClient,
        private CompanyRepositoryInterface $companyRepository,
        private AddressRepositoryInterface $addressRepository,
        private CountryRepositoryInterface $countryRepository,
        private PspCustomerRepositoryInterface $pspCustomerRepository,
        private CompanyAccessTokenRepository $companyAccessTokenRepository,
        private OperatorClient $operatorClient
    ) {
    }

    public function createCustomer(int $userId): PspCustomer
    {
        $company = $this->companyRepository->findByUserId($userId);
        if (!$company instanceof Company) {
            throw new PspCustomerException('company not found');
        }
        return $this->createCustomerCommon($company);
    }

    public function createCustomerByCompanyId(int $companyId): PspCustomer
    {
        $company = $this->companyRepository->getCompanyById($companyId);
        if (!$company instanceof Company) {
            throw new PspCustomerException('company not found');
        }

        $this->setLocale($company->getCountry()->getLocale());
        return $this->createCustomerCommon($company);
    }

    private function createCustomerCommon(Company $company): PspCustomer
    {
        $companyId = $company->getId();
        if ($companyId === null) {
            throw new PspCustomerException('company not found');
        }

        $companyExternalId = $company->getExternalId();
        if ($companyExternalId === null) {
            throw new PspCustomerException('company external ID not found');
        }

        $pspCustomer = $this->pspCustomerRepository->findByCompany($companyId);
        if ($pspCustomer instanceof PspCustomer) {
            return $pspCustomer;
        }

        $billingAddress = $this->addressRepository->findBillingAddress($companyId);
        if (!$billingAddress instanceof Address) {
            throw new PspCustomerException('billing address not found');
        }

        $country = $this->countryRepository->findById($billingAddress->getCountryId());
        if (!$country instanceof Country) {
            throw new PspCustomerException('country not found');
        }

        $code = $this->generateCode($companyId);

        $email = $company->getAccountingEmail();
        // précaution supplémentaire pour éviter les espaces en début ou fin d'email
        if ($email !== null) {
            $email = trim($email);
        }
        $corporateName = $company->getName();
        $vatNumber = $company->getVatNumber();
        $language = $this->locale;
        $billingCity = $billingAddress->getCity();
        $billingCountryCode = $country->getIzbCountryCode();
        $billingStreet = $billingAddress->getAddress1();
        $billingZipcode = $billingAddress->getZipCode();

        //TLC-2061: self-employed (without vat number)
        if ($vatNumber !== null && str_starts_with($vatNumber, "AUTO")) {
            $vatNumber = null;
        }

        $idCustomer = $this->webhelpClient->customerApi->createCustomer(
            $code,
            $email,
            $corporateName,
            $vatNumber,
            $language,
            $billingCity,
            $billingCountryCode,
            $billingStreet,
            $billingZipcode
        );

        $pspCustomer = new PspCustomer($companyId, $idCustomer, $language);

        $this->saveCustomerCustomAttributes($corporateName, $companyExternalId, $language, $idCustomer, $companyId);

        return $this->pspCustomerRepository->update($pspCustomer);
    }

    private function generateCode(int $companyId)
    {
        return hash('sha256', strval($companyId) . strval(time()));
    }

    public function setLocale(string $locale)
    {
        $this->locale = $locale;
    }

    public function getLocale()
    {
        return $this->locale;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }


    public function saveCustomerCustomAttributes(
        string $corporateName,
        string $companyExternalId,
        string $language,
        string $idCustomer,
        int $companyId
    ): void {
        $attributes = [
            new CustomerAttribute("corporate_name", $corporateName),
            new CustomerAttribute("customer_external_id", $companyExternalId),
            new CustomerAttribute("default_language", $language),
            new CustomerAttribute("psp_customer_id", $idCustomer),
        ];

        $userId = null;
        try {
            $userId = $this->companyAccessTokenRepository->getUserDistantId($companyId);
            if ($userId === null) {
                throw new \Exception("distant user not found with companyId:" . $companyId);
            }
            $this->operatorClient->attributeApi()->createOrUpdateCustomerAttributes($userId, $attributes);
        } catch (Exception $exception) {
            $this->logger->alert(
                'Adding customer custom attributes failed when creating PSP customer',
                [
                    'custom-attribute-values' => $attributes,
                    'izberg-user-id' => $userId,
                    'exceptionCode' => $exception->getCode(),
                    'exceptionMessage' => $exception->getMessage(),
                    'exceptionTrace' => $exception->getTrace(),
                ]
            );
        }
    }
}
