<?php

namespace Marketplace\Component\Payment\Infrastructure\Adapter\Service;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetLocaleInterface;
use Marketplace\Component\Order\Domain\Port\Service\AuthorizeOrderServiceInterface;
use Marketplace\Component\Payment\Domain\Port\Repository\PaymentActionRepositoryInterface;
use Open\Izberg\Client\OperatorClient;
use Open\Webhelp\WebhelpClient;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;

abstract class WebhelpPaymentService
{
    protected ?string $checkPaymentUrl = null;
    protected string $paymentCondition; // defined in strategies
    protected array $paymentInstallments = []; // Used in TermPayment strategy
    protected string $gatewayType;

    private string $checkCreditCardPaymentRoute;
    private string $paymentConfirmedRoute;

    public function __construct(
        protected MessageBusInterface $messageBus,
        protected RouterInterface $router,
        protected AuthorizeOrderServiceInterface $authorizeOrderService,
        protected PaymentActionRepositoryInterface $paymentActionRepository,
        protected WebhelpClient $webhelpClient,
        protected OperatorClient $operatorClient,
        protected MerchantPspIdService $merchantPspIdService,
        protected LoggerInterface $logger,
        protected GetLocaleInterface $getLocale
    ) {

        /**
         * Check Payment Route has to be a real route in your symfony application
         * To configure this service use in config/packages/marketplace_cart.yaml
         * marketplace_cart:
         *      check_credit_card_payment_route: 'YOUR_check_credit_card_payment_route'
         *      payment_confirmed_route: 'YOUR_payment_confirmed_route'
         */
        $this->checkCreditCardPaymentRoute = 'this_route_needs_to_be_configured';
        $this->paymentConfirmedRoute = 'this_route_needs_to_be_configured';

        $this->init();
    }

    public function setPaymentConfirmedRoute(string $paymentConfirmedRoute): self
    {
        $this->paymentConfirmedRoute = $paymentConfirmedRoute;
        return $this;
    }

    public function setCheckCreditCardPaymentRoute(string $checkCreditCardPaymentRoute): self
    {
        $this->checkCreditCardPaymentRoute = $checkCreditCardPaymentRoute;
        return $this;
    }

    abstract protected function init(): void;

    protected function paymentConfirmedUrl(int $cartId): string
    {
        $parameters = ['cartId' => $cartId];
        return $this->generateUrl($this->paymentConfirmedRoute, $parameters);
    }

    protected function configureCheckPaymentUrl(int $cartId): void
    {
        $parameters = ['cartId' => $cartId];
        $this->checkPaymentUrl = $this->generateUrl($this->checkCreditCardPaymentRoute, $parameters);
    }

    private function generateUrl(string $route, array $parameters): string
    {
        return $this->router->generate($route, $parameters, UrlGeneratorInterface::ABSOLUTE_URL);
    }
}
