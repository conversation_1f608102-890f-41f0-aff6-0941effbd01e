<?php

namespace Marketplace\Component\Payment\Infrastructure\Mapper;

use Marketplace\Component\Payment\Domain\Model\PspCustomer;
use Marketplace\Component\Payment\Infrastructure\Entity\PspCustomer as DoctrinePspCustomer;

abstract class PspCustomerMapper
{
    public static function domainToDoctrine(PspCustomer $pspCustomer, DoctrinePspCustomer $doctrinePspCustomer): void
    {
        $doctrinePspCustomer
            ->setId($pspCustomer->getId())
            ->setCompanyId($pspCustomer->getCompanyId())
            ->setLanguage($pspCustomer->getLanguage())
        ;
    }

    public static function doctrineToDomain(DoctrinePspCustomer $doctrinePspCustomer): PspCustomer
    {
        return new PspCustomer(
            $doctrinePspCustomer->getCompanyId(),
            $doctrinePspCustomer->getId(),
            $doctrinePspCustomer->getLanguage()
        );
    }
}
