<?php

namespace Marketplace\Component\Payment\Bundle\MessageHandler;

use Marketplace\Component\Payment\Infrastructure\Strategy\WebhelpAutomaticResponseContext;
use Open\Webhelp\Model\AutomaticResponse;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Throwable;

class WebhelpAutomaticResponseHandler implements MessageHandlerInterface
{
    public function __construct(private WebhelpAutomaticResponseContext $automaticResponseContext)
    {
    }

    public function __invoke(AutomaticResponse $automaticResponse)
    {
        try {
            $this->automaticResponseContext->process($automaticResponse);
        } catch (Throwable $exception) {
            throw new UnrecoverableMessageHandlingException($exception->getMessage(), 500, $exception);
        }
    }
}
