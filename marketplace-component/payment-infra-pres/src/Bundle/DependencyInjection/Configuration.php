<?php

namespace Marketplace\Component\Payment\Bundle\DependencyInjection;

use Symfony\Component\Config\Definition\Builder\TreeBuilder;
use Symfony\Component\Config\Definition\ConfigurationInterface;

class Configuration implements ConfigurationInterface
{
    public function getConfigTreeBuilder()
    {
        $treeBuilder = new TreeBuilder('marketplace_payment');

        $treeBuilder->getRootNode()
            ->children()
                ->scalarNode('check_credit_card_payment_route')->end()
                ->scalarNode('payment_confirmed_route')->end()
            ->end()
        ;

        return $treeBuilder;
    }
}
