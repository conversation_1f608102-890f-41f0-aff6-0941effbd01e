<?php

namespace Marketplace\Component\Payment\Bundle\Command;

use Marketplace\Component\Payment\Domain\Model\PaymentAction;
use Marketplace\Component\Payment\Domain\Port\Repository\PaymentActionRepositoryInterface;
use Marketplace\Component\Payment\Domain\Presenter\AuthorizePresenterInterface;
use Marketplace\Component\Payment\Domain\UseCase\Authorize\AuthorizeUseCase;
use Marketplace\Component\Payment\Domain\UseCase\Authorize\DTO\AuthorizeRequest;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class AuthorizePaymentCommand extends Command
{
    public function __construct(
        private AuthorizeUseCase $useCase,
        private AuthorizePresenterInterface $presenter,
        private PaymentActionRepositoryInterface $paymentActionRepository
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('marketplace:payment:authorize')
            ->setDescription('authorize step of the payment strategy')
            ->addArgument('orderExternalId', InputArgument::REQUIRED, 'order external ID');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $orderExternalId = $input->getArgument('orderExternalId');
        $paymentAction = $this->paymentActionRepository->findByOrderExternalId($orderExternalId);

        if (!$paymentAction instanceof PaymentAction) {
            $output->writeln("Cannot found payment action with given order external Id");
            return 0;
        }

        $request = new AuthorizeRequest($paymentAction);
        $this->useCase->execute($request, $this->presenter);

        return 0;
    }
}
