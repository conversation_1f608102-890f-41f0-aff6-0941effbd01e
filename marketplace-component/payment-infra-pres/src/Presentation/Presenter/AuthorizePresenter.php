<?php

namespace Marketplace\Component\Payment\Presentation\Presenter;

use Marketplace\Component\CleanArchiCore\Presentation\Presenter\MessengerPresenterInterface;
use Marketplace\Component\Payment\Domain\Presenter\AuthorizePresenterInterface;
use Marketplace\Component\Payment\Domain\UseCase\Authorize\DTO\AuthorizeResponse;
use Marketplace\Component\Payment\Infrastructure\Exception\PaymentException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class AuthorizePresenter implements
    AuthorizePresenterInterface,
    MessengerPresenterInterface,
    LoggerAwareInterface
{
    private LoggerInterface $logger;

    public function present(AuthorizeResponse $response): void
    {
        if (!$response->succeeded()) {
            $message = sprintf(
                'Authorize use case failed. using payment method: %s - order external id: %s',
                $response->paymentMethod,
                $response->orderExternalId
            );
            $this->logger->error($message, $this->logContext($response));
            throw new PaymentException($message);
        }


        $message = sprintf(
            'Authorize use case succeed. using payment method: %s - order external id: %s',
            $response->paymentMethod,
            $response->orderExternalId
        );

        $this->logger->info($message, $this->logContext($response));
    }

    private function logContext(AuthorizeResponse $response)
    {
        return [
            'payment_method' => $response->paymentMethod,
            'order_external_id' => $response->orderExternalId,
            'errors' => $response->formatErrorsForLogContext()
        ];
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
