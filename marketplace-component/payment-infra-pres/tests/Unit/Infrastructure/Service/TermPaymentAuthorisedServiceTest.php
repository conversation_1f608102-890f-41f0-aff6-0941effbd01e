<?php

namespace Marketplace\Component\Payment\Tests\Unit\Infrastructure\Service;

use Marketplace\Component\Invoice\Domain\Port\Repository\InvoiceRepositoryInterface;
use Marketplace\Component\Invoice\Infrastructure\Adapter\Repository\InvoiceRepository;
use Marketplace\Component\Payment\Domain\Model\Method\BankTransferMethod;
use Marketplace\Component\Order\Domain\Model\Order;
use Marketplace\Component\Order\Infrastructure\Adapter\Repository\OrderRepository;
use Marketplace\Component\Payment\Infrastructure\Adapter\Service\TermPaymentAuthorisedService;
use Marketplace\Component\TermPayment\Domain\Port\Service\CalculateAmountServiceInterface;
use Marketplace\Component\User\Domain\Model\TermPayment;
use Marketplace\Component\User\Infrastructure\Adapter\Repository\TermPaymentRepository;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

class TermPaymentAuthorisedServiceTest extends TestCase
{
    private TermPaymentAuthorisedService $termPaymentAuthorisedService;
    protected function setUp(): void
    {
        parent::setUp();
        $prophet = new Prophet();
        $termPaymentRepository = $prophet->prophesize(TermPaymentRepository::class);
        $termPaymentRepository->getTermPaymentOfCompany(Argument::type('int'))->
            will(function ($args) {
                if ($args[0] === 0) {
                    return null;
                }
                $termPayment = new TermPayment();
                $termPayment->setStatus(TermPayment::TERM_PAYMENT_ACCEPTED);
                $termPayment->setThreshold(100);
                return $termPayment;
            });
        $termPaymentCalculateAmountService = $prophet->prophesize(CalculateAmountServiceInterface::class);
        $termPaymentCalculateAmountService->calculateInProgressAmount(Argument::type('int'), Argument::type('float'))->will(function ($args) {
            if ($args[0] == 1 && $args[1] == 60) {
                return 120;
            }
            return 0.0;
        });

        $this->termPaymentAuthorisedService = new TermPaymentAuthorisedService(
            $termPaymentRepository->reveal(),
            $termPaymentCalculateAmountService->reveal()
        );
    }

    /**
     * @param int $companyId
     * @param float $amount
     * @param bool $expected
     * @dataProvider provideIsTermPaymentReached
     */
    public function testIsTermPaymentReached(int $companyId, float $amount, bool $expected)
    {
        $result = $this->termPaymentAuthorisedService->isTermPaymentReached($companyId, $amount);
        $this->assertEquals($expected, $result);
    }

    public function provideIsTermPaymentReached()
    {
        yield [0, 40, true];
        yield [1, 60, true];
        yield [1, 20, false];
    }

    /**
     * @param int $companyId
     * @param bool $expected
     * @dataProvider provideIsAuthorised
     */
    public function testIsAuthorised(int $companyId, bool $expected)
    {
        $result = $this->termPaymentAuthorisedService->isAuthorised($companyId);
        $this->assertEquals($expected, $result);
    }

    public function provideIsAuthorised()
    {
        yield [0,false];
        yield [1,true];
    }
}
