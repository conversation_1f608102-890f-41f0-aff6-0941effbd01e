<?php

namespace Marketplace\Component\Payment\Tests\Unit\Infrastructure\Service;

use Exception;
use Generator;
use Marketplace\Component\Payment\Domain\Model\PspCustomer;
use Marketplace\Component\Payment\Domain\Port\Repository\PspCustomerRepositoryInterface;
use Marketplace\Component\Payment\Infrastructure\Adapter\Service\PspCustomerCreationService;
use Marketplace\Component\Payment\Infrastructure\Exception\PspCustomerException;
use Marketplace\Component\User\Domain\Model\Address;
use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\Country;
use Marketplace\Component\User\Domain\Port\Repository\AddressRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\CountryRepositoryInterface;
use Marketplace\Component\User\Infrastructure\Adapter\Repository\CompanyAccessTokenRepository;
use Open\Izberg\Api\AttributeApi;
use Open\Izberg\Client\OperatorClient;
use Open\Webhelp\WebhelpClient;
use Open\Webhelp\WebhelpConfiguration;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Symfony\Contracts\HttpClient\ResponseStreamInterface;

/**
 * @coversDefaultClass \Marketplace\Component\Payment\Infrastructure\Adapter\Service\PspCustomerCreationService
 */
final class PspCustomerCreationServiceTest extends TestCase
{
    private const LOCALE = 'fr';
    private PspCustomerCreationService $pspCustomerCreationService;
    private array $scenarioData;

    /**
     * @param int $userId
     * @param PspCustomer|null $expectedPspCustomer
     * @param Exception|null $expectedException
     * @dataProvider provideCreateCustomer
     */
    public function testCreateCustomer(
        int $userId,
        ?PspCustomer $expectedPspCustomer,
        ?Exception $expectedException
    ): void {
        if ($expectedException !== null) {
            $this->expectException($expectedException::class);
            $this->expectExceptionMessage($expectedException->getMessage());
        }

        $pspCustomer = $this->pspCustomerCreationService->createCustomer($userId);

        if ($expectedPspCustomer !== null) {
            $this->assertEquals($expectedPspCustomer, $pspCustomer);
        }
    }

    public function provideCreateCustomer(): Generator
    {
        $this->buildFixtures();

        $data = $this->scenarioData[1];
        $userId = $data['userId'];
        $expectedPspCustomer = new PspCustomer($userId, 1, self::LOCALE);
        $expectedException = null;
        yield 'Scenario 1: create customer successfully' => [$userId, $expectedPspCustomer, $expectedException];

        $data = $this->scenarioData[2];
        $userId = $data['userId'];
        $expectedPspCustomer = null;
        $expectedException = new PspCustomerException('company not found');
        yield 'Scenario 2: company not found' => [$userId, $expectedPspCustomer, $expectedException];

        $data = $this->scenarioData[3];
        $userId = $data['userId'];
        $expectedPspCustomer = null;
        $expectedException = new PspCustomerException('billing address not found');
        yield 'Scenario 3: billing address not found' => [$userId, $expectedPspCustomer, $expectedException];

        $data = $this->scenarioData[4];
        $userId = $data['userId'];
        $expectedPspCustomer = null;
        $expectedException = new PspCustomerException('country not found');
        yield 'Scenario 4: country not found' => [$userId, $expectedPspCustomer, $expectedException];

        // Given the user ID with a company without external ID set
        // When we try to create or retrieve his psp customer
        $data = $this->scenarioData[5];
        $userId = $data['userId'];
        $expectedPspCustomer = null;
        // Then an exception should be thrown telling that "company external ID not found"
        $expectedException = new PspCustomerException('company external ID not found');
        yield 'Scenario 5: company external ID not found' => [$userId, $expectedPspCustomer, $expectedException];
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->buildFixtures();

        $webhelpClient = $this->setUpWebhelpClient();
        $companyRepository = $this->setUpCompanyRepository();
        $addressRepository = $this->setUpAddressRepository();
        $countryRepository = $this->setUpCountryRepository();
        $pspCustomerRepository = $this->setUpPspCustomerRepository();
        $companyAccessTokenRepository = $this->setUpCompanyAccessTokenRepository();
        $operatorClient = $this->setUpOperatorClient();

        $this->pspCustomerCreationService = new PspCustomerCreationService(
            $webhelpClient,
            $companyRepository,
            $addressRepository,
            $countryRepository,
            $pspCustomerRepository,
            $companyAccessTokenRepository,
            $operatorClient
        );
        $this->pspCustomerCreationService->setLocale(self::LOCALE);
        $this->pspCustomerCreationService->setLogger($this->createMock(LoggerInterface::class));
    }

    private function setUpWebhelpClient(): WebhelpClient
    {
        $prophet = new Prophet();
        $webhelpConfiguration = new WebhelpConfiguration(
            'https://test.example.local/',
            'username',
            'password'
        );

        $response = $prophet->prophesize(ResponseInterface::class);
        $response->getContent(Argument::type('bool'))->willReturn('{"idCustomerWPS":1}');
        $response->getContent()->willReturn('{"idCustomerWPS":1}');
        $response->getContent(false)->willReturn('{"idCustomerWPS":1}');
        $responseStream = $prophet->prophesize(ResponseStreamInterface::class);


        $httpClient = new class ($response->reveal(), $responseStream->reveal()) implements HttpClientInterface {
            public function __construct(
                private ResponseInterface $response,
                private ResponseStreamInterface $responseStream
            ) {
            }

            public function request(string $method, string $url, array $options = []): ResponseInterface
            {
                return $this->response;
            }

            public function stream($responses, float $timeout = null): ResponseStreamInterface
            {
                return $this->responseStream;
            }
        };

        $webhelpClient = new WebhelpClient($webhelpConfiguration, $httpClient);
        $logger = $prophet->prophesize(LoggerInterface::class);
        $webhelpClient->setLogger($logger->reveal());
        return $webhelpClient;
    }

    private function setUpCompanyRepository(): CompanyRepositoryInterface
    {
        return new class ($this->scenarioData) implements CompanyRepositoryInterface{
            public function __construct(private array $scenarioData)
            {
            }

            public function getExternalId(int $companyId): ?string
            {
                return null;
            }


            public function findByVatNumber(string $vatNumber): ?Company
            {
                return null;
            }

            public function findByUserId(int $userId): ?Company
            {
                $data = $this->scenarioData[$userId];

                return $data['company'];
            }

            public function checkIfCompanyExist(string $vatNumber, Country $country): bool
            {
                return false;
            }

            public function getCompanyById(int $id): ?Company
            {
                $data = $this->scenarioData[$id];

                return $data['company'];
            }

            public function update(Company $company): void
            {
            }

            public function findBuyerCompanies(
                string $filteredName,
                string $filteredVat,
                string $filteredCountry,
                string $filteredStatus,
                string $filteredCreationDate
            ): array {
                return [];
            }

            public function isExternalIdExist(string $externalId): bool
            {
                return false;
            }

            public function getByExternalId(string $externalId): ?Company
            {
                // TODO: Implement getByExternalId() method.
                return null;
            }

            public function isEmailUnique(?string $email): bool
            {
                return true;
            }

            public function changeDistantCompanyName(int $id, string $name): void
            {
            }

            public function getNameFromVatNumber(string $vatNumber): ?string
            {
                return null;
            }

            public function getLastSelfEmployedVatNumber(): ?string
            {
                return null;
            }
        };
    }

    private function setUpAddressRepository(): AddressRepositoryInterface
    {
        return new class ($this->scenarioData) implements AddressRepositoryInterface
        {
            public function __construct(private array $scenarioData)
            {
            }

            public function findBillingAddress(int $companyId): ?Address
            {
                $data = $this->scenarioData[$companyId];

                return $data['billingAddress'];
            }

            public function findShippingAddresses(int $companyId, bool $active = true, bool $filterWithRegion = true): array
            {
                return [];
            }

            public function findDefaultShippingAddress(int $companyId): ?Address
            {
                return null;
            }

            public function findById(int $addressId): ?Address
            {
                return null;
            }

            public function isBillingAddressExist(int $companyId): bool
            {
                return false;
            }

            public function save(Address $address): Address
            {
            }

            public function delete(int $addressId): bool
            {
                return false;
            }

            public function createPickAndCollectAddressIfNotExist(Address $address, int $userDistantId): int
            {
                // TODO: Implement createPickAndCollectAddressIfNotExist() method.
            }

            public function findDistantAddressById(int $addressId): ?Address
            {
                // TODO: Implement findDistantAddressById() method.
            }

            public function findAllActiveAddresses(): array
            {
                return [];
            }

            public function updateWithoutSync(Address $address): bool
            {
                return true;
            }
        };
    }

    private function setUpCountryRepository(): CountryRepositoryInterface
    {
        return new class ($this->scenarioData) implements CountryRepositoryInterface
        {
            public function __construct(private array $scenarioData)
            {
            }

            public function create(Country $country): void
            {
            }

            public function save(): void
            {
            }

            public function getCountries(array $criteria): array
            {
                return [];
            }

            public function findById(int $countryId): ?Country
            {
                $data = $this->scenarioData[$countryId];

                return $data['country'];
            }

            public function findByCode(string $code): ?Country
            {
                return null;
            }

            public function findByIsoCode(string $code): ?Country
            {
                return null;
            }

            public function findByDistantId(int $id): ?Country
            {
                // TODO: Implement findByDistantId() method.
            }
        };
    }

    private function setUpPspCustomerRepository(): PspCustomerRepositoryInterface
    {
        return new class () implements PspCustomerRepositoryInterface
        {
            public function findByCompany(int $companyId): ?PspCustomer
            {
                return null;
            }

            public function findByDistantCompany(int $distantCompanyId): ?PspCustomer
            {
                return null;
            }

            public function update(PspCustomer $pspCustomer): PspCustomer
            {
                return $pspCustomer;
            }

            public function findById(string $id): ?PspCustomer
            {
                return null;
            }

            public function findAllPsp(): array
            {
                // TODO: Implement findAllPsp() method.
            }
        };
    }

    private function setUpCompanyAccessTokenRepository(): CompanyAccessTokenRepository
    {
        $prophet = new Prophet();
        $companyAccessTokenRepository = $prophet->prophesize(CompanyAccessTokenRepository::class);
        return $companyAccessTokenRepository->reveal();
    }

    private function setUpOperatorClient(): OperatorClient
    {
        $prophet = new Prophet();
        $operatorClient = $prophet->prophesize(OperatorClient::class);
        $operatorClient->attributeApi()->will(function ($args) {
            $prophet = new Prophet();
            $attributeApi = $prophet->prophesize(AttributeApi::class);

            $attributeApi->getCustomerAttributeId(Argument::type('string'))->willReturn('attributeId');

            return $attributeApi->reveal();
        });
        return $operatorClient->reveal();
    }

    private function buildFixtures(): void
    {
        $this->scenarioData[1] = [
            'userId' => 1,
            'company' => (new Company())
                ->setId(1)
                ->setExternalId('411-company1')
                ->setName('company 1')
                ->setVatNumber('companyVatNumber')
            ,
            'billingAddress' => new Address(
                Address::BILLING,
                'Billing address company',
                'test address',
                null,
                '75000',
                'Paris',
                1,
                1,
                "vatZone",
                "region"
            ),
            'country' => (new Country())
                ->setIzbCountryCode('france'),
        ];

        $this->scenarioData[2] = [
            'userId' => 2,
            'company' => null,
            'billingAddress' => new Address(
                Address::BILLING,
                'Billing address company',
                'test address',
                null,
                '75000',
                'Paris',
                2,
                2,
                "vatZone",
                "region"
            ),
            'country' => (new Country())
                ->setIzbCountryCode('france'),
        ];

        $this->scenarioData[3] = [
            'userId' => 3,
            'company' => (new Company())
                ->setId(3)
                ->setExternalId('411-company3')
                ->setName('company 3')
                ->setVatNumber('companyVatNumber')
            ,
            'billingAddress' => null,
            'country' => (new Country())
                ->setIzbCountryCode('france'),
        ];

        $this->scenarioData[4] = [
            'userId' => 4,
            'company' => (new Company())
                ->setId(4)
                ->setExternalId('411-company4')
                ->setName('company 4')
                ->setVatNumber('companyVatNumber'),
            'billingAddress' => new Address(
                Address::BILLING,
                'Billing address company',
                'test address',
                null,
                '75000',
                'Paris',
                4,
                4,
                "vatZone",
                "region"
            ),
            'country' => null,
        ];

        $this->scenarioData[5] = [
            'userId' => 5,
            'company' => (new Company())
                ->setId(5)
                ->setName('company 5')
                ->setVatNumber('companyVatNumber'),
            'billingAddress' => new Address(
                Address::BILLING,
                'Billing address company',
                'test address',
                null,
                '75000',
                'Paris',
                5,
                5,
                "vatZone",
                "region"
            ),
            'country' => null,
        ];
    }
}
