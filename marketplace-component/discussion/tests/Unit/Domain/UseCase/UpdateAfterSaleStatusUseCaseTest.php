<?php

namespace Marketplace\Component\Discussion\Domain\UseCase\UpdateAfterSaleStatus;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CheckMerchantServiceInterface;
use Marketplace\Component\Discussion\Domain\Model\AfterSales;
use Marketplace\Component\Discussion\Domain\Port\Repository\AfterSalesRepositoryInterface;
use Marketplace\Component\Discussion\Domain\Presenter\UpdateAfterSaleStatusPresenterInterface;
use Marketplace\Component\Discussion\Domain\UseCase\UpdateAfterSaleStatus\DTO\UpdateAfterSaleStatusRequest;
use Marketplace\Component\Discussion\Domain\UseCase\UpdateAfterSaleStatus\DTO\UpdateAfterSaleStatusResponse;
use Marketplace\Component\Order\Domain\Model\MerchantOrder;
use Marketplace\Component\Order\Domain\Port\Repository\MerchantOrderRepositoryInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;
use Psr\Log\LoggerInterface;

class UpdateAfterSaleStatusUseCaseTest extends TestCase
{
    private UpdateAfterSaleStatusPresenterInterface $presenter;

    private UpdateAfterSaleStatusUseCase $useCase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements UpdateAfterSaleStatusPresenterInterface {
            public UpdateAfterSaleStatusResponse $response;

            public function present(UpdateAfterSaleStatusResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        $afterSalesRepository = $prophet->prophesize(AfterSalesRepositoryInterface::class);
        $afterSalesRepository->findAfterSale(Argument::type('int'))->will(function ($args) {
            $afterSaleId = $args[0];
            if ($afterSaleId === 1) {
                return null;
            }
            $afterSale = new AfterSales();
            $afterSale->setMerchantOrderId($args[0]);
            return $afterSale;
        });
        $afterSalesRepository->updateAfterSales(Argument::type(AfterSales::class))->willReturn(new AfterSales());
        $checkMerchantService = $prophet->prophesize(CheckMerchantServiceInterface::class);
        $checkMerchantService->isMKPOwnerMerchant(Argument::type('int'))->will(function ($args) {
            $merchantId = $args[0];
                return $merchantId === 5;
        });
        $merchantOrderRepository = $prophet->prophesize(MerchantOrderRepositoryInterface::class);
        $merchantOrderRepository->getMerchantOrder(Argument::type('int'))->will(function ($args) {
            $merchantOrderId = $args[0];
            if ($merchantOrderId === 0) {
                return null;
            }
            $merchantOrder = new MerchantOrder(1, $merchantOrderId);
            $merchantOrder->setMerchantDistantId($merchantOrderId);
            return $merchantOrder;
        });
        $this->useCase = new UpdateAfterSaleStatusUseCase(
            $afterSalesRepository->reveal(),
            $checkMerchantService->reveal(),
            $merchantOrderRepository->reveal()
        );
        $logger = $prophet->prophesize(LoggerInterface::class);
        $this->useCase->setLogger($logger->reveal());
    }

    /**
     * @param UpdateAfterSaleStatusRequest $request
     * @param UpdateAfterSaleStatusResponse $response
     * @return void
     * @dataProvider provideExecute
     */
    public function testExecute(UpdateAfterSaleStatusRequest $request, UpdateAfterSaleStatusResponse $response)
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($response, $this->presenter->response);
    }

    public function provideExecute()
    {
        //given : je veux changer le status d'un SAV inexistant
        $request = new UpdateAfterSaleStatusRequest(1, "OPEN");
        //when : je soumet la requête
        $response = new UpdateAfterSaleStatusResponse();
        $response->getNotification()->addError('afterSaleId', 'after_sales.id.unkown');
        //then : j'obtiens un message d'erreur m'expliquant que le sav n'existe pas
        yield 'aftersale does not exist' => [$request,$response];

        //given : je veux changer le status d'un SAV dont la merchant order n'existe pas
        $request = new UpdateAfterSaleStatusRequest(0, "OPEN");
        //when : je soumet la requête
        $response = new UpdateAfterSaleStatusResponse();
        $response->getNotification()->addError('merchantOrderId', 'after_sales.merchant_order_id.not_exist');
        //then : j'obtiens un message d'erreur m'expliquant que la ressource n'existe pas
        yield 'aftersale as an invalid merchantOrder' => [$request,$response];

        //given : je veux changer le status d'un SAV n'appartenant pas au MKPOwner
        $request = new UpdateAfterSaleStatusRequest(2, "OPEN");
        //when : je soumet la requête
        $response = new UpdateAfterSaleStatusResponse();
        $response->getNotification()->addError('merchantId', 'after_sales.merchant.not_mkp_owner');
        //then : j'obtiens un message d'erreur m'expliquant que je n'ai pas le droit d'accéder à cette ressource
        yield 'aftersale is not own by MKPOwner' => [$request,$response];

        //given : je veux changer le status d'un SAV en utilisant un status inexistant
        $request = new UpdateAfterSaleStatusRequest(5, "toto");
        //when : je soumet la requête
        $response = new UpdateAfterSaleStatusResponse();
        $response->getNotification()->addError('status', 'after_sales.status.unkown');
        //then : j'obtiens un message d'erreur m'expliquant que ce status n'existe pas
        yield 'wrong status' => [$request,$response];

        //given : je veux changer le status d'un SAV
        $request = new UpdateAfterSaleStatusRequest(5, "OPEN");
        //when : je soumet la requête
        $response = new UpdateAfterSaleStatusResponse();
        $response->setSuccess(true);
        //then : j'obtiens un message de succès
        yield 'sav update OK' => [$request,$response];
    }
}
