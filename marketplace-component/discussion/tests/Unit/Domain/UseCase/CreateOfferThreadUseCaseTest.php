<?php

namespace Marketplace\Component\Discussion\Domain\UseCase\CreateAfterSales;

use Marketplace\Component\Discussion\Domain\Model\MessageDetail;
use Marketplace\Component\Discussion\Domain\Model\Thread;
use Marketplace\Component\Discussion\Domain\Port\Service\CreateThreadServiceInterface;
use Marketplace\Component\Discussion\Domain\Port\Service\FileServiceInterface;
use Marketplace\Component\Discussion\Domain\Presenter\CreateOfferThreadPresenterInterface;
use Marketplace\Component\Discussion\Domain\UseCase\CreateOfferThreadUseCase\CreateOfferThreadUseCase;
use Marketplace\Component\Discussion\Domain\UseCase\CreateOfferThreadUseCase\DTO\CreateOfferThreadRequest;
use Marketplace\Component\Discussion\Domain\UseCase\CreateOfferThreadUseCase\DTO\CreateOfferThreadResponse;
use Marketplace\Component\Discussion\Infrastructure\Adapter\Repository\ThreadRepository;
use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

class CreateOfferThreadUseCaseTest extends TestCase
{
    private CreateOfferThreadUseCase $useCase;

    private CreateOfferThreadPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements CreateOfferThreadPresenterInterface {
            public CreateOfferThreadResponse $response;

            public function present(CreateOfferThreadResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophecy = new Prophet();
        $userRepository = $prophecy->prophesize(UserRepositoryInterface::class);
        $userRepository->findById(
            Argument::type('int')
        )->will(function ($args) {
            $id = $args[0];
            if ($id === 0) {
                return null;
            }
            $user = new User();
            $user->setId($id);
            $company = new Company();
            $company->setId($id);
            $user->setCompany($company);
            return $user;
        });
        $createThreadService = $prophecy->prophesize(CreateThreadServiceInterface::class);
        $createThreadService->setupMessage(
            Argument::type('string'),
            Argument::type('string'),
            Argument::type('string'),
            Argument::type('array'),
            Argument::type('int'),
            Argument::type('int')
        )->willReturn(new MessageDetail());
        $createThreadService->createThread(
            Argument::type(MessageDetail::class),
            Argument::type('int')
        )->will(function ($args) {
                $userId = $args[1];
            if ($userId === 10) {
                return null;
            }
                return (new Thread())->setCreatedAt(new \DateTimeImmutable('12/12/12'));
        });

        $threadRepository = $prophecy->prophesize(ThreadRepository::class);
        $fileService = $prophecy->prophesize(FileServiceInterface::class);
        $fileService->checkNumberAndSizeFiles(Argument::type('array'))->will(
            function ($args) {
                $files = $args[0];

                if (count($files) === 0) {
                    return true;
                }

                return false;
            }
        );
        $this->useCase = new CreateOfferThreadUseCase(
            $threadRepository->reveal(),
            $createThreadService->reveal(),
            $userRepository->reveal(),
            $fileService->reveal()
        );
    }

    /**
     * @dataProvider provideExecute
     */
    public function testExecute(CreateOfferThreadRequest $request, CreateOfferThreadResponse $response)
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($response, $this->presenter->response);
    }

    public function provideExecute()
    {
        //Request ok
        $request = new CreateOfferThreadRequest(
            1,
            1,
            "Message offre",
            "Question concernant offre : Titre",
            []
        );

        $thread = (new Thread())->setCreatedAt(new \DateTimeImmutable('12/12/12'));
        $response = new CreateOfferThreadResponse();
        $response->setThread($thread);
        yield 'request OK' => [$request, $response];

        //user not found
        $request = new CreateOfferThreadRequest(
            0,
            1,
            "Message offre",
            "Question concernant offre : Titre",
            []
        );

        $response = new CreateOfferThreadResponse();
        $response->getNotification()->addError('user', 'after_sales.user.unknown');
        yield 'User not found' => [$request, $response];

        //create thread error
        $request = new CreateOfferThreadRequest(
            10,
            1,
            "Message offre",
            "Question concernant offre : Titre",
            []
        );

        $response = new CreateOfferThreadResponse();
        $response->getNotification()->addError('message', 'after_sales.message.error');
        yield 'Create thread error' => [$request, $response];

        //Files too big
        $request = new CreateOfferThreadRequest(
            1,
            1,
            "Message offre",
            "Question concernant offre : Titre",
            ['files']
        );

        $response = new CreateOfferThreadResponse();
        $response->getNotification()->addError('files', '', 'discussion.uploaded_files.too_big');
        yield 'Files too big' => [$request, $response];
    }
}
