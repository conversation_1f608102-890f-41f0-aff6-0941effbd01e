<?php

declare(strict_types=1);

namespace App\Tests\Unit\Domain;

use DateTimeImmutable;
use Generator;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetLocaleInterface;
use Marketplace\Component\Discussion\Domain\Model\AfterSales;
use Marketplace\Component\Discussion\Domain\Model\Thread;
use Marketplace\Component\Discussion\Domain\Port\Repository\AfterSalesRepositoryInterface;
use Marketplace\Component\Discussion\Domain\Port\Repository\ThreadRepositoryInterface;
use Marketplace\Component\Discussion\Domain\Port\Service\ReplyAfterSaleInterface;
use Marketplace\Component\Discussion\Domain\Presenter\ReplyAfterSaleThreadPresenterInterface;
use Marketplace\Component\Discussion\Domain\UseCase\ReplyAfterSaleThread\DTO\ReplyAfterSaleThreadResponse;
use
Marketplace\Component\Discussion\Domain\UseCase\ReplyAfterSaleThreadVendorUseCase\DTO\ReplyAfterSaleThreadVendorRequest;
use Marketplace\Component\Discussion\Domain\UseCase\ReplyAfterSaleThreadVendorUseCase\ReplyAfterSaleThreadVendorUseCase;
use Marketplace\Component\Mail\Domain\Port\Service\EmailServiceInterface;
use Marketplace\Component\Mail\Infrastructure\Exception\TemplateException;
use Marketplace\Component\User\Domain\Model\Merchant;
use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Port\Repository\MerchantRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

final class ReplyAfterSaleThreadVendorUseCaseTest extends TestCase
{
    private ReplyAfterSaleThreadVendorUseCase $useCase;

    private ReplyAfterSaleThreadPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements ReplyAfterSaleThreadPresenterInterface {
            public ReplyAfterSaleThreadResponse $response;
            public function present(ReplyAfterSaleThreadResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        /** @var ReplyAfterSaleInterface|ObjectProphecy $replyAfterSale */
        $replyAfterSale = $prophet->prophesize(ReplyAfterSaleInterface::class);
        $replyAfterSale->reply(
            Argument::type('bool'),
            Argument::type('string'),
            Argument::type('int'),
            Argument::type('string'),
            Argument::type('array'),
            Argument::type('string'),
            Argument::type(ReplyAfterSaleThreadResponse::class),
        )->willReturn(true);
        /** @var EmailServiceInterface|ObjectProphecy $emailService */
        $emailService = $prophet->prophesize(EmailServiceInterface::class);
        $emailService->getNoReplyUser()->willReturn(['<EMAIL>', 'no reply']);
        $emailService->send(
            Argument::type('array'),
            Argument::type('array'),
            Argument::type('string'),
            Argument::type('string'),
            Argument::type('array')
        )->willReturn(true);
        /** @var GetLocaleInterface|ObjectProphecy $getLocale */
        $getLocale = $prophet->prophesize(GetLocaleInterface::class);
        $getLocale->getLocale()->willReturn('FR');
        /** @var UrlGeneratorInterface|ObjectProphecy $urlGenerator */
        $urlGenerator = $prophet->prophesize(UrlGeneratorInterface::class);
        $urlGenerator->generate(Argument::type('int'), Argument::type('array'), Argument::type('int'))
            ->willReturn('url');
        /** @var AfterSalesRepositoryInterface|ObjectProphecy $afterSaleRepository */
        $afterSaleRepository = $prophet->prophesize(AfterSalesRepositoryInterface::class);
        $afterSaleRepository->findAfterSale(Argument::type('int'))->willReturn((new AfterSales())
            ->setId(1)
            ->setThreadId(1)
            ->setCreatedAt(DateTimeImmutable::createFromFormat('Y-m-d H:i:s', '2022-01-01 00:00:00')));
        /** @var ThreadRepositoryInterface|ObjectProphecy $threadRepository */
        $threadRepository = $prophet->prophesize(ThreadRepositoryInterface::class);
        $threadRepository->findThreadById(Argument::type('int'))->willReturn((new Thread())
            ->setUserId(1)
            ->setVendor('1')
            ->setLastMessageDate(
                DateTimeImmutable::createFromFormat('Y-m-d H:i:s', '2022-01-01 01:00:00')
            ));
        /** @var UserRepositoryInterface|ObjectProphecy $userRepository */
        $userRepository = $prophet->prophesize(UserRepositoryInterface::class);
        $userRepository->findById(Argument::type('int'))->willReturn((new User())
            ->setEmail('<EMAIL>')
            ->setLastname('lastname')
            ->setFirstname('firstname'));
        /** @var MerchantRepositoryInterface|ObjectProphecy $merchantRepository */
        $merchantRepository = $prophet->prophesize(MerchantRepositoryInterface::class);
        $merchantRepository->findByDistantId(Argument::type('int'))->willReturn((new Merchant())
            ->setCompanyName('companyName')
            ->setEmail('<EMAIL>'));

        $this->useCase = new ReplyAfterSaleThreadVendorUseCase(
            $replyAfterSale->reveal(),
            $emailService->reveal(),
            $getLocale->reveal(),
            $urlGenerator->reveal(),
            $afterSaleRepository->reveal(),
            $threadRepository->reveal(),
            $userRepository->reveal(),
            $merchantRepository->reveal()
        );
    }

    /**
     * @param ReplyAfterSaleThreadVendorRequest $request
     * @param ReplyAfterSaleThreadResponse $response
     * @dataProvider provideExecute
     * @throws TemplateException
     */
    public function testExecute(ReplyAfterSaleThreadVendorRequest $request, ReplyAfterSaleThreadResponse $response)
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($response, $this->presenter->response);
    }

    public function provideExecute(): Generator
    {
        //Given : nous voulons répondre à un SAV existant (1)
        //When : je soumet cette requête
        //Then : Le thread et le SAV correspondant
        $request =  new ReplyAfterSaleThreadVendorRequest(
            1,
            'toto',
            []
        );
        $response = new ReplyAfterSaleThreadResponse();
        yield 'reply ok' => [$request, $response];
    }
}
