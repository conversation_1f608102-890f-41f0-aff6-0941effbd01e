<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Presentation\View;

use Marketplace\Component\Discussion\Presentation\ViewModel\RefreshThreadViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class RefreshThreadView
{
    public function __construct(
        private SerializerInterface $serializer,
    ) {
    }

    public function generateView(RefreshThreadViewModel $viewModel): Response
    {
        return JsonResponse::fromJsonString($this->serializer->serialize(
            $viewModel,
            'json'
        ));
    }
}
