<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Presentation\View;

use Marketplace\Component\Discussion\Presentation\ViewModel\ListThreadsViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class ListThreadsView
{
    private static string $renderPage = 'pages/threads-list.html.twig';
    public function __construct(
        private SerializerInterface $serializer,
    ) {
    }

    public function generateView(ListThreadsViewModel $viewModel): Response
    {
        return JsonResponse::fromJsonString($this->serializer->serialize(
            $viewModel,
            'json'
        ));
    }
}
