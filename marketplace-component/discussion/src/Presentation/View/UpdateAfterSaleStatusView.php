<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Presentation\View;

use Marketplace\Component\Discussion\Presentation\ViewModel\UpdateAfterSaleStatusViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class UpdateAfterSaleStatusView
{
    public function __construct(
        private SerializerInterface $serializer,
    ) {
    }

    public function generateView(UpdateAfterSaleStatusViewModel $viewModel): Response
    {
        return JsonResponse::fromJsonString($this->serializer->serialize(
            $viewModel,
            'json'
        ));
    }
}
