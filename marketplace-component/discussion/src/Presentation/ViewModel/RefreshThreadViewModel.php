<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Presentation\ViewModel;

class RefreshThreadViewModel
{
    public bool $notFound = true;

    private array $messages = [];

    /**
     * @return array
     */
    public function getMessages(): array
    {
        return $this->messages;
    }

    /**
     * @param array $messages
     * @return RefreshThreadViewModel
     */
    public function setMessages(array $messages): RefreshThreadViewModel
    {
        $this->messages = $messages;
        return $this;
    }
}
