<?php

namespace Marketplace\Component\Discussion\Presentation\ViewModel;

use Marketplace\Component\Discussion\Domain\Model\AfterSales;
use Marketplace\Component\Discussion\Domain\Model\Thread;

class ReplyAfterSaleThreadViewModel
{
    private ?AfterSales $afterSales = null;

    private ?Thread $thread = null;

    private array $errors = [];

    /**
     * @return AfterSales|null
     */
    public function getAfterSales(): ?AfterSales
    {
        return $this->afterSales;
    }

    /**
     * @param AfterSales|null $afterSales
     * @return $this
     */
    public function setAfterSales(?AfterSales $afterSales): self
    {
        $this->afterSales = $afterSales;
        return $this;
    }

    /**
     * @return Thread|null
     */
    public function getThread(): ?Thread
    {
        return $this->thread;
    }

    /**
     * @param Thread|null $thread
     * @return $this
     */
    public function setThread(?Thread $thread): self
    {
        $this->thread = $thread;
        return $this;
    }

    /**
     * @return array
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * @param array $errors
     * @return $this
     */
    public function setErrors(array $errors): self
    {
        $this->errors = $errors;
        return $this;
    }
}
