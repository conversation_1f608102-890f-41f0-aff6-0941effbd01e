<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Presentation\ViewModel;

use Marketplace\Component\Discussion\Domain\Model\Thread;

class CreateOfferThreadViewModel
{
    private array $errors = [];

    private ?Thread $thread = null;

    /**
     * @return array
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * @param array $errors
     * @return CreateOfferThreadViewModel
     */
    public function setErrors(array $errors): CreateOfferThreadViewModel
    {
        $this->errors = $errors;
        return $this;
    }

    /**
     * @return Thread|null
     */
    public function getThread(): ?Thread
    {
        return $this->thread;
    }

    /**
     * @param Thread|null $thread
     * @return CreateOfferThreadViewModel
     */
    public function setThread(?Thread $thread): CreateOfferThreadViewModel
    {
        $this->thread = $thread;
        return $this;
    }
}
