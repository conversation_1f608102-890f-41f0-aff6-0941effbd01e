<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Presentation\Presenter;

use Marketplace\Component\Discussion\Domain\Presenter\AfterSalesListPresenterInterface;
use Marketplace\Component\Discussion\Presentation\ViewModel\AfterSalesListViewModel;
use Symfony\Contracts\Translation\TranslatorInterface;

class AfterSalesListPresenter implements AfterSalesListPresenterInterface
{
    private AfterSalesListViewModel $viewModel;

    public function __construct(
        private TranslatorInterface $translator,
    ) {
        $this->viewModel = new AfterSalesListViewModel($this->translator);
    }

    public function present(): void
    {
    }

    public function viewModel(array $menu): AfterSalesListViewModel
    {
        $this->viewModel->setMenu($menu);
        return $this->viewModel;
    }
}
