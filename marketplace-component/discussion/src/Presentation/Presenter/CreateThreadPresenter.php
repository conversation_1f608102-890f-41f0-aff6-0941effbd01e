<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Presentation\Presenter;

use Marketplace\Component\Discussion\Domain\Presenter\CreateThreadPresenterInterface;
use Marketplace\Component\Discussion\Domain\UseCase\CreateThread\DTO\CreateThreadResponse;
use Marketplace\Component\Discussion\Presentation\ViewModel\CreateThreadViewModel;

class CreateThreadPresenter implements CreateThreadPresenterInterface
{
    private CreateThreadViewModel $viewModel;

    public function __construct()
    {
        $this->viewModel = new CreateThreadViewModel();
    }

    public function present(CreateThreadResponse $response): void
    {
        $this->viewModel->notFound = $response->getThread() === null;
        $this->viewModel->setThread($response->getThread());
    }

    public function viewModel(): CreateThreadViewModel
    {
        return $this->viewModel;
    }
}
