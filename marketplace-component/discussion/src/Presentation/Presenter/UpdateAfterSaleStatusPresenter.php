<?php

namespace Marketplace\Component\Discussion\Presentation\Presenter;

use Marketplace\Component\Discussion\Domain\Presenter\UpdateAfterSaleStatusPresenterInterface;
use Marketplace\Component\Discussion\Domain\UseCase\UpdateAfterSaleStatus\DTO\UpdateAfterSaleStatusResponse;
use Marketplace\Component\Discussion\Presentation\ViewModel\UpdateAfterSaleStatusViewModel;
use Symfony\Contracts\Translation\TranslatorInterface;

class UpdateAfterSaleStatusPresenter implements UpdateAfterSaleStatusPresenterInterface
{
    private UpdateAfterSaleStatusViewModel $viewModel;


    public function __construct(private TranslatorInterface $translator)
    {
        $this->viewModel = new UpdateAfterSaleStatusViewModel();
    }

    public function present(UpdateAfterSaleStatusResponse $response): void
    {
        $this->viewModel->setSuccess($response->isSuccess());
        $errors = [];
        foreach ($response->getNotification()->getErrors() as $error) {
            $errors[$error->getFieldName() ?? 'unknown'][] =
                $this->translator->trans($error->getMessage(), domain: 'translations');
        }
        $this->viewModel->setErrors($errors);
    }
    public function viewModel(): UpdateAfterSaleStatusViewModel
    {
        return $this->viewModel;
    }
}
