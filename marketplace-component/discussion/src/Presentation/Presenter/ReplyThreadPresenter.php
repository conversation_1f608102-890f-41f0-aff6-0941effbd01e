<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Presentation\Presenter;

use Marketplace\Component\Discussion\Domain\Presenter\ReplyThreadPresenterInterface;
use Marketplace\Component\Discussion\Domain\UseCase\ReplyThread\DTO\ReplyThreadResponse;
use Marketplace\Component\Discussion\Presentation\ViewModel\ReplyThreadViewModel;
use Symfony\Contracts\Translation\TranslatorInterface;

class ReplyThreadPresenter implements ReplyThreadPresenterInterface
{
    private ReplyThreadViewModel $viewModel;

    public function __construct(private TranslatorInterface $translator)
    {
        $this->viewModel = new ReplyThreadViewModel($this->translator);
    }

    public function present(ReplyThreadResponse $response): void
    {
        $this->viewModel->notFound = $response->getThread() === null;
        $this->viewModel->setThread($response->getThread());
    }

    public function viewModel(): ReplyThreadViewModel
    {
        return $this->viewModel;
    }
}
