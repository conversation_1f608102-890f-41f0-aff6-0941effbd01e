<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Presentation\Presenter;

use Knp\Component\Pager\PaginatorInterface;
use Marketplace\Component\Discussion\Domain\Presenter\ListThreadsPresenterInterface;
use Marketplace\Component\Discussion\Domain\UseCase\ListThreads\DTO\ListThreadsResponse;
use Marketplace\Component\Discussion\Presentation\ViewModel\ListThreadsViewModel;
use Symfony\Contracts\Translation\TranslatorInterface;

class ListThreadsPresenter implements ListThreadsPresenterInterface
{
    private ListThreadsViewModel $viewModel;

    private PaginatorInterface $paginator;

    public function __construct(PaginatorInterface $paginator, private TranslatorInterface $translator)
    {
        $this->viewModel = new ListThreadsViewModel($this->translator);
        $this->paginator = $paginator;
    }

    public function present(ListThreadsResponse $response): void
    {
        $pagination = $this->paginator->paginate(
            $response->getMessages(),
            $response->getPage(),
            $response->getItemPerPage()
        );
        $parameter = [];
        $parameter['filteredSubject'] = $response->getFilteredSubject();
        $parameter['filteredCreationDateStart'] = $response->getFilteredCreationDateStart();
        $parameter['filteredCreationDateEnd'] = $response->getFilteredCreationDateEnd();
        $this->viewModel->setPaginatedResult($pagination);
        $this->viewModel->setFilterParameters($parameter);
    }

    public function viewModel(): ListThreadsViewModel
    {
        return $this->viewModel;
    }
}
