<?php

namespace Marketplace\Component\Discussion\Domain\UseCase\ShowAfterSales;

use Marketplace\Component\Discussion\Domain\Port\Repository\AfterSalesRepositoryInterface;
use Marketplace\Component\Discussion\Domain\Presenter\ShowAfterSalesPresenterInterface;
use Marketplace\Component\Discussion\Domain\UseCase\ShowAfterSales\DTO\ShowAfterSalesRequest;
use Marketplace\Component\Discussion\Domain\UseCase\ShowAfterSales\DTO\ShowAfterSalesResponse;

class ShowAfterSalesUseCase
{
    /**
     * ShowAfterSalesUseCase constructor.
     * @param AfterSalesRepositoryInterface $afterSalesRepository
     */
    public function __construct(private AfterSalesRepositoryInterface $afterSalesRepository)
    {
    }

    public function execute(ShowAfterSalesRequest $request, ShowAfterSalesPresenterInterface $presenter)
    {
        $status = $request->getStatus();
        $afterSaleType = $request->getAfterSaleType();
        $createdAtMax = $request->getCreatedAtMax();
        $createdAtMin = $request->getCreatedAtMin();
        $orderNumber = $request->getOrderNumber();
        $companyId = $request->getCompanyId();

        $resumedAfterSale = $this->afterSalesRepository->findResumedAfterSales(
            $afterSaleType,
            $status,
            $orderNumber,
            $createdAtMax,
            $createdAtMin,
            $companyId
        );

        $response = new ShowAfterSalesResponse(
            $resumedAfterSale,
            $request->getRequestedPage(),
            $request->getItemPerPage(),
            $orderNumber,
            $status,
            $createdAtMin,
            $createdAtMax
        );
        $presenter->present($response);
    }
}
