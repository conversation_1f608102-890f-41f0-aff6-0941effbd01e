<?php

namespace Marketplace\Component\Discussion\Domain\UseCase\UpdateAfterSaleStatus\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Error\NotificationTrait;

class UpdateAfterSaleStatusResponse
{
    use NotificationTrait;


    private bool $success = false;
/**
     * @return bool
     */
    public function isSuccess(): bool
    {
        return $this->success;
    }

    /**
     * @param bool $success
     * @return $this
     */
    public function setSuccess(bool $success): self
    {
        $this->success = $success;
        return $this;
    }
}
