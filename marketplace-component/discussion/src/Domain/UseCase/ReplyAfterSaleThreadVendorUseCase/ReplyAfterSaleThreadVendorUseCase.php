<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Domain\UseCase\ReplyAfterSaleThreadVendorUseCase;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetLocaleInterface;
use Marketplace\Component\Discussion\Domain\Model\AfterSales;
use Marketplace\Component\Discussion\Domain\Model\AfterSalesStatus;
use Marketplace\Component\Discussion\Domain\Model\Thread;
use Marketplace\Component\Discussion\Domain\Port\Repository\AfterSalesRepositoryInterface;
use Marketplace\Component\Discussion\Domain\Port\Repository\ThreadRepositoryInterface;
use Marketplace\Component\Discussion\Domain\Port\Service\ReplyAfterSaleInterface;
use Marketplace\Component\Discussion\Domain\Presenter\ReplyAfterSaleThreadPresenterInterface;
use Marketplace\Component\Discussion\Domain\UseCase\ReplyAfterSaleThread\DTO\ReplyAfterSaleThreadResponse;
use
Marketplace\Component\Discussion\Domain\UseCase\ReplyAfterSaleThreadVendorUseCase\DTO\ReplyAfterSaleThreadVendorRequest;
use Marketplace\Component\Mail\Domain\Model\EmailTemplateSlug;
use Marketplace\Component\Mail\Domain\Port\Service\EmailServiceInterface;
use Marketplace\Component\Mail\Infrastructure\Exception\TemplateException;
use Marketplace\Component\User\Domain\Model\Merchant;
use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Port\Repository\MerchantRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

final class ReplyAfterSaleThreadVendorUseCase
{
    public function __construct(
        private ReplyAfterSaleInterface $replyAfterSale,
        private readonly EmailServiceInterface $emailService,
        private readonly GetLocaleInterface $getLocale,
        private readonly UrlGeneratorInterface $urlGenerator,
        private readonly AfterSalesRepositoryInterface $afterSalesRepository,
        private readonly ThreadRepositoryInterface $threadRepository,
        private readonly UserRepositoryInterface $userRepository,
        private readonly MerchantRepositoryInterface $merchantRepository
    ) {
    }

    /** @throws TemplateException */
    public function execute(
        ReplyAfterSaleThreadVendorRequest $request,
        ReplyAfterSaleThreadPresenterInterface $presenter
    ): void {
        $response = new ReplyAfterSaleThreadResponse();
        /**
         * @var int $afterSaleId
         */
        $afterSaleId = $request->afterSaleId;

        $this->replyAfterSale->reply(
            isVendor: true,
            status: AfterSalesStatus::ACTIVE,
            afterSaleId: $afterSaleId,
            message: $request->message,
            files: $request->files,
            recipient: ThreadRepositoryInterface::USER,
            response: $response,
        );

        /** @var AfterSales $afterSale */
        $afterSale = $this->afterSalesRepository->findAfterSale($afterSaleId);
        /** @var int $threadId */
        $threadId = $afterSale->getThreadId();
        /** @var Thread $thread */
        $thread = $this->threadRepository->findThreadById($threadId);
        $merchant = $this->merchantRepository->findByDistantId((int)$thread->getVendor());
        if (!$merchant instanceof Merchant) {
            $response->getNotification()->addError('merchantDistantId', 'merchant.distant_id.unknown');
            $presenter->present($response);
            return;
        }
        $userId = $thread->getUserId();
        if (!is_int($userId)) {
            $response->getNotification()->addError('thread', 'thread.user_id.unknown');
            $presenter->present($response);
            return;
        }
        $user = $this->userRepository->findById($userId);
        if (!$user instanceof User) {
            $response->getNotification()->addError('userId', 'user.id.unknown');
            $presenter->present($response);
            return;
        }

        $this->emailService->send(
            [$this->emailService->getNoReplyUser()],
            [$user->getEmailInfos()],
            EmailTemplateSlug::AFTER_SALES_SERVICE_MESSAGE_MERCHANT_TO_USER,
            $this->getLocale->getLocale(),
            [
                'messageDate' => $thread->getLastMessageDate()->format('d/m/Y H:i:s'),
                'merchantName' => $merchant->getCompanyName(),
                'messageContent' => $request->message,
                'ctaToSavAsk' => $this->urlGenerator->generate(
                    name: 'after_sales_detail',
                    parameters: ['afterSaleId' => $afterSaleId],
                    referenceType: UrlGeneratorInterface::ABSOLUTE_URL
                )
            ]
        );
        $presenter->present($response);
    }
}
