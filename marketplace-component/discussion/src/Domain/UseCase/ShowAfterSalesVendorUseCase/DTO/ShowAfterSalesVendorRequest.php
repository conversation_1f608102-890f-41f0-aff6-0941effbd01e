<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Domain\UseCase\ShowAfterSalesVendorUseCase\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Model\Interface\PaginatedRequestInterface;

final class ShowAfterSalesVendorRequest implements PaginatedRequestInterface
{
    public function __construct(
        private string $afterSaleType,
        private int $vendorId,
        private string $orderNumber = "",
        private string $status = "",
        private string $createdAtMin = "",
        private string $createdAtMax = "",
        private int $requestedPage = self::DEFAULT_PAGE,
        private int $itemPerPage = self::DEFAULT_VENDORS_PER_PAGE
    ) {
    }

    /**
     * @return string
     */
    public function getAfterSaleType(): string
    {
        return $this->afterSaleType;
    }

    /**
     * @return string
     */
    public function getOrderNumber(): string
    {
        return $this->orderNumber;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @return string
     */
    public function getCreatedAtMin(): string
    {
        return $this->createdAtMin;
    }

    /**
     * @return string
     */
    public function getCreatedAtMax(): string
    {
        return $this->createdAtMax;
    }

    /**
     * @return int
     */
    public function getRequestedPage(): int
    {
        return $this->requestedPage;
    }

    /**
     * @return int
     */
    public function getItemPerPage(): int
    {
        return $this->itemPerPage;
    }

    /**
     * @return int
     */
    public function getVendorId(): int
    {
        return $this->vendorId;
    }
}
