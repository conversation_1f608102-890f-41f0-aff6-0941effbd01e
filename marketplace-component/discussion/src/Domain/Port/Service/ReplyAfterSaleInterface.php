<?php

namespace Marketplace\Component\Discussion\Domain\Port\Service;

use Marketplace\Component\Discussion\Domain\UseCase\ReplyAfterSaleThread\DTO\ReplyAfterSaleThreadResponse;

interface ReplyAfterSaleInterface
{
    public function reply(
        bool $isVendor,
        string $status,
        int $afterSaleId,
        string $message,
        array $files,
        string $recipient,
        ReplyAfterSaleThreadResponse &$response
    ): bool;
}
