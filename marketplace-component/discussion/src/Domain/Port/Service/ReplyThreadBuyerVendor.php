<?php

namespace Marketplace\Component\Discussion\Domain\Port\Service;

use Marketplace\Component\Discussion\Domain\Exception\BadRecipientException;
use Marketplace\Component\Discussion\Domain\Model\Thread;
use Marketplace\Component\Discussion\Domain\Port\Repository\ThreadRepositoryInterface;

class ReplyThreadBuyerVendor
{
    public function __construct(
        private ThreadRepositoryInterface $threadRepository,
    ) {
    }

    public function replyThreadBuyerVendor(
        int $threadId,
        string $message,
        array $files,
        string $recipient,
        ?string $messageType = null,
        ?string $messageLabel = null
    ): ?Thread {
        $thread = $this->threadRepository->findThreadById($threadId);
        if (!$thread instanceof Thread) {
            return null;
        }
        if ($recipient === ThreadRepositoryInterface::VENDOR) {
            $thread = $this->threadRepository
                ->replyToMerchant($threadId, $message, $files, (int)$thread->getVendor(), $messageType, $messageLabel);
        } elseif ($recipient === ThreadRepositoryInterface::USER) {
            $thread = $this->threadRepository
                ->replyToUser(
                    $threadId,
                    $message,
                    $files,
                    (int)$thread->getVendor(),
                    $thread->getCompanyId(),
                    $messageType,
                    $messageLabel
                );
        } else {
            throw new BadRecipientException($recipient);
        }
        return $thread;
    }
}
