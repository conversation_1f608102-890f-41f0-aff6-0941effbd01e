<?php

namespace Marketplace\Component\Discussion\Domain\Port\Repository;

use Doctrine\ORM\QueryBuilder;
use Marketplace\Component\Discussion\Domain\Model\AfterSales;

interface AfterSalesRepositoryInterface
{
    public function createAfterSales(AfterSales $afterSales): AfterSales;

    public function updateAfterSales(AfterSales $afterSales): AfterSales;

    public function findAfterSale(int $afterSaleId): ?AfterSales;

    public function findResumedAfterSalesQB(
        string $afterSaleType,
        string $status,
        string $orderNumber,
        string $createdAtMax,
        string $createdAtMin,
        ?int $companyId = null,
        ?int $merchantId = null
    ): QueryBuilder;

    public function findResumedAfterSales(
        string $afterSaleType,
        string $status,
        string $orderNumber,
        string $createdAtMax,
        string $createdAtMin,
        ?int $companyId = null,
        ?int $merchantId = null
    ): array;

    public function findByThreadId(int $threadId): ?AfterSales;

    public function markAsRead(int $afterSaleId, bool $isVendor = false): void;
}
