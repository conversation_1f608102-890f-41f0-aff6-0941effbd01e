<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Infrastructure\Entity;

use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use Marketplace\Component\User\Infrastructure\Entity\Company;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Marketplace\Component\Discussion\Infrastructure\Adapter\Repository\ThreadRepository;

/**
 * @ORM\Entity(repositoryClass=ThreadRepository::class)
 */
class Thread
{

    /**
     * @ORM\Id
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\ManyToOne(targetEntity="Marketplace\Component\User\Infrastructure\Entity\User", cascade={"persist"})
     * ORM\JoinColumn(name="user_id", referencedColumnName="id", nullable=true)
     */
    private User $user;

    /**
     * @ORM\ManyToOne(targetEntity="Marketplace\Component\User\Infrastructure\Entity\Company", cascade={"persist"})
     * @ORM\JoinColumn(name="company_id", referencedColumnName="id", nullable=true)
     */
    private ?Company $company;

    /**
     * @ORM\Column(type="string", length=50, nullable=true)
     */
    private string $vendor;

    /**
     * @ORM\Column(type="string", length=200, nullable=true)
     */
    private string $subject;

    /**
     * @ORM\Column(type="datetime_immutable", nullable=false)
     */
    private DateTimeImmutable $createdAt;

    /**
     * @ORM\Column(type="datetime_immutable", nullable=true)
     */
    private DateTimeImmutable $lastMessageDate;

    /**
     * @ORM\Column(type="string", length=15, nullable=false)
     */
    private string $type;

    /**
     * @ORM\Column(type="string", length=15, nullable=false)
     */
    private string $status;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private ?int $buyerUnread;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private ?int $vendorUnread;

    /**
     * Cart constructor.
     */
    public function __construct()
    {
        $this->createdAt = new DateTimeImmutable();
        $this->lastMessageDate = new DateTimeImmutable();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): Thread
    {
        $this->user = $user;
        return $this;
    }

    /**
     * @return Company|null
     */
    public function getCompany(): ?Company
    {
        return $this->company;
    }

    /**
     * @param Company|null $company
     * @return Thread
     */
    public function setCompany(?Company $company): Thread
    {
        $this->company = $company;
        return $this;
    }

    public function getVendor(): string
    {
        return $this->vendor;
    }

    public function setVendor(string $vendor): Thread
    {
        $this->vendor = $vendor;
        return $this;
    }

    public function getSubject(): string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): Thread
    {
        $this->subject = $subject;
        return $this;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTimeImmutable $createdAt): Thread
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getLastMessageDate(): DateTimeImmutable
    {
        return $this->lastMessageDate;
    }

    public function setLastMessageDate(DateTimeImmutable $lastMessageDate): Thread
    {
        $this->lastMessageDate = $lastMessageDate;
        return $this;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @param string $type
     * @return Thread
     */
    public function setType(string $type): Thread
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return Thread
     */
    public function setStatus(string $status): Thread
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getBuyerUnread(): ?int
    {
        return $this->buyerUnread;
    }

    /**
     * @param int|null $buyerUnread
     * @return Thread
     */
    public function setBuyerUnread(?int $buyerUnread): Thread
    {
        $this->buyerUnread = $buyerUnread;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getVendorUnread(): ?int
    {
        return $this->vendorUnread;
    }

    /**
     * @param int|null $vendorUnread
     * @return Thread
     */
    public function setVendorUnread(?int $vendorUnread): Thread
    {
        $this->vendorUnread = $vendorUnread;
        return $this;
    }
}
