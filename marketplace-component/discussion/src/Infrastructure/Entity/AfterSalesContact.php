<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Infrastructure\Entity;

use Doctrine\ORM\Mapping as ORM;
use Marketplace\Component\User\Infrastructure\Entity\Country;

/**
 * @ORM\Table(name="after_sales_contacts")
 * @ORM\Entity(repositoryClass="AfterSalesContactRepository::class")
 */
class AfterSalesContact
{
    /**
     * @ORM\Column(type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private int $id;

    /**
     * @ORM\Column(name="department", type="string", nullable=false)
     */
    private string $department;

    /**
     * @var String
     * @ORM\Column(type="string", length=50, nullable=false)
     */
    private string $qualityTrigram;

    /**
     * @var String
     * @ORM\Column(type="string", length=50, nullable=false)
     */
    private string $qualityName;

    /**
     * @ORM\Column(type="json")
     */
    private array $qualityEmails = [];

    /**
     * @ORM\ManyToOne(targetEntity="Marketplace\Component\User\Infrastructure\Entity\Country")
     */
    private Country $country;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getDepartment(): string
    {
        return $this->department;
    }

    public function setDepartment(string $department): self
    {
        $this->department = $department;
        return $this;
    }

    public function getQualityTrigram(): string
    {
        return $this->qualityTrigram;
    }

    public function setQualityTrigram(string $qualityTrigram): self
    {
        $this->qualityTrigram = $qualityTrigram;
        return $this;
    }

    public function getQualityName(): string
    {
        return $this->qualityName;
    }

    public function setQualityName(string $qualityName): self
    {
        $this->qualityName = $qualityName;
        return $this;
    }

    public function getQualityEmails(): array
    {
        return $this->qualityEmails;
    }

    public function setQualityEmails(array $qualityEmails): self
    {
        $this->qualityEmails = $qualityEmails;
        return $this;
    }

    /**
     * @return Country
     */
    public function getCountry(): Country
    {
        return $this->country;
    }

    /**
     * @param Country $country
     * @return AfterSalesContact
     */
    public function setCountry(Country $country): self
    {
        $this->country = $country;
        return $this;
    }
}
