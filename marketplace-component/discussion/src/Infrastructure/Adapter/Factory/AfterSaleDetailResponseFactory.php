<?php

namespace Marketplace\Component\Discussion\Infrastructure\Adapter\Factory;

use Marketplace\Component\Discussion\Domain\Model\AfterSales;
use Marketplace\Component\Discussion\Domain\Model\Thread;
use Marketplace\Component\Discussion\Domain\Port\Factory\AfterSaleDetailResponseFactoryInterface;
use Marketplace\Component\Discussion\Domain\Port\Repository\AfterSalesRepositoryInterface;
use Marketplace\Component\Discussion\Domain\Port\Repository\ThreadRepositoryInterface;
use Marketplace\Component\Order\Domain\Model\Order;
use Marketplace\Component\Order\Domain\Port\Repository\OrderItemRepositoryInterface;
use Marketplace\Component\Discussion\Domain\UseCase\ShowAfterSaleDetails\DTO\ShowAfterSaleDetailsResponse;
use Marketplace\Component\Order\Domain\Port\Repository\OrderRepositoryInterface;

class AfterSaleDetailResponseFactory implements AfterSaleDetailResponseFactoryInterface
{
    public function __construct(
        private AfterSalesRepositoryInterface $afterSalesRepository,
        private OrderItemRepositoryInterface $orderItemRepository,
        private OrderRepositoryInterface $orderRepository,
        private ThreadRepositoryInterface $threadRepository
    ) {
    }
    public function build(int $afterSaleId, ShowAfterSaleDetailsResponse &$response): bool
    {
        $afterSale = $this->afterSalesRepository->findAfterSale($afterSaleId);
        if (!$afterSale instanceof AfterSales) {
            $response->getNotification()->addError('afterSaleId', 'after_sale.id.unkown');
            return false;
        }

        $afterSale->setBuyerUnread(false);
        $this->afterSalesRepository->updateAfterSales($afterSale);

        /**
         * @var int $threadId
         */
        $threadId = $afterSale->getThreadId();
        /**
         * @var Thread $thread
         */
        $thread = $this->threadRepository->findThreadById($threadId);
        $orderItems = $this->orderItemRepository->getOrderItems($afterSale->getOrderItemIds());
        $cartId = $orderItems[0]->getMerchantOrder()->getCartId();
        /** @var Order $order */
        $order = $this->orderRepository->findByCartId($cartId);
        /** @var int $orderId */
        $orderId = $order->getIzbergId();
        $response->setAfterSales($afterSale);
        $response->setOrderItems($orderItems);
        $response->setThread($thread);
        $response->setOrderId($orderId);
        return true;
    }
}
