<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Infrastructure\Adapter\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Marketplace\Component\Discussion\Domain\Model\AfterSalesContact;
use Marketplace\Component\Discussion\Domain\Port\Repository\AfterSalesContactRepositoryInterface;
use Marketplace\Component\Discussion\Infrastructure\Entity\AfterSalesContact as DoctrineAfterSalesContact;

class AfterSalesContactRepository extends ServiceEntityRepository implements AfterSalesContactRepositoryInterface
{
    public function __construct(
        ManagerRegistry $registry,
        private EntityManagerInterface $entityManager
    ) {
        parent::__construct($registry, DoctrineAfterSalesContact::class);
    }

    public function createDoctrine(DoctrineAfterSalesContact $afterSalesContact): void
    {
        $this->entityManager->persist($afterSalesContact);
    }

    public function save(): void
    {
        $this->entityManager->flush();
        $this->entityManager->clear();
    }

    public function getContactByCountryAndDepartment(int $countryId, ?string $department = null): ?AfterSalesContact
    {
        $doctrineAfterSalesContactQuery = $this->createQueryBuilder('after');

        if ($department !== null) {
            $doctrineAfterSalesContactQuery->andWhere('after.department = :department')
                ->setParameter('department', $department);
        }

        $doctrineAfterSalesContact = $doctrineAfterSalesContactQuery
            ->andWhere('after.country = :countryId')
            ->setParameter('countryId', $countryId)
            ->getQuery()->getOneOrNullResult();

        if ($doctrineAfterSalesContact === null) {
            return null;
        }

        return (new AfterSalesContact())
            ->setId($doctrineAfterSalesContact->getId())
            ->setDepartment($doctrineAfterSalesContact->getDepartment())
            ->setQualityTrigram($doctrineAfterSalesContact->getQualityTrigram())
            ->setQualityName($doctrineAfterSalesContact->getQualityName())
            ->setQualityEmails($doctrineAfterSalesContact->getQualityEmails());
    }
}
