<?php

declare(strict_types=1);

namespace Marketplace\Component\Discussion\Infrastructure\Mapper;

use DateTime;
use DateTimeImmutable;
use Marketplace\Component\Discussion\Domain\Model\MessageDetail;
use Marketplace\Component\Discussion\Domain\Model\Thread;
use Marketplace\Component\Discussion\Infrastructure\Entity\Thread as DoctrineThread;
use Marketplace\Component\Discussion\Infrastructure\Entity\ThreadStatus;
use Marketplace\Component\Discussion\Infrastructure\Entity\ThreadType;
use Marketplace\Component\User\Infrastructure\Entity\Company;
use Marketplace\Component\User\Infrastructure\Entity\User;

abstract class ThreadMapper
{
    public static function doctrineToModel(?DoctrineThread $doctrineThread): ?Thread
    {
        if (is_null($doctrineThread)) {
            return null;
        }
        $thread = new Thread();
        $thread->setId($doctrineThread->getId());
        $thread->setUserId($doctrineThread->getUser()->getId());
        $thread->setVendor($doctrineThread->getVendor());
        $thread->setSubject($doctrineThread->getSubject());
        $thread->setCreatedAt($doctrineThread->getCreatedAt());
        $thread->setLastMessageDate($doctrineThread->getLastMessageDate());
        $thread->setStatus($doctrineThread->getStatus());
        /**
         * @var Company $company
         */
        $company = $doctrineThread->getCompany();
        $thread->setCompanyId($company->getId());
        $thread->setBuyerUnread($doctrineThread->getBuyerUnread());
        $thread->setType($doctrineThread->getType());

        return $thread;
    }

    public static function modelToDoctrine(Thread $thread): DoctrineThread
    {
        $doctrineThread = new DoctrineThread();
        $doctrineThread->setId($thread->getId());
        $doctrineThread->setVendor($thread->getVendor());
        $doctrineThread->setSubject($thread->getSubject());
        $doctrineThread->setCreatedAt($thread->getCreatedAt());
        $doctrineThread->setLastMessageDate($thread->getLastMessageDate());
        $doctrineThread->setBuyerUnread($thread->getBuyerUnread());
        $doctrineThread->setType($thread->getType());

        return $doctrineThread;
    }

    public static function modelDetailToDoctrine(MessageDetail $messageDetail, User $user): DoctrineThread
    {
        $dateNow = DateTimeImmutable::createFromMutable((new DateTime('now')));
        $vendor = $messageDetail->getReceiverId();
        return (new DoctrineThread())
            ->setUser($user)
            ->setSubject($messageDetail->getSubject())
            ->setCompany($user->getCompany())
            ->setId($messageDetail->getId())
            ->setCreatedAt($dateNow)
            ->setVendor((string)$vendor)
            ->setLastMessageDate($dateNow)
            ->setType(ThreadType::getThreadType($messageDetail->getType()))
            ->setStatus(ThreadStatus::STATUS_OPEN)
            ->setBuyerUnread(null)
            ->setType($messageDetail->getType());
    }
}
