<?php

declare(strict_types=1);

namespace Unit\Infrastructure\Adapter\Service;

use Marketplace\Component\Order\Infrastructure\Adapter\Service\FetchCarrierService;
use Open\Izberg\Api\OrderApi;
use Open\Izberg\Client\OperatorClient;
use Open\Izberg\Exception\IzbergShippingOptionNotFoundException;
use Open\Izberg\Model\ShippingOption;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;

final class FetchCarrierServiceTest extends TestCase
{
    private FetchCarrierService $carrierService;

    protected function setUp(): void
    {
        parent::setUp();

        $prophecy = new Prophet();

        /** @var OrderApi|ObjectProphecy $orderApiMock */
        $orderApiMock = $prophecy->prophesize(OrderApi::class);
        $orderApiMock
            ->fetchShippingOptionsOfMerchantOrder(Argument::type('int'))
            ->will(function (array $args): ?ShippingOption {
                $merchantOrderId = $args[0];
                if ($merchantOrderId < 1) {
                    return (new ShippingOption())->setObjects([['carrier' => ['idd' => 2, 'name' => 'carrierName']]]);
                }
                if ($merchantOrderId === 3) {
                    return null;
                }
                return (new ShippingOption())->setObjects([['carrier' => ['id' => '2', 'name' => 'carrierName']]]);
            });

        /** @var OperatorClient|ObjectProphecy $operatorClient */
        $operatorClient = $prophecy->prophesize(OperatorClient::class);
        $operatorClient->orderApi()->willReturn($orderApiMock);

        $this->carrierService = new FetchCarrierService($operatorClient->reveal());
    }

    public function testGetCarrierIdData(): void
    {
        $carrierId = $this->carrierService->findCarrierIdByMerchantOrder(2);

        $this->assertEquals(2, $carrierId);
    }

    public function testGetCarrierNameData(): void
    {
        $carrierName = $this->carrierService->getCarrierName(2);

        $this->assertEquals('carrierName', $carrierName);
    }

    public function testGetCarrierDataWithInvalidFieldName(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Carrier not found for 0 merchant order');

        $carrierId = $this->carrierService->findCarrierIdByMerchantOrder(0);

        $this->assertEquals(2, $carrierId);
    }

    public function testThrowExceptionIfShippingOptionIsNull(): void
    {
        $this->expectException(IzbergShippingOptionNotFoundException::class);

        $carrierId = $this->carrierService->findCarrierIdByMerchantOrder(3);

        $this->assertEquals(2, $carrierId);
    }
}
