<?php

declare(strict_types=1);

namespace Unit\Infrastructure\Adapter\Service;

use Exception;
use Marketplace\Component\Order\Infrastructure\Adapter\Service\Warehouse\FetchWarehouseService;
use Open\Izberg\Api\OrderApi;
use Open\Izberg\Client\OperatorClient;
use Open\Izberg\Exception\IzbergShippingOptionNotFoundException;
use Open\Izberg\Model\ShippingOption;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;

final class FetchWarehouseServiceTest extends TestCase
{
    private FetchWarehouseService $warehouseService;

    protected function setUp(): void
    {
        parent::setUp();

        $prophecy = new Prophet();

        /** @var OrderApi|ObjectProphecy $orderApiMock */
        $orderApiMock = $prophecy->prophesize(OrderApi::class);
        $orderApiMock
            ->fetchShippingOptionsOfMerchantOrder(Argument::type('int'))
            ->will(function (array $args): ?ShippingOption {
                $merchantOrderId = $args[0];
                if ($merchantOrderId < 1) {
                    return (new ShippingOption())->setObjects([['method' => 'option']]);
                }
                if ($merchantOrderId === 2) {
                    return (new ShippingOption())->setObjects([['name' => '']]);
                }
                if ($merchantOrderId === 3) {
                    return null;
                }
                return (new ShippingOption())->setObjects([['name' => 'WAREHOUSE_1']]);
            });

        /** @var OperatorClient|ObjectProphecy $operatorClient */
        $operatorClient = $prophecy->prophesize(OperatorClient::class);
        $operatorClient->orderApi()->willReturn($orderApiMock);

        $this->warehouseService = new FetchWarehouseService($operatorClient->reveal());
    }

    public function testGetWarehouseMethod(): void
    {
        $this->assertSame('WAREHOUSE_1', $this->warehouseService->warehouseNameFromMethod(4));
    }

    public function testThrowExceptionForMerchantOrderIsNull(): void
    {
        $this->expectException(IzbergShippingOptionNotFoundException::class);

        $this->warehouseService->warehouseNameFromMethod(3);
    }

    public function testThrowExceptionForMethodKeyIsInvalid(): void
    {
        $this->expectException(Exception::class);

        $this->warehouseService->warehouseNameFromMethod(0);
    }

    public function testGivenGetTheWarehouseMethodOfMerchantOrder(): void
    {
        // Where: The shipping method is empty

        // Then: The method should return null
        $this->assertNull($this->warehouseService->warehouseNameFromMethod(2));
    }
}
