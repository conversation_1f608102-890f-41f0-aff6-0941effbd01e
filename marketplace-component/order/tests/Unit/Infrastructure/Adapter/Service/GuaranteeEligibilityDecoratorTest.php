<?php

namespace Unit\Infrastructure\Adapter\Service;

use Generator;
use Marketplace\Component\Invoice\Domain\Model\Parcel;
use Marketplace\Component\Invoice\Domain\Model\ParcelItem;
use Marketplace\Component\Invoice\Infrastructure\Adapter\Repository\ParcelRepository;
use Marketplace\Component\Order\Domain\Model\MerchantOrder;
use Marketplace\Component\Order\Domain\Model\Order;
use Marketplace\Component\Order\Domain\Model\OrderItem;
use Marketplace\Component\Order\Infrastructure\Adapter\Service\GuaranteeEligibilityDecorator;
use Marketplace\Component\Payment\Domain\Model\Method\TermPaymentMethod;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

class GuaranteeEligibilityDecoratorTest extends TestCase
{
    private GuaranteeEligibilityDecorator $decorator;

    protected function setUp(): void
    {
        parent::setUp();

        $parcelRepository = (new Prophet())->prophesize(ParcelRepository::class);
        $parcelRepository->findParcelsByMerchantOrder(Argument::type('integer'))->will(function ($args) {
            return [
                (new Parcel())
                    ->setCarrierName("Colissimo")
                    ->setStatus(Parcel::STATUS_RECEIVED)
                    ->setEffectiveDeliveryDate(new \DateTimeImmutable("2021-12-01"))
                    ->setParcelItems([
                        (new ParcelItem())
                            ->setOrderItemId(123)
                            ->setOfferName("item1")
                            ->setQuantity(2)
                    ]),
                (new Parcel())
                    ->setCarrierName("Colissimo")
                    ->setStatus(Parcel::STATUS_IN_TRANSIT)
                    ->setParcelItems([
                        (new ParcelItem())
                            ->setOrderItemId(321)
                            ->setOfferName("item1")
                            ->setQuantity(2)
                    ])
            ];
        });

        $this->decorator = new GuaranteeEligibilityDecorator($parcelRepository->reveal());
    }


    /**
     * @param Order $order
     * @param bool $expected
     * @dataProvider dataProvider
     */
    public function testDecorator(Order $order, bool $expected): void
    {
        $this->decorator->decorate($order);
        $this->assertEquals($expected, $order->getMerchantOrders()[0]->getOrderItems()[0]->isGuaranteeEligible());
    }

    public function dataProvider(): Generator
    {
        // Given an order
        // WHEN on appelle le GuaranteeEligibilityDecorator->decorate
        // Then on retourne un  order avec un init des garantie eligible

        $order = (new Order(1, new TermPaymentMethod(), 1))
            ->setMerchantOrders([
                (new MerchantOrder(1, 1))
                    ->setOrderItems([
                        (new OrderItem(1))
                            ->setDistantId(123)
                            ->setGuaranteeMonth(24)
                    ])
                    ->setId(1)
            ]);

        // Scenario 1: order with one eligible item
        yield 'Scenario 1: eligible' => [$order, true];

        $order = (new Order(1, new TermPaymentMethod(), 1))
            ->setMerchantOrders([
                (new MerchantOrder(1, 1))
                    ->setOrderItems([
                        (new OrderItem(1))
                            ->setDistantId(321)
                    ])
                    ->setId(1)
            ]);

        // Scenario 2: order with one  item not eligible
        yield 'Scenario 2: not eligible' => [$order, false];

        $order = (new Order(1, new TermPaymentMethod(), 1))
            ->setMerchantOrders([
                (new MerchantOrder(1, 1))
                    ->setOrderItems([
                        (new OrderItem(1))
                            ->setDistantId(666)
                    ])
                    ->setId(1)
            ]);

        // Scenario 3: order with one  item but no corresponding parcel
        yield 'Scenario 3: no corresponding parcel' => [$order, false];
    }
}
