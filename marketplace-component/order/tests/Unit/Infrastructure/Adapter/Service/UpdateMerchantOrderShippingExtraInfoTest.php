<?php

namespace Marketplace\Component\Order\Infrastructure\Adapter\Service;

use Open\Izberg\Api\AttributeApi;
use Open\Izberg\Client\OperatorClient;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

class UpdateMerchantOrderShippingExtraInfoTest extends TestCase
{
    private UpdateMerchantOrderShippingExtraInfo $service;
    protected function setUp():void
    {
        parent::setUp();
        $prophet = new Prophet();
        $client = $prophet->prophesize(OperatorClient::class);
        $attributeApi = $prophet->prophesize(AttributeApi::class);
        $attributeApi->getMerchantAttributeId(Argument::type('string'))->willReturn(1);

        $client->attributeApi()->willReturn($attributeApi->reveal());
        $this->service = new UpdateMerchantOrderShippingExtraInfo($client->reveal());
    }


    public function testUpdateShippingExtraInfo()
    {
        $this->service->updateShippingExtraInfo(1,'attribute','value');
        $this->assertTrue(true);
    }
}
