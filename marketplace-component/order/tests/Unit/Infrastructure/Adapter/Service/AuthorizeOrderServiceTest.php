<?php

namespace Marketplace\Component\Order\Infrastructure\Adapter\Service;

use Marketplace\Component\Order\Domain\Exception\AuthorizeOrderException;
use Marketplace\Component\Order\Domain\UseCase\SyncOrder\DTO\SyncOrderRequest;
use Open\Izberg\Api\OrderApi;
use Open\Izberg\Api\PaymentApi;
use Open\Izberg\Client\OperatorClient;
use Open\Izberg\Exception\NotFoundException;
use Open\Izberg\Model\Order;
use Open\Izberg\Model\OrderPayment;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

class AuthorizeOrderServiceTest extends TestCase
{
    private AuthorizeOrderService $authorizeOrderService;
    private MessageBusInterface $messageBus;

    protected function setUp(): void
    {
        parent::setUp();

        $prophecy = new Prophet();

        /** @var OrderApi|ObjectProphecy $orderApiMock */
        $orderApiMock = $prophecy->prophesize(OrderApi::class);
        $orderApiMock
            ->fetchOrderByExternalId(Argument::type('string'))
            ->will(function (array $args): Order {
                $id = $args[0];
                if ($id === "0") {
                    throw new NotFoundException(sprintf('Order not found with order external ID "%s"', $id));
                }

                $payment = new OrderPayment();
                $payment->setId($id);
                $order = new Order();
                $order->setId($id)
                    ->setStatus(0)
                    ->setPayment($payment);
                return $order;
            });

        $paymentApiMock = $prophecy->prophesize(PaymentApi::class);

        /** @var OperatorClient|ObjectProphecy $operatorClient */
        $operatorClient = $prophecy->prophesize(OperatorClient::class);
        $operatorClient->orderApi()->willReturn($orderApiMock);
        $operatorClient->paymentApi()->willReturn($paymentApiMock);

        $this->messageBus = new class implements MessageBusInterface {
            public Envelope $envelope;

            public function dispatch(object $message, array $stamps = []): Envelope
            {
                $this->envelope = new Envelope($message);
                return $this->envelope;
            }
        };
        $logger = $prophecy->prophesize(LoggerInterface::class);

        $this->authorizeOrderService = new AuthorizeOrderService(
            $operatorClient->reveal(),
            $this->messageBus,
            1,
            1,
            $logger->reveal()
        );
    }

    public function testAuthorize()
    {
        $this->authorizeOrderService->authorize(1);
        $this->assertEquals(new Envelope(new SyncOrderRequest(1, true)), $this->messageBus->envelope);
    }

    public function testAuthorizeException()
    {
        $this->expectException(AuthorizeOrderException::class);
        $this->expectExceptionMessage('cannot retrieve order in izberg with external id : ' . 0);
        $this->authorizeOrderService->authorize(0);
    }
}
