<?php

namespace Marketplace\Component\Order\Infrastructure\Utils;

use Marketplace\Component\Order\Infrastructure\Entity\OrderItem;
use PHPUnit\Framework\TestCase;

class OrderItemUtilsTest extends TestCase
{

    /**
     * @param array $orderItems
     * @param OrderItem|null $expected
     * @dataProvider provideGetMostExpensiveOrderItem
     */
    public function testGetMostExpensiveOrderItem(array $orderItems, ?OrderItem $expected)
    {
        $result = OrderItemUtils::getMostExpensiveOrderItem($orderItems);
        $this->assertEquals($expected, $result);
    }

    public function provideGetMostExpensiveOrderItem(){
        //given je veux obtenir orderItem le plus cher d'un tableau vide
        //when j'appel getMostExpensiveOrderItem
        //then on me retourne null
        yield 'empty array'=>[[], null];

        //given je veux obtenir orderItem le plus cher d'un tableau d'un élément
        //when j'appel getMostExpensiveOrderItem
        //then on me retourne le seul élément du tableau
        $orderItem = new OrderItem();
        $orderItem->setId(1);
        $orderItem->setAmountTTC(10);
        $orderItem->setQuantity(1);
        yield 'one element array'=>[[$orderItem], $orderItem];

        //given je veux obtenir orderItem le plus cher d'un tableau de plusieurs éléments
        //when j'appel getMostExpensiveOrderItem
        //then on me retourne l'élément le plus cher
        $orderItembis = new OrderItem();
        $orderItembis->setId(2);
        $orderItembis->setAmountTTC(10);
        $orderItembis->setQuantity(100);
        yield 'multiple elements array'=>[[$orderItem, $orderItembis], $orderItembis];
    }


}
