<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Tests\Infratructure\ValueObject;

use Marketplace\Component\Order\Domain\Exception\InvalidWarehouseException;
use Marketplace\Component\Order\Domain\ValueObject\WarehouseCode\WarehouseCode;
use PHPUnit\Framework\TestCase;

final class WarehouseCodeTest extends TestCase
{
    /**
     * @dataProvider provideWarehouseMethod
     */
    public function testCreateFromWarehouse(string $warehouseMethod, string $expected): void
    {
        $warehouseCode = WarehouseCode::createFromMethod($warehouseMethod);

        $this->assertInstanceOf(WarehouseCode::class, $warehouseCode);
        $this->assertSame($expected, (string) $warehouseCode);
    }

    public function testThrowExceptionInCreateFromWarehouse(): void
    {
        $this->expectException(InvalidWarehouseException::class);
        $this->expectExceptionMessage(
            'The Fail warehouse or click and collect does not exist in Warehouse_1, Warehouse_2, Warehouse_3, Warehouse_4, Warehouse_5, Depôt_1, Depôt_2, Depôt_3, Depôt_4'
        );

        WarehouseCode::createFromMethod('fail');
    }

    public function provideWarehouseMethod(): iterable
    {
        yield ['WAREHOUSE_1', 'TD'];
        yield ['Warehouse_1', 'TD'];
        yield ['warehouse_1', 'TD'];
        yield ['Depôt_2', 'NANTERRE'];
        yield ['depôt_2', 'NANTERRE'];
    }
}
