<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Infrastructure\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity
 * @ORM\Table(name="order_items")
 */
class OrderItem
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\Column(name="name", type="string", nullable=true)
     */
    private string $name;

    /**
     * @ORM\Column(name="amountTTC", type="float", nullable=true)
     */
    private float $amountTTC;

    /**
     * @ORM\Column(name="amountHT", type="float", nullable=true)
     */
    private float $amountHT;

    /**
     * @ORM\Column(name="description", type="string", length=100, nullable=true)
     */
    private string $description;

    /**
     * @ORM\Column(name="quantity", type="integer", nullable=true)
     */
    private int $quantity;

    /**
     * @ORM\ManyToOne (targetEntity="MerchantOrder", inversedBy="orderItems", cascade={"persist"})
     * @ORM\JoinColumn(name="merchant_order_id", referencedColumnName="id", nullable=false)
     */
    private MerchantOrder $merchantOrder;

    /**
     * @ORM\Column(name="image", type="string", nullable=true)
     */
    private string $image;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private int $izbergId;

    /**
     * @ORM\Column(type="integer", nullable=false, options={"default" : 0})
     */
    private int $guarantyMonth;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private ?int $offerId = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private ?int $subCategoryId = null;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    private ?string $regionCode = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private ?int $variationId = null;

    /**
     * @ORM\Column(name="status", type="string", nullable=true, length=50)
     */
    private ?string $status = null;

    /**
     * @ORM\Column(name="batchsize", type="integer", nullable=false, options={"default": 1})
     */
    private int $batchsize = 1;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    private ?string $sku;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): OrderItem
    {
        $this->id = $id;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): OrderItem
    {
        $this->name = $name;
        return $this;
    }

    public function getAmountTTC(): float
    {
        return $this->amountTTC;
    }

    public function setAmountTTC(float $amountTTC): OrderItem
    {
        $this->amountTTC = $amountTTC;
        return $this;
    }

    public function getAmountHT(): float
    {
        return $this->amountHT;
    }

    public function setAmountHT(float $amountHT): OrderItem
    {
        $this->amountHT = $amountHT;
        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): OrderItem
    {
        $this->description = $description;
        return $this;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): OrderItem
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function getMerchantOrder(): MerchantOrder
    {
        return $this->merchantOrder;
    }

    public function setMerchantOrder(MerchantOrder $merchantOrder): OrderItem
    {
        $this->merchantOrder = $merchantOrder;
        return $this;
    }

    public function getIzbergId(): int
    {
        return $this->izbergId;
    }

    public function setIzbergId(int $izbergId): OrderItem
    {
        $this->izbergId = $izbergId;
        return $this;
    }

    public function getImage(): string
    {
        return $this->image;
    }

    public function setImage(string $image): OrderItem
    {
        $this->image = $image;
        return $this;
    }

    /**
     * @return int
     */
    public function getGuarantyMonth(): int
    {
        return $this->guarantyMonth;
    }

    /**
     * @param int $guarantyMonth
     * @return OrderItem
     */
    public function setGuarantyMonth(int $guarantyMonth): OrderItem
    {
        $this->guarantyMonth = $guarantyMonth;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getOfferId(): ?int
    {
        return $this->offerId;
    }

    /**
     * @param int|null $offerId
     * @return $this
     */
    public function setOfferId(?int $offerId): self
    {
        $this->offerId = $offerId;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getSubCategoryId(): ?int
    {
        return $this->subCategoryId;
    }

    /**
     * @param int|null $subCategoryId
     * @return $this
     */
    public function setSubCategoryId(?int $subCategoryId): self
    {
        $this->subCategoryId = $subCategoryId;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getRegionCode(): ?string
    {
        return $this->regionCode;
    }

    /**
     * @param string|null $regionCode
     * @return $this
     */
    public function setRegionCode(?string $regionCode): self
    {
        $this->regionCode = $regionCode;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getVariationId(): ?int
    {
        return $this->variationId;
    }

    /**
     * @param int|null $variationId
     * @return OrderItem
     */
    public function setVariationId(?int $variationId): OrderItem
    {
        $this->variationId = $variationId;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return int
     */
    public function getBatchsize(): int
    {
        return $this->batchsize;
    }

    /**
     * @param int $batchsize
     * @return OrderItem
     */
    public function setBatchsize(int $batchsize): self
    {
        $this->batchsize = $batchsize;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSku(): ?string
    {
        return $this->sku;
    }

    /**
     * @param string|null $sku
     * @return OrderItem
     */
    public function setSku(?string $sku): self
    {
        $this->sku = $sku;
        return $this;
    }
}
