<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Infrastructure\Entity;

use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity
 * @ORM\Table(name="merchant_orders")
 */
class MerchantOrder
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\Column(name="name", type="string", nullable=true)
     */
    private string $name;

    /**
     * @ORM\Column(name="merchant_distant_id", type="integer", nullable=true)
     */
    private int $merchantDistantId;

    /**
     * @ORM\OneToMany(targetEntity="OrderItem", mappedBy="merchantOrder", cascade={"persist","remove"})
     */
    private Collection $orderItems;

    /**
     * @ORM\ManyToOne(targetEntity="Order", inversedBy="merchantOrders", cascade={"persist"})
     * @ORM\JoinColumn(name="order_id", referencedColumnName="id", nullable=false)
     */
    private Order $order;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private ?int $izbergId = null;

    /**
     * @ORM\Column(name="amount", type="float", nullable=true)
     */
    private float $amountHT;

    /**
     * @ORM\Column(name="amount_ttc", type="float", nullable=true)
     */
    private ?float $amountTTC = null;

    /**
     * @ORM\Column(name="shipping", type="float", nullable=true)
     */
    private float $shippingHT;

    /**
     * @ORM\Column(name="extra_fees", type="float")
     */
    private float $extraFees;

    /**
     * @ORM\Column(name="status", type="string", nullable=true, length=50)
     */
    private ?string $status = null;

    /**
     * @ORM\Column(name="expected_delivery_date", type="datetime_immutable",  nullable=true)
     */
    private ?DateTimeImmutable $expectedDeliveryDate = null;

    public function __construct()
    {
        $this->orderItems = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): MerchantOrder
    {
        $this->id = $id;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): MerchantOrder
    {
        $this->name = $name;
        return $this;
    }

    public function getOrderItems(): Collection
    {
        return $this->orderItems;
    }

    public function setOrderItems(Collection $orderItems): self
    {
        $this->orderItems = $orderItems;
        return $this;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function setOrder(Order $order): MerchantOrder
    {
        $this->order = $order;
        return $this;
    }

    public function getIzbergId(): ?int
    {
        return $this->izbergId;
    }

    public function setIzbergId(?int $izbergId): MerchantOrder
    {
        $this->izbergId = $izbergId;
        return $this;
    }

    public function getAmountHT(): float
    {
        return $this->amountHT;
    }

    public function setAmountHT(float $amountHT): MerchantOrder
    {
        $this->amountHT = $amountHT;
        return $this;
    }

    public function getAmountTTC(): ?float
    {
        return $this->amountTTC;
    }

    public function setAmountTTC(?float $amountTTC): MerchantOrder
    {
        $this->amountTTC = $amountTTC;
        return $this;
    }

    public function getShippingHT(): float
    {
        return $this->shippingHT;
    }

    public function setShippingHT(float $shippingHT): MerchantOrder
    {
        $this->shippingHT = $shippingHT;
        return $this;
    }

    public function getExtraFees(): float
    {
        return $this->extraFees;
    }

    public function setExtraFees(float $extraFees): MerchantOrder
    {
        $this->extraFees = $extraFees;
        return $this;
    }

    /**
     * @return int
     */
    public function getMerchantDistantId(): int
    {
        return $this->merchantDistantId;
    }

    /**
     * @param int $merchantDistantId
     * @return MerchantOrder
     */
    public function setMerchantDistantId(int $merchantDistantId): MerchantOrder
    {
        $this->merchantDistantId = $merchantDistantId;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return DateTimeImmutable|null
     */
    public function getExpectedDeliveryDate(): ?DateTimeImmutable
    {
        return $this->expectedDeliveryDate;
    }

    /**
     * @param DateTimeImmutable|null $expectedDeliveryDate
     * @return MerchantOrder
     */
    public function setExpectedDeliveryDate(?DateTimeImmutable $expectedDeliveryDate): MerchantOrder
    {
        $this->expectedDeliveryDate = $expectedDeliveryDate;
        return $this;
    }
}
