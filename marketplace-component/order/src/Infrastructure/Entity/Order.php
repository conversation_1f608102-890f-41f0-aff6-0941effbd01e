<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Infrastructure\Entity;

use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use Marketplace\Component\User\Infrastructure\Entity\Company;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Marketplace\Component\Order\Infrastructure\Adapter\Repository\OrderRepository;

/**
 * @ORM\Table(name="orders")
 * @ORM\Entity(repositoryClass=OrderRepository::class)
 */
class Order
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private int $id;

    /**
     * @ORM\Column(name="external_id", type="string")
     */
    private string $externalId;

    /**
     * @ORM\Column(name="amount", type="float", nullable=true)
     */
    private ?float $amount = null;

    /**
     * @var ?Company
     *
     * @ORM\ManyToOne(targetEntity="Marketplace\Component\User\Infrastructure\Entity\Company", cascade={"persist"})
     * @ORM\JoinColumn(name="company_id", referencedColumnName="id", nullable=true)
     */
    private ?Company $company;

    /**
     * @ORM\Column(name="state_status", type="string", length=50, nullable=true)
     */
    private string $stateStatus;

    /**
     * @ORM\Column(name="creation_date", type="datetime_immutable")
     */
    private DateTimeImmutable $creationDate;

    /**
     * @ORM\OneToMany(targetEntity="MerchantOrder", mappedBy="order", cascade={"persist","remove"}))
     * @ORM\JoinColumn(nullable=false)
     */
    private Collection $merchantOrders;

    /**
     * @ORM\OneToOne(targetEntity="AddressOrder", cascade={"persist","remove"})
     * @ORM\JoinColumn(name="address_id", referencedColumnName="id")
     */
    private ?AddressOrder $shippingAddress = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private ?int $izbergId = null;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private int $cartId;

    /**
     * @ORM\Column(name="amountTTC", type="float", nullable=true)
     */
    private ?float $amountTTC = null;

    /**
     * @ORM\Column(type="float", nullable=true)
     */
    private ?float $totalAmount = null;

    /**
     * @ORM\Column(name="amountVAT", type="float", nullable=true)
     */
    private ?float $amountVAT = null;

    /**
     * @ORM\Column(name="shippingHT", type="float", nullable=true)
     */
    private ?float $shippingHT = null;

    /**
     * @ORM\Column(name="extraFeesHT", type="float")
     */
    private float $extraFeesHT = 0;

    /**
     * @ORM\Column(name="image", type="string", nullable=true)
     */
    private ?string $image = null;

    /**
     * @ORM\Column(name="status", type="string", length=50)
     */
    private ?string $status = null;

    /**
     * @ORM\Column(name="currency", type="string", length=50, nullable=false)
     */
    private string $currency = "EUR";

    /**
     * @ORM\Column(name="payment_type", type="string", length=50, nullable=false)
     */
    private string $payment_type;

    public function __construct()
    {
        $this->merchantOrders = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId($id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getExternalId(): string
    {
        return $this->externalId;
    }

    public function setExternalId(string $externalId): self
    {
        $this->externalId = $externalId;
        return $this;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    /**
     * @param float $amount
     */
    public function setAmount(?float $amount): void
    {
        $this->amount = $amount;
    }

    /**
     * @return Company|null
     */
    public function getCompany(): ?Company
    {
        return $this->company;
    }

    /**
     * @param Company|null $company
     * @return Order
     */
    public function setCompany(?Company $company): Order
    {
        $this->company = $company;
        return $this;
    }


    public function getStateStatus(): string
    {
        return $this->stateStatus;
    }

    public function setStateStatus(string $stateStatus): self
    {
        $this->stateStatus = $stateStatus;

        return $this;
    }

    public function getCreationDate(): DateTimeImmutable
    {
        return $this->creationDate;
    }

    public function setCreationDate(\DateTimeImmutable $creationDate): self
    {
        $this->creationDate = $creationDate;
        return $this;
    }

    public function getMerchantOrders(): Collection
    {
        return $this->merchantOrders;
    }

    public function setMerchantOrders(Collection $merchantOrders): self
    {
        $this->merchantOrders = $merchantOrders;
        return $this;
    }

    /**
     * @return AddressOrder|null
     */
    public function getShippingAddress(): ?AddressOrder
    {
        return $this->shippingAddress;
    }

    /**
     * @param AddressOrder|null $shippingAddress
     * @return $this
     */
    public function setShippingAddress(?AddressOrder $shippingAddress): self
    {
        $this->shippingAddress = $shippingAddress;
        return $this;
    }

    public function getIzbergId(): ?int
    {
        return $this->izbergId;
    }

    public function setIzbergId(?int $izbergId): Order
    {
        $this->izbergId = $izbergId;
        return $this;
    }

    public function getCartId(): int
    {
        return $this->cartId;
    }

    public function setCartId(int $cartId): Order
    {
        $this->cartId = $cartId;
        return $this;
    }

    public function getAmountTTC(): ?float
    {
        return $this->amountTTC;
    }

    public function setAmountTTC(?float $amountTTC): Order
    {
        $this->amountTTC = $amountTTC;
        return $this;
    }

    public function getAmountVAT(): ?float
    {
        return $this->amountVAT;
    }

    public function setAmountVAT(?float $amountVAT): Order
    {
        $this->amountVAT = $amountVAT;
        return $this;
    }

    public function getShippingHT(): ?float
    {
        return $this->shippingHT;
    }

    public function setShippingHT(?float $shippingHT): Order
    {
        $this->shippingHT = $shippingHT;
        return $this;
    }

    public function getExtraFeesHT(): ?float
    {
        return $this->extraFeesHT;
    }

    public function setExtraFeesHT(float $extraFeesHT): Order
    {
        $this->extraFeesHT = $extraFeesHT;
        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(?string $image): Order
    {
        $this->image = $image;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): Order
    {
        $this->status = $status;
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): Order
    {
        $this->currency = $currency;
        return $this;
    }

    public function getPaymentType(): string
    {
        return $this->payment_type;
    }

    public function setPaymentType(string $payment_type): Order
    {
        $this->payment_type = $payment_type;
        return $this;
    }

    public function getTotalAmount(): ?float
    {
        return $this->totalAmount;
    }

    public function setTotalAmount(?float $totalAmount): self
    {
        $this->totalAmount = $totalAmount;

        return $this;
    }
}
