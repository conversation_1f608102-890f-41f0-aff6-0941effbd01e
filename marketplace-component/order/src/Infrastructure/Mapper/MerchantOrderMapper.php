<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Infrastructure\Mapper;

use Marketplace\Component\Order\Domain\Enum\MerchantOrderStatusEnum;
use Marketplace\Component\Order\Domain\Model\MerchantOrder;
use Marketplace\Component\Order\Infrastructure\Entity\MerchantOrder as DoctrineMerchantOrder;
use Marketplace\Component\Order\Infrastructure\Enum\MerchantOrderStatusIzberg;
use Open\Izberg\Model\MerchantOrder as IzbergMerchantOrder;

class MerchantOrderMapper
{
    public static function mappingDoctrineToModel(DoctrineMerchantOrder $doctrineMerchantOrder): MerchantOrder
    {
        $status = null;
        $doctrineMerchantOrderStatus = $doctrineMerchantOrder->getStatus();
        if ($doctrineMerchantOrderStatus !== null) {
            $status = MerchantOrderStatusEnum::status($doctrineMerchantOrderStatus);
        }
        $merchantOrder = (new MerchantOrder(
            $doctrineMerchantOrder->getOrder()->getCartId(),
            $doctrineMerchantOrder->getMerchantDistantId()
        ))
            ->setId($doctrineMerchantOrder->getId())
            ->setName($doctrineMerchantOrder->getName())
            ->setAmountHT($doctrineMerchantOrder->getAmountHT())
            ->setAmountTTC($doctrineMerchantOrder->getAmountTTC())
            ->setShippingHT($doctrineMerchantOrder->getShippingHT())
            ->setExtraFees($doctrineMerchantOrder->getExtraFees())
            ->setMerchantOrderDistantId($doctrineMerchantOrder->getIzbergId())
            ->setMerchantDistantId($doctrineMerchantOrder->getMerchantDistantId())
            ->setExpectedDeliveryDate($doctrineMerchantOrder->getExpectedDeliveryDate())
            ->setStatus($status);

        $orderItems = [];
        foreach ($doctrineMerchantOrder->getOrderItems() as $doctrineOrderItem) {
            $orderItem = OrderItemMapper::mappingDoctrineToModel($doctrineOrderItem);
            $orderItems[] = $orderItem;
        }
        $merchantOrder->setOrderItems($orderItems);
        return $merchantOrder;
    }

    public static function mappingIzbergToModel(IzbergMerchantOrder $izbergMerchantOrder): MerchantOrder
    {

        $izbergStatus = MerchantOrderStatusIzberg::from($izbergMerchantOrder->getStatus());
        $merchantOrder = (new MerchantOrder(
            -1,
            $izbergMerchantOrder->getMerchant()->getId()
        ));
            $merchantOrder->setId($izbergMerchantOrder->getId())
            ->setMerchantOrderDistantId($izbergMerchantOrder->getId())
            ->setName($izbergMerchantOrder->getMerchant()->getName())
            ->setAmountHT($izbergMerchantOrder->getAmount())
            ->setAmountTTC($izbergMerchantOrder->getAmountVatIncluded())
            ->setShippingHT($izbergMerchantOrder->getShipping())
            ->setStatus(MerchantOrderStatusEnum::status($izbergStatus->name));

            $orderItems = [];
        foreach ($izbergMerchantOrder->getItems() as $item) {
            $orderItems [] =  OrderItemMapper::izbergToDomain($item);
        }
            $merchantOrder->setOrderItems($orderItems);

        return $merchantOrder;
    }

    public static function domainToDoctrine(
        MerchantOrder $merchantOrder,
        DoctrineMerchantOrder $doctrineMerchantOrder
    ): void {
        $status = null;
        $merchantOrderStatus = $merchantOrder->getStatus();
        if ($merchantOrderStatus instanceof MerchantOrderStatusEnum) {
            $status = $merchantOrderStatus->value;
        }

        $doctrineMerchantOrder->setName($merchantOrder->getName());
        $doctrineMerchantOrder->setMerchantDistantId($merchantOrder->getMerchantId());

        if ($merchantOrder->getMerchantOrderDistantId() !== null) {
            $doctrineMerchantOrder->setIzbergId($merchantOrder->getMerchantOrderDistantId());
        }

        $doctrineMerchantOrder->setAmountHT($merchantOrder->getAmountHT());
        $doctrineMerchantOrder->setAmountTTC($merchantOrder->getAmountTTC());
        $doctrineMerchantOrder->setShippingHT($merchantOrder->getShippingHT());
        $doctrineMerchantOrder->setExtraFees($merchantOrder->getExtraFees());
        $doctrineMerchantOrder->setStatus($status);
        $doctrineMerchantOrder->setExpectedDeliveryDate($merchantOrder->getExpectedDeliveryDate());
    }
}
