<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Infrastructure\Mapper;

use Marketplace\Component\Order\Domain\Model\AddressOrder;
use Marketplace\Component\Order\Infrastructure\Entity\AddressOrder as DoctrineAddress;

abstract class AddressOrderMapper
{

    public static function mappingDoctrineToModel(DoctrineAddress $doctrineAddress): AddressOrder
    {
        $address = new AddressOrder(
            $doctrineAddress->getId(),
            $doctrineAddress->getType(),
            $doctrineAddress->getAddress(),
            $doctrineAddress->getAddress2(),
            $doctrineAddress->getCity(),
            $doctrineAddress->getZipCode(),
            $doctrineAddress->getCountry()->getId(),
            $doctrineAddress->getDistantId(),
            $doctrineAddress->isClickAndCollect()
        );
        return $address;
    }

    public static function domainToDoctrine(AddressOrder $address, DoctrineAddress $doctrineAddress): void
    {
        $doctrineAddress->setId($address->getId());
        $doctrineAddress->setType($address->getType());
        $doctrineAddress->setAddress($address->getAddress1());
        $doctrineAddress->setAddress2($address->getAddress2());
        $doctrineAddress->setCity($address->getCity());
        $doctrineAddress->setZipCode($address->getZipCode());
        $doctrineAddress->setDistantId($address->getDistantId());
        $doctrineAddress->setClickAndCollect($address->isClickAndCollect());
    }
}
