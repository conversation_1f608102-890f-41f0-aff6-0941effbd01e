<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Infrastructure\MessageHandler;

use Marketplace\Component\Mail\Domain\Model\EmailTemplateSlug;
use Marketplace\Component\Mail\Domain\Port\Service\EmailServiceInterface;
use Marketplace\Component\Mail\Infrastructure\Exception\TemplateException;
use Marketplace\Component\Order\Domain\UseCase\InfosMerchantOrderCancelledUseCase\DTO\InfosMerchantOrderCancelledRequest;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;

final class InfosMerchantOrderCancelledHandler implements MessageHandlerInterface
{
    public function __construct(private EmailServiceInterface $emailService)
    {
    }

    /**
     * @throws TemplateException
     */
    public function __invoke(InfosMerchantOrderCancelledRequest $infosMerchantOrderCancelledRequest): void
    {
        $this->emailService->send(
            [$this->emailService->getNoReplyUser()],
            [$infosMerchantOrderCancelledRequest->userMailInfos],
            EmailTemplateSlug::MERCHANT_ORDER_CANCEL_TO_USER,
            $infosMerchantOrderCancelledRequest->language,
            [
                'orderId' => $infosMerchantOrderCancelledRequest->orderId,
                'orderDate' => $infosMerchantOrderCancelledRequest->createdAt,
                'merchantName' => $infosMerchantOrderCancelledRequest->merchantName,
                'products' => $infosMerchantOrderCancelledRequest->items
            ]
        );
    }
}
