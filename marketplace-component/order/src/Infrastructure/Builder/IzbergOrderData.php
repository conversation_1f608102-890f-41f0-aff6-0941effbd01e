<?php

namespace Marketplace\Component\Order\Infrastructure\Builder;

use Symfony\Component\Serializer\Annotation\SerializedName;

final class IzbergOrderData
{
    public int $id;

    #[SerializedName('amount_vat_included')]
    public float $amountVatIncluded = 0.0;

    #[SerializedName('vat_on_products')]
    public float $vatOnProducts = 0.0;

    #[SerializedName('vat_on_shipping')]
    public float $vatOnShipping = 0.0;

    public array $order = [];

    public array $items = [];

    #[SerializedName('payment_type')]
    public ?string $paymentType = null;

    public float $price = 0.0;

    public ?string $amount = null;

    #[SerializedName('shipping_address')]
    public array $shippingAddress = [];

    #[SerializedName('shipping_vat_included')]
    public ?string $shippingVatIncluded = null;

    public array $user = [];

    #[SerializedName('created_on')]
    public string $createdOn;

    public array $merchant = [];

    #[SerializedName('items_count')]
    public int $itemsCount;

    public ?float $shipping = null;

    #[SerializedName('extra_data')]
    public ?array $extraData = [];
}
