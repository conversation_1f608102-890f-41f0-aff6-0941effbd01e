<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Infrastructure\Builder\InfosMerchantOrderCancelledRequestBuilder;

use DateTimeImmutable;
use Exception;
use Marketplace\Component\Order\Domain\UseCase\InfosMerchantOrderCancelledUseCase\DTO\InfosMerchantOrderCancelledRequest;
use Marketplace\Component\Order\Infrastructure\Builder\IzbergOrderCollection;
use Marketplace\Component\Order\Infrastructure\DTO\CartToUser;
use Marketplace\Component\Order\Infrastructure\Exceptions\UndefinedMerchantOrderKeyException;
use Marketplace\Component\User\Domain\Model\User;
use Psr\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Serializer\SerializerInterface;

final class InfosMerchantOrderCancelledRequestBuilder implements InfosMerchantOrderCancelledRequestBuilderInterface
{
    public function __construct(
        private SerializerInterface $serializer,
        private EventDispatcherInterface $dispatcher
    ) {
    }

    /**
     * @throws Exception
     */
    public function buildFromRequest(Request $request): InfosMerchantOrderCancelledRequest
    {
        /** @var string $requestContent */
        $requestContent = $request->getContent();

        /** @var IzbergOrderCollection $izbergOrderCollection */
        $izbergOrderCollection = $this->serializer->deserialize(
            $requestContent,
            IzbergOrderCollection::class,
            'json',
            ['disable_type_enforcement' => true]
        );
        $izbergOrderData = $izbergOrderCollection->data;
        $cartId = $this->getIzbergData('cart_id', $izbergOrderData->order);
        $cartToUser = new CartToUser($cartId);
        $this->dispatcher->dispatch($cartToUser);
        /** @var User $user */
        $user = $cartToUser->user;
        $priceFormatter = new \NumberFormatter($user->getLanguage(), \NumberFormatter::CURRENCY);
        $currency = $this->getIzbergData('currency', $izbergOrderData->items[0])['code'];
        return new InfosMerchantOrderCancelledRequest(
            userMailInfos: $user->getEmailInfos(),
            createdAt: (new DateTimeImmutable($izbergOrderData->createdOn))->format("d/m/Y"),
            orderId: $this->getIzbergData('id', $izbergOrderData->order),
            merchantName: $this->getIzbergData('name', $izbergOrderData->merchant),
            language: $user->getLanguage(),
            items: array_map(
                function (array $item) use ($priceFormatter, $currency): array {
                    $unitPriceHT = $priceFormatter->formatCurrency((float)$item['amount'], $currency);
                    $quantity = $item['quantity'];
                    return [
                        'sku' => $item['sku'],
                        'quantity' => $quantity,
                        'unitPrice' => $unitPriceHT
                    ];
                },
                $izbergOrderData->items
            )
        );
    }

    private function getIzbergData(string $key, array $izbergOrderData): mixed
    {
        if (!array_key_exists($key, $izbergOrderData)) {
            throw new UndefinedMerchantOrderKeyException($key);
        }

        return $izbergOrderData[$key];
    }
}
