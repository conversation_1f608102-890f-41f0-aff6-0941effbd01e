<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Infrastructure\Builder;

use Exception;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CheckMerchantServiceInterface;
use Marketplace\Component\Invoice\Domain\ValueObject\CarrierName\CarrierName;
use Marketplace\Component\Order\Domain\Model\GetDataFromCartByCartId;
use Marketplace\Component\Order\Domain\Model\GetQuoteNumberByQuoteId;
use Marketplace\Component\Order\Domain\Model\GetCartContactInfosByCartId;
use Marketplace\Component\Order\Domain\Model\GetUserIdByCartId;
use Marketplace\Component\Order\Domain\Model\Order;
use Marketplace\Component\Order\Domain\Port\Repository\OrderRepositoryInterface;
use Marketplace\Component\Order\Domain\UseCase\ExportOrder\DTO\ExportOrderRequest;
use Marketplace\Component\Order\Infrastructure\Adapter\Service\FetchCarrierServiceInterface;
use Marketplace\Component\Order\Infrastructure\Adapter\Service\Warehouse\FetchWarehouseServiceInterface;
use Marketplace\Component\Order\Infrastructure\Exceptions\OrderSyncException;
use Marketplace\Component\Order\Infrastructure\Exceptions\UndefinedMerchantOrderKeyException;
use Marketplace\Component\User\Domain\Model\Country;
use Marketplace\Component\User\Domain\Port\Repository\AddressRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\CountryRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use Marketplace\Component\User\Infrastructure\Adapter\Service\CompanyExternalId\FindCompanyExternalIdInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\HandledStamp;
use Symfony\Component\Serializer\SerializerInterface;

final class ExportOrderRequestBuilder implements ExportOrderRequestBuilderInterface
{

    private const REGION_CODE_ANTILLES = "antilles";
    private const REGION_CODE_REUNION = "reunion";
    private const REGION_CODE_FRANCE = "france";

    private const ZIP_CODE_NANTERRE = "92000";
    private const ZIP_CODE_GRENOBLE = "38950";

    private const CARRIER_CLICK_AND_COLLECT = "ENLEV PAR VOS SOINS";

    public function __construct(
        private SerializerInterface $serializer,
        private FetchCarrierServiceInterface $fetchCarrierService,
        private FetchWarehouseServiceInterface $fetchWarehouseService,
        private FindCompanyExternalIdInterface $findCompanyExternalId,
        private MessageBusInterface $messageBus,
        private UserRepositoryInterface $userRepository,
        private CountryRepositoryInterface $countryRepository,
        private AddressRepositoryInterface $addressRepository,
        private CheckMerchantServiceInterface $checkMerchantService,
        private OrderRepositoryInterface $orderRepository,
    ) {
    }

    /**
     * @throws Exception
     */
    public function buildFromRequest(string $payload): ?ExportOrderRequest
    {

        /** @var IzbergOrderCollection $izbergOrderCollection */
        $izbergOrderCollection = $this->serializer->deserialize(
            $payload,
            IzbergOrderCollection::class,
            'json',
            ['disable_type_enforcement' => true]
        );

        $izbergOrderData = $izbergOrderCollection->data;

        if (
            !$this->checkMerchantService->isMKPOwnerMerchant(
                $this->getIzbergData('id', $izbergOrderData->merchant)
            )
        ) {
            {
                return null;
            }
        }

        $merchantOrderId = $izbergOrderData->id;
        $warehouseMethod = $this->fetchWarehouseService->warehouseNameFromMethod($merchantOrderId) ?? 'Warehouse_1';
        $izbergUserId = $this->getIzbergData('id', $izbergOrderData->user);

        try {
            $carrierName = (string)CarrierName::createFromMKPName(
                $this->fetchCarrierService->getCarrierName($merchantOrderId)
            );
            $carrierId = $this->fetchCarrierService->findCarrierIdByMerchantOrder($merchantOrderId);
        } catch (\InvalidArgumentException $ex) { //it's possible for click and collect
            $carrierName = null;
            $carrierId = null;
        }

        $cartId = $this->getIzbergData('cart_id', $izbergOrderData->order);

        //Get Data from Cart
        $enveloppeCart = $this->messageBus->dispatch(new GetDataFromCartByCartId($cartId));
        /** @var ?HandledStamp $handledStampCart */
        $handledStampCart = $enveloppeCart->last(HandledStamp::class);
        /** @var array $dataFromCart */
        $dataFromCart = $handledStampCart?->getResult();

        // Get User Email
        $userId = $dataFromCart['userId'] ?? null;
        $userEmail = null;
        if ($userId !== null) {
            $user = $this->userRepository->findById($userId);
            $userEmail = $user?->getEmail();
        }

        // Get QuoteId
        $quoteId = $dataFromCart['quoteId'] ?? null;

        // Get QuoteNumber
        $quoteNumber = null;
        if ($quoteId !== null) {
            $enveloppeQuoteNumber = $this->messageBus->dispatch(new GetQuoteNumberByQuoteId($quoteId));
            /** @var ?HandledStamp $handledStampQuoteNumber */
            $handledStampQuoteNumber = $enveloppeQuoteNumber->last(HandledStamp::class);
            $quoteNumber = $handledStampQuoteNumber?->getResult();
        }

        $country = $this->getCountry($izbergOrderData);

        if ($country === null) {
            throw new OrderSyncException("Export Order:No shipping country, order:" . $izbergOrderData->id);
        }

        $region = $country->getRegionCode();
        $clickAndCollect = $this->isClickAndCollect($warehouseMethod);
        if ($clickAndCollect) {
            $carrierName = self::CARRIER_CLICK_AND_COLLECT;
        }

        $warehousevalue = $this->getWareHouseValue(
            $region,
            $clickAndCollect,
            $this->getIzbergData('zipcode', $izbergOrderData->shippingAddress)
        );
        $warehouseMethod = $warehousevalue ?? $warehouseMethod;
        $accountingCategory = $this->getAccountingCategory($region, $clickAndCollect);

        $orderId = $this->getIzbergData('id', $izbergOrderData->order);
        $order = $this->orderRepository->findByDistantId($orderId);

        if (!$order instanceof Order) {
            throw new OrderSyncException("Export Order:No order with id:" . $orderId);
        }

        $extraFee = 0.0;
        if (isset($izbergOrderData->extraData) && isset($izbergOrderData->extraData['extra_fee'])) {
            $extraFee = (float)$izbergOrderData->extraData['extra_fee'];
        }

        $shippingAmount = (float)$izbergOrderData->shipping;
        if($extraFee > 0) {
            $shippingAmount += $extraFee;
        }

        return ExportOrderRequest::create(
            merchantId: $this->getIzbergData('id', $izbergOrderData->merchant),
            merchantOrderId: $izbergOrderData->id,
            ref: $this->getIzbergData('id_number', $izbergOrderData->order),
            createdAt: $izbergOrderData->createdOn,
            paymentType: $order->getPaymentMethod()->getType(),
            companyExternalId: $this->findCompanyExternalId->getExternalIdFromIzberg($izbergUserId),
            companyName: $this->getIzbergData('display_name', $izbergOrderData->user),
            accountingEmail: $this->getIzbergData('email', $izbergOrderData->user),
            shippingAddressName: $this->getIzbergData('name', $izbergOrderData->shippingAddress),
            shippingAddress: $this->getIzbergData('address', $izbergOrderData->shippingAddress),
            complementShippingAddress: $this->getIzbergData('address2', $izbergOrderData->shippingAddress),
            shippingZipCode: $this->getIzbergData('zipcode', $izbergOrderData->shippingAddress),
            cityShippingAddress: $this->getIzbergData('city', $izbergOrderData->shippingAddress),
            shippingAmount: $shippingAmount,
            carrierId: $carrierId,
            items: array_map(
                fn(array $item): array => [
                    'name' => $item['name'],
                    'sku' => $item['variation_sku'] !== "" ? $item['variation_sku'] : $item['sku'],
                    'id' => $item['id'],
                    'offerId' => $item['offer_id'],
                    'unitPrice' => $item['amount'],
                    'quantity' => $item['quantity'],
                    'publicUnitPrice' => $item['previous_price_without_vat'],
                ],
                $izbergOrderData->items
            ),
            quantity: $izbergOrderData->itemsCount,
            orderId: $orderId,
            clientEmail: $userEmail ?? 'unknown',
            hostName: $dataFromCart['hostName'] ?? '',
            hostPhone: $dataFromCart['hostPhone'] ?? '',
            clientComment: $dataFromCart['clientComment'] ?? '',
            clientOrderId: $dataFromCart['clientOrderId'] ?? '',
            carrierName: $carrierName,
            totalAmount: (float)$izbergOrderData->amount,
            unitPrice: $izbergOrderData->price,
            warehouse: $warehouseMethod,
            country: strtoupper($country->getCode()),
            currency: $this->getIzbergData('currency', $izbergOrderData->items[0])['code'],
            accountingCategory: $accountingCategory,
            quoteNumber: $quoteNumber ?? ''
        );
    }

    private function getIzbergData(string $key, array $izbergOrderData): mixed
    {
        if (!array_key_exists($key, $izbergOrderData)) {
            throw new UndefinedMerchantOrderKeyException($key);
        }

        return $izbergOrderData[$key];
    }

    private function isClickAndCollect(string $shippingName): bool
    {
        return strcasecmp(trim($shippingName), "Click and collect") === 0;
    }

    private function getCountry(IzbergOrderData $orderData): ?Country
    {
        $countryCode = $this->getCountryCode($orderData);
        if ($countryCode === null) {
            return null;
        }
        return $this->countryRepository->findByCode($countryCode);
    }

    private function getCountryCode(IzbergOrderData $orderData): ?string
    {
        if (!isset($orderData->shippingAddress["id"])) {
            return null;
        }
        $address = $this->addressRepository->findDistantAddressById($orderData->shippingAddress["id"]);

        if ($address === null) {
            return null;
        }

        return $address->getCountryName();
    }

    //JIRA 1266
    private function getAccountingCategory(string $region, bool $clickAndCollect): ?string
    {
        if ($region === self::REGION_CODE_ANTILLES || $region === self::REGION_CODE_REUNION) {
            if ($clickAndCollect) {
                return "DOM";
            } else {
                return "HORS CEE";
            }
        }
        return null;
    }

    //JIRA 1266
    private function getWareHouseValue(string $region, bool $clickAndCollect, string $zipCode): ?string
    {
        if ($clickAndCollect) {
            if ($region === self::REGION_CODE_ANTILLES) {
                return "MARTINIQ";
            }
            if ($region === self::REGION_CODE_REUNION) {
                return "REUNION";
            }
        } else {
            if ($region === self::REGION_CODE_ANTILLES || $region === self::REGION_CODE_REUNION) {
                return "TD";
            }
        }

        if ($region === self::REGION_CODE_FRANCE) {
            if ($clickAndCollect) {
                if ($zipCode === self::ZIP_CODE_NANTERRE) {
                    return "NANTERRE";
                }
                if ($zipCode === self::ZIP_CODE_GRENOBLE) {
                    return "TD";
                }
            } else {
                return "TD";
            }
        }
        return "TD";
    }
}
