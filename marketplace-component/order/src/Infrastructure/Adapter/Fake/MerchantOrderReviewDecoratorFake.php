<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Infrastructure\Adapter\Fake;

use Marketplace\Component\Order\Domain\Model\Order;
use Marketplace\Component\Order\Domain\Port\Service\MerchantOrderReviewDecoratorInterface;

class MerchantOrderReviewDecoratorFake implements MerchantOrderReviewDecoratorInterface
{
    public function decorate(?Order $order): void
    {
        // TODO: Implement decorate() method.
    }
}
