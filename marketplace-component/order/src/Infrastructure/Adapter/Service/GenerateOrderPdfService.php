<?php

namespace Marketplace\Component\Order\Infrastructure\Adapter\Service;

use Marketplace\Component\CleanArchiCore\Infrastructure\Service\GeneratePdfService;
use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Marketplace\Component\Order\Domain\Exception\OrderPdfException;
use Marketplace\Component\Order\Domain\Model\MerchantOrderPdf;
use Marketplace\Component\Order\Domain\Model\Order;
use Marketplace\Component\Order\Domain\Port\Repository\MerchantOrderRepositoryInterface;
use Marketplace\Component\Order\Domain\Port\Service\GenerateOrderPdfServiceInterface;
use Marketplace\Component\User\Domain\Port\Repository\AddressRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Open\Izberg\Client\OperatorClient;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class GenerateOrderPdfService implements GenerateOrderPdfServiceInterface
{
    public function __construct(
        private OperatorClient $operatorClient,
        private Environment $twig,
        private TranslatorInterface $translator,
        private MerchantOrderRepositoryInterface $merchantOrderRepository,
        private AddressRepositoryInterface $addressRepository,
        private GeneratePdfService $generatePdfService,
        private CompanyRepositoryInterface $companyRepository,
        private OfferRepositoryInterface $offerRepository
    ) {
    }

    public function generateOrderPdf(Order $order): string
    {

        $companyId = $order->getCompanyId();
        if ($companyId === null) {
            throw new OrderPdfException("no company id in order:" . $order->getId());
        }

        $company = $this->companyRepository->getCompanyById($companyId);
        if ($company === null) {
            throw new OrderPdfException("no company found with:" . $companyId);
        }

        $buyerAddress = $this->addressRepository->findBillingAddress($companyId);

        $merchantOrders = $order->getMerchantOrders();
        $merchantOrdersSimplified = [];
        foreach ($merchantOrders as $merchantOrder) {
            $pdfMerchant = new MerchantOrderPdf();
            $pdfMerchant->setName($merchantOrder->getName())
                ->setOrderItems($merchantOrder->getOrderItems())
                ->setShipping($merchantOrder->getShippingHT());
            $merchantOrdersSimplified[] = $pdfMerchant;
        }

        $header = $this->twig->render(
            '@MarketplaceOrder/pdf/order_header_block.html.twig',
            [
                'creationDate' => $order->getCreationDate()
            ]
        );

        $body = $this->twig->render(
            "@MarketplaceOrder/pdf/order_pdf.html.twig",
            [
                'order' => $order,
                'buyerAddress' => $buyerAddress,
                'merchantOrders' => $merchantOrdersSimplified,
                'company' => $company,
            ]
        );

        $footer = $this->twig->render(
            '@MarketplaceOrder/pdf/order_footer_block.html.twig'
        );

        return $this->generatePdfService->generatePdf(
            $body,
            $this->translator->trans('pdf.order.header.title', [], 'translations'),
            $header,
            $footer
        );
    }
}
