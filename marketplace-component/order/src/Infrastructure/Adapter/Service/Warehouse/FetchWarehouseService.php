<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Infrastructure\Adapter\Service\Warehouse;

use Open\Izberg\Client\OperatorClient;
use Open\Izberg\Exception\IzbergShippingOptionNotFoundException;

final class FetchWarehouseService implements FetchWarehouseServiceInterface
{
    public function __construct(private OperatorClient $operatorClient)
    {
    }


    public function warehouseNameFromMethod(int $distantMerchantId): ?string
    {
        $shippingOptions = $this->operatorClient->orderApi()->fetchShippingOptionsOfMerchantOrder($distantMerchantId);
        if ($shippingOptions === null) {
            throw new IzbergShippingOptionNotFoundException();
        }

        $shippingOption = $shippingOptions->getIterator()->current();
        if (!array_key_exists('name', $shippingOption)) {
            throw new \Exception(sprintf('The method key is invalid for %d merchant order', $distantMerchantId));
        }

        return !empty($shippingOption['name']) ? $shippingOption['name'] : null;
    }
}
