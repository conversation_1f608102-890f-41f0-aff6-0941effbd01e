<?php

namespace Marketplace\Component\Order\Infrastructure\Adapter\Service;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CheckMerchantServiceInterface;
use Marketplace\Component\Order\Domain\Model\Order;
use Marketplace\Component\Order\Domain\Model\OrderContact;
use Marketplace\Component\Order\Domain\Port\Repository\OrderRepositoryInterface;
use Marketplace\Component\Order\Domain\Port\Service\MkpOwnerServiceInterface;
use Marketplace\Component\Order\Domain\Port\Service\OrderContactServiceInterface;
use Marketplace\Component\Order\Infrastructure\Exceptions\NotFoundException;
use Marketplace\Component\User\Domain\Model\Merchant;
use Marketplace\Component\User\Domain\Port\Repository\MerchantRepositoryInterface;
use Psr\Log\LoggerInterface;

class MkpOwnerService implements MkpOwnerServiceInterface
{
    public function __construct(
        private readonly CheckMerchantServiceInterface $checkMerchantService,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly MerchantRepositoryInterface $merchantRepository,
        private readonly OrderContactServiceInterface $orderContactService,
        private readonly array $supportedLocales,
        private readonly string $defaultMerchantLanguage,
        private readonly LoggerInterface $logger
    ) {
    }

    public function getMerchantEmails(int $merchantId, int $orderId): ?array
    {
        $merchant = $this->merchantRepository->findByDistantId($merchantId);
        if (!$merchant instanceof Merchant) {
            return null;
        }
        if ($this->checkMerchantService->isMKPOwnerMerchant($merchant->getDistantId())) {
            $order = $this->orderRepository->findByDistantId($orderId);
            if (!$order instanceof Order) {
                throw new NotFoundException(sprintf("Can't found order with id : %s", $orderId));
            }

            /** @var int $countryId */
            $countryId = $order->getShippingAddressModel()?->getCountryId();
            $addressZipCode = $order->getShippingAddressModel()?->getZipCode();
            $orderContact = $this->orderContactService->getOrderContact(
                $countryId,
                $addressZipCode
            );
            if (!$orderContact instanceof OrderContact) {
                $context = [
                    'Order Id' => $orderId,
                    'Address Zip Code' => $addressZipCode,
                    'Country Id' => $countryId
                ];
                $this->logger->error('Order Contact not found', $context);

                return null;
            }

            $companyName = $merchant->getCompanyName();
            return array_map(function ($email) use ($companyName) {
                return [$email, $companyName];
            }, $orderContact->getAdvEmails());
        } else {
            return null;
        }
    }

    public function getMerchantLanguage(int $merchantId): string
    {
        $merchant = $this->merchantRepository->findByDistantId($merchantId);
        if (!$merchant instanceof Merchant || !in_array($merchant->getLanguage(), $this->supportedLocales)) {
            return $this->defaultMerchantLanguage;
        }
        return $merchant->getLanguage() ?? $this->defaultMerchantLanguage;
    }
}
