<?php

namespace Marketplace\Component\Order\Domain\Port\Service;

use Marketplace\Component\Order\Domain\Exception\CreateOrderException;
use Marketplace\Component\Order\Domain\Model\Order;
use Marketplace\Component\Payment\Domain\Model\PaymentAction;

interface CreateOrderServiceInterface
{
    /**
     * Create a real order based on the Order domain model passed in parameters
     * If any error occurs during the creation process it should throw a CreateOrderException
     *
     * @param Order $order
     *
     * @throws CreateOrderException
     */
    public function create(Order $order, PaymentAction $paymentAction): void;
}
