<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Domain\UseCase\OrderViewPdfUseCase\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Model\UseCaseRequestInterface;

class OrderViewPdfRequest implements UseCaseRequestInterface
{
    public function __construct(public int $orderId)
    {
    }

    public function infoToLog(): array
    {
        return get_object_vars($this);
    }
}
