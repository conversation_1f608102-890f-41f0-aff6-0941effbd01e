<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Domain\UseCase\CreateMerchantOrderReview\DTO;

use Assert\LazyAssertionException;
use Marketplace\Component\CleanArchiCore\Domain\Model\UseCaseRequestInterface;
use Marketplace\Component\User\Domain\Assert\Assert;

class CreateMerchantOrderReviewRequest implements UseCaseRequestInterface
{
    public int $merchantOrderId;

    public function __construct(
        public int $merchantId,
        public float $score,
        public string $comment,
    ) {
    }

    public function validate(CreateMerchantOrderReviewResponse $response): bool
    {
        try {
            Assert::lazy()
                ->that($this->score, 'score')->notBlank('review_create.value.mandatory')
                ->that($this->comment, 'comment')->notBlank('review_create.value.mandatory')
                ->verifyNow()
            ;
            return true;
        } catch (LazyAssertionException $exception) {
            foreach ($exception->getErrorExceptions() as $errorException) {
                $response
                    ->getNotification()
                    ->addError($errorException->getPropertyPath(), $errorException->getMessage())
                ;
            }
            return false;
        }
    }

    public function infoToLog(): array
    {
        return get_object_vars($this);
    }
}
