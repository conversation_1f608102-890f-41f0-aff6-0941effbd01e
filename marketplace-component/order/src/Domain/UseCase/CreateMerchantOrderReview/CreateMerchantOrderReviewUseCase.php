<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Domain\UseCase\CreateMerchantOrderReview;

use Marketplace\Component\CleanArchiCore\Domain\UseCase\AbstractUseCase;
use Marketplace\Component\Order\Domain\Model\MerchantOrderReview;
use Marketplace\Component\Order\Domain\Port\Service\CreateMerchantOrderReviewServiceInterface;
use Marketplace\Component\Order\Domain\Presenter\CreateMerchantOrderReviewPresenterInterface;
use Marketplace\Component\Order\Domain\UseCase\CreateMerchantOrderReview\DTO\CreateMerchantOrderReviewRequest;
use Marketplace\Component\Order\Domain\UseCase\CreateMerchantOrderReview\DTO\CreateMerchantOrderReviewResponse;

class CreateMerchantOrderReviewUseCase extends AbstractUseCase
{
    public function __construct(
        private CreateMerchantOrderReviewServiceInterface $createMerchantOrderReviewService
    ) {
    }

    public function execute(
        CreateMerchantOrderReviewRequest $request,
        CreateMerchantOrderReviewPresenterInterface $presenter
    ) {
        $this->logUseCaseRequest($request, __CLASS__);
        $response = new CreateMerchantOrderReviewResponse();

        $isValid = $request->validate($response);

        if ($isValid) {
            $review = (new MerchantOrderReview())
                ->setMerchantId($request->merchantId)
                ->setMerchantOrderId($request->merchantOrderId)
                ->setScore($request->score)
                ->setComment($request->comment)
            ;

            $result = $this->createMerchantOrderReviewService->createReview($review);

            if ($result === null) {
                $response->getNotification()->addError('review', 'An error occurred');
                $presenter->present($response);
                return;
            }

            $response->created = true;
        }
        $presenter->present($response);
    }
}
