<?php

namespace Marketplace\Component\Order\Domain\Model;

class MerchantOrderPdf
{
    /**
     * @var OrderItem[]
     */
    private array $orderItems;
    private float $shipping;
    private string $name;

    /**
     * @return OrderItem[]
     */
    public function getOrderItems(): array
    {
        return $this->orderItems;
    }

    /**
     * @param OrderItem[] $orderItems
     * @return MerchantOrderPdf
     */
    public function setOrderItems(array $orderItems): MerchantOrderPdf
    {
        $this->orderItems = $orderItems;
        return $this;
    }

    /**
     * @return float
     */
    public function getShipping(): float
    {
        return $this->shipping;
    }

    /**
     * @param float $shipping
     * @return MerchantOrderPdf
     */
    public function setShipping(float $shipping): MerchantOrderPdf
    {
        $this->shipping = $shipping;
        return $this;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return MerchantOrderPdf
     */
    public function setName(string $name): MerchantOrderPdf
    {
        $this->name = $name;
        return $this;
    }
}
