<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Domain\Model;

use DateTimeImmutable;
use Marketplace\Component\Invoice\Domain\Model\Invoice;
use Marketplace\Component\Order\Domain\Enum\MerchantOrderStatusEnum;
use Marketplace\Component\Payment\Domain\Model\Method\BankTransferMethod;
use Marketplace\Component\Payment\Domain\Model\Method\CreditCardMethod;
use Marketplace\Component\User\Domain\Model\Address;

class MerchantOrder
{
    private ?int $id = null;
    private string $name;
    /**
     * @var OrderItem[]
     */
    private array $orderItems = [];
    private Order $order;
    private float $amountHT;
    private ?float $amountTTC;
    private float $shippingHT;
    private float $extraFees = 0;
    private ?int $merchantOrderDistantId = null;
    private ?int $userId = null;
    private ?MerchantOrderStatusEnum $status = null;
    private string $parcelStatus;
    private ?int $merchantDistantId = null;
    private ?Address $shippingAddress = null;
    private bool $showSav = false;
    /**
     * @var Invoice[]
     */
    private array $invoices = [];
    private ?DateTimeImmutable $expectedDeliveryDate = null;

    public function __construct(private int $cartId, private int $merchantId)
    {
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): static
    {
        $this->id = $id;
        return $this;
    }

    public function getCartId(): int
    {
        return $this->cartId;
    }

    public function getMerchantId(): int
    {
        return $this->merchantId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return OrderItem[]
     */
    public function getOrderItems(): array
    {
        return $this->orderItems;
    }

    public function setOrderItems(array $orderItems): static
    {
        $this->orderItems = $orderItems;
        return $this;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function setOrder(Order $order): static
    {
        $this->order = $order;
        return $this;
    }

    public function getAmountHT(): float
    {
        return $this->amountHT;
    }

    public function setAmountHT(float $amountHT): static
    {
        $this->amountHT = $amountHT;
        return $this;
    }

    public function getAmountTTC(): ?float
    {
        return $this->amountTTC;
    }

    public function setAmountTTC(?float $amountTTC): static
    {
        $this->amountTTC = $amountTTC;
        return $this;
    }

    public function getShippingHT(): float
    {
        return $this->shippingHT;
    }

    public function setShippingHT(float $shippingHT): static
    {
        $this->shippingHT = $shippingHT;
        return $this;
    }

    public function getMerchantOrderDistantId(): ?int
    {
        return $this->merchantOrderDistantId;
    }

    public function setMerchantOrderDistantId(?int $merchantOrderDistantId): static
    {
        $this->merchantOrderDistantId = $merchantOrderDistantId;
        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function setUserId(?int $userId): self
    {
        $this->userId = $userId;
        return $this;
    }

    public function hasItems(): bool
    {
        return \count($this->orderItems) > 0;
    }

    /**
     * @param int[] $requestOrderItems
     * @return OrderItem[]
     */
    public function computeOrderItemsValid(array $requestOrderItems): array
    {
        return array_filter(
            $this->getOrderItems(),
            function (OrderItem $orderItem) use ($requestOrderItems) {
                return $orderItem->getStatus() !== OrderItem::CANCELLED_STATUS
                    && in_array($orderItem->getId(), $requestOrderItems);
            }
        );
    }

    /**
     * @param array $orderItemsToBeInvoiced
     * @return array<array-key, array<OrderItem, int>>
     * @example return example: [OrderItem, $quantity]
     */
    public function itemToBeInvoiced(array $orderItemsToBeInvoiced): array
    {
        return array_filter(
            $orderItemsToBeInvoiced,
            function (array $invoiceableItem) {
                /** @var OrderItem $orderItem */
                [$orderItem] = $invoiceableItem;
                return ($orderItem->getInvoicedQuantity() < $orderItem->getQuantity());
            }
        );
    }

    public function getStatus(): ?MerchantOrderStatusEnum
    {
        return $this->status;
    }

    public function setStatus(?MerchantOrderStatusEnum $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getParcelStatus(): string
    {
        return $this->parcelStatus;
    }

    public function setParcelStatus(string $parcelStatus): MerchantOrder
    {
        $this->parcelStatus = $parcelStatus;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getMerchantDistantId(): ?int
    {
        return $this->merchantDistantId;
    }

    /**
     * @param int|null $merchantDistantId
     * @return $this
     */
    public function setMerchantDistantId(?int $merchantDistantId): self
    {
        $this->merchantDistantId = $merchantDistantId;
        return $this;
    }

    /**
     * @return Invoice[]
     */
    public function getInvoices(): array
    {
        return $this->invoices;
    }

    /**
     * @param Invoice[] $invoices
     * @return $this
     */
    public function setInvoices(array $invoices): self
    {
        $this->invoices = $invoices;
        return $this;
    }

    public function isCreditCardPayment(): bool
    {
        return $this->getOrder()->getPaymentMethod() instanceof CreditCardMethod;
    }

    public function isBankTransferPayment(): bool
    {
        return $this->getOrder()->getPaymentMethod() instanceof BankTransferMethod;
    }

    /**
     * @return DateTimeImmutable|null
     */
    public function getExpectedDeliveryDate(): ?DateTimeImmutable
    {
        return $this->expectedDeliveryDate;
    }

    /**
     * @param DateTimeImmutable|null $expectedDeliveryDate
     * @return MerchantOrder
     */
    public function setExpectedDeliveryDate(?DateTimeImmutable $expectedDeliveryDate): MerchantOrder
    {
        $this->expectedDeliveryDate = $expectedDeliveryDate;
        return $this;
    }

    public function hasNoInvoiced(): bool
    {
        foreach ($this->orderItems as $item) {
            if ($item->getInvoicedQuantity() > 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * @return Address|null
     */
    public function getShippingAddress(): ?Address
    {
        return $this->shippingAddress;
    }

    /**
     * @param Address|null $shippingAddress
     * @return MerchantOrder
     */
    public function setShippingAddress(?Address $shippingAddress): MerchantOrder
    {
        $this->shippingAddress = $shippingAddress;
        return $this;
    }

    /**
     * @return bool
     */
    public function isShowSav(): bool
    {
        return $this->showSav;
    }

    /**
     * @param bool $showSav
     * @return MerchantOrder
     */
    public function setShowSav(bool $showSav): MerchantOrder
    {
        $this->showSav = $showSav;
        return $this;
    }

    public function findOrderItem(int $orderItemId): ?OrderItem
    {
        foreach ($this->orderItems as $orderItem) {
            if ($orderItemId === $orderItem->getDistantId()) {
                return $orderItem;
            }
        }
        return null;
    }

    public function amountHTWithoutShipping(): float
    {
        return ($this->amountHT - $this->shippingHT - $this->extraFees);
    }

    public function getExtraFees(): float
    {
        return $this->extraFees;
    }

    public function setExtraFees(float $extraFees): self
    {
        $this->extraFees = $extraFees;
        return $this;
    }
}
