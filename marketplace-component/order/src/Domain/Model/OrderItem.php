<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Domain\Model;

use Marketplace\Component\Order\Infrastructure\ValueObject\IzbergStatus;
use JsonSerializable;

class OrderItem implements JsonSerializable
{
    public const CANCELLED_STATUS = "cancelled";

    private int $id;
    private string $name;
    private float $amountTTC;
    private float $amountHT;
    private string $description;
    private int $quantity;
    private MerchantOrder $merchantOrder;
    private string $image;
    private bool $guaranteeEligible = false;
    private int $distantId;
    private int $guaranteeMonth = 0;
    private int $invoicedQuantity = 0;
    private ?string $offerExternalId = null;
    private int $invoiceable_quantity = 0;
    private ?int $offerId;
    private ?int $subCategoryId;
    private ?string $regionCode;
    private ?int $variationId = null;
    private ?string $status = null;
    private ?string $sku = null;
    private ?int $batchsize = null;

    public function __construct(int $id)
    {
        $this->setId($id);
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): OrderItem
    {
        $this->id = $id;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): OrderItem
    {
        $this->name = $name;
        return $this;
    }

    public function getAmountTTC(): float
    {
        return $this->amountTTC;
    }

    public function setAmountTTC(float $amountTTC): OrderItem
    {
        $this->amountTTC = $amountTTC;
        return $this;
    }

    public function getAmountHT(): float
    {
        return $this->amountHT;
    }

    public function setAmountHT(float $amountHT): OrderItem
    {
        $this->amountHT = $amountHT;
        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): OrderItem
    {
        $this->description = $description;
        return $this;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): OrderItem
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function getMerchantOrder(): MerchantOrder
    {
        return $this->merchantOrder;
    }

    public function setMerchantOrder(MerchantOrder $merchantOrder): OrderItem
    {
        $this->merchantOrder = $merchantOrder;
        return $this;
    }

    public function getImage(): string
    {
        return $this->image;
    }

    public function setImage(string $image): OrderItem
    {
        $this->image = $image;
        return $this;
    }

    /**
     * @return bool
     */
    public function isGuaranteeEligible(): bool
    {
        return $this->guaranteeEligible;
    }

    /**
     * @param bool $guaranteeEligible
     * @return OrderItem
     */
    public function setGuaranteeEligible(bool $guaranteeEligible): OrderItem
    {
        $this->guaranteeEligible = $guaranteeEligible;
        return $this;
    }

    /**
     * @return int
     */
    public function getDistantId(): int
    {
        return $this->distantId;
    }

    /**
     * @param int $distantId
     * @return OrderItem
     */
    public function setDistantId(int $distantId): OrderItem
    {
        $this->distantId = $distantId;
        return $this;
    }

    /**
     * @return int
     */
    public function getGuaranteeMonth(): int
    {
        return $this->guaranteeMonth;
    }

    /**
     * @param int $guaranteeMonth
     * @return OrderItem
     */
    public function setGuaranteeMonth(int $guaranteeMonth): OrderItem
    {
        $this->guaranteeMonth = $guaranteeMonth;
        return $this;
    }

    public function getInvoicedQuantity(): int
    {
        return $this->invoicedQuantity;
    }

    public function setInvoicedQuantity(int $invoicedQuantity): self
    {
        $this->invoicedQuantity = $invoicedQuantity;
        return $this;
    }

    public function getOfferExternalId(): ?string
    {
        return $this->offerExternalId;
    }

    public function setOfferExternalId(?string $offerExternalId): self
    {
        $this->offerExternalId = $offerExternalId;
        return $this;
    }

    public function getInvoiceableQuantity(): int
    {
        return $this->invoiceable_quantity;
    }

    public function setInvoiceableQuantity(int $invoiceable_quantity): self
    {
        $this->invoiceable_quantity = $invoiceable_quantity;

        return $this;
    }

    /**
     * @return int|null
     */
    public function getOfferId(): ?int
    {
        return $this->offerId;
    }

    /**
     * @param int|null $offerId
     * @return $this
     */
    public function setOfferId(?int $offerId): self
    {
        $this->offerId = $offerId;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getSubCategoryId(): ?int
    {
        return $this->subCategoryId;
    }

    /**
     * @param int|null $subCategoryId
     * @return $this
     */
    public function setSubCategoryId(?int $subCategoryId): self
    {
        $this->subCategoryId = $subCategoryId;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getRegionCode(): ?string
    {
        return $this->regionCode;
    }

    /**
     * @param string|null $regionCode
     * @return $this
     */
    public function setRegionCode(?string $regionCode): self
    {
        $this->regionCode = $regionCode;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getVariationId(): ?int
    {
        return $this->variationId;
    }

    /**
     * @param int|null $variationId
     * @return OrderItem
     */
    public function setVariationId(?int $variationId): OrderItem
    {
        $this->variationId = $variationId;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): static
    {
        $this->status = $status;
        return $this;
    }

    public function isCancelled(): bool
    {
        return $this->status === self::CANCELLED_STATUS;
    }

    /**
     * @return string|null
     */
    public function getSku(): ?string
    {
        return $this->sku;
    }

    /**
     * @param string|null $sku
     * @return OrderItem
     */
    public function setSku(?string $sku): OrderItem
    {
        $this->sku = $sku;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getBatchsize(): ?int
    {
        return $this->batchsize;
    }

    /**
     * @param int|null $batchsize
     * @return OrderItem
     */
    public function setBatchsize(?int $batchsize): OrderItem
    {
        $this->batchsize = $batchsize;
        return $this;
    }

    public function jsonSerialize(): mixed
    {
        return get_object_vars($this);
    }
}
