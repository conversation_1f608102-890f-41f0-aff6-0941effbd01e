<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Presentation\Presenter;

use Marketplace\Component\Order\Domain\Presenter\ShowOrderPresenterInterface;
use Marketplace\Component\Order\Domain\UseCase\ShowOrder\DTO\ShowOrderResponse;
use Marketplace\Component\Order\Presentation\ViewModel\ShowOrderViewModel;
use Symfony\Contracts\Translation\TranslatorInterface;

class ShowOrderPresenter implements ShowOrderPresenterInterface
{

    private ShowOrderViewModel $viewModel;

    public function __construct(private TranslatorInterface $translator)
    {
        $this->viewModel = new ShowOrderViewModel($this->translator);
    }

    public function present(ShowOrderResponse $response): void
    {
        $this->viewModel->notFound = $response->getOrder() === null;
        $this->viewModel->setOrder($response->getOrder());
        $this->viewModel->setOffers($response->getOffers());
    }

    public function viewModel(array $menu): ShowOrderViewModel
    {
        $this->viewModel->setMenu($menu);
        return $this->viewModel;
    }
}
