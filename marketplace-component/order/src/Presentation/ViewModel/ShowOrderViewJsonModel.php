<?php

declare(strict_types=1);

namespace Marketplace\Component\Order\Presentation\ViewModel;

use Marketplace\Component\Order\Domain\Model\Order;
use Symfony\Contracts\Translation\TranslatorInterface;

final class ShowOrderViewJsonModel
{
    private ?Order $order = null;

    private array $offers = [];

    /**
     * @return Order|null
     */
    public function getOrder(): ?Order
    {
        return $this->order;
    }

    /**
     * @param Order|null $order
     */
    public function setOrder(?Order $order): void
    {
        $this->order = $order;
    }

    /**
     * @return array
     */
    public function getOffers(): array
    {
        return $this->offers;
    }

    /**
     * @param array $offers
     */
    public function setOffers(array $offers): void
    {
        $this->offers = [];
        foreach ($offers as $offer) {
            $this->offers[$offer->getId()] = $offer;
        }
    }
}
