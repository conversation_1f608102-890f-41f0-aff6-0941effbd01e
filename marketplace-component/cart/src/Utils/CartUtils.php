<?php

namespace Marketplace\Component\Cart\Utils;

use Doctrine\Common\Collections\Collection;

class CartUtils
{
    /**
     * This function will calculate the sum of a collection items with a given method.
     * @param string $methode
     * @param Collection $collection
     * @param $service
     * @return float|null
     */
    public static function sum(string $methode, Collection $collection, $service): ?float
    {
        $collectionArray = $collection->toArray();
        return array_reduce($collectionArray, function ($sum, $element) use ($methode, $service) {
            if (!method_exists($service, $methode)) {
                return null;
            }
            $sum += $service->{$methode}($element);
            return $sum;
        });
    }
}
