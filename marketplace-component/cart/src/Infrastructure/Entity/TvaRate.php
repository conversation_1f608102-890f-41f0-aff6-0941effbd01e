<?php

namespace Marketplace\Component\Cart\Infrastructure\Entity;

use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use Marketplace\Component\Cart\Infrastructure\Adapter\Repository\TvaRateRepository;

/**
 * @ORM\Table(name="tva_rate")
 * @ORM\Entity(repositoryClass=TvaRateRepository::class)
 * @ORM\HasLifecycleCallbacks()
 */
class TvaRate
{
    /**
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private int $id;

    /**
     * @ORM\ManyToOne(targetEntity="Marketplace\Component\Cart\Infrastructure\Entity\TvaGroup")
     */
    private TvaGroup $group;

    /**
     * @ORM\Column(name="from_date", type="datetime", nullable=true)
     */
    private \DateTime $fromDate;


    /**
     * @ORM\Column(name="rate", type="float")
     */
    private float $rate;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return TvaRate
     */
    public function setId(int $id): TvaRate
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return TvaGroup
     */
    public function getGroup(): TvaGroup
    {
        return $this->group;
    }

    /**
     * @param TvaGroup $group
     * @return TvaRate
     */
    public function setGroup(TvaGroup $group): TvaRate
    {
        $this->group = $group;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getFromDate(): \DateTime
    {
        return $this->fromDate;
    }

    /**
     * @param \DateTime $fromDate
     * @return TvaRate
     */
    public function setFromDate(\DateTime $fromDate): TvaRate
    {
        $this->fromDate = $fromDate;
        return $this;
    }

    /**
     * @return float
     */
    public function getRate(): float
    {
        return $this->rate;
    }

    /**
     * @param float $rate
     * @return TvaRate
     */
    public function setRate(float $rate): TvaRate
    {
        $this->rate = $rate;
        return $this;
    }
}
