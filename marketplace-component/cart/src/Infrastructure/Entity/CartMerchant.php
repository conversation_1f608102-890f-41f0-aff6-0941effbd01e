<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Infrastructure\Entity;

class CartMerchant
{
    private int $id;

    private string $name;

    private array $cartItems = [];

    private Cart $cart;

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): CartMerchant
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return array
     */
    public function getCartItems(): array
    {
        return $this->cartItems;
    }

    /**
     * @param array $cartItems
     * @return CartMerchant
     */
    public function setCartItems(array $cartItems): CartMerchant
    {
        $this->cartItems = $cartItems;
        return $this;
    }

    public function getCart(): Cart
    {
        return $this->cart;
    }

    public function setCart(Cart $cart): CartMerchant
    {
        $this->cart = $cart;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }
}
