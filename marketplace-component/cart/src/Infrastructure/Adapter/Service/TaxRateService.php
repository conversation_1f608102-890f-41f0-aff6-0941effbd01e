<?php

namespace Marketplace\Component\Cart\Infrastructure\Adapter\Service;

use Marketplace\Component\Cart\Domain\Model\Cart;
use Marketplace\Component\Cart\Domain\Model\CartItem;
use Marketplace\Component\Cart\Domain\Port\Repository\CartItemRepositoryInterface;
use Marketplace\Component\Cart\Domain\Port\Service\TaxRateServiceInterface;
use Marketplace\Component\Cart\Infrastructure\Adapter\Repository\TvaRateRepository;
use Marketplace\Component\Cart\Infrastructure\Entity\TvaRate;
use Marketplace\Component\CleanArchiCore\Domain\Exception\NotFoundException;
use Marketplace\Component\User\Domain\Model\Address;
use Marketplace\Component\User\Domain\Model\Country;
use Marketplace\Component\User\Domain\Port\Repository\AddressRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\CountryRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\MerchantRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;

class TaxRateService implements TaxRateServiceInterface
{


    public function __construct(
        private MerchantRepositoryInterface $merchantRepository,
        private UserRepositoryInterface $userRepository,
        private TvaRateRepository $tvaRepository,
        private CartItemRepositoryInterface $cartItemRepository,
        private CountryRepositoryInterface $countryRepository,
        private AddressRepositoryInterface $addressRepository
    ) {
    }

    public function sameVatZone(?Country $merchantCountry, ?Country $buyerCountry): bool
    {
        $merchantZoneCode = $merchantCountry?->getVatZoneCode();
        $buyerZoneCode = $buyerCountry?->getVatZoneCode();
        if ($merchantZoneCode === null || $buyerZoneCode === null) {
            return false;
        }
        return $merchantZoneCode === $buyerZoneCode;
    }

    public function getTaxRateToApplyByCountry(?string $taxGroupName, int $merchantId, int $countryId): float
    {
        $merchant = $this->merchantRepository->findById($merchantId);
        $countryBuyer = $this->countryRepository->findById($countryId);
        return $this->getTaxRate($taxGroupName, $merchant?->getCountry(), $countryBuyer);
    }

    /**
     * @throws NotFoundException
     */
    public function getTaxRateToApplyByAddress(?string $taxGroupName, int $merchantId, int $addressId): float
    {
        $address = $this->addressRepository->findById($addressId);
        if (!$address instanceof Address) {
            throw new NotFoundException("can't find country for buyer");
        }
        return $this->getTaxRateToApplyByCountry($taxGroupName, $merchantId, $address->getCountryId());
    }

    /**
     * @throws NotFoundException
     */
    public function getTaxRateToApply(?string $taxGroupName, int $merchantId, int $buyerId): float
    {
        $merchant = $this->merchantRepository->findByDistantId($merchantId);
        $buyer = $this->userRepository->findById($buyerId);
        return $this->getTaxRate($taxGroupName, $merchant?->getCountry(), $buyer?->getCompany()?->getCountry());
    }

    public function updateTvaRate(Cart $cart, int $merchantId, string $merchantVatZone): array
    {
        $shippingAddress = $cart->getShippingAddress();
        if ($shippingAddress === null) {
            return [];
        }
        $updated = []; //key is old Id, value is new CartItem obj
        foreach ($cart->getCartItems() as $cartItem) {
            if (
                $cartItem->getOffer()->getStatus() === "active" // if inactive, izberg throw an error and delete offer
                && $cartItem->getMerchantId() === $merchantId
            ) {
                $taxGroupName = $cartItem->getOffer()->getTaxGroupName() ?? "";
                $taxRate = $this->getTaxRateByVatRegion(
                    $taxGroupName,
                    $merchantVatZone,
                    $shippingAddress->getVatZoneName(),
                    $shippingAddress->getCountryId()
                );

                if ($cartItem->getTaxRate() !== $taxRate) {
                    $cartItem->setTaxRate($taxRate);
                    $oldId = $cartItem->getId();
                    $cartItemUpdated = $this->cartItemRepository->update($cartItem);
                    $cartItemUpdated->setOffer($cartItem->getOffer());
                    $updated[$oldId] = $cartItemUpdated;
                }
            }
        }

        if ($updated === []) {
            return [];
        }
        //remove old cartItem info
        $items =
            array_filter($cart->getCartItems(), function (CartItem $item) use ($updated) {
                return !array_key_exists($item->getId(), $updated);
            });
        //adding updated cartItem
        array_push($items, ...array_values($updated));
        $cart->setCartItems($items);

        return $updated;
    }

    public function getTaxRateByVatRegion(
        string $taxGroupName,
        string $merchantVatZone,
        string $buyerVatZone,
        int $countryVatRefId
    ): float {
        if ($merchantVatZone !== $buyerVatZone) {
            return 0;
        }

        /** @var TvaRate $tva */
        $tva = $this->tvaRepository->getTaxRateFromTaxGroupAndDate(
            $countryVatRefId,
            $taxGroupName,
            new \DateTime()
        );
        if ($tva != null) {
            return $tva->getRate();
        }

        return 0;
    }

    public function getAgencyCountryAntilles(): Country
    {

        $country = $this->countryRepository->findByCode("martinique");
        if (!$country instanceof Country) {
            throw new NotFoundException("Agency Country not found for antilles");
        }
        return $country;
    }

    private function getTaxRate(
        ?string $taxGroupName,
        ?Country $merchantCountry,
        ?Country $buyerCountry
    ): float {

        if (!$this->sameVatZone($merchantCountry, $buyerCountry)) {
            return 0;
        }

        $merchantCountryId = $merchantCountry?->getId();
        if ($merchantCountryId === null || $taxGroupName === null) {
            return 0;
        }

        /** @var TvaRate $tva */
        $tva = $this->tvaRepository->getTaxRateFromTaxGroupAndDate(
            $merchantCountryId,
            $taxGroupName,
            new \DateTime()
        );

        if ($tva === null) {
            return 0;
        }
        return $tva->getRate();
    }
}
