<?php

namespace Marketplace\Component\Cart\Infrastructure\Adapter\Service;

use Marketplace\Component\Cart\Domain\Exceptions\CartException;
use Marketplace\Component\Cart\Domain\Exceptions\OfferDoesNotWarehouseException;
use Marketplace\Component\Cart\Domain\Exceptions\UnknownWareHouseCountryException;
use Marketplace\Component\Cart\Domain\Model\Cart;
use Marketplace\Component\Cart\Domain\Model\CartItem;
use Marketplace\Component\Cart\Domain\Model\CCPointOffer;
use Marketplace\Component\Cart\Domain\Model\CCpointReference;
use Marketplace\Component\Cart\Domain\Model\Shipping\Suggestion;
use Marketplace\Component\Cart\Domain\Model\Shipping\SuggestionCollection;
use Marketplace\Component\Cart\Domain\Port\Service\MatchShippingServiceInterface;
use Marketplace\Component\Cart\Domain\Port\Service\TaxRateServiceInterface;
use Marketplace\Component\Cart\Domain\Port\Service\WareHouseServiceInterface;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CustomAttributesInterface;
use Marketplace\Component\CleanArchiCore\Utils\Str;
use Marketplace\Component\Offer\Domain\Model\Enum\OfferAvailabilityEnum;
use Marketplace\Component\Offer\Domain\Model\Offer;

class MatchShippingService implements MatchShippingServiceInterface
{
    public function __construct(
        private readonly CustomAttributesInterface $customAttributes,
        private WareHouseServiceInterface $wareHouseService
    ) {
    }


    /**
     * @param CartItem[] $cartItems
     * @param array $shippingSuggestions
     * @throws OfferDoesNotWarehouseException
     */
    public function extractMatchShippingModeOfCartItem(
        array $cartItems,
        array $shippingSuggestions
    ): void {

        $commonWarehouseForMerchant = $this->getCommonWarehouseForMerchant($cartItems, $shippingSuggestions);
        $availabilityHomogeneity = $this->getAvailabilityHomogeneityForMerchant($cartItems);

        foreach ($cartItems as $cartItem) {
            $offer = $cartItem->getOffer();
            $replenishmentLeadTime = $this->getReplenishmentLeadTime($offer);
            $warehousesOfOffer = $cartItem->getOffer()->getWarehouses();

            if ($warehousesOfOffer === []) {
                throw new OfferDoesNotWarehouseException(
                    sprintf('The offer id %d does not warehouse available.', $cartItem->getOfferId())
                );
            }
            $merchantId = $cartItem->getMerchantId();
            if (!isset($shippingSuggestions[$merchantId])) {
                continue;
            }

            $customDelivery = $this->isCustomDelivery(
                $commonWarehouseForMerchant,
                $merchantId,
                $availabilityHomogeneity[$merchantId]
            );

            foreach ($shippingSuggestions[$merchantId] as $suggestion) {
                if (
                    $suggestion['name'] === 'click-and-collect' || $suggestion['name'] === 'click-and-collect_demand'
                ) {
                    //$this->addPickUpInStore($offer, $cartItem, $suggestion, $ccRef);
                    continue;
                }

                $availability = Str::lastRightPart($suggestion['name'], '_');
                $shortName = Str::extractLeftPart($suggestion['name'], '_', 2);

                $countryShipping = $this->wareHouseService->getWareHouseCountry($shortName, $merchantId);
                if ($countryShipping === null) {
                    throw new UnknownWareHouseCountryException(
                        sprintf("Can't determine country for warehouse: %s", $shortName)
                    );
                }

                $monocolis = Str::extractLeftPart(Str::lastRightPart($suggestion['name'], '_', 2), '_');
                if ($this->testSinglePackage($merchantId, $cartItem, $offer, $monocolis, $shortName)) {
                    $onDemand = $availability === SuggestionCollection::DEMAND_TYPE
                        && $offer->isOnDemand() === true;
                    $inStock = $availability === SuggestionCollection::STOCK_TYPE
                        && $offer->isOnDemand() === false;
                    if ($onDemand) {
                        $cartItem->addSinglePackageShippingOnDemand(
                            $shortName,
                            new Suggestion(
                                name: $suggestion['carrier_name'],
                                shortName: $shortName,
                                cartItemId: $cartItem->getId(),
                                deliveryWithinHours: $suggestion['delivery_within_hours'] + $replenishmentLeadTime,
                                collectionWithinHours: $suggestion['collection_within_hours'],
                                amountWithVat: (float)$suggestion['amount_with_vat'],
                                amountWithoutVat: (float)$suggestion['amount_without_vat'],
                                vatAmount: (float)$suggestion['vat_amount'],
                                vatRate: (float)$suggestion['vat_rate'],
                                vatRegion: $countryShipping->getVatZoneCode(),
                                deliveryCountryName: $countryShipping->getCode(),
                                carrierId: (int)$suggestion['carrier_id'],
                                availability: false,
                                onDemand: true,
                                singlePackage: true,
                                customDelivery: $customDelivery
                            )
                        );
                    } elseif ($inStock) {
                        $cartItem->addSinglePackageShipping(
                            $shortName,
                            new Suggestion(
                                name: $suggestion['carrier_name'],
                                shortName: $shortName,
                                cartItemId: $cartItem->getId(),
                                deliveryWithinHours: $suggestion['delivery_within_hours'],
                                collectionWithinHours: $suggestion['collection_within_hours'],
                                amountWithVat: (float)$suggestion['amount_with_vat'],
                                amountWithoutVat: (float)$suggestion['amount_without_vat'],
                                vatAmount: (float)$suggestion['vat_amount'],
                                vatRate: (float)$suggestion['vat_rate'],
                                vatRegion: $countryShipping->getVatZoneCode(),
                                deliveryCountryName: $countryShipping->getCode(),
                                carrierId: (int)$suggestion['carrier_id'],
                                availability: true,
                                singlePackage: true,
                                customDelivery: $customDelivery
                            )
                        );
                    }
                } elseif (
                    $monocolis !== SuggestionCollection::SINGLE_PACKAGE &&
                    $merchantId === $cartItem->getMerchantId() &&
                    $this->testAvailability($shortName, $warehousesOfOffer, $availability, $offer->isOnDemand())
                ) {
                    $onDemand = $availability === SuggestionCollection::DEMAND_TYPE
                        && $offer->isOnDemand() === true;
                    $replenishmentLeadTime = 0;
                    if ($onDemand) {
                        $replenishmentLeadTime = $this->getReplenishmentLeadTime($offer);
                    }

                    $cartItem->addShippingSuggestion(
                        new Suggestion(
                            name: $suggestion['carrier_name'],
                            shortName: $shortName,
                            cartItemId: $cartItem->getId(),
                            deliveryWithinHours: $suggestion['delivery_within_hours'] + $replenishmentLeadTime,
                            collectionWithinHours: $suggestion['collection_within_hours'],
                            amountWithVat: (float)$suggestion['amount_with_vat'],
                            amountWithoutVat: (float)$suggestion['amount_without_vat'],
                            vatAmount: (float)$suggestion['vat_amount'],
                            vatRate: (float)$suggestion['vat_rate'],
                            vatRegion: $countryShipping->getVatZoneCode(),
                            deliveryCountryName: $countryShipping->getCode(),
                            carrierId: (int)$suggestion['carrier_id'],
                            availability: $availability === SuggestionCollection::STOCK_TYPE
                            && $offer->isOnDemand() === false,
                            onDemand: $availability === SuggestionCollection::DEMAND_TYPE
                            && $offer->isOnDemand() === true,
                            customDelivery: $customDelivery
                        )
                    );
                }
            }
        }
    }

    /**
     * @param CartItem[] $cartItems $cartItems
     * @param array $shippingSuggestions
     * @return array
     */
    private function getCommonWarehouseForMerchant(array $cartItems, array $shippingSuggestions): array
    {
        $result = [];
        foreach ($shippingSuggestions as $merchantId => $suggestions) {
            $warehousesInCommon = null;
            foreach ($cartItems as $cartItem) {
                if ($cartItem->getMerchantId() === $merchantId) {
                    $offer = $cartItem->getOffer();
                    $warehousesOfOffer = $offer->getWarehouses();
                    if ($warehousesInCommon === null) {
                        $warehousesInCommon = $warehousesOfOffer;
                        continue;
                    }
                    $warehousesInCommon = array_intersect($warehousesInCommon, $warehousesOfOffer);
                }
            }
            $result[$merchantId] = $warehousesInCommon;
        }
        return $result;
    }

    /**
     * @param CartItem[] $cartItems $cartItems
     * @return array return homogeneity by merchant. result[id_merchant] = true/false
     */
    private function getAvailabilityHomogeneityForMerchant(array $cartItems): array
    {
        $result = [];
        $availability = [];
        foreach ($cartItems as $cartItem) {
            //init result
            if (!isset($result[$cartItem->getMerchantId()])) {
                $result[$cartItem->getMerchantId()] = true;
            }
            // init avaibility ref
            if (!isset($availability[$cartItem->getMerchantId()])) {
                $availability[$cartItem->getMerchantId()] = $this->getAvailabilityWeight($cartItem->getOffer());
            } else {
                // check current of against ref
                if ($availability[$cartItem->getMerchantId()] !== $this->getAvailabilityWeight($cartItem->getOffer())) {
                    $result[$cartItem->getMerchantId()] = false;
                }
            }
        }
        return $result;
    }

    /**
     * @throws CartException
     */
    private function getAvailabilityWeight(Offer $offer): int
    {
        switch ($offer->getAvailabilityTag()) {
            case (OfferAvailabilityEnum::SOON_OUT_OF_STOCK):
            case (OfferAvailabilityEnum::IN_STOCK):
                return 1;
            case (OfferAvailabilityEnum::ON_ORDER):
            case (OfferAvailabilityEnum::IN_RESTOCK):
                return 2;
        }
        return -1;
    }

    private function testSinglePackage(
        int $merchantId,
        CartItem $cartItem,
        Offer $offer,
        string $availability,
        string $shortName,
    ): bool {
        $singlePackage = $offer->getAttributes()[$this->customAttributes->singlePackage()] ?? '';
        return $merchantId === $cartItem->getMerchantId() &&
            strtolower($singlePackage) === 'no' &&
            $availability === SuggestionCollection::SINGLE_PACKAGE &&
            in_array($shortName, $offer->getWarehouses());
    }

    private function testAvailability(
        string $shortName,
        array $warehousesOfOffer,
        string $availability,
        bool $onDemand
    ): bool {
        return in_array($shortName, $warehousesOfOffer) &&
            (($availability === SuggestionCollection::STOCK_TYPE && !$onDemand)
                || ($availability === SuggestionCollection::DEMAND_TYPE && $onDemand));
    }


    private function getReplenishmentLeadTime(Offer $offer): int
    {
        if (isset($offer->getAttributes()[$this->customAttributes->getReplenishmentLeadTime()])) {
            return 24 * $offer->getAttributes()[$this->customAttributes->getReplenishmentLeadTime()];
        }
        return 0;
    }

    public function getCCpoint(Cart $cart): CCpointReference
    {
        $ref = new CCpointReference();

        $offerIds = [];
        foreach ($cart->getCartItems() as $cartItem) {
            $offer = $cartItem->getOffer();
            $offerIds[] = $offer->getId();
            foreach ($offer->getAgencies() as $agency) {
                $ref->addOffer($agency, (new CCPointOffer())
                    ->setQuantity($agency["quantity"])
                    ->setOfferId($offer->getId())
                    ->setSku($offer->getSku() ?? '')
                    ->setName($offer->getTitle())
                    ->setWantedQty($cartItem->getQuantity())
                    ->setResplenishDelay($this->getReplenishmentLeadTime($offer)));
            }
        }
        $ref->computeAvailableCCPoints($offerIds);
        return $ref;
    }

    /**
     * @param array $commonWarehouseForMerchant
     * @param int $merchantId
     * @param $availabilityHomogeneity
     * @return bool
     */
    public function isCustomDelivery(array $commonWarehouseForMerchant, int $merchantId, $availabilityHomogeneity): bool
    {
        $customDelivery = false;

        if (isset($commonWarehouseForMerchant[$merchantId])) {
            $noCommonWareHouse = count($commonWarehouseForMerchant[$merchantId]) === 0;
            $availabilityHomogeneityVal = $availabilityHomogeneity ?? null;

            if ($noCommonWareHouse) {
                $customDelivery = true;
            } else {
                if (!$availabilityHomogeneityVal) {
                    $customDelivery = true;
                }
            }
        }
        return $customDelivery;
    }
}
