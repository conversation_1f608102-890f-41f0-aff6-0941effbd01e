<?php

namespace Marketplace\Component\Cart\Infrastructure\Adapter\Service;

use Marketplace\Component\Cart\Domain\Port\Service\WareHouseServiceInterface;
use Marketplace\Component\CleanArchiCore\Utils\Str;
use Marketplace\Component\User\Domain\Model\Country;
use Marketplace\Component\User\Domain\Port\Repository\CountryRepositoryInterface;
use Open\Izberg\Service\AttributeServiceInterface;
use Open\Izberg\Service\ClickAndCollectCountryMapper;

class WareHouseService implements WareHouseServiceInterface
{
    public function __construct(
        private AttributeServiceInterface $attributeService,
        public CountryRepositoryInterface $countryRepository
    ) {
    }

    public function getWareHouseCountry(string $wareHouse, int $merchantId): ?Country
    {
        $attributes = $this->attributeService->getMerchantAttributes($merchantId);
        $wareHouseIndex = intval(Str::lastRightPart($wareHouse, '_'));
        $attributeCountryKey = sprintf('0%d4_COUNTRY_CLICK_COLLECT', $wareHouseIndex);

        $countryAttr = $attributes[$attributeCountryKey] ?? null;
        if ($countryAttr === null) {
            return null;
        }
        $countryCode = ClickAndCollectCountryMapper::mapClickAndCollectCountryCode($countryAttr->getValue());
        if ($countryCode === null) {
            return null;
        }
        return  $this->countryRepository->findByCode($countryCode);
    }
}
