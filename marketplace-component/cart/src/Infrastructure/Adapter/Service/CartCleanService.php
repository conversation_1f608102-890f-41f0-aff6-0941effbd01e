<?php

namespace Marketplace\Component\Cart\Infrastructure\Adapter\Service;

use Marketplace\Component\Cart\Domain\Port\Repository\CartRepositoryInterface;
use Marketplace\Component\Cart\Domain\Port\Service\CartCleanServiceInterface;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Symfony\Component\Security\Core\Security;

class CartCleanService implements CartCleanServiceInterface
{

    public function __construct(
        private CartRepositoryInterface $cartRepository,
        private Security $security
    ) {
    }

    public function cartClean(int $userId, ?bool $preventQuote = false): void
    {
        $this->cartRepository->cleanCurrentCart($userId, $preventQuote);
    }

    public function updateCartRegion(string $currentRegion): void
    {
        $user = $this->security->getUser();

        if (!$user instanceof User) {
            return;
        }
        $userId = $user->getId();
        if ($userId === null) {
            return;
        }
        $cart = $this->cartRepository->findCurrentCartForUser($userId);
        if ($cart !== null && $cart->getRegion() !== $currentRegion) {
            $this->cartRepository->cleanCurrentCart($userId);
        }
    }
}
