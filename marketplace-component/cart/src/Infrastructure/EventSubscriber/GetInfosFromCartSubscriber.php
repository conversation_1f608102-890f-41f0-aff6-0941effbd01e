<?php

namespace Marketplace\Component\Cart\Infrastructure\EventSubscriber;

use Marketplace\Component\Cart\Domain\Port\Repository\CartRepositoryInterface;
use Marketplace\Component\CleanArchiCore\Domain\Event\GetInfosFromCartEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final class GetInfosFromCartSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private CartRepositoryInterface $cartRepository,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            GetInfosFromCartEvent::class => 'onGetUserId',
        ];
    }

    public function onGetUserId(GetInfosFromCartEvent $event): void
    {
        $userId = $this->cartRepository->getUserIdByCartId($event->getCartId());
        $countryCode = $this->cartRepository->findCartRegion($event->getCartId());

        if ($userId === null) {
            throw new \InvalidArgumentException('User Id doesn\'t exist for this cart ID : ' . $event->getCartId());
        }
        $event->userId = $userId;

        if ($countryCode === null) {
            throw new \InvalidArgumentException('Country doesn\'t exist for this cart ID : ' . $event->getCartId());
        }
        $event->countryCode = $countryCode;
    }
}
