<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Infrastructure\Mapper;

use Marketplace\Component\Cart\Domain\Model\SimplifiedCart;
use Marketplace\Component\Cart\Infrastructure\Entity\Cart as DoctrineCart;

abstract class SimplifiedCartMapper
{
    public static function domainToDoctrine(SimplifiedCart $cart): DoctrineCart
    {
        return (new DoctrineCart())
            ->setId($cart->getId())
            ->setCurrency($cart->getCurrency())
        ;
    }

    public static function doctrineToDomain(DoctrineCart $doctrineCart): SimplifiedCart
    {
        return (new SimplifiedCart())
            ->setId($doctrineCart->getId())
            ->setCurrency($doctrineCart->getCurrency())
            ->setContainQuote($doctrineCart->isContainQuote())
        ;
    }
}
