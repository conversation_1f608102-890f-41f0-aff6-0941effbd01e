<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Presentation\Presenter;

use Marketplace\Component\Cart\Domain\Presenter\SelectShippingPresenterInterface;
use Marketplace\Component\Cart\Domain\UseCase\SelectShipping\DTO\SelectShippingResponse;
use Marketplace\Component\Cart\Presentation\ViewModel\SelectShippingViewModel;

class SelectShippingPresenter implements SelectShippingPresenterInterface
{
    private SelectShippingViewModel $viewModel;

    public function present(SelectShippingResponse $response): void
    {
        $this->viewModel = new SelectShippingViewModel();
        $this->viewModel->created = $response->created;
        foreach ($response->getNotification()->getErrors() as $propertyName => $error) {
            $this->viewModel->errors[$error->getFieldName() ?? 'unknown'][] = $error->getMessage();
        }
    }

    public function viewModel(): SelectShippingViewModel
    {
        return $this->viewModel;
    }
}
