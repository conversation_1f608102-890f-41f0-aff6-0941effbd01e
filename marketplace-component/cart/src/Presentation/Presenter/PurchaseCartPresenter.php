<?php

namespace Marketplace\Component\Cart\Presentation\Presenter;

use Marketplace\Component\Cart\Domain\Presenter\PurchaseCartPresenterInterface;
use Marketplace\Component\Cart\Domain\UseCase\PurchaseCart\DTO\PurchaseCartResponse;
use Marketplace\Component\Cart\Presentation\ViewModel\ErrorViewModel;
use Marketplace\Component\Cart\Presentation\ViewModel\PurchaseCartViewModel;
use Marketplace\Component\Payment\Domain\Model\PaymentAction;
use Symfony\Contracts\Translation\TranslatorInterface;

class PurchaseCartPresenter implements PurchaseCartPresenterInterface
{
    private PurchaseCartViewModel $viewModel;

    public function __construct(private readonly TranslatorInterface $translator)
    {
    }


    public function present(PurchaseCartResponse $response): void
    {
        $this->viewModel = new PurchaseCartViewModel();

        $this->viewModel->setError($this->getError($response));

        $purchased = ($response->getCode() === 201);
        $this->viewModel->setPurchased($purchased);

        $paymentAction = $response->getPaymentAction();
        if ($paymentAction instanceof PaymentAction) {
            $redirectUrl = $paymentAction->getRedirectUrl();
            $this->viewModel->setRedirectUrl($redirectUrl);
        }
    }

    public function viewModel(): PurchaseCartViewModel
    {
        return $this->viewModel;
    }

    private function getError(PurchaseCartResponse $response): ?ErrorViewModel
    {

        if ($response->getCode() === 201) {
            return null;
        }
        return new ErrorViewModel($this->translator->trans(
            id: $response->getMessage(),
            parameters: [],
            domain: 'translations'
        ));
    }
}
