<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Presentation\View;

use Marketplace\Component\Cart\Presentation\ViewModel\SelectShippingViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

final class SelectShippingView
{
    public function __construct(private SerializerInterface $serializer)
    {
    }

    public function generateJson(SelectShippingViewModel $viewModel): Response
    {
        return JsonResponse::fromJsonString($this->serializer->serialize(
            $viewModel,
            'json'
        ));
    }
}
