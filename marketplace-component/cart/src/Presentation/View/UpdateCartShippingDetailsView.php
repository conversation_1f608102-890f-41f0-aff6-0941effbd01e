<?php

namespace Marketplace\Component\Cart\Presentation\View;

use Marketplace\Component\Cart\Presentation\ViewModel\PatchCartShippingCommentViewModel;
use Marketplace\Component\Cart\Presentation\ViewModel\UpdateCartShippingDetailsViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class UpdateCartShippingDetailsView
{
    public function __construct(private SerializerInterface $serializer)
    {
    }

    public function generateJson(UpdateCartShippingDetailsViewModel $viewModel): Response
    {
        return JsonResponse::fromJsonString($this->serializer->serialize(
            $viewModel,
            'json'
        ));
    }
}
