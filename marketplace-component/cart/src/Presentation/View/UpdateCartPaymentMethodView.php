<?php

namespace Marketplace\Component\Cart\Presentation\View;

use Marketplace\Component\Cart\Presentation\ViewModel\UpdateCartPaymentMethodViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class UpdateCartPaymentMethodView
{
    public function __construct(
        private SerializerInterface $serializer,
        private TranslatorInterface $translator
    ) {
    }

    public function generateView(UpdateCartPaymentMethodViewModel $viewModel): JsonResponse
    {
        if ($viewModel->isSucceed() === false) {
            return new JsonResponse(
                [
                    'success' => false,
                    'message' => $this->translator->trans('resource.notFound', domain: 'translations')
                ],
                Response::HTTP_NOT_FOUND,
            );
        }

        return JsonResponse::fromJsonString($this->serializer->serialize($viewModel, 'json'));
    }
}
