<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Presentation\View;

use Marketplace\Component\Cart\Presentation\ViewModel\UpdateQuantityCartOfferViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class UpdateQuantityCartOfferView
{
    public function __construct(
        private SerializerInterface $serializer,
    ) {
    }

    public function generateView(UpdateQuantityCartOfferViewModel $viewModel): Response
    {
        $statusCode = $viewModel->found === false ? Response::HTTP_NOT_FOUND : Response::HTTP_OK;
        return JsonResponse::fromJsonString($this->serializer->serialize(
            $viewModel,
            'json'
        ), $statusCode);
    }
}
