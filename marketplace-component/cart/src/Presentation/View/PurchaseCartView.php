<?php

namespace Marketplace\Component\Cart\Presentation\View;

use Marketplace\Component\Cart\Presentation\ViewModel\PurchaseCartViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class PurchaseCartView
{
    public function __construct(
        private SerializerInterface $serializer,
        private TranslatorInterface $translator
    ) {
    }

    public function generateView(PurchaseCartViewModel $viewModel): JsonResponse
    {
        if ($viewModel->getError() !== null) {
            return JsonResponse::fromJsonString($this->serializer->serialize($viewModel->getError(), 'json'));
        }

        return JsonResponse::fromJsonString($this->serializer->serialize($viewModel, 'json'));
    }
}
