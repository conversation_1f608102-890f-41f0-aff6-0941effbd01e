<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Presentation\ViewModel;

class SetEarlyDeliveryViewModel
{
    private int $code;
    private string $message;
    private int $cardId;

    public function getCode(): int
    {
        return $this->code;
    }

    public function setCode(int $code): SetEarlyDeliveryViewModel
    {
        $this->code = $code;
        return $this;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function setMessage(string $message): SetEarlyDeliveryViewModel
    {
        $this->message = $message;
        return $this;
    }

    public function getCardId(): int
    {
        return $this->cardId;
    }

    public function setCardId(int $cardId): SetEarlyDeliveryViewModel
    {
        $this->cardId = $cardId;
        return $this;
    }
}
