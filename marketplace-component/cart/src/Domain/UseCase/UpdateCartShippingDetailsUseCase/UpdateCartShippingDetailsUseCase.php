<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Domain\UseCase\UpdateCartShippingDetailsUseCase;

use Marketplace\Component\Cart\Domain\Model\Cart;
use Marketplace\Component\Cart\Domain\Port\Repository\CartRepositoryInterface;
use Marketplace\Component\Cart\Domain\Presenter\UpdateCartShippingDetailsPresenterInterface;
use Marketplace\Component\Cart\Domain\UseCase\UpdateCartShippingDetailsUseCase\DTO\UpdateCartShippingDetailsRequest;
use Marketplace\Component\Cart\Domain\UseCase\UpdateCartShippingDetailsUseCase\DTO\UpdateCartShippingDetailsResponse;
use Marketplace\Component\CleanArchiCore\Domain\UseCase\AbstractUseCase;

final class UpdateCartShippingDetailsUseCase extends AbstractUseCase
{
    public function __construct(private CartRepositoryInterface $cartRepository)
    {
    }
    public function execute(
        UpdateCartShippingDetailsRequest $request,
        UpdateCartShippingDetailsPresenterInterface $presenter
    ): void {
        $this->logUseCaseRequest($request, __CLASS__);
        $response = new UpdateCartShippingDetailsResponse();

        $userId = $request->getUserId();
        $cart = $this->cartRepository->findCurrentCartForUser($userId, true);
        if (!$cart instanceof Cart) {
            $response->getNotification()->addError('userId', 'cart.unknown');
            $presenter->present($response);
            return;
        }

        $contactName = $request->getName() ? trim($request->getName()) : '';
        $contactPhone = $request->getPhone() ? trim($request->getPhone()) : '';
        $orderNumber = $request->getOrder() ? substr($request->getOrder(), 0, 17) : null;

        $response
            ->setName($contactName)
            ->setPhone($contactPhone)
            ->setOrder($orderNumber);

        if (strlen($contactName) < 1) {
            $response->getNotification()->addError(
                'cart-shipping-contact-name',
                'cart.shipping.contact.validation.required'
            );
            $presenter->present($response);
            return;
        }

        if (strlen($contactPhone) < 1) {
            $response->getNotification()->addError(
                'cart-shipping-contact-phone',
                'cart.shipping.contact.validation.required'
            );
            $presenter->present($response);
            return;
        }

        $cart->setContactName($contactName);
        $cart->setContactPhone($contactPhone);
        $cart->setOrderNumber($orderNumber);
        $this->cartRepository->update($cart);

        $response->setSuccess(true);
        $presenter->present($response);
    }
}
