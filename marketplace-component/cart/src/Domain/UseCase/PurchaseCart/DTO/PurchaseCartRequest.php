<?php

namespace Marketplace\Component\Cart\Domain\UseCase\PurchaseCart\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Model\UseCaseRequestInterface;

class PurchaseCartRequest implements UseCaseRequestInterface
{
    public function __construct(private int $cartId, private int $userId)
    {
    }

    public function getCartId(): int
    {
        return $this->cartId;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function infoToLog(): array
    {
        return get_object_vars($this);
    }
}
