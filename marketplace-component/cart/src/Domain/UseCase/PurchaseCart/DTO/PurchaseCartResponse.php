<?php

namespace Marketplace\Component\Cart\Domain\UseCase\PurchaseCart\DTO;

use Marketplace\Component\Cart\Domain\Model\Cart;
use Marketplace\Component\Order\Domain\Model\Order;
use Marketplace\Component\Payment\Domain\Model\PaymentAction;

class PurchaseCartResponse
{
    public function __construct(
        private int $code,
        private string $message,
        private ?Order $order = null,
        private ?Cart $cart = null,
        private ?PaymentAction $paymentAction = null
    ) {
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function setCode(int $code): self
    {
        $this->code = $code;
        return $this;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;
        return $this;
    }

    public function getOrder(): ?Order
    {
        return $this->order;
    }

    public function setOrder(?Order $order): self
    {
        $this->order = $order;
        return $this;
    }

    public function getCart(): ?Cart
    {
        return $this->cart;
    }

    public function setCart(?Cart $cart): void
    {
        $this->cart = $cart;
    }

    public function getPaymentAction(): ?PaymentAction
    {
        return $this->paymentAction;
    }
}
