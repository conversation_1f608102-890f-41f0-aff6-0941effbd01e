<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Domain\UseCase\ShowCartContactDetailsUseCase\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Error\NotificationTrait;

final class ShowCartContactDetailsResponse
{
    use NotificationTrait;

    private ?string $name;
    private ?string $phone;
    private ?string $order;
    private bool $success = false;

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @param string|null $name
     * @return $this
     */
    public function setName(?string $name): self
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPhone(): ?string
    {
        return $this->phone;
    }

    /**
     * @param string|null $phone
     * @return $this
     */
    public function setPhone(?string $phone): self
    {
        $this->phone = $phone;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getOrder(): ?string
    {
        return $this->order;
    }

    /**
     * @param string|null $order
     * @return $this
     */
    public function setOrder(?string $order): self
    {
        $this->order = $order;
        return $this;
    }

    /**
     * @return bool
     */
    public function isSuccess(): bool
    {
        return $this->success;
    }

    /**
     * @param bool $success
     * @return $this
     */
    public function setSuccess(bool $success): self
    {
        $this->success = $success;
        return $this;
    }
}
