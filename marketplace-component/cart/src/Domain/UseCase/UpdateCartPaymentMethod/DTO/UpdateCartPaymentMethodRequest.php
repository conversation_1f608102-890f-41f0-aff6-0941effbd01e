<?php

namespace Marketplace\Component\Cart\Domain\UseCase\UpdateCartPaymentMethod\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Model\UseCaseRequestInterface;

class UpdateCartPaymentMethodRequest implements UseCaseRequestInterface
{
    public function __construct(
        private int $cartId,
        private int $userId,
        private int $companyId,
        private string $payment
    ) {
    }

    public function getCartId(): int
    {
        return $this->cartId;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function getPayment(): string
    {
        return $this->payment;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function infoToLog(): array
    {
        return get_object_vars($this);
    }
}
