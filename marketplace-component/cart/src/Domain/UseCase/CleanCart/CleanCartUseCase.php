<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Domain\UseCase\CleanCart;

use Marketplace\Component\Cart\Domain\Model\Cart;
use Marketplace\Component\Cart\Domain\Port\Repository\CartItemRepositoryInterface;
use Marketplace\Component\Cart\Domain\Port\Repository\CartRepositoryInterface;
use Marketplace\Component\Cart\Domain\Presenter\CleanCartPresenterInterface;
use Marketplace\Component\Cart\Domain\UseCase\CleanCart\DTO\CleanCartRequest;
use Marketplace\Component\Cart\Domain\UseCase\CleanCart\DTO\CleanCartResponse;
use Marketplace\Component\CleanArchiCore\Domain\UseCase\AbstractUseCase;

final class CleanCartUseCase extends AbstractUseCase
{
    public function __construct(
        private readonly CartItemRepositoryInterface $cartItemRepository,
        private readonly CartRepositoryInterface $cartRepository
    ) {
    }

    public function execute(CleanCartRequest $request, CleanCartPresenterInterface $presenter): void
    {
        $this->logUseCaseRequest($request, __CLASS__);
        $cart = $this->cartRepository->findCartById($request->cartId);

        if (!$cart instanceof Cart) {
            $presenter->present(new CleanCartResponse());
            return;
        }

        foreach ($cart->getCartItems() as $cartItem) {
            $this->cartItemRepository->remove($cartItem);
        }
        $this->cartRepository->cleanCurrentCart($request->userId);

        $presenter->present(new CleanCartResponse(isCleaned: true));
    }
}
