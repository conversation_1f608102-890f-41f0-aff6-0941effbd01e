<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Domain\UseCase\CleanCart\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Model\UseCaseRequestInterface;

final class CleanCartRequest implements UseCaseRequestInterface
{
    public function __construct(
        public int $cartId,
        public int $userId,
    ) {
    }

    public function infoToLog(): array
    {
        return get_object_vars($this);
    }
}
