<?php

namespace Marketplace\Component\Cart\Domain\UseCase\SetEarlyDelivery\DTO;

class SetEarlyDeliveryResponse
{
    private int $code;
    private string $message;
    private int $cartId;

    /**
     * @return int
     */
    public function getCode(): int
    {
        return $this->code;
    }

    /**
     * @param int $code
     * @return SetEarlyDeliveryResponse
     */
    public function setCode(int $code): SetEarlyDeliveryResponse
    {
        $this->code = $code;
        return $this;
    }

    /**
     * @return string
     */
    public function getMessage(): string
    {
        return $this->message;
    }

    /**
     * @param string $message
     * @return SetEarlyDeliveryResponse
     */
    public function setMessage(string $message): SetEarlyDeliveryResponse
    {
        $this->message = $message;
        return $this;
    }

    /**
     * @return int
     */
    public function getCartId(): int
    {
        return $this->cartId;
    }

    /**
     * @param int $cartId
     * @return SetEarlyDeliveryResponse
     */
    public function setCartId(int $cartId): SetEarlyDeliveryResponse
    {
        $this->cartId = $cartId;
        return $this;
    }
}
