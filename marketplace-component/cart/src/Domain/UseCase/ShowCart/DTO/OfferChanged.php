<?php

namespace Marketplace\Component\Cart\Domain\UseCase\ShowCart\DTO;

class OfferChanged
{
    private string $title;
    private string $sku;
    private int $id;

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @param string $title
     * @return OfferChanged
     */
    public function setTitle(string $title): OfferChanged
    {
        $this->title = $title;
        return $this;
    }

    /**
     * @return string
     */
    public function getSku(): string
    {
        return $this->sku;
    }

    /**
     * @param string $sku
     * @return OfferChanged
     */
    public function setSku(string $sku): OfferChanged
    {
        $this->sku = $sku;
        return $this;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return OfferChanged
     */
    public function setId(int $id): OfferChanged
    {
        $this->id = $id;
        return $this;
    }
}
