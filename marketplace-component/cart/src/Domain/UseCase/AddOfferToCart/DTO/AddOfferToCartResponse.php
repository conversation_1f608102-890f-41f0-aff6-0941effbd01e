<?php

namespace Marketplace\Component\Cart\Domain\UseCase\AddOfferToCart\DTO;

use Marketplace\Component\Cart\Domain\Model\Cart;
use Marketplace\Component\Offer\Domain\Model\Offer;

class AddOfferToCartResponse implements AddOfferToCartResponseInterface
{
    public function __construct(
        private int $code,
        private string $message,
        private ?Offer $offer,
        private ?Cart $cart,
        private ?int $numberOfItems,
        private bool $showNotice
    ) {
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function setCode(int $code): self
    {
        $this->code = $code;
        return $this;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;
        return $this;
    }

    public function getOffer(): ?Offer
    {
        return $this->offer;
    }

    public function setOffer(?Offer $offer): self
    {
        $this->offer = $offer;
        return $this;
    }

    public function getCart(): ?Cart
    {
        return $this->cart;
    }

    public function setCart(?Cart $cart): self
    {
        $this->cart = $cart;
        return $this;
    }

    public function getNumberOfItems(): ?int
    {
        return $this->numberOfItems;
    }

    public function setNumberOfItems(?int $numberOfItems): self
    {
        $this->numberOfItems = $numberOfItems;
        return $this;
    }

    public function isShowNotice(): bool
    {
        return $this->showNotice;
    }

    public function setShowNotice(bool $showNotice): void
    {
        $this->showNotice = $showNotice;
    }
}
