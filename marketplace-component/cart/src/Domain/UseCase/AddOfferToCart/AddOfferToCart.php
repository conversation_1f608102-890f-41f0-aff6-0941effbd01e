<?php

namespace Marketplace\Component\Cart\Domain\UseCase\AddOfferToCart;

use Marketplace\Component\Cart\Domain\Model\AddToCartOffer;
use Marketplace\Component\Cart\Domain\Model\Cart;
use Marketplace\Component\Cart\Domain\Port\Service\AddOfferToCartServiceInterface;
use Marketplace\Component\Cart\Domain\Presenter\AddOfferToCartPresenterInterface;
use Marketplace\Component\Cart\Domain\UseCase\AddOfferToCart\DTO\AddOfferToCartRequest;
use Marketplace\Component\Cart\Domain\UseCase\AddOfferToCart\DTO\AddOfferToCartResponse;
use Marketplace\Component\CleanArchiCore\Domain\UseCase\AbstractUseCase;
use Marketplace\Component\Offer\Domain\Model\Offer;

class AddOfferToCart extends AbstractUseCase
{

    /**
     * @param AddOfferToCartServiceInterface $addOfferToCartService
     */
    public function __construct(
        private AddOfferToCartServiceInterface $addOfferToCartService
    ) {
    }


    public function execute(
        AddOfferToCartRequest $request,
        AddOfferToCartPresenterInterface $presenter
    ): void {

        $this->logUseCaseRequest($request, __CLASS__);

        $result = $this->addOfferToCartService->addItemsToCart(
            [new AddToCartOffer($request->getOfferId(), $request->getVariationId(), $request->getQuantity())],
            $request->getUserId()
        );
        $offers = $result->getOffers();
        $offer = $offers !== null ? reset($offers) : null;
        $this->setUpPresenter(
            $result->getCode() ?? 400,
            $result->getMessage() ?? '',
            $offer,
            $result->getCart(),
            $result->getNumberOfItems(),
            $result->isShowNotice(),
            $presenter
        );
    }

    private function setUpPresenter(
        int $code,
        string $message,
        ?Offer $offer,
        ?Cart $cart,
        ?int $numberOfItems,
        bool $showNotice,
        AddOfferToCartPresenterInterface $presenter
    ) {

        $response = new AddOfferToCartResponse($code, $message, $offer, $cart, $numberOfItems, $showNotice);
        $presenter->present($response);
    }
}
