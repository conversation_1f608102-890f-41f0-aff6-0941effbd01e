<?php

namespace Marketplace\Component\Cart\Domain\Model;

class AddOfferToCartResult
{

    /**
     * AddOfferToCartResult constructor.
     * @param int|null $code
     * @param string|null $message
     * @param array|null $offers
     * @param Cart|null $cart
     * @param int|null $numberOfItems
     */
    public function __construct(
        private ?int $code = null,
        private ?string $message = null,
        private ?array $offers = null,
        private ?Cart $cart = null,
        private ?int $numberOfItems = null,
        private bool $showNotice = false
    ) {
    }

    /**
     * @return int|null
     */
    public function getCode(): ?int
    {
        return $this->code;
    }

    /**
     * @param int|null $code
     * @return $this
     */
    public function setCode(?int $code): self
    {
        $this->code = $code;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getMessage(): ?string
    {
        return $this->message;
    }

    /**
     * @param string|null $message
     * @return $this
     */
    public function setMessage(?string $message): self
    {
        $this->message = $message;
        return $this;
    }

    /**
     * @return array|null
     */
    public function getOffers(): ?array
    {
        return $this->offers;
    }

    /**
     * @param array|null $offers
     * @return $this
     */
    public function setOffers(?array $offers): self
    {
        $this->offers = $offers;
        return $this;
    }

    /**
     * @return Cart|null
     */
    public function getCart(): ?Cart
    {
        return $this->cart;
    }

    /**
     * @param Cart|null $cart
     * @return $this
     */
    public function setCart(?Cart $cart): self
    {
        $this->cart = $cart;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getNumberOfItems(): ?int
    {
        return $this->numberOfItems;
    }

    /**
     * @param int|null $numberOfItems
     * @return $this
     */
    public function setNumberOfItems(?int $numberOfItems): self
    {
        $this->numberOfItems = $numberOfItems;
        return $this;
    }

    /**
     * @return bool
     */
    public function isShowNotice(): bool
    {
        return $this->showNotice;
    }
}
