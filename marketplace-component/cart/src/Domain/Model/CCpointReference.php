<?php

namespace Marketplace\Component\Cart\Domain\Model;

class CCpointReference
{
    /** @var CCpoint[] */
    private array $ccPoints = [];
    //overall stock through every C&C, by offerId
    private array $offerStock = [];
    private array $delayByOfferId = [];
    private array $availableCCPoints = [];

    /**
     * @return array
     */
    public function getCcPoints(): array
    {
        return $this->ccPoints;
    }

    public function getCCPoint(string $name): CCpoint
    {
        if (isset($this->ccPoints[$name])) {
            return $this->ccPoints[$name];
        } else {
            return (new CCpoint())->setName($name);
        }
    }

    public function saveCCpoint(CCpoint $ccpoint): void
    {
        $this->ccPoints[$ccpoint->getName()] = $ccpoint;
    }

    public function addOffer(array $agency, CCPointOffer $ccPointOffer): void
    {
        $ccpoint = $this->getCCPoint($agency["name"]);
        $ccpoint->setOriginAgency($agency);
        $ccpoint->addOffer($ccPointOffer);
        $this->saveCCpoint($ccpoint);
        $this->addToStock($ccPointOffer);
    }

    public function getStock(int $offerId): int
    {
        if (isset($this->offerStock[$offerId])) {
            return $this->offerStock[$offerId];
        } else {
            return 0;
        }
    }

    private function addToStock(CCPointOffer $ccPointOffer): void
    {
        if (isset($this->offerStock[$ccPointOffer->getOfferId()])) {
            $this->offerStock[$ccPointOffer->getOfferId()] += $ccPointOffer->getQuantity();
        } else {
            $this->offerStock[$ccPointOffer->getOfferId()] = $ccPointOffer->getQuantity();
            ;
        }
        if (!isset($this->delayByOfferId[$ccPointOffer->getOfferId()])) {
            $this->delayByOfferId[$ccPointOffer->getOfferId()] = $ccPointOffer->getResplenishDelay();
        }
    }

    public function hasOverallDepletion(): bool
    {
        foreach ($this->offerStock as $stock) {
            if ($stock === 0) {
                return true;
            }
        }
        return false;
    }

    public function getMaxDelay(): int
    {
        $max = 0;
        foreach ($this->delayByOfferId as $id => $delay) {
            if ($this->offerStock[$id] === 0) {
                if ($delay > $max) {
                    $max = $delay;
                }
            }
        }
        return $max;
    }

    public function computeAvailableCCPoints(array $offerIds): void
    {

        foreach ($this->ccPoints as $ccPoint) {
            if ($ccPoint->proposeAllOffers($offerIds)) {
                $this->availableCCPoints[] = $ccPoint;
            }
        }
    }

    /**
     * @return CCpoint[]
     */
    public function getAvailableCCPoints(): array
    {
        return $this->availableCCPoints;
    }
}
