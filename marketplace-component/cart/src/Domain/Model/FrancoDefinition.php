<?php

namespace Marketplace\Component\Cart\Domain\Model;

class FrancoDefinition
{
    private array $regions;

    public function __construct(public int $merchantId, public float $threshold, public string $regionDef)
    {
        $this->regions = array_map(function (string $region) {
            return trim($region);
        }, explode(',', $regionDef));
    }

    public function matchRegion(string $region): bool
    {
        return in_array($region, $this->regions);
    }
}
