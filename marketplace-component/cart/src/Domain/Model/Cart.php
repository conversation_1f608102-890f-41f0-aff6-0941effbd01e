<?php

namespace Marketplace\Component\Cart\Domain\Model;

use Marketplace\Component\Payment\Domain\Model\Method\NoPaymentMethod;
use Marketplace\Component\Payment\Domain\Model\Method\PaymentMethod;
use Marketplace\Component\User\Domain\Model\Address;
use Marketplace\Component\User\Infrastructure\Adapter\Repository\UserRepository;
use Open\Izberg\Client\OperatorClient;
use Open\Izberg\Factory\OperatorClientFactory;

class Cart
{
    public const STATUS_CURRENT = 'current';
    public const STATUS_ORDERED = 'ordered';
    public const STATUS_CLEAN = 'clean';

    private int $id;
    private int $userId;
    /** @var CartMerchant[] */
    private array $merchants = [];
    private string $status;
    private string $region;
    private float $total;
    private float $totalWithoutVat = 0;
    private array $extraFeesConfig = [];
    private bool $extraFeesEnabled = false;
    private float $extraFeesAmount = 0;
    private float $extraFeesVatAmount = 0;
    private PaymentMethod $paymentMethod;
    private ?Address $shippingAddress = null;
    private ?Address $billingAddress = null;
    private string $currency;
    private bool $shippingOverride = false;
    private bool $containQuote = false;
    private ?int $quoteShippingDeliveryTime = null;
    private ?int $quoteId = null;
    private ?string $contactName = null;
    private ?string $contactPhone = null;
    private ?string $shippingComment = null;
    private ?string $orderNumber = null;

    /**
     * @var CartItem[]
     */
    private array $cartItems = [];

    /**
     * @var ShippingDetail[]
     */
    private array $shippingDetails = [];

    private ?int $pickAndCollectAddress = null;

    private bool $earlyDelivery = false;

    public function __construct(
        int $userId,
        private OperatorClient $operatorClient,
        private UserRepository $userRepository)
    {
        $this->userId = $userId;
        $this->status = self::STATUS_CURRENT;
        $this->total = 0.0;
        $this->currency = 'EUR';
        $this->paymentMethod = PaymentMethod::create(NoPaymentMethod::TYPE);

        $this->extraFeesConfig = $this->operatorClient->globalConfigApi()->fetchExtraFees();
        $this->extraFeesEnabled = !empty($this->extraFeesConfig['enable_extra_fee_setting'])
            && $this->userRepository->findById($this->userId)->getCompany()->isAddExtraFees();
    }

    public function countMerchants(): int
    {
        return count($this->getMerchants());
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): self
    {
        $this->userId = $userId;
        return $this;
    }

    public function getMerchants(): array
    {
        return $this->merchants;
    }

    public function setMerchants(array $merchants): self
    {
        $this->merchants = $merchants;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getRegion(): string
    {
        return $this->region;
    }

    public function setRegion(string $region): self
    {
        $this->region = $region;
        return $this;
    }

    /**
     * @return CartItem[]
     */
    public function getCartItems(): array
    {
        return $this->cartItems;
    }

    /**
     * @param CartItem[] $cartItems
     */
    public function setCartItems(array $cartItems): self
    {
        $this->cartItems = $cartItems;
        return $this;
    }

    public function getTotal(): float
    {
        return $this->total;
    }

    public function setTotal(float $total): self
    {
        $this->total = $total;
        return $this;
    }

    public function hasPaymentMethod(): bool
    {
        return ($this->paymentMethod->getType() !== NoPaymentMethod::TYPE);
    }

    public function getPaymentMethod(): PaymentMethod
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod(PaymentMethod $paymentMethod): self
    {
        $this->paymentMethod = $paymentMethod;
        return $this;
    }

    public function getShippingAddress(): ?Address
    {
        return $this->shippingAddress;
    }

    public function setShippingAddress(?Address $shippingAddress): self
    {
        $this->shippingAddress = $shippingAddress;
        return $this;
    }

    public function getBillingAddress(): ?Address
    {
        return $this->billingAddress;
    }

    public function setBillingAddress(?Address $billingAddress): self
    {
        $this->billingAddress = $billingAddress;
        return $this;
    }


    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    /**
     * @return bool
     */
    public function isShippingOverride(): bool
    {
        return $this->shippingOverride;
    }

    /**
     * @param bool $shippingOverride
     * @return Cart
     */
    public function setShippingOverride(bool $shippingOverride): Cart
    {
        $this->shippingOverride = $shippingOverride;
        return $this;
    }

    /**
     * @return ShippingDetail[]
     */
    public function getShippingDetails(): array
    {
        return $this->shippingDetails;
    }

    /**
     * @param ShippingDetail[] $shippingDetails
     * @return Cart
     */
    public function setShippingDetails(array $shippingDetails): Cart
    {
        $this->shippingDetails = $shippingDetails;
        return $this;
    }

    /**
     * @return bool
     */
    public function isContainQuote(): bool
    {
        return $this->containQuote;
    }

    /**
     * @param bool $containQuote
     * @return Cart
     */
    public function setContainQuote(bool $containQuote): Cart
    {
        $this->containQuote = $containQuote;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getQuoteShippingDeliveryTime(): ?int
    {
        return $this->quoteShippingDeliveryTime;
    }

    /**
     * @param int|null $quoteShippingDeliveryTime
     * @return Cart
     */
    public function setQuoteShippingDeliveryTime(?int $quoteShippingDeliveryTime): Cart
    {
        $this->quoteShippingDeliveryTime = $quoteShippingDeliveryTime;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getQuoteId(): ?int
    {
        return $this->quoteId;
    }

    /**
     * @param int|null $quoteId
     * @return Cart
     */
    public function setQuoteId(?int $quoteId): Cart
    {
        $this->quoteId = $quoteId;
        return $this;
    }

    public function isMultiMerchant(): bool
    {
        return count($this->merchants) > 1;
    }

    /**
     * @return string|null
     */
    public function getContactName(): ?string
    {
        return $this->contactName;
    }

    /**
     * @param string|null $contactName
     * @return $this
     */
    public function setContactName(?string $contactName): self
    {
        $this->contactName = $contactName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getContactPhone(): ?string
    {
        return $this->contactPhone;
    }

    /**
     * @param string|null $contactPhone
     * @return $this
     */
    public function setContactPhone(?string $contactPhone): self
    {
        $this->contactPhone = $contactPhone;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getShippingComment(): ?string
    {
        return $this->shippingComment;
    }

    /**
     * @param string|null $shippingComment
     * @return $this
     */
    public function setShippingComment(?string $shippingComment): self
    {
        $this->shippingComment = $shippingComment;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getOrderNumber(): ?string
    {
        return $this->orderNumber;
    }

    /**
     * @param string|null $orderNumber
     * @return $this
     */
    public function setOrderNumber(?string $orderNumber): self
    {
        $this->orderNumber = $orderNumber;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getPickAndCollectAddress(): ?int
    {
        return $this->pickAndCollectAddress;
    }

    /**
     * @param int|null $pickAndCollectAddress
     * @return Cart
     */
    public function setPickAndCollectAddress(?int $pickAndCollectAddress): Cart
    {
        $this->pickAndCollectAddress = $pickAndCollectAddress;
        return $this;
    }

    /**
     * @return float
     */
    public function getTotalWithoutVat(): float
    {
        return $this->totalWithoutVat;
    }

    /**
     * @return bool
     */
    public function isEarlyDelivery(): bool
    {
        return $this->earlyDelivery;
    }

    /**
     * @param bool $earlyDelivery
     * @return Cart
     */
    public function setEarlyDelivery(bool $earlyDelivery): Cart
    {
        $this->earlyDelivery = $earlyDelivery;
        return $this;
    }


    /**
     * @param float $totalWithoutVat
     * @return Cart
     */
    public function setTotalWithoutVat(float $totalWithoutVat): Cart
    {
        $this->totalWithoutVat = $totalWithoutVat;

        $this->refreshExtraFees();

        return $this;
    }

    /**
     * <AUTHOR> Bulochnik
     * @return void
     */
    public function refreshExtraFees()
    {
        if ($this->extraFeesEnabled && $this->totalWithoutVat < $this->extraFeesConfig['extra_fee_threshold']) {
            $this->setExtraFeesVatAmount((float) ($this->extraFeesConfig['extra_fee_amount'] * 20 / 100)); // TODO: use tax rate of the first product
            $this->setExtraFeesAmount((float) ($this->extraFeesConfig['extra_fee_amount'] + $this->getExtraFeesVatAmount()));
        }
    }

    public function getTotalWithoutVatByMerchant(int $merchantId): float
    {
        $merchantItems = array_filter($this->cartItems, function (CartItem $item) use ($merchantId) {
            return $item->getMerchantId() === $merchantId;
        });
        return array_reduce($merchantItems, function (float $carry, CartItem $item) {
            $carry += $item->getUnitPrice() * $item->getQuantity();
            return $carry;
        }, 0);
    }

    public function getOffersIdByMerchant(): array
    {
        $result = [];
        foreach ($this->cartItems as $item) {
            if (!isset($result[$item->getMerchantId()])) {
                $result[$item->getMerchantId()] = [];
            }
            $result[$item->getMerchantId()][] = $item->getOfferId();
        }
        return $result;
    }

    /**
     * @param float $extraFeesAmount
     * @return Cart
     */
    public function setExtraFeesAmount(float $extraFeesAmount): Cart
    {
        $this->extraFeesAmount = $extraFeesAmount;

        return $this;
    }

    /**
     * @return float
     */
    public function getExtraFeesAmount(): float
    {
        return $this->extraFeesAmount;
    }

    /**
     * @param float $extraFeesAmount
     * @return Cart
     */
    public function setExtraFeesVatAmount(float $extraFeesVatAmount): Cart
    {
        $this->extraFeesVatAmount = $extraFeesVatAmount;

        return $this;
    }

    /**
     * @return float
     */
    public function getExtraFeesVatAmount(): float
    {
        return $this->extraFeesVatAmount;
    }

    /**
     * @return bool
     */
    public function getExtraFeesEnabled(): bool
    {
        return $this->extraFeesEnabled;
    }

    /**
     * @return array
     */
    public function getExtraFeesConfig(): array
    {
        return $this->extraFeesConfig;
    }
}
