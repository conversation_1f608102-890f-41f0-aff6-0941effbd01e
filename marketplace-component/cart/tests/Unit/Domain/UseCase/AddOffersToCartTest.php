<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Tests\Unit\Domain\UseCase;

use Marketplace\Component\Cart\Domain\Model\AddOfferToCartResult;
use Marketplace\Component\Cart\Domain\Model\AddToCartOffer;
use Marketplace\Component\Cart\Domain\Port\Service\AddOfferToCartServiceInterface;
use Marketplace\Component\Cart\Domain\Presenter\AddOfferToCartPresenterInterface;
use Marketplace\Component\Cart\Domain\UseCase\AddOffersToCart\DTO\AddOffersToCartRequest;
use Marketplace\Component\Cart\Domain\UseCase\AddOffersToCart\DTO\AddOffersToCartResponse;
use Marketplace\Component\Cart\Domain\UseCase\AddOffersToCart\AddOffersToCart;
use Marketplace\Component\Cart\Domain\UseCase\AddOfferToCart\DTO\AddOfferToCartResponseInterface;
use Marketplace\Component\Offer\Domain\Model\Offer;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

class AddOffersToCartTest extends TestCase
{
    private AddOffersToCart $useCase;
    private AddOfferToCartPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();
        $this->presenter = new class implements AddOfferToCartPresenterInterface {
            public AddOfferToCartResponseInterface $response;
            public function present(AddOfferToCartResponseInterface $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        $addOfferToCartService = $prophet->prophesize(AddOfferToCartServiceInterface::class);
        $addOfferToCartService->addItemsToCart(
            Argument::type('array'),
            Argument::type('int')
        )->willReturn(
            new AddOfferToCartResult(200, 'ok', [new Offer()], null, 1)
        );
        $this->useCase = new AddOffersToCart($addOfferToCartService->reveal());
    }


    /**
     */
    public function testExecute(): void
    {
        $request = new AddOffersToCartRequest([new AddToCartOffer(1, 10)]);
        $request->setUserId(1);
        $this->useCase->execute($request, $this->presenter);
        $expected = new AddOffersToCartResponse(200, 'ok', [new Offer()], null, 1, false);
        $this->assertEquals($expected, $this->presenter->response);
    }
}
