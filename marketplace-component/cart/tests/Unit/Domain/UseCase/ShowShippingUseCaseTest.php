<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Tests\Domain\UseCase;

use DateTimeImmutable;
use Generator;
use Marketplace\Component\Cart\Domain\Exceptions\OfferDoesNotWarehouseException;
use Marketplace\Component\Cart\Domain\Model\CCPointOffer;
use Marketplace\Component\Cart\Domain\Model\Shipping\CartItemLight;
use Marketplace\Component\Cart\Domain\Model\Shipping\Mode\Cheapest;
use Marketplace\Component\Cart\Domain\Model\Shipping\Mode\Fastest;
use Marketplace\Component\Cart\Domain\Model\Shipping\Mode\PickUpInStore;
use Marketplace\Component\Cart\Domain\Model\Shipping\Mode\ShippingMode;
use Marketplace\Component\Cart\Domain\Model\Shipping\ShippingGroup;
use Marketplace\Component\Cart\Domain\Model\Shipping\ShippingOption;
use Marketplace\Component\Cart\Domain\Model\ShippingDetail;
use Marketplace\Component\Cart\Domain\Port\Service\TaxRateServiceInterface;
use Marketplace\Component\Cart\Domain\Port\Service\WareHouseServiceInterface;
use Marketplace\Component\Cart\Domain\Presenter\ShowShippingPresenterInterface;
use Marketplace\Component\Cart\Domain\UseCase\ShowShipping\DTO\ShowShippingRequest;
use Marketplace\Component\Cart\Domain\UseCase\ShowShipping\DTO\ShowShippingResponse;
use Marketplace\Component\Cart\Domain\UseCase\ShowShipping\ShowShippingUseCase;
use Marketplace\Component\Cart\Infrastructure\Adapter\Service\MatchShippingService;
use Marketplace\Component\Cart\Tests\Unit\Fake\AddressRepositoryFake;
use Marketplace\Component\Cart\Tests\Unit\Fake\ShippingRepositoryFake;
use Marketplace\Component\Cart\Tests\Unit\Fake\ShowShippingCartRepositoryFake;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CustomAttributesInterface;
use Marketplace\Component\CleanArchiCore\Utils\Date\DateUtils;
use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Marketplace\Component\Offer\Domain\Model\Enum\OfferAvailabilityEnum;
use Marketplace\Component\Offer\Domain\Model\Merchant;
use Marketplace\Component\Offer\Domain\Model\MerchantSearchResult;
use Marketplace\Component\Offer\Domain\Model\Offer;
use Marketplace\Component\Offer\Domain\Model\OfferCollection;
use Marketplace\Component\Offer\Domain\Model\OfferIdent;
use Marketplace\Component\Offer\Domain\Model\SearchResult;
use Marketplace\Component\Offer\Domain\Model\SuggestionSearchResult;
use Marketplace\Component\User\Domain\Model\Address;
use Marketplace\Component\User\Domain\Model\Country;
use Marketplace\Component\User\Domain\Port\Repository\CountryRepositoryInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;

final class ShowShippingUseCaseTest extends TestCase
{
    private ShowShippingUseCase $useCase;

    private ShowShippingPresenterInterface $presenter;

    private array $scenarioData;

    private ObjectProphecy|CustomAttributesInterface $customAttributes;

    protected function setUp(): void
    {
        parent::setUp();

        $this->buildInitialFixtures();

        $this->presenter = new class implements ShowShippingPresenterInterface {
            public ShowShippingResponse $response;

            public function present(ShowShippingResponse $response): void
            {
                $this->response = $response;
            }
        };

        $prophecy = new Prophet();
        $this->customAttributes = $prophecy->prophesize(CustomAttributesInterface::class);
        $countryRepository = $prophecy->prophesize(CountryRepositoryInterface::class);

        $this->useCase = new ShowShippingUseCase(
            addressRepository: new AddressRepositoryFake(),
            cartRepository: new ShowShippingCartRepositoryFake(new ShippingRepositoryFake(), $this->scenarioData),
            shippingRepository: new ShippingRepositoryFake(),
            matchShippingService: $this->makeMatchShippingService(),
            taxRateService: $this->makeTaxRateService(),
            francoDefinitions: [],
            countryRepository: $countryRepository->reveal()
        );
    }

    /**
     * @param ShowShippingRequest $request
     * @param ShowShippingResponse $expected
     * @dataProvider provideWithDifferentsWarhouseForOneMerchant
     */
    public function testWithDifferentsWarhouseForOneMerchant(
        ShowShippingRequest $request,
        ShowShippingResponse $expected
    ): void {
        $this->useCase->execute($request, $this->presenter);

        $this->assertEquals($expected, $this->presenter->response);
    }

    public function testMultiplesProductsWithTheSinglePackageAvailable(): void
    {
        $request = new ShowShippingRequest(cartId: 16, companyId: 2, userId: 16);
        $this->useCase->execute($request, $this->presenter);
        $cartItems = [$this->cartItem(1), $this->cartItem(2), $this->cartItem(3)];
        $this->assertEquals([
            (new ShippingGroup())
                ->setMerchantId(11)
                ->setOptions([
                    $this->makeShippingOptionClickCollect($cartItems),
                    $this->makeShippingOptionClickCollect(
                        $cartItems,
                        'click-and-collect_2',
                        'Address click and collect 2'
                    ),
                    $this->makeShippingOptionClickCollect(
                        $cartItems,
                        'click-and-collect_3',
                        'Address click and collect 3'
                    ),
                    $this->makeShippingOption($cartItems, name: 'Warehouse_1_monocolis', customDelivery: true),
                    $this->makeShippingOption(
                        $cartItems,
                        name: 'Warehouse_2_monocolis',
                        deliveryCount: 24,
                        mode: 'fastest',
                        amountWithoutVat: 20,
                        amountWithVat: 24,
                        method: 'Warehouse_2',
                        vatAmount: 4,
                        customDelivery: true
                    ),
                ])
        ], $this->presenter->response->shippingGroup);
    }

    /**
     *
     */
    public function provideWithDifferentsWarhouseForOneMerchant()
    {
        //given : I want to get the shipping for a merchant with 2 carts items with no warehouse in common.
        //Then : I've got the first shipping offer sum with the best shipping offer from the other cart item
        $shippingAddress = $this->makeCompanyAddress('shipping');
        $cartItems = [$this->cartItem(1), $this->cartItem(1)];
        $shippingGroup =
            (new ShippingGroup())
                ->setMerchantId(11)
                ->setOptions([
                    $this->makeShippingOption($cartItems, deliveryCount: 24, customDelivery: true),
                    $this->makeShippingOption(
                        $cartItems,
                        name: 'Warehouse_2_stock',
                        deliveryCount: 20,
                        mode: 'fastest',
                        amountWithoutVat: 29,
                        amountWithVat: 34.8,
                        method: 'Warehouse_2',
                        vatAmount: 5.8,
                        customDelivery: true
                    ),
                    $this->makeShippingOptionClickCollect($cartItems),
                    $this->makeShippingOptionClickCollect(
                        $cartItems,
                        'click-and-collect_2',
                        'Address click and collect 2'
                    ),
                    $this->makeShippingOptionClickCollect(
                        $cartItems,
                        'click-and-collect_3',
                        'Address click and collect 3'
                    ),
                ]);

        $request = new ShowShippingRequest(cartId: 14, companyId: 2, userId: 14);
        $expected = new ShowShippingResponse($shippingAddress, [$shippingGroup]);
        yield "cartItems with different warehouse" => [$request, $expected];

        //given : I want to get the shipping for a merchant with 3 carts items with only two cartItems with
        // same warehouse.
        //Then : I've got the first shipping offer sum with the most common warehouse
        $shippingAddress = $this->makeCompanyAddress('shipping');
        $cartItems = [$this->cartItem(1), $this->cartItem(1), $this->cartItem(1)];
        $shippingGroup =
            (new ShippingGroup())
                ->setMerchantId(11)
                ->setOptions([
                    $this->makeShippingOption($cartItems, deliveryCount: 24, customDelivery: true),
                    $this->makeShippingOption(
                        $cartItems,
                        name: 'Warehouse_2_stock',
                        deliveryCount: 20,
                        mode: 'fastest',
                        amountWithoutVat: 29,
                        amountWithVat: 34.8,
                        method: 'Warehouse_2',
                        vatAmount: 5.8,
                        customDelivery: true
                    ),
                    $this->makeShippingOptionClickCollect($cartItems),
                    $this->makeShippingOptionClickCollect(
                        $cartItems,
                        'click-and-collect_2',
                        'Address click and collect 2'
                    ),
                    $this->makeShippingOptionClickCollect(
                        $cartItems,
                        'click-and-collect_3',
                        'Address click and collect 3'
                    ),
                ]);

        $request = new ShowShippingRequest(cartId: 15, companyId: 2, userId: 15);
        $expected = new ShowShippingResponse($shippingAddress, [$shippingGroup]);
        yield "cartItems have partial common warehouse" => [$request, $expected];
    }

    public function testWithTwoShippingOptionsInTwoMerchant(): void
    {
        // Check if billing address exist for the current company
        $shippingAddress = $this->makeCompanyAddress('shipping');
        $cartItems = [$this->cartItem(1)];
        $shippingGroup =
            (new ShippingGroup())
                ->setMerchantId(11)
                ->setOptions([
                    $this->makeShippingOption(cartItems: $cartItems, deliveryCount: 24),
                    $this->makeShippingOption(
                        $cartItems,
                        name: 'Warehouse_2_stock',
                        deliveryCount: 20,
                        mode: 'fastest',
                        amountWithoutVat: 29,
                        amountWithVat: 34.8,
                        method: 'Warehouse_2',
                        vatAmount: 5.8
                    ),

                ]);
        $shippingGroup2 = (new ShippingGroup())
            ->setMerchantId(20)
            ->setOptions([$this->makeShippingOption([$this->cartItem(2)])]);

        $request = new ShowShippingRequest(cartId: 10, companyId: 2, userId: 10);
        $expected = new ShowShippingResponse($shippingAddress, [$shippingGroup, $shippingGroup2]);
        $this->useCase->execute($request, $this->presenter);


        $this->assertEquals($expected, $this->presenter->response);
    }

    /**
     * @dataProvider showShippingProvider
     * @param ShowShippingRequest $request
     * @param ShowShippingResponse $expected
     */
    public function testExecute(ShowShippingRequest $request, ShowShippingResponse $expected): void
    {
        $this->useCase->execute($request, $this->presenter);
        //dd($this->presenter->response);
        $this->assertEquals($expected, $this->presenter->response);
    }

    public function testGivenIWantShowShippingForOnceOffer(): void
    {
        // When: je sélectionne mon offre 1 du merchantId 1 qui possède 1 seul warehouse identique
        $request = new ShowShippingRequest(cartId: 1, companyId: 2, userId: 1);
        $this->useCase->execute($request, $this->presenter);

        // Then: Je dois avoir comme shipping 1 warehouse_1_stock cheapest et 1 fastest
        $this->assertInstanceOf(ShowShippingResponse::class, $this->presenter->response);
        $this->assertInstanceOf(Address::class, $this->presenter->response->shippingAddress);
        $this->assertCount(1, $this->presenter->response->shippingGroup);
        $cartItems = [$this->cartItem(1)];
        $this->assertEquals([
            (new ShippingGroup())
                ->setMerchantId(1)
                ->setOptions([
                    $this->makeShippingOption($cartItems, name: 'Warehouse_1_stock_cheapest'),
                    $this->makeShippingOption(
                        $cartItems,
                        'Warehouse_1_stock_fastest',
                        deliveryCount: 24,
                        mode: 'fastest',
                        amountWithoutVat: 29,
                        amountWithVat: 34.8,
                        vatAmount: 5.8
                    ),
                    $this->makeShippingOptionClickCollect($cartItems),
                    $this->makeShippingOptionClickCollect(
                        $cartItems,
                        'click-and-collect_3',
                        'Address click and collect 3'
                    ),
                ])

        ], $this->presenter->response->shippingGroup);
    }

    public function testGivenIWantShowShippingForTwoOffers(): void
    {
        // When: J'ai deux offres dans mon panier, offer 1 warehouse_1_stock,
        // offer 2 warehouse_1_stock et warehouse_2_stock
        $request = new ShowShippingRequest(cartId: 2, companyId: 2, userId: 2);
        $this->useCase->execute($request, $this->presenter);

        // Then: Je dois avoir comme shipping 1 warehouse_1_stock cheapest et 1 fastest
        $this->assertInstanceOf(ShowShippingResponse::class, $this->presenter->response);
        $this->assertCount(1, $this->presenter->response->shippingGroup);
        $cartItems = [$this->cartItem(3), $this->cartItem(2)];
        $this->assertEquals([
            (new ShippingGroup())
                ->setMerchantId(1)
                ->setOptions([
                    $this->makeShippingOption($cartItems, name: 'Warehouse_1_stock_cheapest'),
                    $this->makeShippingOption(
                        $cartItems,
                        name: 'Warehouse_2_stock',
                        deliveryCount: 22,
                        mode: 'fastest',
                        amountWithoutVat: 30,
                        amountWithVat: 36,
                        method: 'Warehouse_2',
                        vatAmount: 6
                    ),
                    $this->makeShippingOptionClickCollect($cartItems),
                    $this->makeShippingOptionClickCollect(
                        $cartItems,
                        'click-and-collect_3',
                        'Address click and collect 3'
                    )
                ])
        ], $this->presenter->response->shippingGroup);
    }

    public function testGivenIHaveAnProductWithTheSinglePackageAvailable(): void
    {
        // When: J'ai une offre avec le custom attribute monocolis à oui
        $request = new ShowShippingRequest(cartId: 12, companyId: 2, userId: 12);
        $this->useCase->execute($request, $this->presenter);

        // Then: Je dois avoir comme shipping que du Warehouse_1_monocolis
        $this->assertInstanceOf(ShowShippingResponse::class, $this->presenter->response);
        $this->assertCount(1, $this->presenter->response->shippingGroup);
        $this->assertEquals([
            (new ShippingGroup())
                ->setMerchantId(12)
                ->setOptions([
                    $this->makeShippingOption([$this->cartItem(1)], name: 'Warehouse_1_monocolis'),
                ])
        ], $this->presenter->response->shippingGroup);
    }

    public function testGivenIHaveAnProductWithTheSinglePackageAvailableAndWithOtherSuggestions(): void
    {
        // When: J'ai une offre avec le custom attribute monocolis à non et d'autre suggestion de shipping
        $request = new ShowShippingRequest(cartId: 13, companyId: 2, userId: 13);
        $this->useCase->execute($request, $this->presenter);

        // Then: Je dois avoir comme shipping que du Warehouse_1_monocolis et pas les autres
        $this->assertInstanceOf(ShowShippingResponse::class, $this->presenter->response);
        $this->assertCount(1, $this->presenter->response->shippingGroup);
        $cartItems = [$this->cartItem(1)];
        $this->assertEquals([
            (new ShippingGroup())
                ->setMerchantId(11)
                ->setOptions([
                    $this->makeShippingOptionClickCollect($cartItems),
                    $this->makeShippingOptionClickCollect(
                        $cartItems,
                        'click-and-collect_2',
                        'Address click and collect 2'
                    ),
                    $this->makeShippingOptionClickCollect(
                        $cartItems,
                        'click-and-collect_3',
                        'Address click and collect 3'
                    ),
                    $this->makeSinglePackageOption($cartItems, method: 'Warehouse_1'),
                    $this->makeShippingOption(
                        $cartItems,
                        name: 'Warehouse_2_monocolis',
                        deliveryCount: 24,
                        mode: 'fastest',
                        amountWithoutVat: 20,
                        amountWithVat: 24,
                        method: 'Warehouse_2',
                        vatAmount: 4
                    ),

                ])
        ], $this->presenter->response->shippingGroup);
    }

    public function testGivenIWantShowShippingForMoreOffers(): void
    {
        /*
         * When: J'ai trois produits dans mon panier avec comme proposition de shipping
         *  - warehouse_1_stock (produit 1 et produit 2)
         *  - warehouse_2_stock (produit 2)
         *  - warehouse_3_demand (produit 3)
         */
        $request = new ShowShippingRequest(cartId: 3, companyId: 2, userId: 3);
        $this->useCase->execute($request, $this->presenter);

        /**
         * Then: Je dois avoir de sortie uniquement le warehouse_2_demand
         */
        $this->assertInstanceOf(ShowShippingResponse::class, $this->presenter->response);
        $this->assertCount(1, $this->presenter->response->shippingGroup);
        $cartItems = [$this->cartItem(1), $this->cartItem(2), $this->cartItem(3)];
        $this->assertEquals([
            (new ShippingGroup())
                ->setMerchantId(2)
                ->setOptions([
                    $this->makeShippingOption(
                        cartItems: $cartItems,
                        name: 'Warehouse_1_stock_cheapest',
                        customDelivery: true
                    ),
                    $this->makeShippingOption(
                        $cartItems,
                        name: 'Warehouse_2_stock',
                        deliveryCount: 22,
                        mode: 'fastest',
                        amountWithoutVat: 30,
                        amountWithVat: 36,
                        method: 'Warehouse_2',
                        vatAmount: 6,
                        customDelivery: true
                    ),
                    $this->makeShippingOptionClickCollect($cartItems),
                    $this->makeShippingOptionClickCollect(
                        $cartItems,
                        'click-and-collect_3',
                        'Address click and collect 3'
                    )
                ])
        ], $this->presenter->response->shippingGroup);
    }

    public function showShippingProvider(): Generator
    {
        $this->buildInitialFixtures();

        $shippingAddress = $this->makeCompanyAddress('shipping');

        $cartItems = [$this->cartItem(1), $this->cartItem(2)];
        $shippingGroup = (new ShippingGroup())
            ->setMerchantId(2)
            ->setOptions([
                $this->makeShippingOption(cartItems: $cartItems, name: 'Warehouse_1_stock_cheapest'),
                $this->makeShippingOption(
                    $cartItems,
                    name: 'Warehouse_2_stock',
                    deliveryCount: 22,
                    mode: 'fastest',
                    amountWithoutVat: 30,
                    amountWithVat: 36,
                    method: 'Warehouse_2',
                    vatAmount: 6
                ),

            ]);
        $cartItems = [$this->cartItem(3), $this->cartItem(3)];
        $shippingGroup2 = (new ShippingGroup())
            ->setMerchantId(10)
            ->setOptions([
                $this->makeShippingOption($cartItems, name: 'Warehouse_1_stock_cheapest'),
                $this->makeShippingOption(
                    $cartItems,
                    name: 'Warehouse_1_stock_fastest',
                    deliveryCount: 24,
                    mode: 'fastest',
                    amountWithoutVat: 29,
                    amountWithVat: 34.8,
                    vatAmount: 5.8
                ),
            ]);

        yield "Scenario 4 : With different merchant" => [
            new ShowShippingRequest(cartId: 4, companyId: 2, userId: 4),
            new ShowShippingResponse($shippingAddress, [$shippingGroup, $shippingGroup2])
        ];

        $cartItems = [$this->cartItem(4)];
        $shippingGroup = (new ShippingGroup())
            ->setMerchantId(2)
            ->setOptions([
                $this->makeShippingOption(
                    cartItems: $cartItems,
                    name: 'Warehouse_3_demand',
                    amountWithoutVat: 29,
                    amountWithVat: 34.8,
                    method: 'Warehouse_3',
                    vatAmount: 5.8
                ),
                $this->makeShippingOptionClickCollect($cartItems),
                $this->makeShippingOptionClickCollect(
                    $cartItems,
                    'click-and-collect_2',
                    'Address click and collect 2'
                ),
                $this->makeShippingOptionClickCollect(
                    $cartItems,
                    'click-and-collect_3',
                    'Address click and collect 3'
                ),
            ]);

        yield "Scenario 5 : With only on demand shipping" => [
            new ShowShippingRequest(cartId: 6, companyId: 2, userId: 6),
            new ShowShippingResponse($shippingAddress, [$shippingGroup])
        ];

        $cartItems = [$this->cartItem(1), $this->cartItem(2)];
        $shippingGroup = (new ShippingGroup())
            ->setMerchantId(2)
            ->setOptions([
                $this->makeShippingOption($cartItems, name: 'Warehouse_1_stock_cheapest', customDelivery: true),
                $this->makeShippingOption(
                    $cartItems,
                    name: 'Warehouse_2_stock',
                    deliveryCount: 22,
                    mode: "fastest",
                    amountWithoutVat: 30,
                    amountWithVat: 36,
                    method: 'Warehouse_2',
                    vatAmount: 6,
                    customDelivery: true
                ),
                $this->makeShippingOptionClickCollect($cartItems),
                $this->makeShippingOptionClickCollect(
                    $cartItems,
                    'click-and-collect_2',
                    'Address click and collect 2'
                ),
                $this->makeShippingOptionClickCollect(
                    $cartItems,
                    'click-and-collect_3',
                    'Address click and collect 3'
                ),
            ]);

        yield "Scenario 6 : Suggestion shipping with same amount" => [
            new ShowShippingRequest(cartId: 8, companyId: 2, userId: 8),
            new ShowShippingResponse($shippingAddress, [$shippingGroup])
        ];

        yield "Scenario 7 : Cart does not exist" => [
            new ShowShippingRequest(cartId: 0, companyId: 2, userId: 0),
            new ShowShippingResponse(message: 'shipping.cart.not_found')
        ];

        $shippingDetail = (new ShippingDetail())
            ->setMerchantId(2)
            ->setMerchantShipping(20)
            ->setVatOnShipping(10)
            ->setMerchantShippingVatIncluded(30)
            ->setVatRateOnShipping(20);
        $shippingGroup = new ShippingGroup();
        $shippingGroup->setMerchantId($shippingDetail->getMerchantId());
        $cartItems = [$this->cartItem(2)];
        $option = (new ShippingOption())
            ->setMode(ShippingMode::create('cheapest'))
            ->setAmountWithoutVat($shippingDetail->getMerchantShipping())
            ->setAmountWithVat($shippingDetail->getMerchantShippingVatIncluded())
            ->setVatAmount($shippingDetail->getVatOnShipping())
            ->setDeliveryCount(1)
            ->setExpectedDeliveryDate(DateUtils::getExpectedDeliveryDate(48))
            ->setCarrierId(-1)
            ->setVatRate($shippingDetail->getVatRateOnShipping())
        ;
        $shippingGroup->addOption($option);


        yield "Scenario 8 : Case quote shipping is already defined" => [
            new ShowShippingRequest(cartId: 20, companyId: 2, userId: 20),
            new ShowShippingResponse($shippingAddress, [$shippingGroup])
        ];

        $cartItems = [$this->cartItem(231), $this->cartItem(232)];
        $shippingGroup = (new ShippingGroup())
            ->setMerchantId(23)
            ->setOptions([
                $this->makeShippingOptionClickCollect(
                    $cartItems
                ),
                $this->makeShippingOptionClickCollect(
                    $cartItems,
                    'click-and-collect_2',
                    'Address click and collect 2',
                    OfferAvailabilityEnum::ON_ORDER,
                    deliveryCount: 72,
                    amountWithoutVat: 40,
                    amountWithVat: 48,
                    vatAmount: 8,
                    insufficientStock: [
                        (new CCPointOffer())
                            ->setOfferId(231)
                            ->setName("title")
                            ->setSku("sku")
                            ->setWantedQty(100)
                            ->setQuantity(0)
                            ->setResplenishDelay(24),
                        (new CCPointOffer())
                            ->setOfferId(232)
                            ->setName("title")
                            ->setSku("sku")
                            ->setWantedQty(100)
                            ->setQuantity(0)
                            ->setResplenishDelay(24)
                    ]
                ),
            ]);

        // Offre #1 dispo en C&C_1 et C&C_2
        // Offre #2 en commande C&C1 et en commande en C&C_2
        // Retourne C&C_1_STOCK et C&C_2_DEMAND
        yield "Scenario 9 : C&C_1_DEMAND et C&C_2_DEMAND" => [
            new ShowShippingRequest(cartId: 23, companyId: 2, userId: 23),
            new ShowShippingResponse($shippingAddress, [$shippingGroup])
        ];

        $cartItems = [$this->cartItem(241), $this->cartItem(242)];
        $shippingGroup = (new ShippingGroup())
            ->setMerchantId(24)
            ->setOptions([
                $this->makeShippingOptionClickCollect(
                    $cartItems,
                    deliveryCount: 72 + 48,
                    amountWithoutVat: 40,
                    amountWithVat: 48,
                    vatAmount: 8,
                    insufficientStock: [
                        (new CCPointOffer())
                            ->setOfferId(242)
                            ->setName("title")
                            ->setSku("sku")
                            ->setWantedQty(100)
                            ->setQuantity(0)
                            ->setResplenishDelay(48)
                    ]
                ),
                $this->makeShippingOptionClickCollect(
                    $cartItems,
                    'click-and-collect_2',
                    'Address click and collect 2',
                    OfferAvailabilityEnum::ON_ORDER,
                    deliveryCount: 72 + 48,
                    amountWithoutVat: 40,
                    amountWithVat: 48,
                    vatAmount: 8,
                    insufficientStock: [
                        (new CCPointOffer())
                            ->setOfferId(242)
                            ->setName("title")
                            ->setSku("sku")
                            ->setWantedQty(100)
                            ->setQuantity(0)
                            ->setResplenishDelay(48)
                    ]
                ),

            ]);

        // Offre #1 dispo en C&C_1 et en commande en C&C_2
        // Offre #2 dispo en C&C1 et en commande en C&C_2
        // Retourne C&C_1_DEMAND et C&C_2_DEMAND + delay resplenish
        yield "Scenario 10 : C&C_1_STOCK et C&C_2_DEMAND" => [
            new ShowShippingRequest(cartId: 24, companyId: 2, userId: 24),
            new ShowShippingResponse($shippingAddress, [$shippingGroup])
        ];
    }

    public function testExceptionIfOfferDoesNotWarehouse(): void
    {
        $this->expectException(OfferDoesNotWarehouseException::class);
        $this->expectExceptionMessage('The offer id 6 does not warehouse available.');

        $this->useCase->execute(new ShowShippingRequest(cartId: 7, companyId: 2, userId: 7), $this->presenter);
    }

    private function makeCompanyAddress(string $type = 'billing'): Address
    {
        return (new Address(
            $type,
            'Billing address company',
            'test address',
            null,
            '75000',
            'Paris',
            1,
            2,
            "",
            "france"
        ))
            ->setId(1)
            ->setDefault($type === 'shipping')
            ->setCountryName('France');
    }

    private function makeTaxRateService(): TaxRateServiceInterface
    {
        $prophecy = new Prophet();
        /** @var TaxRateServiceInterface|ObjectProphecy $cartRepository */
        $taxRateService = $prophecy->prophesize(TaxRateServiceInterface::class);
        $taxRateService->getTaxRateByVatRegion(
            Argument::type("string"),
            Argument::type("string"),
            Argument::type("string"),
            Argument::type("int")
        )->will(function (array $args) {
            return 20.0;
        });
        return $taxRateService->reveal();
    }

    private function makeMatchShippingService(): MatchShippingService
    {
        $offerRepository = new class ($this->scenarioData) implements OfferRepositoryInterface {

            public function __construct(private array $scenarioData)
            {
            }

            public function find(int $offerId, ?int $variationId = null, bool $withFilter = false): ?Offer
            {
                return $this->scenarioData[$offerId]['offer'] ?? null;
            }

            public function findFilteredOffer(string $filters): array
            {
                return [];
            }

            public function findByIds(array $offersId, int $limit = Offer::DEFAULT_LIMIT): array
            {
                return [];
            }

            public function searchOffersByIds(array $offersId, int $limit = Offer::DEFAULT_LIMIT): array
            {
                return [];
            }

            public function findDestockingOffers(int $limit): array
            {
                return [];
            }

            public function findOffers(
                ?string $query,
                array $filterQuery,
                int $hitsPerPage,
                int $page,
                ?string $sortField,
                ?string $sortDir,
                string $companyVat = 'id_user',
                string $locale = 'FR'
            ): SearchResult {
                return new SearchResult();
            }

            public function updateCatPrice(string $companyVat, int $merchantDistantId, string $label): bool
            {
                return true;
            }

            public function deleteCatPrice(int $merchantDistantId, string $companyVat): bool
            {
                return true;
            }

            public function updateSpecificPrice(
                string $companyVat,
                string $sku,
                float $specificPrice,
                int $merchantDistantId
            ): bool {
                return true;
            }

            public function deleteSpecificPrice(string $sku, string $companyVat, int $merchantDistantId): bool
            {
                return true;
            }

            public function findSuggestions(
                ?string $query,
                array $filterQuery,
                string $locale = 'FR'
            ): SuggestionSearchResult {
                return new SuggestionSearchResult();
            }

            public function findComplementaryOffers(array $offerSkus, int $merchantId): array
            {
                return [];
            }

            public function fetchOfferById(int $offerId, ?int $variationId = null, ?string $CompanyVat = null): ?Offer
            {
                return null;
            }

            public function findMerchants(): MerchantSearchResult
            {
                return new MerchantSearchResult();
            }

            public function findSimplifiedOffer(int $offerId): ?Offer
            {
                return null;
            }

            public function scrollAllActiveOffer(): array
            {
                return [];
            }

            public function updateMerchantStatus(int $merchantDistantId, bool $status): bool
            {
                return false;
            }

            /**
             * @param OfferIdent[] $offerIdents
             * @return OfferCollection
             */
            public function findMultipleOffers(array $offerIdents): OfferCollection
            {
                $offers = new OfferCollection();
                foreach ($offerIdents as $offerIdent) {
                    $offers->addOffer($this->scenarioData[$offerIdent->offerId]['offer'] ?? null);
                }
                return $offers;
            }

            public function updateMerchantFromWebhook(
                int $merchantDistantId,
                string $merchantName,
                string $merchantDescription,
                string $merchantSlug
            ): array {
                return [];
            }

            public function findOfferBySkuAndMerchantForFront(string $sku, int $merchantDistantId): ?Offer
            {
                return null;
            }
        };
        $this->customAttributes->singlePackage()->willReturn('single_package');
        $this->customAttributes->getReplenishmentLeadTime()->willReturn('PRDD20_replenishment_lead_time');

        $prophecy = new Prophet();
        /** @var WareHouseServiceInterface|ObjectProphecy $wareHouseService */
        $wareHouseService = $prophecy->prophesize(WareHouseServiceInterface::class);
        $wareHouseService->getWareHouseCountry(Argument::type("string"), Argument::type("int"))
            ->will(function (array $args) {
                return (new Country())->setVatZoneCode("france")->setCode('CODE');
            });
        return new MatchShippingService(
            $this->customAttributes->reveal(),
            $wareHouseService->reveal()
        );
    }

    private function makeOffer(
        int $offerId,
        int $cartId,
        array $warehouses = ['Warehouse_1'],
        bool $onDemand = false,
        bool $availability = true,
        int $stock = 0,
        int $merchantId = 1,
        OfferAvailabilityEnum $availabilityTag = OfferAvailabilityEnum::IN_STOCK
    ): Offer {
        /** @var string[][] $agencies */
        $agencies = $this->agenciesFixtures()[$cartId];
        return (new Offer())
            ->setId($offerId)
            ->setSku("sku")
            ->setTitle("title")
            ->setWarehouses($warehouses)
            ->setAgencies($agencies)
            ->setAvailability($availability)
            ->setAvailabilityTag($availabilityTag)
            ->setMerchant(
                (new Merchant())
                    ->setId($merchantId)
                    ->setName(sprintf('Merchant+%d', $merchantId))
            )
            ->setOnDemand($onDemand)
            ->setStock($stock);
    }

    private function agenciesFixtures(): array
    {
        //BY cartId from makeOffer() param
        return [
            1 => [
                [
                    'name' => 'click-and-collect_1',
                    'quantity' => 400,
                    'address' => 'Address click and collect 1',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::IN_STOCK,
                    'country_name' => 'france'
                ],
                [
                    'name' => 'click-and-collect_3',
                    'quantity' => 250,
                    'address' => 'Address click and collect 3',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::IN_STOCK,
                    'country_name' => 'france'
                ]
            ],
            2 => [
                [
                    'name' => 'click-and-collect_1',
                    'quantity' => 400,
                    'address' => 'Address click and collect 1',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::IN_STOCK,
                    'country_name' => 'france'

                ],
                [
                    'name' => 'click-and-collect_2',
                    'quantity' => 500,
                    'address' => 'Address click and collect 2',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::IN_STOCK,
                    'country_name' => 'france'
                ],
                [
                    'name' => 'click-and-collect_3',
                    'quantity' => 250,
                    'address' => 'Address click and collect 3',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::IN_STOCK,
                    'country_name' => 'france'
                ]
            ],
            5 => [
                [
                    'name' => 'click-and-collect_1',
                    'quantity' => 0,
                    'address' => 'Address click and collect 1',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::IN_STOCK,
                    'country_name' => 'france'
                ],
            ],
            23 => [
                [
                    'name' => 'click-and-collect_1',
                    'quantity' => 400,
                    'address' => 'Address click and collect 1',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::IN_STOCK,
                    'country_name' => 'france'
                ],
                [
                    'name' => 'click-and-collect_2',
                    'quantity' => 0,
                    'address' => 'Address click and collect 2',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::ON_ORDER,
                    'country_name' => 'france'
                ]
            ],
            24 => []
        ];
    }

    private function buildInitialFixtures(): void
    {
        //by offer ID
        $this->scenarioData[1] = [
            'offer' => $this->makeOffer(1, 1, stock: 100)->setAttributes(
                ['single_package' => 'yes', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];

        $this->scenarioData[2] = [
            'offer' => $this->makeOffer(2, 2)->setAttributes(
                ['single_package' => 'yes', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];

        $this->scenarioData[3] = [
            'offer' => $this->makeOffer(3, 2, ['Warehouse_1', 'Warehouse_2'])->setAttributes(
                ['single_package' => 'yes', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];

        $this->scenarioData[4] = [
            'offer' => $this->makeOffer(4, 2, ['Warehouse_3'], onDemand: true, availability: false)
                ->setAttributes(['single_package' => 'yes', 'PRDD20_replenishment_lead_time' => 1]),
        ];

        $this->scenarioData[5] = [
            'offer' => $this->makeOffer(5, 2)->setAttributes(
                ['single_package' => 'yes', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];

        $this->scenarioData[6] = [
            'offer' => $this->makeOffer(6, 2, [])->setAttributes(
                ['single_package' => 'yes', 'PRDD20_replenishment_lead_time' => 1]
            ), // Offer without warehouse
        ];

        $this->scenarioData[7] = [
            'offer' => $this->makeOffer(7, 2, ['Warehouse_1', 'Warehouse_2'])->setAttributes(
                ['single_package' => 'yes', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];

        $this->scenarioData[8] = [
            'offer' => $this->makeOffer(8, 2, ['Warehouse_1', 'Warehouse_2'])->setAttributes(
                ['single_package' => 'no', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];
        $this->scenarioData[9] = [
            'offer' => $this->makeOffer(9, 2)->setAttributes(
                ['single_package' => 'yes', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];
        $this->scenarioData[10] = [
            'offer' => $this->makeOffer(10, 2, ['Warehouse_2'])->setAttributes(
                ['single_package' => 'yes', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];
        $this->scenarioData[11] = [
            'offer' => $this->makeOffer(11, 2)->setAttributes(
                ['single_package' => 'yes', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];
        $this->scenarioData[12] = [
            'offer' => $this->makeOffer(12, 2, ['Warehouse_2', 'Warehouse_3'])->setAttributes(
                ['single_package' => 'yes', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];
        $this->scenarioData[13] = [
            'offer' => $this->makeOffer(13, 2, ['Warehouse_2'])->setAttributes(
                ['single_package' => 'yes', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];
        $this->scenarioData[14] = [
            'offer' => $this->makeOffer(14, 2, ['Warehouse_3'])->setAttributes(
                ['single_package' => 'yes', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];
        $this->scenarioData[15] = [
            'offer' => $this->makeOffer(15, 2)->setAttributes(
                ['single_package' => 'no', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];
        $this->scenarioData[16] = [
            'offer' => $this->makeOffer(16, 2, ['Warehouse_2'])->setAttributes(
                ['single_package' => 'no', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];

        $this->scenarioData[231] = [
            'offer' => $this->makeOffer(231, 23)->setAttributes(
                ['single_package' => 'no', 'PRDD20_replenishment_lead_time' => 1]
            ),
        ];

        $offer = $this->makeOffer(232, 23, ['Warehouse_2'])->setAttributes(
            ['single_package' => 'no', 'PRDD20_replenishment_lead_time' => 1]
        );
        $offer->setAgencies(
            [
                [
                    'name' => 'click-and-collect_1',
                    'quantity' => 200,
                    'address' => 'Address click and collect 1',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::IN_STOCK,
                    'country_name' => 'france'
                ],
                [
                    'name' => 'click-and-collect_2',
                    'quantity' => 0,
                    'address' => 'Address click and collect 2',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::ON_ORDER,
                    'country_name' => 'france'
                ]
            ]
        );
        $this->scenarioData[232] = [
            'offer' => $offer
        ];


        $offer241 = $this->makeOffer(241, 23)->setAttributes(
            ['single_package' => 'no', 'PRDD20_replenishment_lead_time' => 1]
        );
        $offer241->setAgencies(
            [
                [
                    'name' => 'click-and-collect_1',
                    'quantity' => 100,
                    'address' => 'Address click and collect 1',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::IN_STOCK,
                    'country_name' => 'france'
                ],
                [
                    'name' => 'click-and-collect_2',
                    'quantity' => 100,
                    'address' => 'Address click and collect 2',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::ON_ORDER,
                    'country_name' => 'france'
                ]
            ]
        );
        $this->scenarioData[241] = [
            'offer' => $offer241
        ];

        $offer242 = $this->makeOffer(242, 24)->setAttributes(
            ['single_package' => 'no', 'PRDD20_replenishment_lead_time' => 2]
        );
        $offer242->setAgencies(
            [
                [
                    'name' => 'click-and-collect_1',
                    'quantity' => 0,
                    'address' => 'Address click and collect 1',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::IN_STOCK,
                    'country_name' => 'france'
                ],
                [
                    'name' => 'click-and-collect_2',
                    'quantity' => 0,
                    'address' => 'Address click and collect 2',
                    'vat_region' => 'france',
                    'availability' => OfferAvailabilityEnum::ON_ORDER,
                    'country_name' => 'france'
                ]
            ]
        );
        $this->scenarioData[242] = [
            'offer' => $offer242
        ];
    }

    private function makeShippingOption(
        array $cartItems = [],
        string $name = 'Warehouse_1_stock',
        int $deliveryCount = 48,
        string $mode = 'cheapest',
        float $amountWithoutVat = 10,
        float $amountWithVat = 12,
        string $method = 'Warehouse_1',
        float $vatAmount = 2,
        bool $customDelivery = false
    ): ShippingOption {
        $mode = $mode === 'cheapest' ? new Cheapest() : new Fastest();
        $date = new \DateTime("today");
        $date->add(new \DateInterval('PT' . $deliveryCount . 'H'));
        if ($date->format('N') == 7) {
            $date->add(new \DateInterval('PT24H'));
        }

        return (new ShippingOption())->setName($name)
            ->setDeliveryCount($deliveryCount)
            ->setExpectedDeliveryDate(DateTimeImmutable::createFromMutable($date))
            ->setMode($mode)
            ->setAmountWithVat($amountWithVat)
            ->setAmountWithoutVat($amountWithoutVat)
            ->setVatAmount($vatAmount)
            ->setVatRate(20.00)
            ->setMethod($method)
            ->setCarrierId(2)
            ->setVatZone("france")
            ->setCartItems($cartItems)
            ->setCustomDelivery($customDelivery)
            ->setDeliveryCountryName("CODE")
            ;
    }

    private function makeSinglePackageOption(
        array $cartItems,
        string $name = 'Warehouse_1_monocolis',
        string $method = 'Warehouse_2',
    ): ShippingOption {
        $date = new \DateTime("today");
        $date->add(new \DateInterval('PT48H'));
        if ($date->format('N') == 7) {
            $date->add(new \DateInterval('PT24H'));
        }

        return (new ShippingOption())->setName($name)
            ->setDeliveryCount(48)
            ->setExpectedDeliveryDate(DateTimeImmutable::createFromMutable($date))
            ->setMode(new Cheapest())
            ->setAmountWithVat(12)
            ->setAmountWithoutVat(10)
            ->setVatAmount(2)
            ->setVatRate(20.00)
            ->setMethod($method)
            ->setCarrierId(2)
            ->setVatZone("france")
            ->setCartItems($cartItems)
            ->setDeliveryCountryName("CODE");
    }

    private function makeShippingOptionClickCollect(
        array $cartItems,
        string $name = 'click-and-collect_1',
        string $address = 'Address click and collect 1',
        OfferAvailabilityEnum $availabilityTag = OfferAvailabilityEnum::IN_STOCK,
        int $deliveryCount = 2,
        float $amountWithoutVat = 29.0,
        float $amountWithVat = 34.8,
        float $vatAmount = 5.8,
        array $insufficientStock = [],
        string $deliveryCountryName = "france"
    ): ShippingOption {
        $date = new \DateTime("today");
        return (new ShippingOption())->setName($name)
            ->setDeliveryCount($deliveryCount)
            ->setExpectedDeliveryDate(DateUtils::getExpectedDeliveryDate($deliveryCount))
            ->setAddress($address)
            ->setMode(new PickUpInStore())
            ->setAmountWithoutVat($amountWithoutVat)
            ->setVatRate(20.0)
            ->setVatAmount($vatAmount)
            ->setAmountWithVat($amountWithVat)
            ->setCartItems($cartItems)
            ->setVatZone("france")
            ->setAvailabilityTag($availabilityTag->name)
            ->setInsufficientStock($insufficientStock)
            ->setDeliveryCountryName($deliveryCountryName);
    }

    private function cartItem(int $id): CartItemLight
    {
        return (new CartItemLight($id))
            ->setUnitPrice(1)
            ->setTaxRate(20.0)
            ->setUnitVat(1.2)
            ->setVatGroupName("standard");
    }
}
