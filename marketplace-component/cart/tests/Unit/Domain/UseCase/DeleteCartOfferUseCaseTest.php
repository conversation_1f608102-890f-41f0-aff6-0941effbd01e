<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Tests\Domain\UseCase;

use Generator;
use Marketplace\Component\Cart\Domain\Model\Cart;
use Marketplace\Component\Cart\Domain\Model\CartItem;
use Marketplace\Component\Cart\Domain\Port\Repository\CartRepositoryInterface;
use Marketplace\Component\Cart\Domain\UseCase\DeleteCartOffer\DeleteCartOfferUseCase;
use Marketplace\Component\Cart\Domain\UseCase\DeleteCartOffer\DTO\DeleteCartOfferRequest;
use Marketplace\Component\Cart\Domain\UseCase\DeleteCartOffer\DTO\DeleteCartOfferResponse;
use Marketplace\Component\Cart\Domain\Presenter\DeleteCartOfferPresenterInterface;
use Marketplace\Component\Cart\Tests\Unit\Fake\CartItemRepositoryFake;
use Marketplace\Component\Offer\Domain\Model\Offer;
use Marketplace\Component\Quote\Domain\Port\Repository\QuoteRepositoryInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;

/**
 * @uses \Marketplace\Component\Cart\Domain\Model\Cart
 * @uses \Marketplace\Component\Cart\Domain\Model\CartItem
 * @uses \Marketplace\Component\Cart\Domain\UseCase\DeleteCartOffer\DTO\DeleteCartOfferResponse
 * @uses \Marketplace\Component\Cart\Domain\UseCase\DeleteCartOffer\DeleteCartOfferUseCase
 */
final class DeleteCartOfferUseCaseTest extends TestCase
{
    private DeleteCartOfferPresenterInterface $presenter;

    private DeleteCartOfferUseCase $useCase;

    private array $scenarioData = [];

    protected function setUp(): void
    {
        parent::setUp();

        $this->buildInitialFixtures();

        $this->presenter = new class implements DeleteCartOfferPresenterInterface {
            public DeleteCartOfferResponse $response;
            public function present(DeleteCartOfferResponse $response): void
            {
                $this->response = $response;
            }
        };


        $this->useCase = new DeleteCartOfferUseCase(
            new CartItemRepositoryFake($this->scenarioData),
            $this->createMock(CartRepositoryInterface::class)
        );
    }

    /**
     * @covers \Marketplace\Component\Cart\Domain\UseCase\DeleteCartOffer\DeleteCartOfferUseCase::execute
     * @dataProvider showOfferProvider
     * @param DeleteCartOfferRequest $request
     * @param DeleteCartOfferResponse $expected
     */
    public function testExecute(DeleteCartOfferRequest $request, DeleteCartOfferResponse $expected): void
    {
        $this->useCase->execute($request, $this->presenter);

        $this->assertEquals($expected, $this->presenter->response);
    }

    public function showOfferProvider(): Generator
    {
        // Successful
        yield [new DeleteCartOfferRequest(1, 1, 1), new DeleteCartOfferResponse(true, 1)];

        // cartItem is not found
        yield [new DeleteCartOfferRequest(0, 1, 1), new DeleteCartOfferResponse(false, 2)];
    }

    private function buildInitialFixtures(): void
    {
        $this->scenarioData[1] = [
            'cart' =>
                (new Cart(1))
                    ->setCartItems([
                        (new CartItem(id: 1, offerId: 2, quantity: 4, cartId: 1))
                            ->setOffer((new Offer())->setId(2)->setBatchSize(1)->setMoq(1)),
                        (new CartItem(id: 2, offerId: 3, quantity: 2, cartId: 1))
                            ->setOffer((new Offer())->setId(3)->setBatchSize(1)->setMoq(1)),
                    ]),
            'cartItem' =>
                (new CartItem(id: 1, offerId: 2, quantity: 4, cartId: 1))
                    ->setOffer((new Offer())->setId(2)->setBatchSize(1)->setMoq(1))
        ];
    }
}
