<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Tests\Unit\Infrastructure\EventSubscriber;

use DateTimeImmutable;
use Exception;
use Marketplace\Component\Cart\Domain\Port\Repository\CartRepositoryInterface;
use Marketplace\Component\Cart\Infrastructure\EventSubscriber\CartToUserSubscriber;
use Marketplace\Component\Order\Infrastructure\DTO\CartToUser;
use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;

final class CartToUserSubscriberTest extends TestCase
{
    private CartToUserSubscriber $eventSubscriber;

    protected function setUp(): void
    {
        parent::setUp();

        $prophecy = new Prophet();

        /** @var CartRepositoryInterface|ObjectProphecy $cartRepository */
        $cartRepository = $prophecy->prophesize(CartRepositoryInterface::class);
        $cartRepository->getUserIdByCartId(Argument::type('int'))->will(
            function ($args) {
                $cartId = $args[0];
                if ($cartId > 0) {
                    return $cartId;
                }
                return null;
            }
        );
        /** @var UserRepositoryInterface|ObjectProphecy $userRepository */
        $userRepository = $prophecy->prophesize(UserRepositoryInterface::class);
        $userRepository->findById(Argument::type('int'))->will(
            function ($args) {
                $userId = $args[0];
                if ($userId === 1) {
                    return (new User())
                        ->setId(1)
                        ->setLastname("userLastname")
                        ->setCreatedAt(new DateTimeImmutable('2012-12-12'))
                        ->setEmail("<EMAIL>")
                        ->setCivility('Mr');
                }
                return null;
            }
        );

        $this->eventSubscriber = new CartToUserSubscriber(
            $cartRepository->reveal(),
            $userRepository->reveal()
        );
    }

    public function testSubscriberEvent(): void
    {
        $this->assertArrayHasKey(CartToUser::class, CartToUserSubscriber::getSubscribedEvents());
    }

    public function testCartNoUser(): void
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("User not found, the email can't be send");
        $cartToUser = new CartToUser(0);
        $this->eventSubscriber->onGetUserIdByCartId($cartToUser);
    }

    public function testUserNotFound(): void
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("User not found, the email can't be send");
        $cartToUser = new CartToUser(2);
        $this->eventSubscriber->onGetUserIdByCartId($cartToUser);
    }

    public function testUserFound(): void
    {
        $cartToUser = new CartToUser(1);
        $this->eventSubscriber->onGetUserIdByCartId($cartToUser);
        $user = (new User())
            ->setId(1)
            ->setLastname("userLastname")
            ->setEmail("<EMAIL>")
            ->setCreatedAt(new DateTimeImmutable('2012-12-12'))
            ->setCivility('Mr');
        $this->assertEquals($user, $cartToUser->user);
    }
}
