<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Tests\Unit\Infrastructure\EventSubscriber;

use Exception;
use Marketplace\Component\Cart\Domain\Model\Cart;
use Marketplace\Component\Cart\Domain\Port\Repository\CartRepositoryInterface;
use Marketplace\Component\Cart\Infrastructure\EventSubscriber\ShippingCommentFromCartSubscriber;
use Marketplace\Component\Order\Infrastructure\DTO\ShippingCommentFromCart;
use PHPUnit\Framework\TestCase;

final class ShippingCommentFromCartSubscriberTest extends TestCase
{
    private ShippingCommentFromCartSubscriber $eventSubscriber;

    protected function setUp(): void
    {
        parent::setUp();
        $cartRepository = $this->createMock(CartRepositoryInterface::class);
        $cartRepository->method('findCartById')->willReturnCallback(
            function (int $cartId) {
                if ($cartId === 1) {
                    return (new Cart(1))
                        ->setId($cartId)
                        ->setShippingComment("ShippingComment")
                    ;
                }
                if ($cartId === 2) {
                    return (new Cart(1))->setId($cartId);
                }
                return null;
            }
        );

        $this->eventSubscriber = new ShippingCommentFromCartSubscriber($cartRepository);
    }

    public function testSubscriberEvent(): void
    {
        $this->assertArrayHasKey(
            key: ShippingCommentFromCart::class,
            array: ShippingCommentFromCartSubscriber::getSubscribedEvents()
        );
    }

    /**
     * @throws Exception
     */
    public function testNoCart(): void
    {
        $shippingCommentFromCart = new ShippingCommentFromCart(0);
        $this->eventSubscriber->getShippingCommentFromCart($shippingCommentFromCart);
        $this->assertNull($shippingCommentFromCart->shippingComment);
    }

    /**
     * @throws Exception
     */
    public function testCartFound(): void
    {
        $shippingCommentFromCart = new ShippingCommentFromCart(1);
        $this->eventSubscriber->getShippingCommentFromCart($shippingCommentFromCart);
        $this->assertEquals("ShippingComment", $shippingCommentFromCart->shippingComment);
    }

    /**
     * @throws Exception
     */
    public function testCartFoundWithoutShippingComment(): void
    {
        $shippingCommentFromCart = new ShippingCommentFromCart(2);
        $this->eventSubscriber->getShippingCommentFromCart($shippingCommentFromCart);
        $this->assertNull($shippingCommentFromCart->shippingComment);
    }
}
