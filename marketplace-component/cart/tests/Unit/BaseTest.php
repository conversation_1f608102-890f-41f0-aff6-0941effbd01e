<?php

declare(strict_types=1);

namespace Marketplace\Component\Cart\Tests\Unit;

use Marketplace\Component\Cart\Domain\Model\Cart;
use Marketplace\Component\Cart\Domain\Port\Repository\CartRepositoryInterface;
use Marketplace\Component\Order\Domain\Model\Order;
use Marketplace\Component\Order\Domain\Port\Repository\OrderRepositoryInterface;
use Marketplace\Component\Payment\Domain\Model\Method\BankTransferMethod;
use Marketplace\Component\Payment\Domain\Model\PaymentAction;
use PHPUnit\Framework\TestCase;

abstract class BaseTest extends TestCase
{
    protected array $scenarioData = [];

    protected function setUp(): void
    {
        parent::setUp();

        $this->makeScenarioData();
    }

    protected function makeScenarioData(): void
    {
        // TODO Add scenario data
        // $this->scenarioData[1] = [...]
    }

    protected function makeCartRepository(): CartRepositoryInterface
    {
        return new class ($this->scenarioData) implements CartRepositoryInterface {

            public function __construct(private array $scenarioData)
            {
            }

            public function findCurrentCartForUser(int $userId, bool $withoutCartItem = false): ?Cart
            {
                return $this->scenarioData[$userId]['cart'] ?? null;
            }

            public function findCartForUser(int $cartId, int $userId): ?Cart
            {
                return $this->scenarioData[$userId]['cart'] ?? null;
            }

            public function create(int $userId, string $region, string $currency): Cart
            {
                return new Cart($userId);
            }

            public function update(Cart $cart): Cart
            {
                return $cart;
            }

            public function cleanCurrentCart(int $userId, ?bool $preventQuote = false): void
            {
            }

            public function checkQuoteInCart(int $cartId): bool
            {
            }

            public function removeQuoteTag(int $cartId): void
            {
            }

            public function findCartRegion(int $cartId): ?string
            {
            }

            public function getUserIdByCartId(int $cartId): ?int
            {
            }

            public function getOrderFromQuoteId(int $quoteId): ?Order
            {
            }

            public function updateCartShipping(int $cartId, int $shippingId): void
            {
                // TODO: Implement updateCartShipping() method.
            }

            public function findCartById(int $cartId): ?Cart
            {
                // TODO: Implement findCartById() method.
            }

            public function findCart(int $cartId): ?Cart
            {
                // TODO: Implement findCart() method.
            }
        };
    }

    protected function makeOrderRepository(): OrderRepositoryInterface
    {
        return new class ($this->scenarioData) implements OrderRepositoryInterface {

            public function __construct(private array $scenarioData)
            {
            }

            public function findByDistantId(int $orderDistantId): ?Order
            {
                return null;
            }

            public function getTotalOrderByCompanyId(int $companyId): float
            {
                return 0.0;
            }

            public function findById(int $orderId): ?Order
            {
                return $this->scenarioData[$orderId]['order'] ?? null;
            }

            public function findByCartId(int $cartId): ?Order
            {
                return $this->scenarioData[$cartId]['order'] ?? null;
            }

            public function findOrders(
                ?int $companyId,
                string $filteredNumber,
                string $filteredStatus,
                string $filteredCreationDateStart,
                string $filteredCreationDateEnd,
                string $tab,
            ): array {
                return [];
            }

            public function createOrder(Order $order, PaymentAction $paymentAction): ?Order
            {
                $bankTransfer = new BankTransferMethod();
                return new Order(1, $bankTransfer, 'cart-1');
            }

            public function save(Order $order, bool $saveMerchantOrder = true)
            {
            }

            public function findInProgressOrders(int $companyId, string $paymentType): array
            {
                return [];
            }

            public function updateDispatchedStatus(Order $order): Order
            {
            }

            public function findTermPaymentOrderNotAuthorized(int $createdSinceSeconds): array
            {
                return [];
            }

            public function findExpiredBankTransferOrder(int $createdSinceDays): array
            {
                return [];
            }

            public function findCompanyWithOrder(): array
            {
                return [];
            }

            public function findProcessedOrdersToDispatched(): array
            {
                return [];
            }
        };
    }
}
