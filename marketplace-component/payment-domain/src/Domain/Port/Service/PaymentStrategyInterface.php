<?php

namespace Marketplace\Component\Payment\Domain\Port\Service;

use Marketplace\Component\Payment\Domain\Model\PaymentAction;

interface PaymentStrategyInterface
{
    public function pay(PaymentAction $paymentAction): void;

    public function authorize(PaymentAction $paymentAction): void;

    public function canPay(PaymentAction $paymentAction): bool;

    public function canAuthorize(PaymentAction $paymentAction): bool;
}
