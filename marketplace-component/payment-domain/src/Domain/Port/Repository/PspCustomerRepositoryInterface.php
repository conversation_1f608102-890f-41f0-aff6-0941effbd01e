<?php

namespace Marketplace\Component\Payment\Domain\Port\Repository;

use Marketplace\Component\Payment\Domain\Model\PspCustomer;

interface PspCustomerRepositoryInterface
{
    public function findByCompany(int $companyId): ?PspCustomer;

    /**
     * Find psp customer by distant company id,
     * the distant company id is the company izberg id when using izberg
     *
     * @param int $distantCompanyId
     * @return PspCustomer|null
     */
    public function findByDistantCompany(int $distantCompanyId): ?PspCustomer;

    public function findById(string $id): ?PspCustomer;

    public function update(PspCustomer $pspCustomer): PspCustomer;

    public function findAllPsp(): array;
}
