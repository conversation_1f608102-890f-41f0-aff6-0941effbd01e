<?php

declare(strict_types=1);

namespace Marketplace\Component\Payment\Domain\UseCase\PayMerchantOrder\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Model\UseCaseRequestInterface;

final class PayMerchantOrderRequest implements UseCaseRequestInterface
{
    public function __construct(public readonly int $distantMerchantOrderId)
    {
    }

    public function infoToLog(): array
    {
        return get_object_vars($this);
    }
}
