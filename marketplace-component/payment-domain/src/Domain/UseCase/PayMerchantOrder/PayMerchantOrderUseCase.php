<?php

declare(strict_types=1);

namespace Marketplace\Component\Payment\Domain\UseCase\PayMerchantOrder;

use Marketplace\Component\CleanArchiCore\Domain\UseCase\AbstractUseCase;
use Marketplace\Component\Order\Domain\Exception\MerchantOrderNotFoundException;
use Marketplace\Component\Order\Domain\Model\MerchantOrder;
use Marketplace\Component\Order\Domain\Port\Repository\MerchantOrderRepositoryInterface;
use Marketplace\Component\Payment\Domain\Exception\PayMerchantOrderException;
use Marketplace\Component\Payment\Domain\Port\Service\PayBankTransferMerchantOrderServiceInterface;
use Marketplace\Component\Payment\Domain\Port\Service\PayCreditCardMerchantOrderServiceInterface;
use Marketplace\Component\Payment\Domain\Presenter\PayMerchantOrderPresenterInterface;
use Marketplace\Component\Payment\Domain\UseCase\PayMerchantOrder\DTO\PayMerchantOrderRequest;
use Psr\Log\LoggerInterface;

final class PayMerchantOrderUseCase extends AbstractUseCase
{
    public function __construct(
        private readonly MerchantOrderRepositoryInterface $merchantOrderRepository,
        private readonly PayCreditCardMerchantOrderServiceInterface $payCreditCardMerchantOrderService,
        private readonly PayBankTransferMerchantOrderServiceInterface $payBankTransferMerchantOrderService,
        private LoggerInterface $logger
    ) {
    }

    public function execute(PayMerchantOrderRequest $request, PayMerchantOrderPresenterInterface $presenter): void
    {
        $this->logUseCaseRequest($request, __CLASS__);
        $distantMerchantOrderId = $request->distantMerchantOrderId;
        $merchantOrder = $this->merchantOrderRepository->findDistantByDistantId($distantMerchantOrderId);
        if (!$merchantOrder instanceof MerchantOrder) {
            $this->logger->error("[PAY] Can't found Merchant Order", ["merchant_order" => $distantMerchantOrderId]);
            throw new MerchantOrderNotFoundException('Cannot found merchant order to proceed to the pay merchant order. Distant merchant order id : ' . $distantMerchantOrderId);
        }

        $status = $merchantOrder->getStatus();
        if ($status === null || !($status->isConfirmed() || $status->isProcessed())) {
            $this->logger->error("[PAY] Merchant order has bad status", ["merchant_order" => $distantMerchantOrderId]);
            throw new PayMerchantOrderException('Merchant order status confirmed required. Distant merchant order id : ' . $distantMerchantOrderId);
        }

        if ($merchantOrder->isCreditCardPayment()) {
            $this->payCreditCardMerchantOrderService->pay($merchantOrder);
        }

        if ($merchantOrder->isBankTransferPayment()) {
            $this->payBankTransferMerchantOrderService->pay($merchantOrder);
        }
    }
}
