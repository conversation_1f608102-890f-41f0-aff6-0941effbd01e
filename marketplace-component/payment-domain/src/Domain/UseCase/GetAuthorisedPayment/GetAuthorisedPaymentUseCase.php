<?php

declare(strict_types=1);

namespace Marketplace\Component\Payment\Domain\UseCase\GetAuthorisedPayment;

use Marketplace\Component\CleanArchiCore\Domain\Port\Repository\RegionRepositoryInterface;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetRegionServiceInterface;
use Marketplace\Component\CleanArchiCore\Domain\UseCase\AbstractUseCase;
use Marketplace\Component\Payment\Domain\Model\Method\AuthorisedBankTransferPaymentMethod;
use Marketplace\Component\Payment\Domain\Model\Method\AuthorisedCreditCardPaymentMethod;
use Marketplace\Component\Payment\Domain\Model\Method\AuthorisedTermPaymentMethod;
use Marketplace\Component\Payment\Domain\Model\Method\BankTransferMethod;
use Marketplace\Component\Payment\Domain\Model\Method\CreditCardMethod;
use Marketplace\Component\Payment\Domain\Model\Method\TermPaymentMethod;
use Marketplace\Component\Payment\Domain\Port\Service\TermPaymentAuthorisedServiceInterface;
use Marketplace\Component\Payment\Domain\Presenter\GetAuthorisedPaymentPresenterInterface;
use Marketplace\Component\Payment\Domain\UseCase\GetAuthorisedPayment\DTO\GetAuthorisedPaymentRequest;
use Marketplace\Component\Payment\Domain\UseCase\GetAuthorisedPayment\DTO\GetAuthorisedPaymentResponse;
use Marketplace\Component\TermPayment\Domain\Port\Service\CalculateAmountServiceInterface;
use Marketplace\Component\User\Domain\Port\Repository\TermPaymentRepositoryInterface;

final class GetAuthorisedPaymentUseCase extends AbstractUseCase
{
    public function __construct(
        private readonly TermPaymentAuthorisedServiceInterface $authorisedService,
        private readonly CalculateAmountServiceInterface $calculateAmountService,
        private readonly TermPaymentRepositoryInterface $termPaymentRepository,
        private readonly GetRegionServiceInterface $getRegionService,
        private readonly RegionRepositoryInterface $regionRepository
    ) {
    }

    /**
     * @param GetAuthorisedPaymentRequest $request
     * @param GetAuthorisedPaymentPresenterInterface $presenter
     */
    public function execute(
        GetAuthorisedPaymentRequest $request,
        GetAuthorisedPaymentPresenterInterface $presenter = null
    ) {
        $this->logUseCaseRequest($request, __CLASS__);
        $companyId = $request->getCompanyId();
        $orderAmountTTC = $request->getOrderAmountTTC();
        $response = new GetAuthorisedPaymentResponse();

        $methods = [];
        $currentRegion = $this->getRegionService->getRegion();
        $authorisedPaymentMethodsForRegion = $this->regionRepository->getAuthorisedPaymentMethods($currentRegion);

        if (in_array(BankTransferMethod::TYPE, $authorisedPaymentMethodsForRegion)) {
            $methods[] = new AuthorisedBankTransferPaymentMethod();
        }

        if (in_array(CreditCardMethod::TYPE, $authorisedPaymentMethodsForRegion)) {
            $methods[] = new AuthorisedCreditCardPaymentMethod();
        }

        if (in_array(TermPaymentMethod::TYPE, $authorisedPaymentMethodsForRegion)) {
            $termPayment = new AuthorisedTermPaymentMethod();
            $methods[] = $termPayment;
            if (!$this->authorisedService->isAuthorised($companyId)) {
                $termPayment->setAuthorised(false);
                $termPayment->setMessage('payment.term_payment.refused');
            } elseif ($this->authorisedService->isTermPaymentReached($companyId, $orderAmountTTC)) {
                $termPayment->setAuthorised(false);
                $termPayment->setMessage('payment.term_payment.reached');
            } else {
                $currentAmount = $this->calculateAmountService->calculateInProgressAmount($companyId, $orderAmountTTC);
                $termPaymentCompany = $this->termPaymentRepository->getTermPaymentOfCompany($companyId);
                $threshold = $termPaymentCompany?->getThreshold();
                $termPayment->setCurrentAmount($currentAmount);
                $termPayment->setThreshold($threshold);
            }
        }

        $response->setPaymentMethods($methods);

        if ($presenter) {
            $presenter->present($response);
        } else {
            return $response;
        }
    }
}
