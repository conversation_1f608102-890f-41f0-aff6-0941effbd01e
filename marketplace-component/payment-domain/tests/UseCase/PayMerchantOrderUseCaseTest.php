<?php

namespace Marketplace\Component\Payment\Domain\Tests\UseCase;

use Marketplace\Component\Order\Domain\Enum\MerchantOrderStatusEnum;
use Marketplace\Component\Order\Domain\Exception\MerchantOrderNotFoundException;
use Marketplace\Component\Order\Domain\Model\MerchantOrder;
use Marketplace\Component\Order\Domain\Model\Order;
use Marketplace\Component\Order\Domain\Port\Repository\MerchantOrderRepositoryInterface;
use Marketplace\Component\Payment\Domain\Model\Method\BankTransferMethod;
use Marketplace\Component\Payment\Domain\Port\Service\PayBankTransferMerchantOrderServiceInterface;
use Marketplace\Component\Payment\Domain\Port\Service\PayCreditCardMerchantOrderServiceInterface;
use Marketplace\Component\Payment\Domain\Presenter\PayMerchantOrderPresenterInterface;
use Marketplace\Component\Payment\Domain\UseCase\PayMerchantOrder\DTO\PayMerchantOrderRequest;
use Marketplace\Component\Payment\Domain\UseCase\PayMerchantOrder\DTO\PayMerchantOrderResponse;
use Marketplace\Component\Payment\Domain\UseCase\PayMerchantOrder\PayMerchantOrderUseCase;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;
use Psr\Log\LoggerInterface;

class PayMerchantOrderUseCaseTest extends TestCase
{
    private PayMerchantOrderUseCase $useCase;
    private PayMerchantOrderPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();
        $this->presenter = new class implements PayMerchantOrderPresenterInterface {
            public PayMerchantOrderResponse $response;
            public function present(PayMerchantOrderResponse $response): void
            {
                $this->response = $response;
            }
        };

        $merchantOrderRepository = $this->mockMerchantOrderRepository();
        $payCreditCardMerchantOrderService  = $this->mockPayCreditCardMerchantOrderService();
            $payBankTransferMerchantOrderService = $this->mockPayBankTransferMerchantOrderService();
        $this->useCase = new PayMerchantOrderUseCase(
            $merchantOrderRepository,
            $payCreditCardMerchantOrderService,
            $payBankTransferMerchantOrderService,
            $this->createMock(LoggerInterface::class)
        );
    }

    public function testPayMerchantOrderExecuteException()
    {
        $this->expectException(MerchantOrderNotFoundException::class);
        $this->useCase->execute(new PayMerchantOrderRequest(0), $this->presenter);
    }

    private function mockMerchantOrderRepository(): MerchantOrderRepositoryInterface
    {
        $prophet = new Prophet();
        $repo = $prophet->prophesize(MerchantOrderRepositoryInterface::class);
        $repo->findByDistantId(Argument::type('int'))->will(function ($args) {
            $id = $args[0];
            if ($id == 0) {
                return null;
            }
            return (new MerchantOrder(1, 1))
                ->setStatus(MerchantOrderStatusEnum::CONFIRMED)
                ->setOrder(new Order(1, new BankTransferMethod(), 1));
        });
        return $repo->reveal();
    }

    private function mockPayCreditCardMerchantOrderService(): PayCreditCardMerchantOrderServiceInterface
    {
        $prophet = new Prophet();
        $service = $prophet->prophesize(PayCreditCardMerchantOrderServiceInterface::class);
        return $service->reveal();
    }

    private function mockPayBankTransferMerchantOrderService(): PayBankTransferMerchantOrderServiceInterface
    {
        $prophet = new Prophet();
        $service = $prophet->prophesize(PayBankTransferMerchantOrderServiceInterface::class);
        return $service->reveal();
    }
}
