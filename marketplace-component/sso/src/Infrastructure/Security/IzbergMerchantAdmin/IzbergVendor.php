<?php

declare(strict_types=1);

namespace Marketplace\Component\Sso\Infrastructure\Security\IzbergMerchantAdmin;

use League\OAuth2\Client\Provider\ResourceOwnerInterface;
use League\OAuth2\Client\Token\AccessToken;
use Marketplace\Component\Sso\Infrastructure\OAuth\OAuthModelInterface;

class IzbergVendor implements ResourceOwnerInterface, OAuthModelInterface
{
    private int $merchantId;

    private string $name;

    private string $preferedLanguage;

    private string $slug;

    private string $companyName;

    private AccessToken $bearer;

    public function getMerchantId(): int
    {
        return $this->merchantId;
    }

    public function setMerchantId(int $merchantId): IzbergVendor
    {
        $this->merchantId = $merchantId;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): IzbergVendor
    {
        $this->name = $name;
        return $this;
    }

    public function getPreferedLanguage(): string
    {
        return $this->preferedLanguage;
    }

    public function setPreferedLanguage(string $preferedLanguage): self
    {
        $this->preferedLanguage = $preferedLanguage;
        return $this;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): IzbergVendor
    {
        $this->slug = $slug;
        return $this;
    }

    public function getCompanyName(): string
    {
        return $this->companyName;
    }

    public function setCompanyName(string $companyName): self
    {
        $this->companyName = $companyName;
        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getId(): int
    {
        return $this->merchantId;
    }

    /**
     * @return AccessToken
     */
    public function getBearer(): AccessToken
    {
        return $this->bearer;
    }

    /**
     * @param AccessToken $bearer
     * @return $this
     */
    public function setBearer(AccessToken $bearer): self
    {
        $this->bearer = $bearer;
        return $this;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            'merchantId' => $this->merchantId,
            'name' => $this->name,
            'slug' => $this->slug,
            'preferedLanguage' => $this->preferedLanguage,
            'companyName' => $this->companyName,
            'bearer' => $this->bearer
        ];
    }
}
