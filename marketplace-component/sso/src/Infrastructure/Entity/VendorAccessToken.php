<?php

namespace Marketplace\Component\Sso\Infrastructure\Entity;

use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Marketplace\Component\Sso\Infrastructure\Adapter\Repository\VendorAccessTokenRepository;

#[ORM\Entity(repositoryClass: VendorAccessTokenRepository::class)]
class VendorAccessToken
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private int $id;

    #[ORM\OneToOne(targetEntity: Vendor::class, cascade: ['persist'])]
    protected Vendor $vendor;

    #[ORM\Column(name: 'izberg_bearer', type: Types::TEXT, nullable: false)]
    private ?string $izbergBearer = null;

    #[ORM\Column(name: 'izberg_bearer_expiration_date', type: Types::DATETIME_IMMUTABLE, nullable: false)]
    private ?DateTimeImmutable $izbergBearerExpirationDate = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getIzbergBearer(): ?string
    {
        return $this->izbergBearer;
    }

    public function setIzbergBearer(?string $izbergBearer): self
    {
        $this->izbergBearer = $izbergBearer;
        return $this;
    }

    public function getIzbergBearerExpirationDate(): ?DateTimeImmutable
    {
        return $this->izbergBearerExpirationDate;
    }

    public function setIzbergBearerExpirationDate(?DateTimeImmutable $izbergBearerExpirationDate): self
    {
        $this->izbergBearerExpirationDate = $izbergBearerExpirationDate;
        return $this;
    }

    /**
     * @return Vendor
     */
    public function getVendor(): Vendor
    {
        return $this->vendor;
    }

    /**
     * @param Vendor $vendor
     * @return $this
     */
    public function setVendor(Vendor $vendor): self
    {
        $this->vendor = $vendor;
        return $this;
    }
}
