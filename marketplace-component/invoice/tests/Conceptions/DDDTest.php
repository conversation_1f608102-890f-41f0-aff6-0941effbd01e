<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Tests\Conceptions;

use Respector\Analyze;
use Respector\Conceptions\BaseConceptionTest;

/**
 * Follow ADR-02
 *  - Not use infrastructure classes in the Domain
 *  - Not use external library in the Domain
 */
final class DDDTest extends BaseConceptionTest
{
    public function testNotInfrastructureReferencesInDomain(): void
    {
        $analyzer = Analyze::create('composer.json')->inNamespace('Marketplace\\Component\\Invoice\\Domain\\');

        foreach ($analyzer as $parsedFile) {
            $this->assertHasNoDependencyTo('Infrastructure\\', $parsedFile);
            $this->assertHasNoDependencyTo('Symfony\\', $parsedFile);
        }
    }
}
