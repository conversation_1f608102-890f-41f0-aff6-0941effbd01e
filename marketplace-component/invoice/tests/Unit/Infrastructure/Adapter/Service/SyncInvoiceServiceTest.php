<?php

namespace Marketplace\Component\Invoice\Infrastructure\Adapter\Service;

use Marketplace\Component\Invoice\Domain\Enum\InvoicePaymentStatus;
use Marketplace\Component\Invoice\Domain\Enum\InvoiceStatus;
use Marketplace\Component\Invoice\Domain\Port\Repository\InvoiceRepositoryInterface;
use Marketplace\Component\Invoice\Domain\UseCase\ExportInvoice\DTO\ExportInvoiceRequest;
use Marketplace\Component\Invoice\Infrastructure\Exception\NoMerchantOrderForInvoiceException;
use Marketplace\Component\Offer\Domain\UseCase\ImportSpecificPrices\DTO\ImportSpecificPricesRequest;
use Open\Izberg\Api\Invoice\InvoiceApiInterface;
use Open\Izberg\Client\OperatorClient;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;
use Open\Izberg\Model\Invoice as InvoiceIzberg;
use Marketplace\Component\Invoice\Domain\Model\Invoice;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

class SyncInvoiceServiceTest extends TestCase
{

    private SyncInvoiceService $syncInvoiceService;

    protected function setUp(): void
    {
        parent::setUp();

        $prophet = new Prophet();

        $invoiceApi = $prophet->prophesize(InvoiceApiInterface::class);
        $invoice = new InvoiceIzberg();
        $invoice->setId(1);
        $invoice->setTotalAmountWithTaxes(12);
        $invoice->setIdNumber(1);
        $invoice->setPdfFile('pdf.pdf');
        $invoice->setStatus('emitted');
        $invoice->setInvoiceLines([['merchant_order' => 'https://api.sandbox.iceberg.technology/v1/merchant_order/1120147/']]);
        $invoiceApi->fetchInvoiceById(Argument::type('int'))->will(function ($args) {
            $invoiceId = $args[0];
            $invoice = new InvoiceIzberg();
            $invoice->setId(1);
            $invoice->setTotalAmountWithTaxes(12);
            $invoice->setIdNumber(1);
            $invoice->setPdfFile('pdf.pdf');
            $invoice->setStatus('emitted');
            $invoice->setInvoiceLines([]);
            $invoice->setPaymentStatus(InvoicePaymentStatus::NOT_PAID->value);
            if ($invoiceId === 1) {
                $invoice->setInvoiceLines([['merchant_order' => 'https://api.sandbox.iceberg.technology/v1/merchant_order/1120147/']]);
            }
            return $invoice;
        });
        $operatorClient = $prophet->prophesize(OperatorClient::class);
        $operatorClient->invoiceApi()->willReturn($invoiceApi->reveal());
        $invoiceRepository = $prophet->prophesize(InvoiceRepositoryInterface::class);
        $messageBus = $prophet->prophesize(MessageBusInterface::class);
        $messageBus->dispatch(Argument::any())->will(function ($args) {
            return new Envelope(new ExportInvoiceRequest(1));
        });

        $this->syncInvoiceService = new SyncInvoiceService(
            $operatorClient->reveal(),
            $invoiceRepository->reveal(),
            $messageBus->reveal()
        );
    }

    public function testSyncInvoice()
    {
        $expected = new Invoice();
        $expected->setId(1);
        $expected->setTotalAmountWithTaxes(12);
        $expected->setNumberId(1);
        $expected->setExternalId(1);
        $expected->setMerchantOrderId(1120147);
        $expected->setPdfUrl('pdf.pdf');
        $expected->setStatus(InvoiceStatus::status('emitted'));
        $expected->setPaymentStatus(InvoicePaymentStatus::NOT_PAID);
        $invoice = $this->syncInvoiceService->syncInvoice(1);
        $this->assertEquals($expected, $invoice);
    }

    public function testSyncInvoiceException()
    {
        $this->expectException(NoMerchantOrderForInvoiceException::class);
        $invoice = $this->syncInvoiceService->syncInvoice(0);
    }
}
