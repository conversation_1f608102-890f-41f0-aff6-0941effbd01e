<?php

declare(strict_types=1);

namespace App\Tests\Unit\Domain;

use Marketplace\Component\Invoice\Domain\Model\Parcel;
use Marketplace\Component\Invoice\Domain\Model\ParcelItem;
use Marketplace\Component\Invoice\Domain\Port\Repository\ParcelRepositoryInterface;
use Marketplace\Component\Invoice\Domain\Presenter\ShowParcelPresenterInterface;
use Marketplace\Component\Invoice\Domain\UseCase\ShowParcel\DTO\ShowParcelRequest;
use Marketplace\Component\Invoice\Domain\UseCase\ShowParcel\DTO\ShowParcelResponse;
use Marketplace\Component\Invoice\Domain\UseCase\ShowParcel\ShowParcelUseCase;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

final class ShowParcelUseCaseTest extends TestCase
{
    private ShowParcelUseCase $useCase;

    private ShowParcelPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements ShowParcelPresenterInterface {
            public ShowParcelResponse $response;

            public function present(ShowParcelResponse $response): void
            {
                $this->response = $response;
            }
        };

        $prophet = new Prophet();

        $parcelRepository = $prophet->prophesize(ParcelRepositoryInterface::class);
        $parcelRepository->findParcelsByMerchantOrder(Argument::type('integer'))->will(function ($args) {
            return [
                (new Parcel())
                    ->setMerchantOrderId(1)
                    ->setCarrierName("Colissimo")
                    ->setStatus("initial")
                    ->setParcelItems([
                        (new ParcelItem())
                            ->setOrderItemId(1231)
                            ->setOfferName("item1")
                            ->setQuantity(2),
                        (new ParcelItem())
                            ->setOrderItemId(1232)
                            ->setOfferName("item2")
                            ->setQuantity(1)
                    ]),
                (new Parcel())
                    ->setCarrierName("poste")
                    ->setStatus("initial")
                    ->setParcelItems([
                        (new ParcelItem())
                            ->setOrderItemId(1231)
                            ->setOfferName("item1")
                            ->setQuantity(1),
                        (new ParcelItem())
                            ->setOrderItemId(1232)
                            ->setOfferName("item2")
                            ->setQuantity(3),
                        (new ParcelItem())
                            ->setOrderItemId(1233)
                            ->setOfferName("item3")
                            ->setQuantity(3)
                    ]),
                (new Parcel())
                    ->setCarrierName("Chronopost")
                    ->setStatus("initial")
                    ->setParcelItems([
                        (new ParcelItem())
                            ->setOrderItemId(1233)
                            ->setOfferName("item3")
                            ->setQuantity(1)
                    ])
            ];
        });

        $this->useCase = new ShowParcelUseCase($parcelRepository->reveal());
    }

    public function testSuccessful(): void
    {
        // Given a merchant order id
        // WHEN on appelle le ShowParcelUseCase->showParcel
        // Then on retourne un  array de Parcels avec consolidation de l'attribut overallQuantity

        $expected = [
            (new Parcel())
                ->setCarrierName("Colissimo")
                ->setMerchantOrderId(1)
                ->setStatus("initial")
                ->setCustomerDate(null)
                ->setParcelItems([
                    (new ParcelItem())
                        ->setOrderItemId(1231)
                        ->setOfferName("item1")
                        ->setQuantity(2)
                        ->setOverallQuantity(2)
                    ,
                    (new ParcelItem())
                        ->setOrderItemId(1232)
                        ->setOfferName("item2")
                        ->setQuantity(1)
                        ->setOverallQuantity(1)
                ]),
            (new Parcel())
                ->setCarrierName("poste")
                ->setStatus("initial")
                ->setCustomerDate(null)
                ->setParcelItems([
                    (new ParcelItem())
                        ->setOrderItemId(1231)
                        ->setOfferName("item1")
                        ->setQuantity(1)
                        ->setOverallQuantity(3)
                    ,
                    (new ParcelItem())
                        ->setOrderItemId(1232)
                        ->setOfferName("item2")
                        ->setQuantity(3)
                        ->setOverallQuantity(4)
                    ,
                    (new ParcelItem())
                        ->setOrderItemId(1233)
                        ->setOfferName("item3")
                        ->setQuantity(3)
                        ->setOverallQuantity(3)
                ]),
            (new Parcel())
                ->setCarrierName("Chronopost")
                ->setStatus("initial")
                ->setCustomerDate(null)
                ->setParcelItems([
                    (new ParcelItem())
                        ->setOrderItemId(1233)
                        ->setOfferName("item3")
                        ->setQuantity(1)
                        ->setOverallQuantity(4)
                ])
        ];

        $this->useCase->execute(new ShowParcelRequest(1), $this->presenter);
        $this->assertInstanceOf(ShowParcelResponse::class, $this->presenter->response);
        $this->assertEquals($expected, $this->presenter->response->getParcels());
    }
}
