<?php

namespace Marketplace\Component\Invoice\Tests\Unit\Domain\UseCase;

use Generator;
use Marketplace\Component\Invoice\Domain\Model\Parcel;
use Marketplace\Component\Invoice\Domain\Presenter\ReceiveParcelPresenterInterface;
use Marketplace\Component\Invoice\Domain\UseCase\ReceiveParcel\DTO\ReceiveParcelRequest;
use Marketplace\Component\Invoice\Domain\UseCase\ReceiveParcel\DTO\ReceiveParcelResponse;
use Marketplace\Component\Invoice\Domain\UseCase\ReceiveParcel\ReceiveParcelUseCase;
use Marketplace\Component\Invoice\Infrastructure\Adapter\Repository\DistantParcelRepositoryInterface;
use Marketplace\Component\Invoice\Infrastructure\Adapter\Repository\ParcelItemRepository;
use Marketplace\Component\Invoice\Infrastructure\Adapter\Repository\ParcelRepository;
use Marketplace\Component\Invoice\Infrastructure\Adapter\Service\SyncParcelService;
use Marketplace\Component\Order\Domain\Model\MerchantOrder;
use Marketplace\Component\Order\Domain\Port\Repository\MerchantOrderRepositoryInterface;
use Marketplace\Component\Order\Infrastructure\Adapter\Repository\OrderItemRepository;
use Open\Izberg\Model\Parcel as IzbergParcel;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;
use Psr\Log\LoggerInterface;

class ReceiveParcelUseCaseTest extends TestCase
{
    private ReceiveParcelUseCase $useCase;
    private ReceiveParcelPresenterInterface $presenter;


    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements ReceiveParcelPresenterInterface {
            public ReceiveParcelResponse $response;
            public function present(ReceiveParcelResponse $response): void
            {
                $this->response = $response;
            }
        };

        $prophet = new Prophet();

        $distantParcelRepository = $prophet->prophesize(DistantParcelRepositoryInterface::class);
        $logger = $prophet->prophesize(LoggerInterface::class);

        $distantParcelRepository->receiveParcel(Argument::type('integer'), Argument::type('array'))->will(function ($args) {
            return (new IzbergParcel())
                ->setId($args[0])
                ->setStatus($args[1]["status"]);
        });

        /** @var Prophet|MerchantOrderRepositoryInterface $merchantOrderRepository */
        $merchantOrderRepository = $prophet->prophesize(MerchantOrderRepositoryInterface::class);
        $merchantOrderRepository->findByDistantId(Argument::type('integer'))->will(function ($args) {
            return new MerchantOrder(1, 1);
        });

        $syncParcelService = new syncParcelService(
            $distantParcelRepository->reveal(),
            $this->createMock(ParcelRepository::class),
            $this->createMock(ParcelItemRepository::class),
            $this->createMock(OrderItemRepository::class),
            $merchantOrderRepository->reveal(),
            $logger->reveal()
        );


        $this->useCase = new ReceiveParcelUseCase($syncParcelService);
    }

    /**
     * @dataProvider pacelsProvider
     */
    public function testExecute(ReceiveParcelRequest $request, ReceiveParcelResponse $expected): void
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($expected, $this->presenter->response);
    }

    protected function pacelsProvider(): Generator
    {
        $parcel = new Parcel();
        $parcel->setId(1);
        $parcel->setInTransitDate(new \DateTimeImmutable('5 days ago'));
        $parcel->setStatus(Parcel::STATUS_IN_TRANSIT);

        $izbergParcel = new IzbergParcel();
        $izbergParcel->setId(1);
        $izbergParcel->setStatus(Parcel::STATUS_IN_TRANSIT);

        $response = new ReceiveParcelResponse([]);

        yield 'Test TransitDate > 7 days ago' =>
        [new ReceiveParcelRequest([$parcel]), $response];


        $parcel = new Parcel();
        $parcel->setId(2);
        $parcel->setInTransitDate(new \DateTimeImmutable('8 days ago'));
        $parcel->setStatus(Parcel::STATUS_IN_TRANSIT);
        $izbergParcel = new IzbergParcel();
        $izbergParcel->setId(2);
        $izbergParcel->setStatus(Parcel::STATUS_RECEIVED);
        $response = new ReceiveParcelResponse([$izbergParcel]);

        yield 'Test TransitDate < 7 days ago' =>
        [new ReceiveParcelRequest([$parcel]), $response];
    }
}
