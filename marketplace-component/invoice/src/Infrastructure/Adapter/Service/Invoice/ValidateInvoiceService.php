<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Infrastructure\Adapter\Service\Invoice;

use Marketplace\Component\Invoice\Domain\Port\Service\ValidateInvoiceServiceInterface;
use Open\Izberg\Client\OperatorClient;

final class ValidateInvoiceService implements ValidateInvoiceServiceInterface
{
    public function __construct(private OperatorClient $operatorClient)
    {
    }

    public function validate(int $invoiceId): void
    {
        $this->operatorClient->invoiceApi()->submitInvoice($invoiceId);
    }
}
