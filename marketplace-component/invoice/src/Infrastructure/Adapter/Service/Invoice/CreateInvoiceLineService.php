<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Infrastructure\Adapter\Service\Invoice;

use Marketplace\Component\Invoice\Domain\Port\Service\CreateInvoiceLineServiceInterface;
use Open\Izberg\Client\OperatorClient;
use Symfony\Contracts\Translation\TranslatorInterface;

final class CreateInvoiceLineService implements CreateInvoiceLineServiceInterface
{
    public function __construct(private OperatorClient $operatorClient, private TranslatorInterface $translator)
    {
    }

    public function addInvoiceLine(int $invoiceId, int $orderItemId, int $quantity): void
    {
        // Update the invoiceable_quantity of order item
        $this->operatorClient->orderItemApi()->patchOrderItem($orderItemId, ['invoiceable_quantity' => $quantity]);
        $this->operatorClient->invoiceLineApi()->create($invoiceId, $orderItemId);
    }

    public function addInvoiceShipping(
        int $invoiceId,
        int $merchantOrderId,
        array $shippingOption,
        string $locale
    ): void {
        $name = $this->translator->trans("invoice.shipping", domain: 'translations', locale: $locale);
        // Update the invoiceable_quantity of order item
        $this->operatorClient->invoiceLineApi()->createShippingInvoice(
            $invoiceId,
            $merchantOrderId,
            $name,
            $shippingOption['amount_without_vat'],
            $shippingOption['vat_rate']
        );
    }

    public function addExtraFees(int $invoiceId, int $merchantOrderId, float $price): void
    {
        $this->operatorClient->invoiceLineApi()->createExtraFees($invoiceId, $merchantOrderId, $price);
    }
}
