<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Infrastructure\Adapter\Service;

use Marketplace\Component\Invoice\Domain\Port\Service\ProcessMerchantOrderServiceInterface;
use Marketplace\Component\Order\Domain\Model\MerchantOrder;
use Open\Izberg\Client\OperatorClient;

final class ProcessMerchantOrderService implements ProcessMerchantOrderServiceInterface
{
    public function __construct(private OperatorClient $operatorClient)
    {
    }

    public function process(MerchantOrder $merchantOrder): void
    {
        $merchantOrderId = $merchantOrder->getId();
        if ($merchantOrderId === null) {
            throw new \InvalidArgumentException('Merchant order does not identifier');
        }

        $this->operatorClient->orderApi()->processMerchantOrder($merchantOrderId);
    }
}
