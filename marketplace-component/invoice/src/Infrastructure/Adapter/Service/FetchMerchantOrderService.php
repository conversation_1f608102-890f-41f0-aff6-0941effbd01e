<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Infrastructure\Adapter\Service;

use Marketplace\Component\Invoice\Domain\Port\Service\FetchMerchantOrderServiceInterface;
use Marketplace\Component\Order\Domain\Enum\MerchantOrderStatusEnum;
use Marketplace\Component\Order\Domain\Model\MerchantOrder;
use Marketplace\Component\Order\Domain\Model\OrderItem;
use Marketplace\Component\Order\Infrastructure\Enum\MerchantOrderStatusIzberg;
use Marketplace\Component\Order\Infrastructure\ValueObject\IzbergStatus;
use Marketplace\Component\User\Domain\Model\Address;
use Marketplace\Component\User\Domain\Port\Repository\CountryRepositoryInterface;
use Open\Izberg\Client\OperatorClient;
use Open\Izberg\Model\MerchantOrder as MerchantOrderIzberg;
use Open\Izberg\Model\ShippingOption;

final class FetchMerchantOrderService implements FetchMerchantOrderServiceInterface
{
    public function __construct(
        private OperatorClient $operatorClient
    ) {
    }

    public function fetchMerchantsOrderByOrderId(int $merchantOrderId): ?MerchantOrder
    {
        $merchantOrder = $this->operatorClient->orderApi()->fetchMerchantOrder($merchantOrderId);


        if (!$merchantOrder instanceof MerchantOrderIzberg) {
            return null;
        }
        $izbergStatus = MerchantOrderStatusIzberg::from($merchantOrder->getStatus());

        return (new MerchantOrder($merchantOrder->getOrder()['cart_id'], $merchantOrder->getMerchant()->getId()))
            ->setId($merchantOrder->getId())
            ->setMerchantOrderDistantId($merchantOrder->getId())
            ->setUserId($merchantOrder->getUser()['id'])
            ->setStatus(MerchantOrderStatusEnum::status($izbergStatus->name))
            ->setOrderItems(
                array_map(
                    fn(array $item) => (new OrderItem((int)$item['id']))
                        ->setName($item['name'])
                        ->setQuantity((int)$item['quantity'])
                        ->setInvoicedQuantity((int)$item['invoiced_quantity'])
                        ->setInvoiceableQuantity((int)$item['invoiceable_quantity'])
                        ->setOfferExternalId($item['offer_external_id'])
                        ->setAmountTTC((float)$item['amount_vat_included'])
                        ->setAmountHT((float)$item['amount'])
                        ->setStatus(IzbergStatus::create((int)$item['status'])->getIzbergStatus()),
                    $merchantOrder->getItems()
                )
            )
            ->setShippingAddress(
                new Address(
                    Address::SHIPPING,
                    $merchantOrder->getShippingAddress()["name"],
                    $merchantOrder->getShippingAddress()["address"],
                    $merchantOrder->getShippingAddress()["address2"],
                    $merchantOrder->getShippingAddress()["zipcode"],
                    $merchantOrder->getShippingAddress()["city"],
                    $merchantOrder->getShippingAddress()["country"]["id"],
                    -1,
                    "",
                    "" // these info are not in izberg. can't be retrieve, as country id is same for multiple country
                )
            )
            ->setShippingHT($merchantOrder->getShipping())
            ->setExtraFees($merchantOrder->getExtraData()->getExtraFee());
    }

    public function shippingOrderItems(int $merchantOrderId): array
    {
        $shippingOptions = $this->operatorClient->orderApi()->fetchShippingOptionsOfMerchantOrder($merchantOrderId);
        if (!$shippingOptions instanceof ShippingOption) {
            return [];
        }

        return array_map(
            fn(array $shippingOption) => $shippingOption,
            $shippingOptions->getObjects()
        );
    }
}
