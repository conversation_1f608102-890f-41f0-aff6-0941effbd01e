<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Infrastructure\Adapter\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Marketplace\Component\Invoice\Domain\Model\CreditNote;
use Marketplace\Component\Invoice\Domain\Port\Repository\CreditNoteRepositoryInterface;
use Marketplace\Component\Invoice\Infrastructure\Adapter\Service\CreditNote\CreateCreditNoteServiceInterface;
use Marketplace\Component\Invoice\Infrastructure\Entity\CreditNote as DoctrineCreditNote;
use Marketplace\Component\Invoice\Infrastructure\Mapper\CreditNoteMapper;

final class CreditNoteRepository extends ServiceEntityRepository implements CreditNoteRepositoryInterface
{
    public function __construct(
        ManagerRegistry $registry,
        private CreateCreditNoteServiceInterface $createCreditNoteService
    ) {
        parent::__construct($registry, DoctrineCreditNote::class);
    }

    public function findByDistantId(int $creditNoteId): CreditNote
    {
        return $this->createCreditNoteService->findCreditNoteByDistantId($creditNoteId);
    }

    public function update(CreditNote $creditNote): void
    {
        $doctrineCreditNote = $this->find($creditNote->getId());
        $doctrineInvoice = CreditNoteMapper::modelToDoctrine($creditNote, $doctrineCreditNote);
        $this->_em->persist($doctrineInvoice);
        $this->_em->flush();
    }


    public function findById(int $creditNoteId): ?CreditNote
    {
        $doctrineCreditNote = $this->find($creditNoteId);

        if ($doctrineCreditNote === null) {
            return null;
        }

        return CreditNoteMapper::doctrineToModel($doctrineCreditNote);
    }
}
