<?php

namespace Marketplace\Component\Invoice\Infrastructure\Entity;

use Marketplace\Component\Invoice\Domain\Enum\InvoicePaymentStatus;
use Marketplace\Component\Invoice\Infrastructure\Adapter\Repository\InvoiceRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: InvoiceRepository::class)]
class Invoice
{
    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    private int $id;
    #[ORM\Column(type: 'integer')]
    private int $merchantOrderId;
    #[ORM\Column(type: 'string')]
    private string $numberId;
    #[ORM\Column(type: 'float')]
    private float $totalAmountWithTaxes;
    #[ORM\Column(type: 'string')]
    private string $status;
    #[ORM\Column(type: 'text', nullable: true)]
    private string $pdfUrl;
    #[ORM\Column(type: 'string', enumType: InvoicePaymentStatus::class, options: ['default' => 'not_paid'])]
    private InvoicePaymentStatus $paymentStatus;

    public function __construct()
    {
        $this->paymentStatus = InvoicePaymentStatus::NOT_PAID;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return $this
     */
    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return int
     */
    public function getMerchantOrderId(): int
    {
        return $this->merchantOrderId;
    }

    /**
     * @param int $merchantOrderId
     * @return $this
     */
    public function setMerchantOrderId(int $merchantOrderId): self
    {
        $this->merchantOrderId = $merchantOrderId;
        return $this;
    }

    /**
     * @return string
     */
    public function getNumberId(): string
    {
        return $this->numberId;
    }

    /**
     * @param string $numberId
     * @return $this
     */
    public function setNumberId(string $numberId): self
    {
        $this->numberId = $numberId;
        return $this;
    }

    /**
     * @return float
     */
    public function getTotalAmountWithTaxes(): float
    {
        return $this->totalAmountWithTaxes;
    }

    /**
     * @param float $totalAmountWithTaxes
     * @return $this
     */
    public function setTotalAmountWithTaxes(float $totalAmountWithTaxes): self
    {
        $this->totalAmountWithTaxes = $totalAmountWithTaxes;
        return $this;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return $this
     */
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return string
     */
    public function getPdfUrl(): string
    {
        return $this->pdfUrl;
    }

    /**
     * @param string $pdfUrl
     * @return $this
     */
    public function setPdfUrl(string $pdfUrl): self
    {
        $this->pdfUrl = $pdfUrl;
        return $this;
    }

    /**
     * @return InvoicePaymentStatus
     */
    public function getPaymentStatus(): InvoicePaymentStatus
    {
        return $this->paymentStatus;
    }

    /**
     * @param InvoicePaymentStatus $paymentStatus
     * @return Invoice
     */
    public function setPaymentStatus(InvoicePaymentStatus $paymentStatus): Invoice
    {
        $this->paymentStatus = $paymentStatus;
        return $this;
    }
}
