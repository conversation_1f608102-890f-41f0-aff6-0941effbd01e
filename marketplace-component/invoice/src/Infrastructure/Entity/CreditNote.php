<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Infrastructure\Entity;

use Doctrine\ORM\Mapping as ORM;
use Marketplace\Component\Invoice\Domain\Enum\CreditNotePaymentStatus;
use Marketplace\Component\Invoice\Infrastructure\Adapter\Repository\CreditNoteRepository;

#[ORM\Entity(repositoryClass: CreditNoteRepository::class)]
class CreditNote
{
    #[ORM\Id]
    #[ORM\Column(type: 'integer')]
    private int $id;
    #[ORM\Column(type: 'integer', nullable: true)]
    private ?int $merchantOrderId = null;
    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $numberId = null;
    #[ORM\Column(type: 'float')]
    private float $totalAmountWithTaxes;
    #[ORM\Column(type: 'string')]
    private string $status;
    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $pdfUrl = null;
    #[ORM\Column(type: 'string', enumType: CreditNotePaymentStatus::class, options: ['default' => 'not_paid'])]
    private CreditNotePaymentStatus $paymentStatus;

    public function __construct()
    {
        $this->paymentStatus = CreditNotePaymentStatus::NOT_PAID;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return $this
     */
    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return ?int
     */
    public function getMerchantOrderId(): ?int
    {
        return $this->merchantOrderId;
    }

    /**
     * @param ?int $merchantOrderId
     * @return $this
     */
    public function setMerchantOrderId(?int $merchantOrderId): self
    {
        $this->merchantOrderId = $merchantOrderId;
        return $this;
    }

    /**
     * @return ?string
     */
    public function getNumberId(): ?string
    {
        return $this->numberId;
    }

    /**
     * @param ?string $numberId
     * @return $this
     */
    public function setNumberId(?string $numberId): self
    {
        $this->numberId = $numberId;
        return $this;
    }

    /**
     * @return float
     */
    public function getTotalAmountWithTaxes(): float
    {
        return $this->totalAmountWithTaxes;
    }

    /**
     * @param float $totalAmountWithTaxes
     * @return $this
     */
    public function setTotalAmountWithTaxes(float $totalAmountWithTaxes): self
    {
        $this->totalAmountWithTaxes = $totalAmountWithTaxes;
        return $this;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return $this
     */
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return string
     */
    public function getPdfUrl(): ?string
    {
        return $this->pdfUrl;
    }

    /**
     * @param ?string $pdfUrl
     * @return $this
     */
    public function setPdfUrl(?string $pdfUrl): self
    {
        $this->pdfUrl = $pdfUrl;
        return $this;
    }

    /**
     * @return CreditNotePaymentStatus
     */
    public function getPaymentStatus(): CreditNotePaymentStatus
    {
        return $this->paymentStatus;
    }

    /**
     * @param CreditNotePaymentStatus $paymentStatus
     * @return CreditNote
     */
    public function setPaymentStatus(CreditNotePaymentStatus $paymentStatus): CreditNote
    {
        $this->paymentStatus = $paymentStatus;
        return $this;
    }
}
