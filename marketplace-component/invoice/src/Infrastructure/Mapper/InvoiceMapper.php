<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Infrastructure\Mapper;

use Marketplace\Component\CleanArchiCore\Utils\Str;
use Marketplace\Component\Invoice\Domain\Enum\InvoicePaymentStatus;
use Marketplace\Component\Invoice\Domain\Enum\InvoiceStatus;
use Marketplace\Component\Invoice\Domain\Model\Invoice;
use Marketplace\Component\Invoice\Infrastructure\Entity\Invoice as DoctrineInvoice;
use Open\Izberg\Model\Invoice as Izberginvoice;

abstract class InvoiceMapper
{
    public static function izbergToDomain(Izberginvoice $invoice): Invoice
    {
        $fileUrl = $invoice->getFileUrl();
        $pdfName = null;
        if ($fileUrl != null) {
            $pdfName = Str::lastRightPart($fileUrl, "/");
        }
        $invoiceModel = (new Invoice())
            ->setId($invoice->getId())
            ->setTotalAmountWithTaxes((float)$invoice->getTotalAmountWithTaxes())
            ->setExternalId($invoice->getId())
            ->setNumberId((string)$invoice->getIdNumber())
            ->setPdfUrl($invoice->getPdfFile())
            ->setPdfFileName($pdfName)
            ->setStatus(InvoiceStatus::status($invoice->getStatus() ?? ''))
            ->setPaymentStatus(InvoicePaymentStatus::status($invoice->getPaymentStatus() ?? ''))
        ;

        $invoiceLines = $invoice->getInvoiceLines();
        $firstLine = null;
        if ($invoiceLines != null && count($invoiceLines) > 0) {
            $firstLine = $invoiceLines[0];
        }

        if ($firstLine != null) {
            $id = Str::extractIdOfIzbergResource($firstLine, "merchant_order");
            if ($id != null) {
                $invoiceModel->setMerchantOrderId($id);
            }
        }
        return $invoiceModel;
    }

    public static function modelToDoctrine(Invoice $invoice, ?DoctrineInvoice $doctrineInvoice): DoctrineInvoice
    {
        if (!$doctrineInvoice instanceof DoctrineInvoice) {
            $doctrineInvoice = new DoctrineInvoice();
        }
        return $doctrineInvoice
            ->setId($invoice->getId())
            ->setPdfUrl($invoice->getPdfUrl() ?? '')
            ->setStatus($invoice->getStatus()->value)
            ->setMerchantOrderId($invoice->getMerchantOrderId())
            ->setNumberId($invoice->getNumberId())
            ->setTotalAmountWithTaxes($invoice->getTotalAmountWithTaxes())
            ->setPaymentStatus($invoice->getPaymentStatus())
            ;
    }

    public static function doctrineToModel(DoctrineInvoice $invoiceDoc): Invoice
    {
        return (new Invoice())
            ->setId($invoiceDoc->getId())
            ->setExternalId($invoiceDoc->getId())
            ->setPdfUrl($invoiceDoc->getPdfUrl())
            ->setStatus(InvoiceStatus::status($invoiceDoc->getStatus()))
            ->setMerchantOrderId($invoiceDoc->getMerchantOrderId())
            ->setNumberId($invoiceDoc->getNumberId())
            ->setTotalAmountWithTaxes($invoiceDoc->getTotalAmountWithTaxes())
            ->setPaymentStatus($invoiceDoc->getPaymentStatus())
            ;
    }
}
