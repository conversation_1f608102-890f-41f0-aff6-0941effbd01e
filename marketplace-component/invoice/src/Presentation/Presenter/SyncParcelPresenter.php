<?php

namespace Marketplace\Component\Invoice\Presentation\Presenter;

use Marketplace\Component\Invoice\Domain\Presenter\SyncParcelPresenterInterface;
use Marketplace\Component\Invoice\Domain\UseCase\SyncParcel\DTO\SyncParcelResponse;
use Marketplace\Component\Invoice\Presentation\ViewModel\SyncParcelViewModel;

class SyncParcelPresenter implements SyncParcelPresenterInterface
{
    private SyncParcelViewModel $viewModel;

    public function __construct()
    {
        $this->viewModel = new SyncParcelViewModel();
    }

    public function present(SyncParcelResponse $response): void
    {
        $this->viewModel->setParcels($response->getParcels());
    }

    public function viewModel(): SyncParcelViewModel
    {
        return $this->viewModel;
    }
}
