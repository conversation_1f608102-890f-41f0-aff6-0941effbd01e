<?php

namespace Marketplace\Component\Invoice\Presentation\Presenter;

use Marketplace\Component\Invoice\Domain\Presenter\ShowParcelPresenterInterface;
use Marketplace\Component\Invoice\Domain\UseCase\ShowParcel\DTO\ShowParcelResponse;
use Marketplace\Component\Invoice\Presentation\ViewModel\ShowParcelViewModel;

class ShowParcelPresenter implements ShowParcelPresenterInterface
{
    private ShowParcelViewModel $viewModel;

    public function __construct()
    {
        $this->viewModel = new ShowParcelViewModel();
    }

    public function present(ShowParcelResponse $response): void
    {
        $this->viewModel->setParcels($response->getParcels());
    }

    public function viewModel(): ShowParcelViewModel
    {
        return $this->viewModel;
    }
}
