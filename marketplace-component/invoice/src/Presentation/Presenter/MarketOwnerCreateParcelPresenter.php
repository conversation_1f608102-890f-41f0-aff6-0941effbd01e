<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Presentation\Presenter;

use Marketplace\Component\Invoice\Domain\Presenter\MarketOwnerCreateParcelPresenterInterface;
use Marketplace\Component\Invoice\Domain\UseCase\MarketOwnerCreateParcel\DTO\MarketOwnerCreateParcelResponse;
use Marketplace\Component\Invoice\Presentation\ViewModel\MarketOwnerCreateParcelViewModel;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

final class MarketOwnerCreateParcelPresenter implements MarketOwnerCreateParcelPresenterInterface, LoggerAwareInterface
{
    private MarketOwnerCreateParcelViewModel $viewModel;

    private LoggerInterface $logger;

    public function present(MarketOwnerCreateParcelResponse $response): void
    {
        $success = $response->getNotification()->hasError() === false;
        $this->viewModel = new MarketOwnerCreateParcelViewModel(
            success: $success,
        );

        if ($success === false) {
            $this->logger->error($response->getNotification(), ['CREATE_PARCEL']);
        }
    }

    public function viewModel(): MarketOwnerCreateParcelViewModel
    {
        return $this->viewModel;
    }

    /**
     * @inheritDoc
     */
    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
