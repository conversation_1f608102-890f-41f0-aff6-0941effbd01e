<?php

namespace Symfony\Component\DependencyInjection\Loader\Configurator;

use Marketplace\Component\Invoice\Domain\Port\Service\ExportInvoiceServiceInterface;
use Marketplace\Component\Invoice\Infrastructure\Adapter\Service\DeliverySlip\ReadDeliverySlipFileInterface;
use Marketplace\Component\Invoice\Infrastructure\Adapter\Service\DeliverySlip\ReadDeliverySlipFileService;
use Marketplace\Component\Invoice\Infrastructure\Adapter\Service\Invoice\ExportInvoiceService;

return function (ContainerConfigurator $configurator) {
    // default configuration for services in *this* file
    $services = $configurator->services()
        ->defaults()
        ->autowire()      // Automatically injects dependencies in your services.
        ->autoconfigure() // Automatically registers your services as commands, event subscribers, etc.
    ;

    // makes classes in src/ available to be used as services
    // this creates a service per class whose id is the fully-qualified class name
    $services->load('Marketplace\\Component\\Invoice\\', '../../../src/*')
        ->exclude('../../../src/{DependencyInjection,Model,Resources,MarketplaceInvoiceBundle.php}');

    $services->set(ReadDeliverySlipFileInterface::class, ReadDeliverySlipFileService::class)
        ->args([
            '$currentPath' => '%env(resolve:IMPORT_DELIVERY_SLIP_CURRENT_PATH)%',
            '$archivePath' => '%env(resolve:IMPORT_DELIVERY_SLIP_ARCHIVE_PATH)%',
            '$errorPath' => '%env(resolve:IMPORT_DELIVERY_SLIP_ERROR_PATH)%'
        ]);

    $services->set(ExportInvoiceServiceInterface::class, ExportInvoiceService::class)
        ->args([
            '$currentPath' => '%env(resolve:EXPORT_INVOICE_CURRENT_PATH)%',
            '$archivePath' => '%env(resolve:EXPORT_INVOICE_ARCHIVE_PATH)%',
        ]);
};
