<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Domain\Port\Service;

interface CreateInvoiceLineServiceInterface
{
    public function addInvoiceLine(int $invoiceId, int $orderItemId, int $quantity): void;

    public function addInvoiceShipping(int $invoiceId, int $merchantOrderId, array $shippingOption, string $locale): void;

    public function addExtraFees(int $invoiceId, int $merchantOrderId, float $price): void;
}
