<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Domain\UseCase\MarketOwnerCreateParcel;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CheckMerchantServiceInterface;
use Marketplace\Component\Invoice\Domain\Dto\ItemData;
use Marketplace\Component\Invoice\Domain\Exception\UnSupportedCarrierNameException;
use Marketplace\Component\Invoice\Domain\Model\DeliverySlip;
use Marketplace\Component\Invoice\Domain\Model\Parcel;
use Marketplace\Component\Invoice\Domain\Model\ParcelItem;
use Marketplace\Component\Invoice\Domain\Port\Repository\DeliverySlipRepositoryInterface;
use Marketplace\Component\Invoice\Domain\Port\Repository\ParcelRepositoryInterface;
use Marketplace\Component\Invoice\Domain\Port\Service\FetchMerchantOrderServiceInterface;
use Marketplace\Component\Invoice\Domain\Presenter\MarketOwnerCreateParcelPresenterInterface;
use Marketplace\Component\Invoice\Domain\UseCase\MarketOwnerCreateParcel\DTO\MarketOwnerCreateParcelRequest;
use Marketplace\Component\Invoice\Domain\UseCase\MarketOwnerCreateParcel\DTO\MarketOwnerCreateParcelResponse;
use Marketplace\Component\Invoice\Domain\ValueObject\CarrierName\CarrierName;
use Marketplace\Component\Order\Domain\Model\MerchantOrder;
use Psr\Log\LoggerInterface;

final class MarketOwnerCreateParcelUseCase
{
    public function __construct(
        private FetchMerchantOrderServiceInterface $merchantOrderService,
        private CheckMerchantServiceInterface $checkMerchantService,
        private ParcelRepositoryInterface $parcelRepository,
        private DeliverySlipRepositoryInterface $deliverySlipRepository,
        private LoggerInterface $logger
    ) {
    }


    public function execute(
        MarketOwnerCreateParcelRequest $request,
        MarketOwnerCreateParcelPresenterInterface $presenter
    ): void {
        $response = new MarketOwnerCreateParcelResponse();

        if ($request->carrierName === null) {
            $response->addError('carrierName', 'Carrier name null');
            $presenter->present($response);
            return;
        }

        $merchantOrder = $this->merchantOrderService->fetchMerchantsOrderByOrderId($request->merchantOrderId);
        if (!$merchantOrder instanceof MerchantOrder) {
            $response->addError('merchantOrder', 'Merchant order does not exist');
            $presenter->present($response);
            return;
        }

        if ($this->checkMerchantService->isMKPOwnerMerchant($merchantOrder->getMerchantId()) === false) {
            $response->addError('merchant', 'Merchant is no marketplace owner');
            $presenter->present($response);
            return;
        }

        if (!$request->validate($response)) {
            $presenter->present($response);
            return;
        }

        $initialParcels = $this->parcelRepository->getInitialParcels($request->merchantOrderId);

        //telenco parcel are shipped immediately,
        // so parcel in "initial" status can only be the default parcel to be removed
        if ($initialParcels !== []) {
            foreach ($initialParcels as $initialParcel) {
                $this->logger->info("deleting parcel", ["id" => $initialParcel->getId()]);
                $this->parcelRepository->deleteParcelOfMerchantOrder((int)$initialParcel->getId());
            }
        } else {
            $this->logger->info("no initial parcel for merchantOrder", ["id" => $request->merchantOrderId]);
        }

        $carrierName = "TD";
        $carrierId = $request->carrierId; //seems to be set to 1 at this point
        //TD: unknown carrier
        if ($request->carrierName !== "TD") {
            try {
                $carrierName = CarrierName::inverseMKPName($request->carrierName);
                $carrierId = $this->parcelRepository->getCarrierIdByName($carrierName);
            } catch (UnSupportedCarrierNameException $ex) {
                $message = sprintf("[PARCEL] carrier name problem:%s", $request->carrierName);
                $this->logger->error($message, ["error" => $ex->getMessage()]);
                $response->addError("carrierName", $message);
                $presenter->present($response);
                return;
            }
        }

        $parcelItems = array_map(function (ItemData $orderItem) {
            return (new ParcelItem())
                ->setId($orderItem->itemId)
                ->setOfferName($orderItem->name)
                ->setQuantity($orderItem->quantity)
                ->setOrderItemId($orderItem->itemId)
                ->setOrderedQuantity($orderItem->quantity);
        }, $request->orderItems);

        $parcelFromDeliverySlip = (new Parcel())
            ->setTrackingNumber($request->trackingNumber)
            ->setMerchantOrderId($request->merchantOrderId)
            ->setCarrierId($carrierId)
            ->setCarrierName($carrierName)
            ->setStatus(Parcel::STATUS_IN_TRANSIT)
            ->setParcelItems($parcelItems);

        $parcelId = $this->parcelRepository->create($parcelFromDeliverySlip);

        $deliverySlip = (new DeliverySlip())->setParcelId($parcelId)->setDeliverySlipId($request->deliverySlipId);
        $this->deliverySlipRepository->update($deliverySlip);

        $this->parcelRepository->shipParcel($parcelId);
        $response->parcel = $parcelFromDeliverySlip;

        $presenter->present($response);
    }
}
