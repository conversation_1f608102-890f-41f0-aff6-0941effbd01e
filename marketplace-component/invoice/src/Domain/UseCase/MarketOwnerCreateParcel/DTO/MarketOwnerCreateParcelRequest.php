<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Domain\UseCase\MarketOwnerCreateParcel\DTO;

use Assert\LazyAssertionException;
use Marketplace\Component\CleanArchiCore\Domain\Assert\Assert;
use Marketplace\Component\Invoice\Domain\Dto\ItemData;

final class MarketOwnerCreateParcelRequest
{
    public function __construct(
        public int $merchantOrderId,
        public int $carrierId,
        public string $trackingNumber,
        public ?string $carrierName = null,
        public string $deliverySlipId,
        public array $orderItems = [],
        public array $shipping = []
    ) {
    }

    public static function create(
        int $merchantOrderId,
        int $carrierId,
        string $trackingNumber,
        ?string $carrierName,
        string $deliverySlipId,
        array $orderItems,
        array $shipping = [],
    ): self {
        return new self(
            $merchantOrderId,
            $carrierId,
            $trackingNumber,
            $carrierName,
            $deliverySlipId,
            $orderItems,
            $shipping
        );
    }

    public function validate(MarketOwnerCreateParcelResponse $response): bool
    {
        try {
            Assert::lazy()
                ->that($this->orderItems, 'orderItems')->notEmpty('Order item cannot be empty')
                ->verifyNow();

            return true;
        } catch (LazyAssertionException $exception) {
            foreach ($exception->getErrorExceptions() as $errorException) {
                $response->addError($errorException->getPropertyPath() ?? '', $errorException->getMessage());
            }

            return false;
        }
    }
}
