<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Domain\UseCase\SyncParcel;

use Marketplace\Component\Invoice\Domain\Port\Service\SyncParcelServiceInterface;
use Marketplace\Component\Invoice\Domain\Presenter\SyncParcelPresenterInterface;
use Marketplace\Component\Invoice\Domain\UseCase\SyncParcel\DTO\SyncParcelRequest;
use Marketplace\Component\Invoice\Domain\UseCase\SyncParcel\DTO\SyncParcelResponse;

class SyncParcelUseCase
{
    public function __construct(
        private SyncParcelServiceInterface $syncParcelService
    ) {
    }

    public function execute(SyncParcelRequest $request, SyncParcelPresenterInterface $presenter)
    {
        $parcels = $this->syncParcelService->syncParcels($request->merchantOrderId);

        $presenter->present(new SyncParcelResponse($parcels));
    }
}
