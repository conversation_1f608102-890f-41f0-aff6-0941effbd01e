<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Domain\UseCase\SyncCreditNote;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\DownloadServiceInterface;
use Marketplace\Component\Invoice\Domain\Enum\CreditNoteStatus;
use Marketplace\Component\Invoice\Domain\Model\CreditNote;
use Marketplace\Component\Invoice\Domain\Port\Repository\CreditNoteRepositoryInterface;
use Marketplace\Component\Invoice\Domain\Presenter\SyncCreditNotePresenterInterface;
use Marketplace\Component\Invoice\Domain\UseCase\SyncCreditNote\DTO\SyncCreditNoteRequest;
use Marketplace\Component\Invoice\Domain\UseCase\SyncCreditNote\DTO\SyncCreditNoteResponse;
use Marketplace\Component\Mail\Domain\Model\EmailTemplateSlug;
use Marketplace\Component\Mail\Domain\Port\Service\EmailServiceInterface;
use Marketplace\Component\Order\Domain\Model\MerchantOrder;
use Marketplace\Component\Order\Domain\Port\Repository\MerchantOrderRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;

final class SyncCreditNoteUseCase
{
    public function __construct(
        private CreditNoteRepositoryInterface $creditNoteRepository,
        private MerchantOrderRepositoryInterface $merchantOrderRepository,
        private EmailServiceInterface $emailService,
        private DownloadServiceInterface $downloadService,
        private CompanyRepositoryInterface $companyRepository
    ) {
    }

    public function execute(SyncCreditNoteRequest $request, SyncCreditNotePresenterInterface $presenter): void
    {

        $creditNote = $this->creditNoteRepository->findByDistantId($request->creditNoteId);

        $this->creditNoteRepository->update($creditNote);
        $response = (new SyncCreditNoteResponse())->setCreditNote($creditNote);

        if ($request->status === CreditNoteStatus::EMITTED->value) {
            $this->sendMail($creditNote, $presenter, $response);
        }

        $presenter->present($response);
    }

    private function sendMail(
        CreditNote $creditNote,
        SyncCreditNotePresenterInterface $presenter,
        SyncCreditNoteResponse $response
    ): void {


        $fileStream = $this->downloadService->getFileStream($creditNote->getPdfUrl(), $creditNote->getPdfFileName());

        $merchantOrderId = $creditNote->getMerchantOrderId();
        if ($merchantOrderId === null) {
            $response->setErrorMessage(sprintf(
                'Merchant Order id is null for this creditNote:%d',
                $creditNote->getId()
            ));
            $presenter->present($response);
            return;
        }
        $merchantOrder = $this->merchantOrderRepository->findByDistantId($merchantOrderId);
        if (!$merchantOrder instanceof MerchantOrder) {
            $response->setErrorMessage(sprintf(
                'Merchant Order doesn\'t exist for this CreditNote:%d',
                $creditNote->getId()
            ));
            $presenter->present($response);
            return;
        }

        $order = $merchantOrder->getOrder();
        $orderDate = $order->getCreationDate()->format('d/m/Y H:i:s');
        $merchantName = $merchantOrder->getName();
        $numberId = $creditNote->getNumberId();

        $emailVariable = [
            'orderDate' => $orderDate,
            'orderId' => $order->getIzbergId(),
            'merchantName' => $merchantName,
            'billId' => $numberId
        ];

        $companyId = $order->getCompanyId();
        if ($companyId === null) {
            $response->setErrorMessage(sprintf(
                'Company Id is null for this Order :%d',
                $order->getOrderId() ?? "null"
            ));
            $presenter->present($response);
            return;
        }

        $company = $this->companyRepository->getCompanyById($companyId);
        if ($company === null) {
            $response->setErrorMessage(sprintf('Company doesn\'t exist for this Id : %d', $companyId));
            $presenter->present($response);
            return;
        }
        $companyName = $company->getName();
        $companyAccountingEmail = $company->getAccountingEmail();
        $locale = $company->getCountry()->getLocale();
        $userInfo = [$companyAccountingEmail, $companyName];

        $emailSend = $this->emailService->send(
            from: [$this->emailService->getNoReplyUser()],
            to: [$userInfo],
            slug: EmailTemplateSlug::NEW_CREDIT_NOTE_TO_USER,
            locale: $locale,
            context: $emailVariable,
            fileStream: $fileStream
        );
        $response->setMailSent($emailSend);
    }
}
