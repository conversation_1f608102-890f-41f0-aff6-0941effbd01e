<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Domain\UseCase\ExportInvoice\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Error\NotificationTrait;

final class ExportInvoiceResponse
{
    use NotificationTrait;

    public bool $exportInvoice = true;

    public function addError(string $fieldName, string $message): void
    {
        $this->getNotification()->addError($fieldName, $message);
    }
}
