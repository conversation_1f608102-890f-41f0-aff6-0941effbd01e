<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Domain\UseCase\CreateInvoice;

use Marketplace\Component\Invoice\Domain\Exception\InvalidMerchantOrderException;
use Marketplace\Component\Invoice\Domain\Model\DeliverySlip;
use Marketplace\Component\Invoice\Domain\Model\ParcelItem;
use Marketplace\Component\Invoice\Domain\Port\Repository\DeliverySlipRepositoryInterface;
use Marketplace\Component\Invoice\Domain\Port\Repository\InvoiceRepositoryInterface;
use Marketplace\Component\Invoice\Domain\Port\Service\CreateInvoiceLineServiceInterface;
use Marketplace\Component\Invoice\Domain\Port\Service\FetchMerchantOrderServiceInterface;
use Marketplace\Component\Invoice\Domain\Port\Service\ProcessMerchantOrderServiceInterface;
use Marketplace\Component\Invoice\Domain\Port\Service\ValidateInvoiceServiceInterface;
use Marketplace\Component\Invoice\Domain\Presenter\CreateInvoicePresenterInterface;
use Marketplace\Component\Invoice\Domain\UseCase\CreateInvoice\DTO\CreateInvoiceRequest;
use Marketplace\Component\Invoice\Domain\UseCase\CreateInvoice\DTO\CreateInvoiceResponse;
use Marketplace\Component\Order\Domain\Enum\MerchantOrderStatusEnum;
use Marketplace\Component\Order\Domain\Model\MerchantOrder;
use Marketplace\Component\Order\Domain\Model\OrderItem;
use Marketplace\Component\User\Domain\Port\Repository\CountryRepositoryInterface;

final class CreateInvoiceUseCase
{
    public function __construct(
        private FetchMerchantOrderServiceInterface $merchantOrderService,
        private InvoiceRepositoryInterface $invoiceRepository,
        private CreateInvoiceLineServiceInterface $createInvoiceLineService,
        private ValidateInvoiceServiceInterface $validateInvoiceService,
        private ProcessMerchantOrderServiceInterface $processMerchantOrderService,
        private CountryRepositoryInterface $countryRepository,
        private DeliverySlipRepositoryInterface $deliverySlipRepository
    ) {
    }

    public function execute(CreateInvoiceRequest $request, CreateInvoicePresenterInterface $presenter): void
    {
        $response = new CreateInvoiceResponse();

        $merchantOrder = $this->merchantOrderService->fetchMerchantsOrderByOrderId($request->merchantOrderId);
        if (!$merchantOrder instanceof MerchantOrder) {
            $response->addError('merchantOrder', 'invoice.create.merchantOrder.notFound');
            $response->merchantOrderFound = false;
            $presenter->present($response);
            return;
        }

        if ($merchantOrder->hasItems() === false) {
            $response->addError('orderItems', 'invoice.create.orderItems.notFound');
            $presenter->present($response);
            return;
        }
        $merchantOrderId = $merchantOrder->getId();
        $merchantDistantId = $merchantOrder->getMerchantId();

        if ($merchantOrderId === null) {
            throw new InvalidMerchantOrderException('The merchant order hasn\'t id or distant id');
        }

        $requestOrderItems = $request->groupParcelItemsById();

        // TODO Manage shipping order item
        $shippingOption = $this->merchantOrderService->shippingOrderItems($merchantOrderId);

        if (empty($shippingOption)) {
            $response->addError('shippingOption', 'invoice.create.shippingOption.notFound');
            $presenter->present($response);
            return;
        }

        $orderItems = $merchantOrder->computeOrderItemsValid($requestOrderItems);

        if ($orderItems === []) {
            $response->addError('orderItems', 'invoice.create.orderItems.invalid');
            $presenter->present($response);
            return;
        }

        $orderItemsToBeInvoiced = $this->orderItemsWithQuantity($orderItems, $request);

        $itemToBeInvoiced = $merchantOrder->itemToBeInvoiced($orderItemsToBeInvoiced);

        if ($itemToBeInvoiced === []) {
            $response->addError('orderItems', 'invoice.create.nothing');
            $presenter->present($response);
            return;
        }

        /** @var int $userId */
        $userId = $merchantOrder->getUserId();
        /**
         * @var MerchantOrderStatusEnum $status
         */
        $status = $merchantOrder->getStatus();
        if ($status->isConfirmed()) {
            $this->processMerchantOrderService->process($merchantOrder);
        }
        $invoice = $this->invoiceRepository->create($merchantDistantId, $userId);

        $noInvoiceYet = $merchantOrder->hasNoInvoiced();


        foreach ($itemToBeInvoiced as [$orderItem, $quantity]) {
            /** @var OrderItem $orderItem */
            $this->createInvoiceLineService->addInvoiceLine(
                $invoice->getId(),
                $orderItem->getId(),
                $quantity
            );
        }

        $deliverySlip = $this->deliverySlipRepository->findByParcelId($request->parcelId);
        if ($deliverySlip instanceof DeliverySlip) {
            $deliverySlip->setInvoiceId($invoice->getId());
            $this->deliverySlipRepository->update($deliverySlip);
        }

        if ($noInvoiceYet) { // shipping and extra fees is added only in first invoice.
            $this->createInvoiceLineService->addInvoiceShipping(
                $invoice->getId(),
                $merchantOrderId,
                $shippingOption[0],
                $this->getLocale($merchantOrder)
            );

            if ($merchantOrder->getExtraFees() > 0) {
                $this->createInvoiceLineService->addExtraFees($invoice->getId(), $merchantOrderId, $merchantOrder->getExtraFees());
            }
        }

        $this->validateInvoiceService->validate($invoice->getId());
        $response->invoice = $invoice;

        $presenter->present($response);
    }

    /**
     * @param array<array-key, OrderItem> $orderItems
     * @param CreateInvoiceRequest $request
     * @return array
     */
    private function orderItemsWithQuantity(array $orderItems, CreateInvoiceRequest $request): array
    {
        return array_map(
            function (OrderItem $orderItem) use ($request): array {
                $quantity = array_reduce(
                    $request->orderItems,
                    function (int $quantity, ParcelItem $parcelItem) use ($orderItem): int {
                        if ($parcelItem->getOrderItemId() === $orderItem->getId()) {
                            $quantity = $parcelItem->getQuantity();
                        }
                        return $quantity;
                    },
                    0
                );
                return [$orderItem, $quantity];
            },
            $orderItems
        );
    }

    private function getLocale(MerchantOrder $merchantOrder): string
    {
        $locale = "fr";
        $countryId = $merchantOrder->getShippingAddress()?->getCountryId();
        if ($countryId === null) {
            return $locale;
        }
        $country = $this->countryRepository->findByDistantId($countryId);
        if ($country !== null) {
            $locale = $country->getLocale();
        }
        return $locale;
    }
}
