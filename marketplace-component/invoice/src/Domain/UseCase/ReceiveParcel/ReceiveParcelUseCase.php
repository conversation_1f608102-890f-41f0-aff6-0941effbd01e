<?php

declare(strict_types=1);

namespace Marketplace\Component\Invoice\Domain\UseCase\ReceiveParcel;

use Marketplace\Component\Invoice\Domain\Port\Service\SyncParcelServiceInterface;
use Marketplace\Component\Invoice\Domain\Presenter\ReceiveParcelPresenterInterface;
use Marketplace\Component\Invoice\Domain\UseCase\ReceiveParcel\DTO\ReceiveParcelResponse;
use Marketplace\Component\Invoice\Domain\UseCase\ReceiveParcel\DTO\ReceiveParcelRequest;
use Marketplace\Component\Invoice\Domain\Model\Parcel;

class ReceiveParcelUseCase
{
    public function __construct(
        private SyncParcelServiceInterface $syncParcelService
    ) {
    }

    public function execute(ReceiveParcelRequest $request, ReceiveParcelPresenterInterface $presenter)
    {
        $parcels = $request->parcels;
        $izebergParcels = [];
        foreach ($parcels as $parcel) {
            $sevenDaysAgo = new \DateTimeImmutable('7 days ago');
            if ($parcel->getInTransitDate() <= $sevenDaysAgo && $parcel->getStatus() === Parcel::STATUS_IN_TRANSIT) {
                $parcel->setStatus(Parcel::STATUS_RECEIVED);
                $izebergParcels[] = $this->syncParcelService->receiveParcel($parcel);
            }
        }
        $presenter->present(new ReceiveParcelResponse($izebergParcels));
    }
}
