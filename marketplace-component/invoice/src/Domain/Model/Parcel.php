<?php

namespace Marketplace\Component\Invoice\Domain\Model;

use DateTimeImmutable;

class Parcel
{
    public const STATUS_INITIAL = "initial";
    public const STATUS_IN_TRANSIT = "in_transit";
    public const STATUS_RECEIVED = "received";
    public const STATUS_DELETED = "deleted";

    private ?int $id = null;
    /** TODO Use CarrierName ValueObject ? */
    private string $carrierName;
    private ?string $trackingNumber = null;
    private ?string $trackingUrl = null;
    private array $parcelItems = [];
    private ?DateTimeImmutable $effectiveDeliveryDate = null;
    private ?DateTimeImmutable $effectiveShippingDate = null;
    private ?DateTimeImmutable $expectedDeliveryDate = null;
    private ?DateTimeImmutable $expectedShippingDate = null;
    private ?DateTimeImmutable $inTransitDate = null;
    private ?DateTimeImmutable $customerDate = null;
    private string $status;
    private int $merchantOrderId;
    private ?int $carrierId = null;
    private bool $newlySent = false;


    /**
     * @return string
     */
    public function getCarrierName(): string
    {
        return $this->carrierName;
    }

    /**
     * @param string $carrierName
     * @return Parcel
     */
    public function setCarrierName(string $carrierName): Parcel
    {
        $this->carrierName = $carrierName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getTrackingNumber(): ?string
    {
        return $this->trackingNumber;
    }

    /**
     * @param string|null $trackingNumber
     * @return Parcel
     */
    public function setTrackingNumber(?string $trackingNumber): Parcel
    {
        $this->trackingNumber = $trackingNumber;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getTrackingUrl(): ?string
    {
        return $this->trackingUrl;
    }

    /**
     * @param string|null $trackingUrl
     * @return Parcel
     */
    public function setTrackingUrl(?string $trackingUrl): Parcel
    {
        $this->trackingUrl = $trackingUrl;
        return $this;
    }


    /**
     * @return ParcelItem[]
     */
    public function getParcelItems(): array
    {
        return $this->parcelItems;
    }

    /**
     * @param array $parcelItems
     * @return Parcel
     */
    public function setParcelItems(array $parcelItems): Parcel
    {
        $this->parcelItems = $parcelItems;
        return $this;
    }

    /**
     * @return DateTimeImmutable|null
     */
    public function getEffectiveDeliveryDate(): ?DateTimeImmutable
    {
        return $this->effectiveDeliveryDate;
    }

    /**
     * @param DateTimeImmutable|null $effectiveDeliveryDate
     * @return Parcel
     */
    public function setEffectiveDeliveryDate(?DateTimeImmutable $effectiveDeliveryDate): Parcel
    {
        $this->effectiveDeliveryDate = $effectiveDeliveryDate;
        return $this;
    }

    /**
     * @return DateTimeImmutable|null
     */
    public function getEffectiveShippingDate(): ?DateTimeImmutable
    {
        return $this->effectiveShippingDate;
    }

    /**
     * @param DateTimeImmutable|null $effectiveShippingDate
     * @return Parcel
     */
    public function setEffectiveShippingDate(?DateTimeImmutable $effectiveShippingDate): Parcel
    {
        $this->effectiveShippingDate = $effectiveShippingDate;
        return $this;
    }

    /**
     * @return DateTimeImmutable|null
     */
    public function getExpectedDeliveryDate(): ?DateTimeImmutable
    {
        return $this->expectedDeliveryDate;
    }

    /**
     * @param DateTimeImmutable|null $expectedDeliveryDate
     * @return Parcel
     */
    public function setExpectedDeliveryDate(?DateTimeImmutable $expectedDeliveryDate): Parcel
    {
        $this->expectedDeliveryDate = $expectedDeliveryDate;
        return $this;
    }

    /**
     * @return DateTimeImmutable|null
     */
    public function getExpectedShippingDate(): ?DateTimeImmutable
    {
        return $this->expectedShippingDate;
    }

    /**
     * @param DateTimeImmutable|null $expectedShippingDate
     * @return Parcel
     */
    public function setExpectedShippingDate(?DateTimeImmutable $expectedShippingDate): Parcel
    {
        $this->expectedShippingDate = $expectedShippingDate;
        return $this;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return Parcel
     */
    public function setStatus(string $status): Parcel
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return int
     */
    public function getMerchantOrderId(): int
    {
        return $this->merchantOrderId;
    }

    /**
     * @param int $merchantOrderId
     * @return Parcel
     */
    public function setMerchantOrderId(int $merchantOrderId): Parcel
    {
        $this->merchantOrderId = $merchantOrderId;
        return $this;
    }

    /**
     * @return DateTimeImmutable|null
     */
    public function getCustomerDate(): ?DateTimeImmutable
    {
        return $this->customerDate;
    }

    /**
     * @param DateTimeImmutable|null $customerDate
     * @return Parcel
     */
    public function setCustomerDate(?DateTimeImmutable $customerDate): Parcel
    {
        $this->customerDate = $customerDate;
        return $this;
    }

    public function isReceived(): bool
    {
        return $this->status === self::STATUS_RECEIVED;
    }

    public function isInTransit(): bool
    {
        return $this->status === self::STATUS_IN_TRANSIT;
    }

    public function getCarrierId(): ?int
    {
        return $this->carrierId;
    }

    public function setCarrierId(?int $carrierId): self
    {
        $this->carrierId = $carrierId;

        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return DateTimeImmutable|null
     */
    public function getInTransitDate(): ?DateTimeImmutable
    {
        return $this->inTransitDate;
    }

    /**
     * @param DateTimeImmutable|null $inTransitDate
     * @return Parcel
     */
    public function setInTransitDate(?DateTimeImmutable $inTransitDate): Parcel
    {
        $this->inTransitDate = $inTransitDate;
        return $this;
    }

    /**
     * @return bool
     */
    public function isNewlySent(): bool
    {
        return $this->newlySent;
    }

    /**
     * @param bool $newlySent
     * @return Parcel
     */
    public function setNewlySent(bool $newlySent): Parcel
    {
        $this->newlySent = $newlySent;
        return $this;
    }
}
