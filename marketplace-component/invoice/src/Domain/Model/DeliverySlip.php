<?php

namespace Marketplace\Component\Invoice\Domain\Model;

class DeliverySlip
{
    private ?int $id = null;
    private int $parcelId;
    private string $deliverySlipId;
    private ?int $invoiceId = null;

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return DeliverySlip
     */
    public function setId(?int $id): DeliverySlip
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return int
     */
    public function getParcelId(): int
    {
        return $this->parcelId;
    }

    /**
     * @param int $parcelId
     * @return DeliverySlip
     */
    public function setParcelId(int $parcelId): DeliverySlip
    {
        $this->parcelId = $parcelId;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliverySlipId(): string
    {
        return $this->deliverySlipId;
    }

    /**
     * @param string $deliverySlipId
     * @return DeliverySlip
     */
    public function setDeliverySlipId(string $deliverySlipId): DeliverySlip
    {
        $this->deliverySlipId = $deliverySlipId;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getInvoiceId(): ?int
    {
        return $this->invoiceId;
    }

    /**
     * @param int|null $invoiceId
     * @return DeliverySlip
     */
    public function setInvoiceId(?int $invoiceId): DeliverySlip
    {
        $this->invoiceId = $invoiceId;
        return $this;
    }
}
