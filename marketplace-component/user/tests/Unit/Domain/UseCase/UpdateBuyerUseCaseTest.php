<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Tests\Domain\UseCase;

use Generator;
use Marketplace\Component\User\Domain\Error\Error;
use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\Country;
use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Presenter\UpdateBuyerPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\UpdateBuyer\UpdateBuyerUseCase;
use Marketplace\Component\User\Domain\UseCase\UpdateBuyer\DTO\UpdateBuyerRequest;
use Marketplace\Component\User\Domain\UseCase\UpdateBuyer\DTO\UpdateBuyerResponse;
use Marketplace\Component\User\Tests\Unit\Fake\CompanyRepositoryFake;
use PHPUnit\Framework\TestCase;

final class UpdateBuyerUseCaseTest extends TestCase
{
    private UpdateBuyerUseCase $useCase;

    private UpdateBuyerPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements UpdateBuyerPresenterInterface {
            public UpdateBuyerResponse $response;

            public function present(UpdateBuyerResponse $response): void
            {
                $this->response = $response;
            }
        };

        $this->useCase = new UpdateBuyerUseCase(new CompanyRepositoryFake());
    }

    public function testBuyerUpdateSuccessful(): void
    {
        $request = $this->makeUpdateBuyerRequest(externalId: 'external+id');

        $this->useCase->execute($request, $this->presenter);

        $this->assertInstanceOf(UpdateBuyerResponse::class, $this->presenter->response);
        $this->assertInstanceOf(Company::class, $this->presenter->response->getCompany());
        $this->assertEquals(2, $this->presenter->response->getCompany()->getId());
        $this->assertEquals('company+update', $this->presenter->response->getCompany()->getName());
        $this->assertEquals('external+id', $this->presenter->response->getCompany()->getExternalId());
        $this->assertEquals('NEWFRVAT', $this->presenter->response->getCompany()->getVatNumber());
        $this->assertNotEmpty($this->presenter->response->getCompany()->getUsers());
        $this->assertContainsOnlyInstancesOf(User::class, $this->presenter->response->getCompany()->getUsers());
    }

    public function testBuyerUpdateWithCompanyDoesNotExit(): void
    {
        $request = $this->makeUpdateBuyerRequest(0);

        $this->useCase->execute($request, $this->presenter);

        $this->assertTrue($this->presenter->response->getNotification()->hasError());
        $this->assertEquals(
            'Company unknown',
            $this->presenter->response->getNotification()->getErrors()[0]->getMessage()
        );
    }

    /**
     * @dataProvider updateDataProvider
     */
    public function testBuyerUpdateWithFailRequest(
        string $name,
        string $accountingEmail,
        string $vatNumber,
        Country $country,
        string $accountingPhone,
        string $identification,
        string $errorMessage,
    ): void {

        $request = $this->makeUpdateBuyerRequest(
            2,
            $name,
            $accountingEmail,
            $vatNumber,
            $country,
            $accountingPhone,
            $identification,
        );

        $this->useCase->execute($request, $this->presenter);

        $this->assertTrue($this->presenter->response->getNotification()->hasError());
        $this->assertEquals(
            [$errorMessage],
            array_map(
                fn (Error $error) => $error->getMessage(),
                $this->presenter->response->getNotification()->getErrors()
            )
        );
    }

    public function updateDataProvider(): Generator
    {
        yield [
            '',
            '<EMAIL>',
            'NEWFRVAT',
            (new Country())->setCode('france'),
            '**********',
            '*********',
            'validator.mandatory'
        ];
        yield [
            'companyName',
            '',
            'NEWFRVAT',
            (new Country())->setCode('france'),
            '**********',
            '*********',
            'validator.mandatory'
        ];
        yield [
            'companyName',
            'fail',
            'NEWFRVAT',
            (new Country())->setCode('france'),
            '**********',
            '*********',
            'validator.email_invalid'
        ];
        yield [
            'companyName',
            '<EMAIL>',
            '',
            (new Country())->setCode('france'),
            '**********',
            '*********',
            'validator.mandatory'
        ];
        yield [
            'companyName',
            '<EMAIL>',
            'NEWFRVAT',
            (new Country())->setCode(''),
            '**********',
            '*********',
            'validator.mandatory'
        ];
    }

    private function makeUpdateBuyerRequest(
        int $id = 2,
        string $name = 'company+update',
        string $accountingEmail = '<EMAIL>',
        string $vatNumber = 'NEWFRVAT',
        Country $country = null,
        string $accountingPhone = '**********',
        string $identification = '*********',
        string $externalId = 'idext123'
    ): UpdateBuyerRequest {
        if ($country === null) {
            $country = (new Country())->setCode('france');
        }
        $request = new UpdateBuyerRequest();
        $request->setId($id);
        $request->setName($name);
        $request->setAccountingEmail($accountingEmail);
        $request->setVatNumber($vatNumber);
        $request->setCountry($country);
        $request->setAccountingPhone($accountingPhone);
        $request->setIdentification($identification);
        $request->setExternalId($externalId);

        return $request;
    }
}
