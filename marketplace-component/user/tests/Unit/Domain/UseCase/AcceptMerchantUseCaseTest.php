<?php

namespace Marketplace\Component\User\Domain\UseCase\AcceptMerchant;

use Generator;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\TranslationInterface;
use Marketplace\Component\Mail\Domain\Port\Service\EmailServiceInterface;
use Marketplace\Component\User\Domain\Model\Merchant;
use Marketplace\Component\User\Domain\Port\Service\MerchantServiceInterface;
use Marketplace\Component\User\Domain\Presenter\AcceptMerchantPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\AcceptMerchant\DTO\AcceptMerchantRequest;
use Marketplace\Component\User\Domain\UseCase\AcceptMerchant\DTO\AcceptMerchantResponse;
use Marketplace\Component\User\Infrastructure\Adapter\Repository\MerchantRepository;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;

class AcceptMerchantUseCaseTest extends TestCase
{
    private AcceptMerchantUseCase $useCase;

    private AcceptMerchantPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements AcceptMerchantPresenterInterface {
            public AcceptMerchantResponse $response;

            public function present(AcceptMerchantResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        $merchantRepository = $prophet->prophesize(MerchantRepository::class);
        $merchantRepository->findById(Argument::type('int'))->will(function ($args) {
            $id = $args[0];
            if ($id !== 1 && $id !== 2 && $id !== 3) {
                return null;
            }
            $merchant = new Merchant();
            $merchant->setId($id);
            if ($id === 1) {
                $merchant->setEmail('<EMAIL>');
            }

            return $merchant;
        });
        /** @var EmailServiceInterface|ObjectProphecy $emailServiceInterface */
        $emailServiceInterface = $prophet->prophesize(EmailServiceInterface::class);
        $emailServiceInterface->send(
            Argument::type('array'),
            Argument::type('array'),
            Argument::type('string'),
            Argument::type('string'),
            Argument::type('array')
        )->willReturn(true);
        $emailServiceInterface->getNoReplyUser()->willReturn([['<EMAIL>', 'no reply']]);

        $merchantServiceInterface = $prophet->prophesize(MerchantServiceInterface::class);
        $merchantServiceInterface->acceptMerchant(Argument::type(Merchant::class))->will(function ($args) {
            if ($args[0]->getId() === 1 || $args[0]->getId() === 3) {
                return true;
            }
            return false;
        });

        $translation = $prophet->prophesize(TranslationInterface::class);
        $translation->trans(Argument::type('string'))->will(function ($args) {
            return $args[0];
        });
        $this->useCase = new AcceptMerchantUseCase(
            $merchantRepository->reveal(),
            $emailServiceInterface->reveal(),
            $merchantServiceInterface->reveal(),
            $translation->reveal(),
            'siteUrl'
        );
    }

    /**
     * @param AcceptMerchantRequest $request
     * @param AcceptMerchantResponse $expected
     * @dataProvider provideExecute
     */
    public function testExecute(AcceptMerchantRequest $request, AcceptMerchantResponse $expected)
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($expected, $this->presenter->response);
    }

    public function provideExecute(): Generator
    {
        //OK
        $request = new AcceptMerchantRequest(1);
        $response = new AcceptMerchantResponse(true, null);
        yield [$request, $response];

        //OK but no email
        $request = new AcceptMerchantRequest(3);
        $response = new AcceptMerchantResponse(true, 'accept_merchant.no_email');
        yield [$request, $response];

        //OK but no email
        $request = new AcceptMerchantRequest(3);
        $response = new AcceptMerchantResponse(true, 'accept_merchant.no_email');
        yield [$request, $response];

        //merchant does not exist
        $request = new AcceptMerchantRequest(5);
        $response = new AcceptMerchantResponse(false, "accept_merchant.no_merchant");
        yield [$request, $response];

        //izberg Merchant creation issues
        $request = new AcceptMerchantRequest(2);
        $response = new AcceptMerchantResponse(false, "accept_merchant.error_register");
        yield [$request, $response];
    }
}
