<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Tests\Domain\UseCase;

use Marketplace\Component\User\Domain\Model\CompanyStatus;
use Marketplace\Component\User\Domain\Presenter\DeleteBuyerPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\DeleteBuyer\DeleteBuyerUseCase;
use Marketplace\Component\User\Domain\UseCase\DeleteBuyer\DTO\DeleteBuyerRequest;
use Marketplace\Component\User\Domain\UseCase\DeleteBuyer\DTO\DeleteBuyerResponse;
use Marketplace\Component\User\Tests\Unit\Fake\CompanyRepositoryFake;
use Marketplace\Component\User\Tests\Unit\Fake\UserRepositoryFake;
use PHPUnit\Framework\TestCase;

class DeleteBuyerUseCaseTest extends TestCase
{
    private DeleteBuyerUseCase $useCase;

    private DeleteBuyerPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements DeleteBuyerPresenterInterface {
            public DeleteBuyerResponse $response;

            public function present(DeleteBuyerResponse $response): void
            {
                $this->response = $response;
            }
        };

        $this->useCase = new DeleteBuyerUseCase(new CompanyRepositoryFake(), new UserRepositoryFake());
    }

    public function testDeleteBuyerSuccessful(): void
    {
        $request = new DeleteBuyerRequest(
            2,
        );

        $this->useCase->execute($request, $this->presenter);

        $this->assertInstanceOf(DeleteBuyerResponse::class, $this->presenter->response);
        $this->assertFalse($this->presenter->response->companyDeleted);
        $this->assertEquals(CompanyStatus::STATUS_DELETED, $this->presenter->response->newStatus);
    }

    public function testDeleteBuyerCompanyDoesntExist(): void
    {
        $request = new DeleteBuyerRequest(
            0,
        );

        $this->useCase->execute($request, $this->presenter);
        $this->assertInstanceOf(DeleteBuyerResponse::class, $this->presenter->response);
        $this->assertFalse($this->presenter->response->companyDeleted);
        $this->assertFalse($this->presenter->response->companyExist);
    }

    public function testDeleteBuyerCompanyAlreadyDeleted(): void
    {
        $request = new DeleteBuyerRequest(
            10,
        );

        $this->useCase->execute($request, $this->presenter);
        $this->assertInstanceOf(DeleteBuyerResponse::class, $this->presenter->response);
        $this->assertTrue($this->presenter->response->companyDeleted);
    }
}
