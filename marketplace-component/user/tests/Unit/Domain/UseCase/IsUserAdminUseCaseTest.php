<?php

namespace Marketplace\Component\User\Domain\UseCase\IsUserAdmin;

use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use Marketplace\Component\User\Domain\Presenter\IsUserAdminPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\IsUserAdmin\DTO\IsUserAdminRequest;
use Marketplace\Component\User\Domain\UseCase\IsUserAdmin\DTO\IsUserAdminResponse;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

class IsUserAdminUseCaseTest extends TestCase
{
    private IsUserAdminUseCase $useCase;
    private IsUserAdminPresenterInterface $presenter;


    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements IsUserAdminPresenterInterface {
            public IsUserAdminResponse $response;

            public function present(IsUserAdminResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();

        $userRepository = $prophet->prophesize(UserRepositoryInterface::class);
        $userRepository->findById(Argument::type('int'))->will(function ($args) {
            $userId = $args[0];
            if ($userId === 1) {
                return null;
            }
            if ($userId === 2) {
                return (new User())->setId(2);
            }
            return (new User())->setId(3);
        });

        $userRepository->isUserBuyerAdmin(Argument::type(User::class))->will(function ($args) {
            $user = $args[0];
            if ($user->getId() == 2) {
                return true;
            }
            return false;
        });


        $this->useCase = new IsUserAdminUseCase(
            $userRepository->reveal()
        );
    }

    /**
     * @param IsUserAdminRequest $request
     * @param IsUserAdminResponse $response
     * @dataProvider provideExecute
     */
    public function testExecute(IsUserAdminRequest $request, IsUserAdminResponse $response)
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($response, $this->presenter->response);
    }

    public function provideExecute()
    {
        // User doesn't exist
        $request = new IsUserAdminRequest(1);
        $response = new IsUserAdminResponse();
        yield [$request, $response];

        // User is Admin
        $request = new IsUserAdminRequest(2);
        $response = new IsUserAdminResponse();
        $response->setIsAdmin(true);
        yield [$request, $response];

        // User is not Admin
        $request = new IsUserAdminRequest(3);
        $response = new IsUserAdminResponse();
        yield [$request, $response];
    }
}
