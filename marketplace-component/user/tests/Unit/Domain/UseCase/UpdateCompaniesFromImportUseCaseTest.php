<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Tests\Domain\UseCase;

use Marketplace\Component\User\Domain\Model\Address;
use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\ImportCompanyData;
use Marketplace\Component\User\Domain\Port\Repository\AddressRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Service\ImportCompaniesServiceInterface;
use Marketplace\Component\User\Domain\Port\Service\UpdateCompaniesDataServiceInterface;
use Marketplace\Component\User\Domain\Presenter\UpdateCompaniesFromImportPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\UpdateCompaniesFromExport\DTO\UpdateCompaniesFromImportResponse;
use Marketplace\Component\User\Domain\UseCase\UpdateCompaniesFromExport\UpdateCompaniesFromImportUseCase;
use Marketplace\Component\User\Tests\Unit\Fake\CompanyRepositoryFake;
use PHPUnit\Framework\TestCase;

final class UpdateCompaniesFromImportUseCaseTest extends TestCase
{
    private UpdateCompaniesFromImportPresenterInterface $presenter;

    private UpdateCompaniesFromImportUseCase $useCase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements UpdateCompaniesFromImportPresenterInterface {
            public UpdateCompaniesFromImportResponse $response;
            public function present(UpdateCompaniesFromImportResponse $response): void
            {
                $this->response = $response;
            }
        };

        $this->useCase = new UpdateCompaniesFromImportUseCase(
            $this->makeImportCompaniesService(),
            new CompanyRepositoryFake(),
            $this->makeAddressRepository(),
            $this->updateCompaniesDataService(),
        );
    }

    public function testSuccessfulUpdateCompanies(): void
    {
        $this->useCase->execute($this->presenter);

        $response = new UpdateCompaniesFromImportResponse();
        // 2 errors : company not found and billing address not found
        $response->getNotification()->addError('company', 'Company does not exist with 0 id');
        $response->getNotification()->addError('billingAddress', 'Billing address of this company 2 does not exist');

        $this->assertEquals($response, $this->presenter->response);
    }

    private function makeImportCompaniesService(): ImportCompaniesServiceInterface
    {
        return new class implements ImportCompaniesServiceInterface {
            /**
             * @inheritDoc
             */
            public function import(): array
            {
                return [
                    $this->makeImportCompanyData(),
                    $this->makeImportCompanyData(0),
                    $this->makeImportCompanyData(2),
                    $this->makeImportCompanyData(4),
                ];
            }

            private function makeImportCompanyData(int $companyId = 1): ImportCompanyData
            {
                return new ImportCompanyData(
                    $companyId,
                    1,
                    sprintf('company%d+update', $companyId),
                    'billing address',
                    'complement address',
                    '56800',
                    'Lannion',
                    sprintf('<EMAIL>', $companyId),
                    '0989786787',
                    'identification'
                );
            }
        };
    }

    public function updateCompaniesDataService(): UpdateCompaniesDataServiceInterface
    {
        return new class implements UpdateCompaniesDataServiceInterface {
            public function updateCompanyData(Company $company, ImportCompanyData $importCompany): void
            {
                // TODO: Implement updateCompanyData() method.
            }

            public function updateBillingAddress(Address $billingAddress, ImportCompanyData $importCompany): void
            {
                // TODO: Implement updateBillingAddress() method.
            }
        };
    }

    private function makeAddressRepository(): AddressRepositoryInterface
    {
        return new class implements AddressRepositoryInterface {
            public function findBillingAddress(int $companyId): ?Address
            {
                if ($companyId === 2) {
                    return null;
                }

                return (new Address(
                    Address::BILLING,
                    'Billing address company',
                    'test address',
                    null,
                    '75000',
                    'Paris',
                    1,
                    $companyId,
                    "france",
                    "france"
                ))
                    ->setId(1)
                    ->setCountryName('France')
                    ;
            }

            public function findShippingAddresses(
                int $companyId,
                bool $active = true,
                bool $filterWithRegion = true
            ): array {
                return [];
            }

            public function findDefaultShippingAddress(int $companyId): ?Address
            {
                // TODO: Implement findDefaultShippingAddress() method.
                return null;
            }

            public function findById(int $addressId): ?Address
            {
                // TODO: Implement findById() method.
                return null;
            }

            public function isBillingAddressExist(int $companyId): bool
            {
                // TODO: Implement isBillingAddressExist() method.
                return true;
            }

            public function save(Address $address): Address
            {
                // TODO: Implement save() method.
                return new Address();
            }

            public function delete(int $addressId): bool
            {
                // TODO: Implement delete() method.
                return false;
            }

            public function createPickAndCollectAddressIfNotExist(Address $address, int $userDistantId): int
            {
                // TODO: Implement createPickAndCollectAddressIfNotExist() method.
            }

            public function findDistantAddressById(int $addressId): ?Address
            {
                // TODO: Implement findDistantAddressById() method.
            }

            /**
             * @return array
             */
            public function findAllActiveAddresses(): array
            {
                // TODO: Implement findAllActiveAddresses() method.
                return [];
            }

            /**
             * @param Address $address
             * @return bool
             */
            public function updateWithoutSync(Address $address): bool
            {
                // TODO: Implement updateWithoutSync() method.
                return true;
            }
        };
    }
}
