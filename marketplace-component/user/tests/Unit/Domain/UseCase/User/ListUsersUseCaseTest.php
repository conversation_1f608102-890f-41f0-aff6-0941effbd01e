<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\UseCase\User;

use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Presenter\User\ListUsersPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\User\ListUsers\DTO\ListUsersRequest;
use Marketplace\Component\User\Domain\UseCase\User\ListUsers\DTO\ListUsersResponse;
use Marketplace\Component\User\Domain\UseCase\User\ListUsers\ListUsersUseCase;
use Marketplace\Component\User\Tests\Unit\Fake\UserRepositoryFake;
use PHPUnit\Framework\TestCase;

/**
 * @coversDefaultClass \Marketplace\Component\User\Domain\UseCase\User\ListUsers\ListUsersUseCase
 */
final class ListUsersUseCaseTest extends TestCase
{
    private ListUsersPresenterInterface $presenter;

    private ListUsersUseCase $useCase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements ListUsersPresenterInterface {
            public ListUsersResponse $response;
            public function present(ListUsersResponse $response): void
            {
                $this->response = $response;
            }
        };
        $this->useCase = new ListUsersUseCase(new UserRepositoryFake());
    }

    /**
     * @covers ::execute
     */
    public function testExecute(): void
    {
        $request = new ListUsersRequest(
            1,
            1,
            5,
        );

        $this->useCase->execute($request, $this->presenter);
        $response = $this->presenter->response;
        $user = (new User())
            ->setId(1)
            ->setCreatedAt(new \DateTimeImmutable('2021-09-28'));

        $expected = new ListUsersResponse(
            1,
            5,
        );
        $expected->setUsers([$user]);
        $this->assertEquals($expected, $response);
    }

    public function testListUsersWhenUserDoesntExist(): void
    {
        $request = new ListUsersRequest(
            0,
        );

        $this->useCase->execute($request, $this->presenter);
        $this->assertInstanceOf(ListUsersResponse::class, $this->presenter->response);
        $this->assertFalse($this->presenter->response->isUserExist());
    }

    public function testListUsersWhenCompanyDoesntExist(): void
    {
        $request = new ListUsersRequest(
            10,
        );

        $this->useCase->execute($request, $this->presenter);
        $this->assertInstanceOf(ListUsersResponse::class, $this->presenter->response);
        $this->assertFalse($this->presenter->response->isCompanyExist());
    }
}
