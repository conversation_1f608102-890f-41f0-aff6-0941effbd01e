<?php

namespace Marketplace\Component\User\Domain\UseCase\User\AdminEditUser;

use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use Marketplace\Component\User\Domain\Presenter\User\AdminEditUserPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\User\AdminEditUser\DTO\AdminEditUserRequest;
use Marketplace\Component\User\Domain\UseCase\User\AdminEditUser\DTO\AdminEditUserResponse;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

class EditUserUseCaseTest extends TestCase
{
    private EditUserUseCase $useCase;

    private AdminEditUserPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements AdminEditUserPresenterInterface {
            public AdminEditUserResponse $response;

            public function present(AdminEditUserResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        $userRepository = $prophet->prophesize(UserRepositoryInterface::class);
        $userRepository->findById(Argument::type('int'))->will(function ($args) {
            $userId = $args[0];
            if ($userId === 1) {
                return null;
            }
            return (new User())->setCreatedAt(new \DateTimeImmutable('12/12/12'));
        });

        $this->useCase = new EditUserUseCase(
            $userRepository->reveal()
        );
    }

    /**
     * @param AdminEditUserRequest $request
     * @param AdminEditUserResponse $expected
     * @dataProvider provideExecute
     */
    public function testExecute(AdminEditUserRequest $request, AdminEditUserResponse $expected)
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($expected, $this->presenter->response);
    }

    public function provideExecute()
    {
        //validate error
        $request = new AdminEditUserRequest();
        $response = new AdminEditUserResponse();
        $notification = $response->getNotification();
        $notification->addError('firstName', 'user_account.profile.validate.required');
        $notification->addError('lastName', 'user_account.profile.validate.required');
        $notification->addError('role', 'user_account.profile.validate.required');
        yield [$request, $response];
        //user doesn't exist
        $request = new AdminEditUserRequest();
        $request->setLastname('toto');
        $request->setFirstname('tutu');
        $request->setId(1);
        $request->setRole('admin');
        $response = new AdminEditUserResponse();
        $notification = $response->getNotification();
        $notification->addError('userId', 'user.not_found');
        yield [$request, $response];
        //ok
        $request = new AdminEditUserRequest();
        $request->setLastname('toto');
        $request->setFirstname('tutu');
        $request->setId(2);
        $request->setRole('admin');
        $response = new AdminEditUserResponse();
        $response->setSucceed(true);
        yield [$request, $response];
    }
}
