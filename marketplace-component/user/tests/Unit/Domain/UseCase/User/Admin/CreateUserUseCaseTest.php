<?php

namespace Marketplace\Component\User\Domain\UseCase\User\AdminCreateUser;

use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Service\PasswordHashInterface;
use Marketplace\Component\User\Domain\Port\Service\ResetPasswordInterface;
use Marketplace\Component\User\Domain\Presenter\User\AdminCreateUserPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\User\AdminCreateUser\DTO\CreateUserRequest;
use Marketplace\Component\User\Domain\UseCase\User\AdminCreateUser\DTO\CreateUserResponse;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;

class CreateUserUseCaseTest extends TestCase
{
    private CreateUserUseCase $useCase;

    private AdminCreateUserPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements AdminCreateUserPresenterInterface {
            public CreateUserResponse $response;

            public function present(CreateUserResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        $userRepository = $prophet->prophesize(UserRepositoryInterface::class);
        $userRepository->findByEmail(Argument::type('string'))->will(function ($args) {
            $userId = $args[0];
            if ($userId === '<EMAIL>') {
                return null;
            }
            return (new User())->setCreatedAt(new \DateTimeImmutable('12/12/12'));
        });

        $companyRepository = $prophet->prophesize(CompanyRepositoryInterface::class);
        $companyRepository->getCompanyById(Argument::type('int'))->will(function ($args) {
            $companyId = $args[0];
            if ($companyId === 1) {
                return null;
            }
            return (new Company())->setCreatedAt(new \DateTimeImmutable('12/12/12'));
        });
        $resetPassword = $prophet->prophesize(ResetPasswordInterface::class);
        $resetPassword->resetPassword(Argument::type(User::class))->willReturn(true);
        $passwordHash = $prophet->prophesize(PasswordHashInterface::class);
        $passwordHash->hash(Argument::type('string'))->willReturn('toto');
        $this->useCase = new CreateUserUseCase(
            $userRepository->reveal(),
            $resetPassword->reveal(),
            $companyRepository->reveal(),
            $passwordHash->reveal()
        );
    }

    /**
     * @param CreateUserRequest $request
     * @param CreateUserResponse $expected
     * @dataProvider provideExecute
     */
    public function testExecute(CreateUserRequest $request, CreateUserResponse $expected)
    {
        $this->useCase->execute($request, $this->presenter);
        $userResponse = $this->presenter->response->getUser();
        if ($userResponse instanceof User) {
            $expected->getUser()->setCreatedAt($userResponse->getCreatedAt());
        }
        $this->assertEquals($expected, $this->presenter->response);
    }

    public function provideExecute()
    {
        //wrong request
        $request = new CreateUserRequest();
        $expected = new CreateUserResponse();
        $expected->getNotification()
            ->addError('firstName', 'user_account.profile.validate.required')
            ->addError('lastName', 'user_account.profile.validate.required')
            ->addError('email', 'user_account.profile.validate.required')
            ->addError('civility', 'user_account.profile.validate.required')
            ->addError('email', 'user_account.profile.validate.email.malformed')
            ->addError('role', 'user_account.profile.validate.required');
        yield [$request, $expected];
        //malformed email
        $request = new CreateUserRequest();
        $request->setLastname('lastname');
        $request->setFirstname('firstname');
        $request->setRole('USER');
        $request->setCivility('mr');
        $request->setEmail('toto');
        $request->setCompanyId(5);
        $expected = new CreateUserResponse();
        $expected->getNotification()
            ->addError('email', 'user_account.profile.validate.email.malformed');
        yield [$request, $expected];
        //unknown user
        $request = new CreateUserRequest();
        $request->setLastname('lastname');
        $request->setFirstname('firstname');
        $request->setCivility('mr');
        $request->setRole('USER');
        $request->setEmail('<EMAIL>');
        $request->setCompanyId(5);
        $expected = new CreateUserResponse();
        $expected->getNotification()
            ->addError('userId', 'user.email.can-not-create');
        yield [$request, $expected];
        //nullable company
        $request = new CreateUserRequest();
        $request->setLastname('lastname');
        $request->setFirstname('firstname');
        $request->setCivility('mr');
        $request->setRole('USER');
        $request->setEmail('<EMAIL>');
        $request->setCompanyId(null);
        $expected = new CreateUserResponse();
        $expected->getNotification()
            ->addError('companyId', 'user.company.does-not-exist');
        yield [$request, $expected];
        //unkown company
        $request = new CreateUserRequest();
        $request->setLastname('lastname');
        $request->setFirstname('firstname');
        $request->setCivility('mr');
        $request->setRole('USER');
        $request->setEmail('<EMAIL>');
        $request->setCompanyId(1);
        $expected = new CreateUserResponse();
        $expected->getNotification()
            ->addError('companyId', 'user.company.does-not-exist');
        yield [$request, $expected];
        //ok
        $request = new CreateUserRequest();
        $request->setLastname('lastname');
        $request->setFirstname('firstname');
        $request->setCivility('mr');
        $request->setRole('USER');
        $request->setEmail('<EMAIL>');
        $request->setCompanyId(5);
        $expected = new CreateUserResponse();
        $user = new User();
        $user->setEmail('<EMAIL>');
        $user->setMainRole('USER');
        $user->setCivility('mr');
        $user->setPassword('toto');
        $user->setCompany((new Company())->setCreatedAt(new \DateTimeImmutable('12/12/12')));
        $user->setFirstname('firstname');
        $user->setLastname('lastname');
        $user->setCreatedAt(new \DateTimeImmutable('12/12/12'));
        $expected->setUser($user);
        $expected->setSucceed(true);
        yield [$request, $expected];
    }
}
