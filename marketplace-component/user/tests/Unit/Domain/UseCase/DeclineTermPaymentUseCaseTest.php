<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Tests\Domain\UseCase;

use Generator;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CurrencyServiceInterface;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetLocaleInterface;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\TranslationInterface;
use Marketplace\Component\Mail\Domain\Port\Service\EmailServiceInterface;
use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\Country;
use Marketplace\Component\User\Domain\Model\TermPayment;
use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\TermPaymentRepositoryInterface;
use Marketplace\Component\User\Domain\Presenter\DeclineTermPaymentPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\DeclineTermPayment\DeclineTermPaymentUseCase;
use Marketplace\Component\User\Domain\UseCase\DeclineTermPayment\DTO\DeclineTermPaymentRequest;
use Marketplace\Component\User\Domain\UseCase\DeclineTermPayment\DTO\DeclineTermPaymentResponse;
use Marketplace\Component\User\Tests\Unit\Fake\UserRepositoryFake;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;

final class DeclineTermPaymentUseCaseTest extends TestCase
{
    private DeclineTermPaymentUseCase $useCase;

    private DeclineTermPaymentPresenterInterface $presenter;

    private array $scenarioData = [];

    protected function setUp(): void
    {
        parent::setUp();

        $this->buildScenarioData();

        $this->presenter = new class implements DeclineTermPaymentPresenterInterface {
            public DeclineTermPaymentResponse $response;
            public function present(DeclineTermPaymentResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        $getLocale = $prophet->prophesize(GetLocaleInterface::class);
        $getLocale->getLocale()->willReturn('fr');
        /** @var CompanyRepositoryInterface|ObjectProphecy $companyRepository */
        $companyRepository = $prophet->prophesize(CompanyRepositoryInterface::class);
        $companyRepository->getCompanyById(Argument::type('int'))
            ->willReturn(
                (new Company())
                    ->setId(1)
                    ->setName('companyName')
                    ->setCountry(
                        (new Country())
                            ->setId(2)
                            ->setCode('france')
                            ->setLocale('fr')
                            ->setIzbCountryCode('FR')
                            ->setRegionCode('france')
                    )
            );

        /** @var TranslationInterface|ObjectProphecy $translation */
        $translation = $prophet->prophesize(TranslationInterface::class);
        $translation->trans(Argument::type('string'))->will(function ($args) {
            return $args[0];
        });

        $currencyService = $prophet->prophesize(CurrencyServiceInterface::class);
        $currencyService->getCurrencyFromRegion(Argument::type('string'))->will(function ($args) {
            if ($args[0] == 'united_kingdom') {
                return 'GBP';
            }
            return 'EUR';
        });

        $this->useCase = new DeclineTermPaymentUseCase(
            termPaymentRepository: $this->makeTermPaymentRepository(),
            companyRepository: $companyRepository->reveal(),
            emailService: $this->createMock(EmailServiceInterface::class),
            userRepository: new UserRepositoryFake(),
            translation: $translation->reveal(),
            currencyService: $currencyService->reveal()
        );
    }

    /**
     * @dataProvider declineTermPaymentProvider
     *
     * @param DeclineTermPaymentRequest $request
     * @param DeclineTermPaymentResponse $expected
     */
    public function testExecute(DeclineTermPaymentRequest $request, DeclineTermPaymentResponse $expected): void
    {
        $this->useCase->execute($request, $this->presenter);

        $this->assertEquals($expected, $this->presenter->response);
    }

    public function declineTermPaymentProvider(): Generator
    {
        $this->buildScenarioData();

        // Successful set status
        $response = new DeclineTermPaymentResponse();
        $response->success = true;

        yield [new DeclineTermPaymentRequest(message: 'Decline message', companyId: 1), $response];

        // Message is blank
        $response = new DeclineTermPaymentResponse();
        $response->success = false;
        $response->errorMessage = 'term_payment.decline.message.mandatory';

        yield [new DeclineTermPaymentRequest(message: '', companyId: 1), $response];

        // Length message to short
        $response = new DeclineTermPaymentResponse();
        $response->success = false;
        $response->errorMessage = 'term_payment.decline.message.short';

        yield [new DeclineTermPaymentRequest(message: 'az', companyId: 1), $response];

        // Delcine a term payment already in the REJECTED status
        $response = new DeclineTermPaymentResponse();
        $response->success = false;
        $response->errorMessage = 'term_payment.decline.message.already_rejected';

        yield [new DeclineTermPaymentRequest(message: 'message of term payment', companyId: 2), $response];
    }

    private function makeTermPaymentRepository(): TermPaymentRepositoryInterface
    {
        return new class ($this->scenarioData) implements TermPaymentRepositoryInterface {
            public function __construct(private array $scenario = [])
            {
            }

            /**
             * @inheritDoc
             */
            public function hasTermPaymentRequest(int $companyId): bool
            {
                return $companyId !== 1;
            }

            /**
             * @inheritDoc
             */
            public function requestTermPayment(int $companyId): void
            {
                // TODO: Implement requestTermPayment() method.
            }

            public function getTermPaymentOfCompany(int $companyId): ?TermPayment
            {
                return $this->scenario[$companyId]['termPayment'] ?? null;
            }

            public function update(TermPayment $termPayment): void
            {
                // TODO: Implement update() method.
            }

            public function acceptTermPayment(int $companyId, int $threshold, string $type): void
            {
                // TODO: Implement acceptTermPayment() method.
            }

            public function hasAcceptedOrRefusedTermPayment(int $companyId): bool
            {
                // TODO: Implement hasAcceptedOrRefusedTermPayment() method.
            }
        };
    }

    private function buildScenarioData(): void
    {
        // Term payment with requested status
        $this->scenarioData[1] = [
            'termPayment' => (new TermPayment())->setId(1)->setStatus(TermPayment::TERM_PAYMENT_REQUESTED),
        ];

        // Term payment with already rejected status
        $this->scenarioData[2] = [
            'termPayment' => (new TermPayment())->setId(1)->setStatus(TermPayment::TERM_PAYMENT_REJECTED),
        ];
    }
}
