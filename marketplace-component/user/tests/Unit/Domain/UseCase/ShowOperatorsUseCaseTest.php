<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Tests\Domain\UseCase;

use Marketplace\Component\User\Domain\Model\Operator;
use Marketplace\Component\User\Domain\Presenter\ShowOperatorsPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\ShowOperators\DTO\ShowOperatorsResponse;
use Marketplace\Component\User\Domain\UseCase\ShowOperators\ShowOperatorsUseCase;
use Marketplace\Component\User\Tests\Unit\Fake\UserRepositoryFake;
use PHPUnit\Framework\TestCase;

/**
 * @coversDefaultClass \Marketplace\Component\User\Domain\UseCase\ShowOperators\ShowOperatorsUseCase
 */
final class ShowOperatorsUseCaseTest extends TestCase
{
    private ShowOperatorsPresenterInterface $presenter;

    private ShowOperatorsUseCase $useCase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements ShowOperatorsPresenterInterface {
            public ShowOperatorsResponse $response;
            public function present(ShowOperatorsResponse $response): void
            {
                $this->response = $response;
            }
        };
        $this->useCase = new ShowOperatorsUseCase(new UserRepositoryFake());
    }

    /**
     * @covers ::execute
     */
    public function testExecute(): void
    {
        $this->useCase->execute($this->presenter);

        $this->assertInstanceOf(ShowOperatorsResponse::class, $this->presenter->response);
        $this->assertCount(1, $this->presenter->response->operators);
        $this->assertContainsOnlyInstancesOf(Operator::class, $this->presenter->response->operators);
    }
}
