<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Tests\Domain\UseCase;

use Generator;
use Marketplace\Component\User\Domain\Presenter\ResetPasswordPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\ResetPassword\DTO\ResetPasswordRequest;
use Marketplace\Component\User\Domain\UseCase\ResetPassword\ResetPasswordUseCase;
use Marketplace\Component\User\Domain\UseCase\ResetPassword\DTO\ResetPasswordResponse;
use Marketplace\Component\User\Tests\Unit\Fake\PasswordHashFake;
use Marketplace\Component\User\Tests\Unit\Fake\UserRepositoryFake;
use PHPUnit\Framework\TestCase;

final class ResetPasswordUseCaseTest extends TestCase
{
    private ResetPasswordUseCase $useCase;

    private ResetPasswordPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements ResetPasswordPresenterInterface {
            public ResetPasswordResponse $response;
            public function present(ResetPasswordResponse $response): void
            {
                $this->response = $response;
            }
        };

        $this->useCase = new ResetPasswordUseCase(new UserRepositoryFake(), new PasswordHashFake());
    }

    /**
     * @dataProvider resetPasswordProvider
     *
     * @param ResetPasswordRequest $request
     * @param ResetPasswordResponse $expected
     */
    public function testExecute(ResetPasswordRequest $request, ResetPasswordResponse $expected): void
    {
        $this->useCase->execute($request, $this->presenter);

        $this->assertEquals($expected, $this->presenter->response);
    }

    public function resetPasswordProvider(): Generator
    {
        // Successful
        $response = new ResetPasswordResponse();
        $response->success = true;
        yield [new ResetPasswordRequest('Password1234@', 'token'), $response];

        // User not found
        $response = new ResetPasswordResponse();
        $response->success = false;
        yield [new ResetPasswordRequest('Password1234@', 'unknown'), $response];

        // Token is expired
        $response = new ResetPasswordResponse();
        $response->success = false;
        $response->tokenExpired = true;
        yield [new ResetPasswordRequest('Password1234@', 'tokenInvalid'), $response];

        // Request error with plainNewPassword is blank
        $response = new ResetPasswordResponse();
        $response->success = false;
        $response->getNotification()->addError('plainNewPassword', 'form.value.mandatory');

        yield [new ResetPasswordRequest('', 'token'), $response];

        // Request error with plainNewPassword is invalid
        $response = new ResetPasswordResponse();
        $response->success = false;
        $response->getNotification()->addError('plainNewPassword', 'form.password.invalid_length');

        yield [new ResetPasswordRequest('fail', 'token'), $response];

        // Request error with token is blank
        $response = new ResetPasswordResponse();
        $response->success = false;
        $response->getNotification()->addError('forgottenPasswordToken', 'form.value.mandatory');

        yield [new ResetPasswordRequest('newPassword123@', ''), $response];
    }
}
