<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Tests\Domain\UseCase;

use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Presenter\ShowCompanyPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\ShowCompany\DTO\ShowCompanyRequest;
use Marketplace\Component\User\Domain\UseCase\ShowCompany\DTO\ShowCompanyResponse;
use Marketplace\Component\User\Domain\UseCase\ShowCompany\ShowCompanyUseCase;
use Marketplace\Component\User\Tests\Unit\Fake\CompanyRepositoryFake;
use PHPUnit\Framework\TestCase;

final class ShowCompanyUseCaseTest extends TestCase
{
    private ShowCompanyPresenterInterface $presenter;

    private ShowCompanyUseCase $useCase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements ShowCompanyPresenterInterface {
            public ShowCompanyResponse $response;
            public function present(ShowCompanyResponse $response): void
            {
                $this->response = $response;
            }
        };
        $this->useCase = new ShowCompanyUseCase(new CompanyRepositoryFake());
    }

    public function testShowCompanySuccessful(): void
    {
        $this->useCase->execute(new ShowCompanyRequest(2), $this->presenter);

        $this->assertInstanceOf(ShowCompanyResponse::class, $this->presenter->response);
        $this->assertInstanceOf(Company::class, $this->presenter->response->getCompany());
        $this->assertIsInt($this->presenter->response->getCompany()->getId());
        $this->assertEquals(2, $this->presenter->response->getCompany()->getId());
    }

    public function testShowCompanyWithUsers(): void
    {
        $this->useCase->execute(new ShowCompanyRequest(2), $this->presenter);

        $this->assertInstanceOf(ShowCompanyResponse::class, $this->presenter->response);
        $this->assertInstanceOf(Company::class, $this->presenter->response->getCompany());
        $this->assertContainsOnlyInstancesOf(User::class, $this->presenter->response->getCompany()->getUsers());
        $this->assertEquals(
            ['User 1', 'User 2'],
            array_map(fn (User $user) => $user->getFirstname(), $this->presenter->response->getCompany()->getUsers())
        );
    }
}
