<?php

namespace Marketplace\Component\User\Model;

use Doctrine\Common\Collections\Collection;

interface CompanyInterface
{
    public function getId(): ?int;

    public function getName(): string;

    public function getIdentification(): ?string;

    public function isEnabled(): ?bool;

    public function getUsers(): Collection;

    public function addUser(UserInterface $user): self;

    public function removeUser(UserInterface $user): self;

    public function getIzbergEmail(): ?string;

    public function getIzbergUserId(): ?int;
}
