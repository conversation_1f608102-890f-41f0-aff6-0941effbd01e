<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Utils\Flash;

use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\Flash\FlashBagInterface;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\HttpFoundation\Session\SessionInterface;

/**
 *
 * @deprecated replaced by Marketplace\Component\CleanArchiCore\Utils\Flash\FlashMessage
 */
class FlashMessage implements FlashMessageInterface
{
    private Session|SessionInterface $session;

    private FlashBagInterface $flashBag;

    public function __construct(private RequestStack $requestStack)
    {
        /** @var Session */
        $this->session = $this->requestStack->getSession();
        $this->flashBag = $this->session->getFlashBag();
    }

    public function add(string $type, mixed $message): void
    {
        $this->flashBag->add($type, $message);
    }
}
