<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Infrastructure\Entity;

use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use Marketplace\Component\User\Infrastructure\Adapter\Repository\MKPOwnerBuyerRepository;

/**
 * @ORM\Entity(repositoryClass=MKPOwnerBuyerRepository::class)
 * MKPOwner = Marketplace Owner
 */
class MKPOwnerBuyer
{
    /**
     * @ORM\Id()
     * @ORM\Column(type="integer", nullable=false)
     */
    private int $companyId;

    /**
     * @ORM\Column(type="datetime_immutable", nullable=false)
     */
    private DateTimeImmutable $createdAt;

    public function __construct()
    {
        $this->createdAt = new DateTimeImmutable();
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function setCompanyId(int $companyId): self
    {
        $this->companyId = $companyId;
        return $this;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTimeImmutable $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }
}
