<?php

namespace Marketplace\Component\User\Infrastructure\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=TermPaymentRepository::class)
 */
class TermPayment
{
    /**
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private ?int $id = null;

    /**
     * @ORM\Column(name="status", type="string", nullable=false)
     */
    private string $status;

    /**
     * @ORM\Column(name="threshold", type="integer", nullable=true)
     */
    private ?int $threshold;

    /**
     * created Time/Date
     *
     * @var \DateTimeImmutable
     *
     * @ORM\Column(name="created_at", type="date_immutable")
     */
    protected \DateTimeImmutable $createdAt;

    /**
     * updated Time/Date
     *
     * @var \DateTimeImmutable
     *
     * @ORM\Column(name="updated_at", type="date_immutable")
     */
    protected \DateTimeImmutable $updatedAt;

    /**
     * @ORM\OneToOne (targetEntity="Company", inversedBy="termPayment")
     * @ORM\JoinColumn(name="company_id", referencedColumnName="id")
     */
    private Company $company;

    /**
     * @var string
     * @ORM\Column(name="type", type="string", nullable=true)
     */
    private ?string $type;

    /**
     * Address constructor.
     */
    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return \DateTimeImmutable
     */
    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    /**
     * @param \DateTimeImmutable $createdAt
     * @return $this
     */
    public function setCreatedAt(\DateTimeImmutable $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    /**
     * @return \DateTimeImmutable
     */
    public function getUpdatedAt(): \DateTimeImmutable
    {
        return $this->updatedAt;
    }

    /**
     * @param \DateTimeImmutable $updatedAt
     * @return $this
     */
    public function setUpdatedAt(\DateTimeImmutable $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     * @return Company
     */
    public function getCompany(): Company
    {
        return $this->company;
    }

    /**
     * @param Company $company
     * @return $this
     */
    public function setCompany(Company $company): self
    {
        $this->company = $company;
        return $this;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return $this
     */
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getThreshold(): ?int
    {
        return $this->threshold;
    }

    /**
     * @param int|null $threshold
     * @return $this
     */
    public function setThreshold(?int $threshold): self
    {
        $this->threshold = $threshold;
        return $this;
    }

    /**
     * @return string
     */
    public function getType(): ?string
    {
        return $this->type;
    }

    /**
     * @param string $type
     * @return $this
     */
    public function setType(?string $type): self
    {
        $this->type = $type;
        return $this;
    }
}
