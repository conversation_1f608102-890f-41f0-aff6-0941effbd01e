<?php

namespace Marketplace\Component\User\Infrastructure\Mapper;

use Marketplace\Component\User\Domain\Model\Address;
use Marketplace\Component\User\Infrastructure\Entity\Address as DoctrineAddress;
use Open\Izberg\Model\Address as IzbergAddress;

class AddressMapper
{
    /**
     * @param DoctrineAddress $doctrineAddress
     * @return Address
     */
    public static function mappingDoctrineToDomain(DoctrineAddress $doctrineAddress): Address
    {
        return (new Address(
            $doctrineAddress->getType(),
            $doctrineAddress->getName(),
            $doctrineAddress->getAddress(),
            $doctrineAddress->getAddress2(),
            $doctrineAddress->getZipCode(),
            $doctrineAddress->getCity(),
            $doctrineAddress->getCountry()->getId(),
            $doctrineAddress->getCompany()->getId(),
            $doctrineAddress->getCountry()->getVatZoneCode(),
            $doctrineAddress->getCountry()->getRegionCode()
        ))
            ->setId($doctrineAddress->getId())
            ->setDefault($doctrineAddress->isDefault())
            ->setCountryName($doctrineAddress->getCountry()->getCode())
            ->setCurrentIzbergId($doctrineAddress->getCurrentIzbergId())
            ;
    }

    /**
     * @param Address $address
     * @param DoctrineAddress|null $doctrineAddress
     * @return DoctrineAddress
     */
    public static function mappingDomainToDoctrine(
        Address $address,
        ?DoctrineAddress $doctrineAddress = null
    ): DoctrineAddress {
        if (!$doctrineAddress instanceof DoctrineAddress) {
            $doctrineAddress = new DoctrineAddress();
        }
        return $doctrineAddress
            ->setName($address->getAddressName())
            ->setDefault($address->isDefault())
            ->setZipCode($address->getZipCode())
            ->setType($address->getAddressType())
            ->setAddress($address->getAddress1())
            ->setAddress2($address->getAddress2())
            ->setCity($address->getCity())
            ->setCurrentIzbergId($address->getCurrentIzbergId());
    }

    public static function mappingIzbergToDomain(IzbergAddress $izbAddress): Address
    {
        $address = (new Address(
            Address::SHIPPING,
            $izbAddress->getName(),
            $izbAddress->getAddress(),
            $izbAddress->getAddress2(),
            $izbAddress->getZipCode(),
            $izbAddress->getCity(),
            $izbAddress->getCountry()->getId(),
            -1,
            "",
            ""
        ))
            ->setId($izbAddress->getId())
            ->setCountryName($izbAddress->getDigicode() ?? '');

        return $address;
    }
}
