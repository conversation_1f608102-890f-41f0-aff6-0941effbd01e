<?php

namespace Marketplace\Component\User\Infrastructure\Mapper;

use Marketplace\Component\User\Infrastructure\Entity\TermPayment as DoctrineTermPayment;
use Marketplace\Component\User\Domain\Model\TermPayment;

class TermPaymentMapper
{
    /**
     * @param DoctrineTermPayment $doctrineTermPayment
     * @return TermPayment
     */
    public static function mappingDoctrineToModel(DoctrineTermPayment $doctrineTermPayment): TermPayment
    {
        $term = new TermPayment();
        $term->setId($doctrineTermPayment->getId());
        $term->setStatus($doctrineTermPayment->getStatus());
        $term->setThreshold($doctrineTermPayment->getThreshold());
        $term->setType($doctrineTermPayment->getType());
        return $term;
    }

    /**
     * @param TermPayment $termPayment
     * @param DoctrineTermPayment|null $docTermPayment
     * @return DoctrineTermPayment
     */
    public static function domainToDoctrine(
        TermPayment $termPayment,
        ?DoctrineTermPayment $docTermPayment = null
    ): DoctrineTermPayment {
        if (!$docTermPayment instanceof DoctrineTermPayment) {
            $docTermPayment = new DoctrineTermPayment();
        }

        $docTermPayment->setStatus($termPayment->getStatus());
        $docTermPayment->setUpdatedAt(new \DateTimeImmutable());
        $docTermPayment->setType($termPayment->getType());
        return $docTermPayment;
    }
}
