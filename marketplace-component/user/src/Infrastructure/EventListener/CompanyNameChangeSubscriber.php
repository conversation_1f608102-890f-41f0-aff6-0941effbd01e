<?php

namespace Marketplace\Component\User\Infrastructure\EventListener;

use Marketplace\Component\User\Domain\Model\Address;
use Marketplace\Component\User\Domain\Event\ChangeCompanyNameEvent;
use Marketplace\Component\User\Domain\Port\Repository\AddressRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Marketplace\Component\User\Domain\UseCase\Address\EditAddress\DTO\EditBillingAddressRequest;
use Marketplace\Component\User\Domain\UseCase\Address\EditAddress\DTO\EditShippingAddressRequest;
use Marketplace\Component\User\Domain\UseCase\Address\EditAddress\EditBillingAddressUseCase;
use Marketplace\Component\User\Domain\UseCase\Address\EditAddress\EditShippingAddressUseCase;
use Marketplace\Component\User\Domain\UseCase\Company\CreateCompanyExternalId\DTO\CreateCompanyExternalIdResponse;
use Marketplace\Component\User\Presentation\Presenter\Address\EditBillingAddressPresenter;
use Marketplace\Component\User\Presentation\Presenter\Address\EditShippingAddressPresenter;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class CompanyNameChangeSubscriber implements
    EventSubscriberInterface
{
    private CreateCompanyExternalIdResponse $response;

    public function __construct(
        private CompanyRepositoryInterface $companyRepository,
        private AddressRepositoryInterface $addressRepository,
        private EditShippingAddressUseCase $editShippingAddressUseCase,
        private EditBillingAddressUseCase $editBillingAddressUseCase,
        private EditShippingAddressPresenter $editShippingAddressPresenter,
        private EditBillingAddressPresenter $editBillingAddressPresenter
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ChangeCompanyNameEvent::class => 'onChangeCompanyName'
        ];
    }

    public function onChangeCompanyName(ChangeCompanyNameEvent $event)
    {


        $this->companyRepository->changeDistantCompanyName($event->companyId, $event->companyName);

        $addresses = $this->addressRepository->findShippingAddresses($event->companyId, true);
        $addressBilling = $this->addressRepository->findBillingAddress($event->companyId);
        if ($addressBilling !== null) {
            $addresses[] = $addressBilling;
        }

        //address containt the company name as firstname in distant system, so they must be updated
        /** @var Address $address */
        foreach ($addresses as $address) {
            if ($address->getAddressType() === Address::SHIPPING) {
                $editAddressRequest = new EditShippingAddressRequest();
            } else {
                $editAddressRequest = new EditBillingAddressRequest();
            }

            $editAddressRequest->setCountryId($address->getCountryId())
                ->setCompanyId($address->getCompanyId())
                ->setCity($address->getCity())
                ->setZipCode($address->getZipCode())
                ->setAddressType($address->getAddressType())
                ->setAddressName($address->getAddressName())
                ->setAddress1($address->getAddress1())
                ->setAddress2($address->getAddress2())
                ->setAddressId($address->getId())
                ->setDefault($address->isDefault())
                ->setCountryName($address->getCountryName());

            if ($editAddressRequest instanceof EditShippingAddressRequest) {
                $this->editShippingAddressUseCase->execute($editAddressRequest, $this->editShippingAddressPresenter);
            } else {
                $this->editBillingAddressUseCase->execute($editAddressRequest, $this->editBillingAddressPresenter);
            }
        }
    }
}
