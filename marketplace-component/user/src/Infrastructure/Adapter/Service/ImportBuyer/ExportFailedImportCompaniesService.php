<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Infrastructure\Adapter\Service\ImportBuyer;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CsvReaderInterface;
use Marketplace\Component\CleanArchiCore\Domain\Record\RecordCollection;
use Symfony\Component\Filesystem\Filesystem;

final class ExportFailedImportCompaniesService implements ExportFailedImportCompaniesInterface
{
    public function __construct(
        private readonly CsvReaderInterface $csvReader,
        private readonly string $failedImportPath
    ) {
    }

    public function writeFailedFile(array $failedRows): void
    {

        $csvContent = $this->csvReader->write(
            header: [
                'companyExternalId',
                'companyNam',
                'billingAddress',
                'billingAddressComplement',
                'billingZip',
                'billingCity',
                'country',
                'accountingEmail',
                'vatNumber',
                'siret',
                'accountingPhone',
                'mainUserEmail',
                'priceCategory',
                'accountingCategory',
                'clientOrigin',
                'termpaymentAccepted',
                'termPaymentCondition',
                'termPaymentTreshold',
                'errorMessages'
            ],
            records: $failedRows
        );

        $fileSystem = new Filesystem();
        $filename = 'failed_import_companies.csv';

        $fileSystem->dumpFile($this->failedImportPath . $filename, $csvContent);
    }
}
