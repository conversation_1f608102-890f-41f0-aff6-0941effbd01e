<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Infrastructure\Adapter\Service\CompanyExternalId;

use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Marketplace\Component\User\Infrastructure\Adapter\Repository\CompanyAccessTokenRepository;
use Marketplace\Component\User\Infrastructure\Exception\UserIzbergDoesNotExistException;

final class FindCompanyExternalIdService implements FindCompanyExternalIdInterface
{
    public function __construct(
        private readonly CompanyAccessTokenRepository $companyAccessTokenRepository,
        private readonly CompanyRepositoryInterface $companyRepository,
    ) {
    }

    public function getExternalIdFromIzberg(int $izebergUserId): ?string
    {
        $companyId = $this->companyAccessTokenRepository->getCompanyId($izebergUserId);
        if ($companyId === null) {
            throw new UserIzbergDoesNotExistException(
                sprintf('The company access token is not found with %d izberg user id', $izebergUserId)
            );
        }

        return $this->companyRepository->getExternalId($companyId);
    }
}
