<?php

namespace Marketplace\Component\User\Infrastructure\Adapter\Service;

use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Service\GenerateTokenInterface;
use Marketplace\Component\User\Domain\Port\Service\MailerServiceInterface;
use Marketplace\Component\User\Domain\Port\Service\ResetPasswordInterface;

class ResetPasswordService implements ResetPasswordInterface
{
    /**
     * ResetPasswordService constructor.
     * @param UserRepositoryInterface $userRepository
     * @param GenerateTokenInterface $generateToken
     */
    public function __construct(
        private UserRepositoryInterface $userRepository,
        private GenerateTokenInterface $generateToken
    ) {
    }

    public function resetPassword(User $user): bool
    {
        User::requestPasswordReset($user, $this->generateToken->generateToken());
        $this->userRepository->update($user);
        return true;
    }
}
