<?php

namespace Marketplace\Component\User\Infrastructure\Adapter\Service;

use Marketplace\Component\User\Domain\Port\Service\TermPaymentIdInterface;

class TermPaymentIdService implements TermPaymentIdInterface
{

    public function __construct(private array $termPaymentIds)
    {
    }

    public function getIdFromKey(string $key): ?string
    {
        if (array_key_exists($key, $this->termPaymentIds)) {
            return $this->termPaymentIds[$key];
        }
        return null;
    }
}
