<?php

namespace Marketplace\Component\User\Infrastructure\Adapter\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetRegionServiceInterface;
use Marketplace\Component\User\Domain\Model\Address;
use Marketplace\Component\User\Domain\UseCase\Company\CreateCompanyExternalId\DTO\CreateCompanyExternalIdRequest;
use Marketplace\Component\User\Infrastructure\Entity\Address as DoctrineAddress;
use Marketplace\Component\User\Domain\Port\Repository\AddressRepositoryInterface;
use Marketplace\Component\User\Infrastructure\Entity\Company;
use Marketplace\Component\User\Infrastructure\Entity\Country;
use Marketplace\Component\User\Infrastructure\Mapper\AddressMapper;
use Open\Izberg\Client\BuyerClient;
use Open\Izberg\Client\OperatorClient;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class AddressRepository extends ServiceEntityRepository implements AddressRepositoryInterface, LoggerAwareInterface
{
    public LoggerInterface $logger;

    public function __construct(
        ManagerRegistry $registry,
        private CountryRepository $countryRepository,
        private CompanyRepository $companyRepository,
        private BuyerClient $buyerClient,
        private OperatorClient $operatorClient,
        private EventDispatcherInterface $dispatcher,
        private GetRegionServiceInterface $getRegionService
    ) {
        parent::__construct($registry, DoctrineAddress::class);
    }

    /**
     * @param int $companyId
     * @return Address|null
     */
    public function findBillingAddress(int $companyId): ?Address
    {
        $result = $this->findOneBy(['type' => Address::BILLING, 'company' => $companyId, 'active' => true]);
        if (!$result instanceof DoctrineAddress) {
            return null;
        }

        return AddressMapper::mappingDoctrineToDomain($result);
    }

    public function findDoctrineBillingAddress(int $companyId): ?DoctrineAddress
    {
        $result = $this->findOneBy(['type' => Address::BILLING, 'company' => $companyId]);
        if (!$result instanceof DoctrineAddress) {
            return null;
        }

        return $result;
    }

    /**
     * @param int $companyId
     * @param bool $active
     * @return array
     */
    public function findShippingAddresses(int $companyId, bool $active = true, bool $filterWithRegion = false): array
    {
        $results = $this->findBy(['type' => Address::SHIPPING, 'company' => $companyId, 'active' => $active]);
        $shippingAddresses = [];
        $region = $this->getRegionService->getRegion();
        /** @var DoctrineAddress $result */
        foreach (array_filter($results) as $result) {
            $this->logger->info(
                sprintf(
                    'AddressRepository : current region : %s / address region : %s',
                    $region,
                    $result->getCountry()->getRegionCode()
                )
            );
            if ($filterWithRegion && $region != $result->getCountry()->getRegionCode()) {
                continue;
            }
            $shippingAddresses[] = AddressMapper::mappingDoctrineToDomain($result);
        }
        return $shippingAddresses;
    }

    /**
     * @param int $companyId
     * @param bool $active
     * @return array
     */
    public function findDoctrineShippingAddresses(int $companyId, bool $active = true): array
    {
        return $this->findBy(['type' => Address::SHIPPING, 'company' => $companyId, 'active' => $active]);
    }

    /**
     * @param int $companyId
     * @return Address|null
     */
    public function findDefaultShippingAddress(int $companyId): ?Address
    {
        $result = $this->findOneBy(['type' => Address::SHIPPING, 'company' => $companyId, 'default' => true, 'active' => true]);
        if (!$result instanceof DoctrineAddress) {
            return null;
        }

        return AddressMapper::mappingDoctrineToDomain($result);
    }

    /**
     * @param int $addressId
     * @return Address|null
     */
    public function findById(int $addressId): ?Address
    {
        $result = $this->find($addressId);
        if (!$result instanceof DoctrineAddress) {
            return null;
        }

        return AddressMapper::mappingDoctrineToDomain($result);
    }

    /**
     * @param int $companyId
     * @return bool
     */
    public function isBillingAddressExist(int $companyId): bool
    {
        $result = $this->findOneBy(['type' => Address::BILLING, 'company' => $companyId, 'active' => true]);
        return $result instanceof DoctrineAddress;
    }

    /**
     * @param Address $address
     * @return Address
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function save(Address $address): Address
    {
        $addressId = $address->getId();

        if ($addressId !== null) {
            $doctrineAddress = $this->find($addressId);
        } else {
            $doctrineAddress = new DoctrineAddress();
        }

        /**  @var Country $country */
        $country = $this->countryRepository->find($address->getCountryId());

        $name = $address->getAddressName();
        $address1 = $address->getAddress1();
        $address2 = $address->getAddress2();
        $zipCode = $address->getZipCode();
        $city = $address->getCity();
        $izbergCountryId = $country->getIzbergCountryId();

        if ($addressId === null) {
            $addressId = $this->buyerClient->addressApi()->createAddress(
                $name,
                $address1,
                $address2,
                $zipCode,
                $city,
                $izbergCountryId,
                $country->getCode()
            );

            $doctrineAddress->setId($addressId);
            $address->setCurrentIzbergId($addressId);
        } else {
            $addressIdUpdated = $this->operatorClient->addressApi()->updateAddress(
                $doctrineAddress->getCurrentIzbergId(),
                $name,
                $address1,
                $address2,
                $zipCode,
                $city,
                $izbergCountryId,
                $country->getCode()
            );
            if ($addressIdUpdated !== $address->getId()) {
                $address->setCurrentIzbergId($addressIdUpdated);
            }
        }

        $doctrineAddress = AddressMapper::mappingDomainToDoctrine($address, $doctrineAddress);

        /** @var Company $company */
        $company = $this->companyRepository->find($address->getCompanyId());
        $doctrineAddress->setCompany($company);
        $doctrineAddress->setCountry($country);
        $doctrineAddress->setUpdatedAt(new \DateTimeImmutable());

        $this->_em->persist($doctrineAddress);
        $this->_em->flush();

        if ($address->isBillingType()) {
            $this->dispatcher->dispatch(
                new CreateCompanyExternalIdRequest($address->getCompanyId(), $address->getZipCode())
            );
        }

        return AddressMapper::mappingDoctrineToDomain($doctrineAddress);
    }

    /**
     * @param int $addressId
     * @return bool
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function delete(int $addressId): bool
    {
        $result = $this->find($addressId);
        if (!$result instanceof DoctrineAddress) {
            return false;
        }
        $result->setActive(false);
        $this->_em->persist($result);
        $this->_em->flush();
        return true;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public function createPickAndCollectAddressIfNotExist(Address $address, int $userDistantId): int
    {
        $name = $address->getAddressName();
        $address1 = $address->getAddress1();
        $address2 = $address->getAddress2();
        $zipCode = $address->getZipCode();
        $city = $address->getCity();

        $distantAddress = $this->findDistantAddress($this->buyerClient->getUserId(), $address);


        if ($distantAddress === null) {
            $addressId = $this->buyerClient->addressApi()->createAddress(
                $name,
                $address1,
                $address2,
                $zipCode,
                $city,
                $address->getCountryId(),
                $address->getCountryName()
            );
        } else {
            $addressId = $distantAddress->getId() ?? -1; //never happen
        }
        return $addressId;
    }

    public function findDistantAddressById(int $addressId): ?Address
    {
        // On récupère l'adresse chez Izberg car elle contient un attribut supplémentaire digiCode,
        // utile pour différencier la france métropolitaine des DOM-TOM (tous marqués FR comme pays)
        $izbergAddress = $this->operatorClient->addressApi()->fetchAddress($addressId);
        return AddressMapper::mappingIzbergToDomain($izbergAddress);
    }


    private function findDistantAddress(int $userId, Address $searchAddress): ?Address
    {
        $addresses = $this->buyerClient->addressApi()->findAddressesByUser($userId);
        foreach ($addresses as $address) {
            $distantAddress = AddressMapper::mappingIzbergToDomain($address);
            if ($this->equalsAddress($searchAddress, $distantAddress)) {
                return $distantAddress;
            }
        }
        return null;
    }

    private function equalsAddress(Address $address1, Address $address2): bool
    {
        return (
            $address1->getAddressName() === $address2->getAddressName() &&
            $address1->getAddress1() === $address2->getAddress1() &&
            $address1->getAddress2() === $address2->getAddress2() &&
            $address1->getZipCode() === $address2->getZipCode() &&
            $address1->getCity() === $address2->getCity() &&
            $address1->getCountryId() === $address2->getCountryId() &&
            $address1->getCountryName() == $address2->getCountryName()
        );
    }

    /**
     * @return array
     */
    public function findAllActiveAddresses(): array
    {
        $result = [];
        $activeAddresses = $this->findBy(['active' => true]);
        foreach ($activeAddresses as $address) {
            $result[] = AddressMapper::mappingDoctrineToDomain($address);
        }

        return $result;
    }

    /**
     * @param Address $address
     * @return bool
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function updateWithoutSync(Address $address): bool
    {
        $addressId = $address->getId();
        if ($addressId === null) {
            return false;
        }

        $doctrineAddress = $this->find($addressId);
        if (!$doctrineAddress instanceof DoctrineAddress) {
            return false;
        }

        $doctrineAddress = AddressMapper::mappingDomainToDoctrine($address, $doctrineAddress);
        $doctrineAddress->setUpdatedAt(new \DateTimeImmutable());

        $this->_em->persist($doctrineAddress);
        $this->_em->flush();

        return true;
    }
}
