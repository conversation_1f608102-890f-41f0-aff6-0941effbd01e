<?php

namespace Marketplace\Component\User\Infrastructure\Security;

use Marketplace\Component\User\Domain\Presenter\LoginPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\Login\DTO\LoginRequest;
use Marketplace\Component\User\Domain\UseCase\Login\DTO\LoginResponse;
use Marketplace\Component\User\Domain\UseCase\Login\LoginUseCase;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Marketplace\Component\User\Model\UserInterface;
use Marketplace\Component\User\Utils\Flash\FlashMessageInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\CsrfTokenBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\PasswordCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\Authenticator\Passport\PassportInterface;
use Symfony\Component\Security\Http\Util\TargetPathTrait;
use Symfony\Contracts\Translation\TranslatorInterface;

class LoginFormAuthenticator extends AbstractAuthenticator implements LoginPresenterInterface
{
    use TargetPathTrait;

    public const LOGIN_ROUTE = 'login';

    private LoginResponse $response;

    public function __construct(
        private UrlGeneratorInterface $urlGenerator,
        private TranslatorInterface $translator,
        private LoginUseCase $loginUseCase,
        private FlashMessageInterface $flashMessage,
    ) {
    }

    public function supports(Request $request): bool
    {
        return self::LOGIN_ROUTE === $request->attributes->get('_route')
            && $request->isMethod('POST');
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        if ($targetPath = $this->getTargetPath($request->getSession(), $firewallName)) {
            return new RedirectResponse($targetPath);
        }
        $user = $token->getUser();
        if ($user instanceof User) {
            $this->flashMessage->add(
                'success',
                $this->translator->trans(
                    'login.authenticator.message.success',
                    domain: 'translations',
                    locale: $user->getLanguage()
                )
            );
            $targetPath = $request->request->get('_target_path');
            if ($targetPath !== null) {
                return new RedirectResponse((string) $targetPath);
            }
            return new RedirectResponse($this->urlGenerator->generate('home'));
        }

        return new RedirectResponse($this->urlGenerator->generate('login'));
    }

    /**
     * @inheritDoc
     */
    public function authenticate(Request $request): Passport
    {
        /** @var string */
        $email = $request->request->get('email', '');
        /** @var string */
        $password = $request->request->get('password', '');
        /** @var string */
        $csrfToken = $request->request->get('_csrf_token');

        $loginRequest = new LoginRequest($email, $password);
        $this->loginUseCase->execute($loginRequest, $this);

        return new Passport(
            new UserBadge($loginRequest->email),
            new PasswordCredentials($loginRequest->password),
            [
                new CsrfTokenBadge('authenticate', $csrfToken)
            ]
        );
    }

    /**
     * @inheritDoc
     */
    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        if (!$this->response->getUser() || $this->response->isPasswordValid() === false) {
            $this->flashMessage->add(
                'error',
                $this->translator->trans('login.authenticator.message.error', domain: 'translations')
            );
            return new RedirectResponse($this->urlGenerator->generate('login'));
        }

        return null;
    }

    public function present(LoginResponse $response): void
    {
        $this->response = $response;
    }
}
