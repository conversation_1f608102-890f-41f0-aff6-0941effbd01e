<?php

namespace Marketplace\Component\User\Presentation\ViewModel\Company;

use Marketplace\Component\CleanArchiCore\Domain\Error\Error;
use Marketplace\Component\CleanArchiCore\Presentation\ViewModel\AbstractViewModel;
use Marketplace\Component\User\Domain\Model\Company;
use Symfony\Contracts\Translation\TranslatorInterface;

class ShowCompanyViewModel extends AbstractViewModel
{
    public ?Company $company = null;
    /**  @var Error[] */
    public array $errors = [];
    private array $menu = [];
    private bool $editRight = false;
    private float $totalOrder = 0.00;
    private float $termPaymentCurrentAmount;

    public function __construct(
        private TranslatorInterface $translator,
    ) {
    }

    /**
     * @return string
     */
    public function headerTitle(): string
    {
        return $this->translator->trans(id: 'user_account.company.title', domain: 'translations');
    }

    /**
     * @return string
     */
    public function pageTitle(): string
    {
        return $this->translator->trans(id: 'user_account.company.title', domain: 'translations');
    }

    /**
     * @return string|null
     */
    public function pageIcon(): ?string
    {
        return 'entreprise';
    }

    /**
     * @param array $menu
     */
    public function setMenu(array $menu): void
    {
        $this->menu = $menu;
    }

    /**
     * @return array
     */
    public function getMenu(): array
    {
        return $this->menu;
    }

    /**
     * @return bool
     */
    public function isEditRight(): bool
    {
        return $this->editRight;
    }

    /**
     * @param bool $editRight
     * @return ShowCompanyViewModel
     */
    public function setEditRight(bool $editRight): ShowCompanyViewModel
    {
        $this->editRight = $editRight;
        return $this;
    }

    /**
     * @return float
     */
    public function getTotalOrder(): float
    {
        return $this->totalOrder;
    }

    /**
     * @param float $totalOrder
     * @return ShowCompanyViewModel
     */
    public function setTotalOrder(float $totalOrder): ShowCompanyViewModel
    {
        $this->totalOrder = $totalOrder;
        return $this;
    }

    public function getTermPaymentCurrentAmount(): float
    {
        return $this->termPaymentCurrentAmount;
    }

    public function setTermPaymentCurrentAmount(float $termPaymentCurrentAmount): self
    {
        $this->termPaymentCurrentAmount = $termPaymentCurrentAmount;
        return $this;
    }
}
