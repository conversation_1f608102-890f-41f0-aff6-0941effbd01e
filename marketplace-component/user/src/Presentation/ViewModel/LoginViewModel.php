<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\ViewModel;

use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;

final class LoginViewModel
{
    private ?string $errorMessage;

    public function __construct(private string $lastname, ?AuthenticationException $exception)
    {
        $this->errorMessage = $exception !== null ? $exception->getMessage() : null;
    }

    public static function fromAuthenticationUtils(AuthenticationUtils $authenticationUtils): self
    {
        return new self(
            $authenticationUtils->getLastUsername(),
            $authenticationUtils->getLastAuthenticationError()
        );
    }

    public function getLastname(): string
    {
        return $this->lastname;
    }

    public function getErrorMessage(): ?string
    {
        return $this->errorMessage;
    }
}
