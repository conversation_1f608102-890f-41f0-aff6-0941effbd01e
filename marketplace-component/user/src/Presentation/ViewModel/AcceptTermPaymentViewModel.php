<?php

namespace Marketplace\Component\User\Presentation\ViewModel;

use Symfony\Component\Form\FormView;

class AcceptTermPaymentViewModel
{
    /**
     * AcceptTermPaymentViewModel constructor.
     * @param bool $success
     * @param int $companyId
     */
    public function __construct(private bool $success, private int $companyId)
    {
    }

    /**
     * @return bool
     */
    public function isSuccess(): bool
    {
        return $this->success;
    }

    /**
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }
}
