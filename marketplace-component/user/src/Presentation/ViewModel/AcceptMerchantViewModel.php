<?php

namespace Marketplace\Component\User\Presentation\ViewModel;

class AcceptMerchantViewModel
{
    /**
     * AcceptMerchantViewModel constructor.
     * @param bool $success
     * @param string|null $message
     */
    public function __construct(private bool $success, private ?string $message)
    {
    }

    /**
     * @return bool
     */
    public function isSuccess(): bool
    {
        return $this->success;
    }

    /**
     * @return string|null
     */
    public function getMessage(): ?string
    {
        return $this->message;
    }
}
