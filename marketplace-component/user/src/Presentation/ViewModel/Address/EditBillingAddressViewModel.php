<?php

namespace Marketplace\Component\User\Presentation\ViewModel\Address;

use Marketplace\Component\CleanArchiCore\Presentation\ViewModel\AbstractViewModel;
use Symfony\Component\Form\FormView;
use Symfony\Contracts\Translation\TranslatorInterface;

class EditBillingAddressViewModel extends AbstractViewModel
{
    public FormView $form;
    public array $formErrors = [];
    public bool $succeed = false;

    /**
     * @param TranslatorInterface $translator
     * @param array $menu
     */
    public function __construct(
        private TranslatorInterface $translator,
        private array $menu = [],
    ) {
    }

    /**
     * @param array $menu
     */
    public function setMenu(array $menu): void
    {
        $this->menu = $menu;
    }


    /**
     * @return array
     */
    public function getMenu(): array
    {
        return $this->menu;
    }

    /**
     * @return bool
     */
    public function isSucceed(): bool
    {
        return $this->succeed;
    }

    /**
     * @param bool $succeed
     * @return EditBillingAddressViewModel
     */
    public function setSucceed(bool $succeed): EditBillingAddressViewModel
    {
        $this->succeed = $succeed;
        return $this;
    }

    public function addFormErrors(string $fieldName, string $translateKey): void
    {
        $this->formErrors[] = ['field' => $fieldName, 'message' => $translateKey];
    }

    public function hasError(): bool
    {
        return \count($this->formErrors) > 0;
    }



    /**
     * @return string
     */
    public function headerTitle(): string
    {
        return $this->translator->trans(id: 'user_account.address.title', domain: 'translations');
    }

    /**
     * @return string
     */
    public function pageTitle(): string
    {
        return $this->translator->trans(id: 'user_account.address.title', domain: 'translations');
    }

    /**
     * @return string|null
     */
    public function pageIcon(): ?string
    {
        return 'localisation';
    }
}
