<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\ViewModel\User;

use Marketplace\Component\CleanArchiCore\Presentation\ViewModel\AbstractViewModel;
use Symfony\Component\Form\FormView;
use Symfony\Contracts\Translation\TranslatorInterface;

class AdminEditUserViewModel extends AbstractViewModel
{
    public FormView $form;
    public array $formErrors = [];
    public bool $succeed = false;

    public function __construct(
        private TranslatorInterface $translator,
        private array $menu = [],
    ) {
    }

    /**
     * @return array
     */
    public function getMenu(): array
    {
        return $this->menu;
    }

    /**
     * @param array $menu
     * @return self
     */
    public function setMenu(array $menu): self
    {
        $this->menu = $menu;
        return $this;
    }

    /**
     * @return bool
     */
    public function isSucceed(): bool
    {
        return $this->succeed;
    }

    /**
     * @param bool $succeed
     * @return self
     */
    public function setSucceed(bool $succeed): self
    {
        $this->succeed = $succeed;
        return $this;
    }

    /**
     * @param string $fieldName
     * @param string $translateKey
     */
    public function addFormErrors(string $fieldName, string $translateKey): void
    {
        $this->formErrors[] = ['field' => $fieldName, 'message' => $translateKey];
    }

    /**
     * @return bool
     */
    public function hasError(): bool
    {
        return \count($this->formErrors) > 0;
    }

    /**
     * @return string
     */
    public function headerTitle(): string
    {
        return $this->translator->trans(id: 'user_account.profile.title', domain: 'translations');
    }

    /**
     * @return string
     */
    public function pageTitle(): string
    {
        return $this->translator->trans(id: 'user_account.profile.title', domain: 'translations');
    }

    /**
     * @return string|null
     */
    public function pageIcon(): ?string
    {
        return 'noconnected';
    }
}
