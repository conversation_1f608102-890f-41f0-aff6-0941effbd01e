<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\ViewModel\User;

use Marketplace\Component\CleanArchiCore\Presentation\ViewModel\AbstractViewModel;
use Marketplace\Component\User\Domain\Model\User;
use Symfony\Contracts\Translation\TranslatorInterface;

class ShowUserViewModel extends AbstractViewModel
{
    public function __construct(
        private ?User $user,
        private TranslatorInterface $translator,
        private array $menu = [],
        private array $errors = [],
    ) {
    }

    /**
     * @return array
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * @param array $errors
     * @return ShowUserViewModel
     */
    public function setErrors(array $errors): ShowUserViewModel
    {
        $this->errors = $errors;
        return $this;
    }

    /**
     * @return User|null
     */
    public function getUser(): ?User
    {
        return $this->user;
    }

    /**
     * @param User|null $user
     * @return ShowUserViewModel
     */
    public function setUser(?User $user): ShowUserViewModel
    {
        $this->user = $user;
        return $this;
    }

    /**
     * @return TranslatorInterface
     */
    public function getTranslator(): TranslatorInterface
    {
        return $this->translator;
    }

    /**
     * @param TranslatorInterface $translator
     * @return ShowUserViewModel
     */
    public function setTranslator(TranslatorInterface $translator): ShowUserViewModel
    {
        $this->translator = $translator;
        return $this;
    }

    /**
     * @return array
     */
    public function getMenu(): array
    {
        return $this->menu;
    }

    /**
     * @param array $menu
     * @return ShowUserViewModel
     */
    public function setMenu(array $menu): ShowUserViewModel
    {
        $this->menu = $menu;
        return $this;
    }

    /**
     * @return string
     */
    public function headerTitle(): string
    {
        return $this->translator->trans(id: 'user_account.profile.title', domain: 'translations');
    }

    /**
     * @return string
     */
    public function pageTitle(): string
    {
        return $this->translator->trans(id: 'user_account.profile.title', domain: 'translations');
    }

    /**
     * @return string|null
     */
    public function pageIcon(): ?string
    {
        return 'noconnected';
    }
}
