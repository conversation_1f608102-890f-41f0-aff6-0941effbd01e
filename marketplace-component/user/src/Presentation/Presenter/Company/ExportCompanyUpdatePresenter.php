<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\Presenter\Company;

use Marketplace\Component\CleanArchiCore\Domain\Record\RecordCollection;
use Marketplace\Component\User\Domain\Port\Service\SendInfosCompanyBuyerToErpServiceInterface;
use Marketplace\Component\User\Domain\Presenter\ExportCompanyUpdatePresenterInterface;
use Marketplace\Component\User\Domain\UseCase\ExportCompanyUpdate\DTO\ExportCompanyUpdateResponse;

final class ExportCompanyUpdatePresenter implements ExportCompanyUpdatePresenterInterface
{
    public function __construct(private readonly SendInfosCompanyBuyerToErpServiceInterface $companyBuyerToErpService)
    {
    }

    public function present(ExportCompanyUpdateResponse $response): void
    {
        $companyRecord = $response->companyRecord;
        if ($companyRecord !== null) {
            $this->companyBuyerToErpService->export(new RecordCollection([$companyRecord]));
        }
    }
}
