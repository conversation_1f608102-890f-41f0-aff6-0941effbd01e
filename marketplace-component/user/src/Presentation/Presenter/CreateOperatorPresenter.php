<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\Presenter;

use Marketplace\Component\User\Domain\Presenter\CreateOperatorPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\CreateOperator\DTO\CreateOperatorResponse;
use Marketplace\Component\User\Presentation\ViewModel\CreateOperatorViewModel;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

final class CreateOperatorPresenter implements CreateOperatorPresenterInterface
{
    private CreateOperatorViewModel $viewModel;

    public function __construct(
        private readonly TranslatorInterface $translator
    ) {
        $this->viewModel = new CreateOperatorViewModel($translator);
    }

    public function present(CreateOperatorResponse $response): void
    {
        $this->viewModel->error = $response->getNotification()->hasError();
        $this->viewModel->saved = $response->updated;

        foreach ($response->getNotification()->getErrors() as $error) {
            $this->viewModel->addFormErrors(
                $error->getFieldName(),
                $this->translator->trans(id: $error->getTranslationKey(), domain: 'translations')
            );
        }
    }

    public function viewModel(FormInterface $form): CreateOperatorViewModel
    {
        if ($this->viewModel->error) {
            foreach ($this->viewModel->formErrors as $error) {
                $form->get($error['field'])->addError(new FormError($error['message']));
            }
        }

        $this->viewModel->form = $form->createView();

        return $this->viewModel;
    }
}
