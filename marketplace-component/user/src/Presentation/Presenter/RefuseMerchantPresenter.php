<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\Presenter;

use Marketplace\Component\User\Domain\Presenter\RefuseMerchantPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\RefuseMerchant\DTO\RefuseMerchantResponse;
use Marketplace\Component\User\Presentation\ViewModel\RefuseMerchantViewModel;

class RefuseMerchantPresenter implements RefuseMerchantPresenterInterface
{
    private RefuseMerchantViewModel $viewModel;

    public function present(RefuseMerchantResponse $response): void
    {
        $this->viewModel = new RefuseMerchantViewModel();
        $this->viewModel->completed = $response->completed;
        $this->viewModel->message = $response->message;
    }

    public function viewModel(): RefuseMerchantViewModel
    {
        return $this->viewModel;
    }
}
