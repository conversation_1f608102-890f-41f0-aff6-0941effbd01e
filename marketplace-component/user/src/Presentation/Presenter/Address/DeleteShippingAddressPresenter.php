<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\Presenter\Address;

use Marketplace\Component\User\Domain\Presenter\Address\DeleteShippingAddressPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\Address\DeleteAddress\DTO\DeleteShippingAddressResponse;
use Marketplace\Component\User\Presentation\ViewModel\Address\DeleteShippingAddressViewModel;

class DeleteShippingAddressPresenter implements DeleteShippingAddressPresenterInterface
{
    private DeleteShippingAddressViewModel $viewModel;

    public function present(DeleteShippingAddressResponse $response): void
    {
        $this->viewModel = new DeleteShippingAddressViewModel();
        $this->viewModel->addressDeleted = $response->isAddressDeleted();
    }

    public function viewModel(): DeleteShippingAddressViewModel
    {
        return $this->viewModel;
    }
}
