<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\Presenter\Address;

use Marketplace\Component\User\Domain\Presenter\Address\ShowShippingAddressesUsePresenterInterface;
use Marketplace\Component\User\Domain\UseCase\Address\ShowShippingAddresses\DTO\ShowShippingAddressesResponse;
use Marketplace\Component\User\Presentation\ViewModel\Address\ShowShippingAddressesViewModel;
use Marketplace\Component\User\Presentation\ViewModel\Address\ShowShippingAddressesViewModelApi;
use Symfony\Contracts\Translation\TranslatorInterface;

class ShowShippingAddressesPresenterApi implements ShowShippingAddressesUsePresenterInterface
{
    private ShowShippingAddressesViewModelApi $viewModel;

    public function __construct()
    {
    }

    public function present(ShowShippingAddressesResponse $response): void
    {
        $this->viewModel = new ShowShippingAddressesViewModelApi($response->getAddresses());
    }

    public function viewModel(): ShowShippingAddressesViewModelApi
    {
        return $this->viewModel;
    }
}
