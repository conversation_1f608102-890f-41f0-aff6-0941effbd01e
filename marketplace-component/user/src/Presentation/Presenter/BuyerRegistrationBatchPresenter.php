<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\Presenter;

use Marketplace\Component\CleanArchiCore\Domain\Error\Error;
use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Presenter\BuyerRegistrationPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\BuyerRegistration\DTO\BuyerRegistrationResponse;
use Marketplace\Component\User\Presentation\ViewModel\RegistrationBatchViewModel;
use Symfony\Component\Console\Output\OutputInterface;

class BuyerRegistrationBatchPresenter implements BuyerRegistrationPresenterInterface
{
    private RegistrationBatchViewModel $viewModel;

    public function __construct()
    {
        $this->viewModel = new RegistrationBatchViewModel();
    }

    public function present(BuyerRegistrationResponse $response): void
    {
        $this->viewModel->error = $response->getNotification()->hasError();
        $this->viewModel->request = $response->getRequest();
        $this->viewModel->user = $response->getUser();
        $this->viewModel->isSaved = $response->getUser() !== null;

        foreach ($response->getNotification()->getErrors() as $error) {
            $this->viewModel->errors [] = $error;
        }
    }

    public function viewModel(OutputInterface $output): void
    {
        if ($this->viewModel->error) {
            /** @var Error $error */
            foreach ($this->viewModel->errors as $error) {
                $output->writeln(
                    sprintf(
                        "<error>For company (%s) error on field %s: %s</error>",
                        $this->viewModel->request->email ?? "empty email", // would never happen
                        $error->getFieldName() ?? "Nofield",
                        $error->getMessage()
                    )
                );
            }
        }
    }

    public function success(): bool
    {
        return !$this->viewModel->error;
    }

    public function getErrors(): array
    {
        return $this->viewModel->errors;
    }

    public function getUser(): ?User
    {
        return $this->viewModel->user;
    }
}
