<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\Presenter;

use Marketplace\Component\User\Domain\Presenter\DeclineTermPaymentPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\DeclineTermPayment\DTO\DeclineTermPaymentResponse;
use Marketplace\Component\User\Presentation\ViewModel\DeclineTermPaymentViewModel;

class DeclineTermPaymentPresenter implements DeclineTermPaymentPresenterInterface
{
    private DeclineTermPaymentViewModel $viewModel;

    public function present(DeclineTermPaymentResponse $response): void
    {
        $this->viewModel = new DeclineTermPaymentViewModel();
        $this->viewModel->completed = $response->success;
        $this->viewModel->message = $response->errorMessage;
    }

    public function viewModel(): DeclineTermPaymentViewModel
    {
        return $this->viewModel;
    }
}
