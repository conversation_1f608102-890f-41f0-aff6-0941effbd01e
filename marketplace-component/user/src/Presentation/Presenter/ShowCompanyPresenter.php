<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\Presenter;

use Marketplace\Component\User\Domain\Presenter\ShowCompanyPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\ShowCompany\DTO\ShowCompanyResponse;
use Marketplace\Component\User\Presentation\ViewModel\ShowCompanyViewModel;
use Symfony\Contracts\Translation\TranslatorInterface;

class ShowCompanyPresenter implements ShowCompanyPresenterInterface
{
    private ShowCompanyViewModel $viewModel;

    public function __construct(private TranslatorInterface $translator)
    {
    }

    public function present(ShowCompanyResponse $response): void
    {
        $this->viewModel = new ShowCompanyViewModel($response->getCompany(), $this->translator);
    }

    public function viewModel(): ShowCompanyViewModel
    {
        return $this->viewModel;
    }
}
