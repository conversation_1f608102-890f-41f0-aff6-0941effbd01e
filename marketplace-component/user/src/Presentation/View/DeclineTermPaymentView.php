<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\View;

use Marketplace\Component\User\Presentation\ViewModel\DeclineTermPaymentViewModel;
use Marketplace\Component\User\Utils\Flash\FlashMessageInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Serializer\SerializerInterface;

final class DeclineTermPaymentView
{
    public function __construct(
        private FlashMessageInterface $flashMessage,
        private TranslatorInterface $translator,
        private UrlGeneratorInterface $urlGenerator,
        private SerializerInterface $serializer,
    ) {
    }

    public function generateView(DeclineTermPaymentViewModel $viewModel): Response
    {
        if ($viewModel->completed === false) {
            $errorMessage = '';
            if ($viewModel->message !== null) {
                $errorMessage = $this->translator->trans($viewModel->message, [], 'translations');
            }
            return new JsonResponse(['message' => $errorMessage], Response::HTTP_BAD_REQUEST);
        }

        $this->flashMessage->add(
            'success',
            $this->translator->trans('decline.term_payment.success', [], 'translations')
        );
        $viewModel->redirectUrl = $this->urlGenerator->generate('buyers_list');

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $viewModel,
                'json'
            )
        );
    }
}
