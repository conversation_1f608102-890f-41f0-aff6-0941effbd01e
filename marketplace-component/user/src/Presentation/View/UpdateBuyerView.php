<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\View;

use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Infrastructure\Adapter\Repository\CompanyRepository;
use Marketplace\Component\User\Presentation\ViewModel\UpdateBuyerViewModel;
use Marketplace\Component\User\Utils\Flash\FlashMessageInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class UpdateBuyerView
{
    public function __construct(
        private Environment $twig,
        private FlashMessageInterface $flashMessage,
        private TranslatorInterface $translator,
        private UrlGeneratorInterface $urlGenerator,
        private CompanyRepository $companyRepository
    ) {
    }

    public function generateView(UpdateBuyerViewModel $viewModel, int $companyId, ?float $inProgressTermPaymentAmount = null): Response
    {
        $viewModel->inProgressTermPaymentAmount = $inProgressTermPaymentAmount;
        $viewModel->company = $this->companyRepository->getCompanyById($companyId);
        if ($viewModel->afterPost && !$viewModel->hasError()) {
            $this->flashMessage->add(
                'success',
                $this->translator->trans(id: 'buyer_update.success', domain: 'translations')
            );
            /**
             * @var Company $company
             */
            $company = $viewModel->company;
            return new RedirectResponse($this->urlGenerator->generate('buyer_show', ['id' => $company->getId()]));
        }

        return new Response($this->twig->render('user/buyer/edit.html.twig', ['viewModel' => $viewModel]));
    }
}
