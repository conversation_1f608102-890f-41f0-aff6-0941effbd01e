<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\View;

use Marketplace\Component\User\Presentation\ViewModel\ResetPasswordViewModel;
use Marketplace\Component\User\Utils\Flash\FlashMessageInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class ResetPasswordView
{
    public function __construct(
        private Environment $twig,
        private UrlGeneratorInterface $urlGenerator,
        private FlashMessageInterface $flashMessage,
        private TranslatorInterface $translator,
    ) {
    }

    public function generateView(ResetPasswordViewModel $viewModel): Response
    {
        if ($viewModel->valid && $viewModel->tokenExpired === false) {
            $this->flashMessage->add(
                'success',
                $this->translator->trans('resetPassword.success', domain: 'translations')
            );
            return new RedirectResponse($this->urlGenerator->generate('login'));
        }

        if ($viewModel->tokenExpired === true) {
            $this->flashMessage->add(
                'error',
                $this->translator->trans('resetPassword.tokenExpired', domain: 'translations')
            );
            return new RedirectResponse($this->urlGenerator->generate('security_askResetPassword'));
        }

        return new Response($this->twig->render('security/reset-password.html.twig', compact('viewModel')));
    }
}
