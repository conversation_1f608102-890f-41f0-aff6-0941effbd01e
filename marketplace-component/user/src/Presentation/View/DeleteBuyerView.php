<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\View;

use Marketplace\Component\User\Presentation\ViewModel\DeleteBuyerViewModel;
use Marketplace\Component\User\Utils\Flash\FlashMessageInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class DeleteBuyerView
{
    public function __construct(
        private RequestStack $requestStack,
        private FlashMessageInterface $flashMessage,
        private TranslatorInterface $translator,
        private UrlGeneratorInterface $urlGenerator,
    ) {
    }

    public function generateView(DeleteBuyerViewModel $viewModel): RedirectResponse
    {
        $error = false;
        $statusCode = null;
        if (!$viewModel->companyExist) {
            $error = true;
            $statusCode = Response::HTTP_NOT_FOUND;
            $this->flashMessage
                ->add('error', $this->translator->trans(id:'company.doesnt.exist', domain:'translations'));
        }
        if ($viewModel->companyDeleted) {
            $error = true;
            $statusCode = Response::HTTP_BAD_REQUEST;
            $this->flashMessage
                ->add('error', $this->translator->trans(id:'company.already.deleted', domain:'translations'));
        }
        if (!$error) {
            $this->flashMessage
                ->add('success', $this->translator->trans(id:'company.deleted', domain:'translations'));
        }
        return $this->redirectToReferrer($statusCode);
    }

    private function redirectToReferrer(int $statusCode = null): RedirectResponse
    {
        $currentRequest = $this->requestStack->getCurrentRequest();
        if ($currentRequest !== null && $currentRequest->server->get('HTTP_REFERRER')) {
            return new RedirectResponse(
                $currentRequest->server->get('HTTP_REFERRER'),
                $statusCode ?? Response::HTTP_OK
            );
        }
        return new RedirectResponse($this->urlGenerator->generate('buyers_list'));
    }
}
