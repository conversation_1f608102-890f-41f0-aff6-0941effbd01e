<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\View\User;

use Marketplace\Component\CleanArchiCore\Utils\Flash\FlashMessageInterface;
use Marketplace\Component\User\Presentation\ViewModel\User\ShowUserViewModel;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class ShowUserView
{
    private const PAGE_URL = 'pages/user-profile.html.twig';
    public function __construct(
        private Environment $twig,
        private FlashMessageInterface $flashMessage,
        private UrlGeneratorInterface $urlGenerator,
        private TranslatorInterface $translator,
    ) {
    }

    public function generateView(ShowUserViewModel $viewModel): Response
    {
        if ($viewModel->getUser() === null) {
            $errors = $viewModel->getErrors();

            foreach ($errors as $error) {
                $this->flashMessage
                    ->add('error', $this->translator->trans(id: $error->getMessage(), domain: 'translations'));
            }
            return new RedirectResponse($this->urlGenerator->generate('home'));
        }

        return new Response(
            $this->twig->render(
                self::PAGE_URL,
                ['viewModel' => $viewModel]
            )
        );
    }
}
