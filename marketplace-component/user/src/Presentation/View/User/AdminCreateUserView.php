<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\View\User;

use Marketplace\Component\CleanArchiCore\Utils\Flash\FlashMessageInterface;
use Marketplace\Component\User\Presentation\ViewModel\User\AdminCreateUserViewModel;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Twig\Environment;

class AdminCreateUserView
{
    private const PAGE_URL = 'pages/admin/user-create-profile.html.twig';
    public function __construct(
        private Environment $twig,
        private UrlGeneratorInterface $urlGenerator,
        private FlashMessageInterface $flashMessage,
    ) {
    }

    public function generateView(AdminCreateUserViewModel $viewModel): Response
    {
        if ($viewModel->hasFlashMessage()) {
            foreach ($viewModel->flashMessages as ['type' => $type, 'message' => $message]) {
                $this->flashMessage->add($type, $message);
            }
        }

        if ($viewModel->isSucceed()) {
            return new RedirectResponse($this->urlGenerator->generate('users-list'));
        }
        return new Response(
            $this->twig->render(
                self::PAGE_URL,
                ['viewModel' => $viewModel]
            )
        );
    }
}
