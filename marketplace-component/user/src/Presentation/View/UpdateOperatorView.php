<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\View;

use Marketplace\Component\CleanArchiCore\Utils\Flash\FlashMessageInterface;
use Marketplace\Component\User\Presentation\ViewModel\UpdateOperatorViewModel;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class UpdateOperatorView
{
    public function __construct(
        private Environment $twig,
        private UrlGeneratorInterface $urlGenerator,
        private FlashMessageInterface $flashMessage,
        private TranslatorInterface $translator,
    ) {
    }

    public function generateView(UpdateOperatorViewModel $viewModel): Response
    {
        if ($viewModel->saved === true) {
            $this->flashMessage->add(
                'success',
                $this->translator->trans(id: 'operator.update.success', domain: 'translations')
            );
            return new RedirectResponse($this->urlGenerator->generate('operator_list'));
        }
        return new Response($this->twig->render('operator/operator-update.html.twig', compact('viewModel')));
    }
}
