<?php

namespace Marketplace\Component\User\Presentation\View\Company;

use Marketplace\Component\User\Presentation\ViewModel\Company\UpdateCompanyViewModel;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Twig\Environment;

class EditCompanyView
{
    private const PAGE_URL = 'pages/user-edit-company.html.twig';
    public function __construct(
        private Environment $twig,
        private UrlGeneratorInterface $urlGenerator,
    ) {
    }

    public function generateView(UpdateCompanyViewModel $viewModel): Response
    {
        if ($viewModel->isSucceed()) {
            return new RedirectResponse($this->urlGenerator->generate('company-infos'));
        }
        return new Response(
            $this->twig->render(
                self::PAGE_URL,
                ['viewModel' => $viewModel]
            )
        );
    }
}
