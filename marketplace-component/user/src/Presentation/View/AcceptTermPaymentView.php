<?php

namespace Marketplace\Component\User\Presentation\View;

use Marketplace\Component\CleanArchiCore\Presentation\View\AbstractView;
use Marketplace\Component\CleanArchiCore\Utils\Flash\FlashMessageInterface;
use Marketplace\Component\User\Presentation\ViewModel\AcceptTermPaymentViewModel;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class AcceptTermPaymentView extends AbstractView
{
    /**
     * AskTermPaymentView constructor.
     * @param SerializerInterface $serializer
     * @param FlashMessageInterface $flashMessage
     * @param TranslatorInterface $translator
     */
    public function __construct(
        private FlashMessageInterface $flashMessage,
        private TranslatorInterface $translator
    ) {
    }

    public function generateView(AcceptTermPaymentViewModel $viewModel): RedirectResponse
    {
        if (!$viewModel->isSuccess()) {
            $this->flashMessage->add(
                'error',
                $this->translator->trans('accept_term_payment.fail', domain: 'translations')
            );
        }

        return new RedirectResponse($this->urlGenerator->generate('buyer_show', [ 'id' => $viewModel->getCompanyId()]));
    }
}
