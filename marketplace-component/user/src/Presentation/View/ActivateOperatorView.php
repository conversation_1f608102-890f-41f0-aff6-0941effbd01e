<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\View;

use Marketplace\Component\CleanArchiCore\Presentation\View\AbstractView;
use Marketplace\Component\CleanArchiCore\Utils\Flash\FlashMessageInterface;
use Marketplace\Component\User\Presentation\ViewModel\ActivateOperatorViewModel;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class ActivateOperatorView extends AbstractView
{
    public function __construct(
        public RequestStack $requestStack,
        private FlashMessageInterface $flashMessage,
        private TranslatorInterface $translator,
        public UrlGeneratorInterface $urlGenerator,
    ) {
    }

    public function generateView(ActivateOperatorViewModel $viewModel): RedirectResponse
    {
        $error = false;
        if (!$viewModel->operatorExist) {
            $error = true;
            $this->flashMessage
                ->add('error', $this->translator->trans(id: 'operator.doesnt.exist', domain: 'translations'));
        }
        if (!$error && $viewModel->activated === true) {
            $this->flashMessage->add(
                'success',
                $this->translator->trans(id: 'operator.activate.success', domain: 'translations')
            );
        }
        return $this->redirectBack('operator_list');
    }
}
