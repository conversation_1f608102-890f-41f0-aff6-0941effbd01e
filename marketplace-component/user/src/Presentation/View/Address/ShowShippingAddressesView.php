<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\View\Address;

use Marketplace\Component\User\Presentation\ViewModel\Address\ShowShippingAddressesViewModel;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Twig\Environment;

class ShowShippingAddressesView
{
    public function __construct(
        private Environment $twig,
        private UrlGeneratorInterface $urlGenerator,
    ) {
    }

    public function generateView(ShowShippingAddressesViewModel $viewModel): Response
    {
        if (empty($viewModel->getAddresses()) && $viewModel->isCanCreate()) {
            return new RedirectResponse($this->urlGenerator->generate('shipping-address-create'));
        }

        return new Response(
            $this->twig->render(
                'pages/user-shipping-addresses.html.twig',
                ['viewModel' => $viewModel]
            )
        );
    }
}
