<?php

namespace Marketplace\Component\User\Presentation\View\Address;

use Marketplace\Component\User\Presentation\ViewModel\Address\EditBillingAddressViewModel;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Twig\Environment;

class EditBillingAddressView
{
    public function __construct(
        private Environment $twig,
        private UrlGeneratorInterface $urlGenerator,
    ) {
    }

    public function generateView(EditBillingAddressViewModel $viewModel): Response
    {
        if ($viewModel->isSucceed()) {
            return new RedirectResponse($this->urlGenerator->generate('billing-address'));
        }
        return new Response(
            $this->twig->render(
                'pages/user-form-billing-address.html.twig',
                ['viewModel' => $viewModel]
            )
        );
    }
}
