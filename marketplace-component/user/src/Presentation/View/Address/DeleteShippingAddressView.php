<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Presentation\View\Address;

use Marketplace\Component\CleanArchiCore\Presentation\View\AbstractView;
use Marketplace\Component\User\Presentation\ViewModel\Address\DeleteShippingAddressViewModel;
use Marketplace\Component\CleanArchiCore\Utils\Flash\FlashMessageInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Contracts\Translation\TranslatorInterface;

class DeleteShippingAddressView extends AbstractView
{
    public function __construct(
        private FlashMessageInterface $flashMessage,
        private TranslatorInterface $translator,
    ) {
    }

    public function generateView(DeleteShippingAddressViewModel $viewModel): RedirectResponse
    {
        $error = false;
        if (!$viewModel->addressDeleted) {
            $error = true;
            $this->flashMessage
                ->add('error', $this->translator->trans(id:'user_account.address.doesnt_exist', domain:'translations'));
        }

        if (!$error) {
            $this->flashMessage
                ->add('success', $this->translator->trans(id:'user_account.address.deleted', domain:'translations'));
        }
        return $this->redirectBack('shipping-addresses');
    }
}
