<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\UseCase\IsUserAdmin\DTO;

class IsUserAdminResponse
{
    private bool $isAdmin = false;

    /**
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->isAdmin;
    }

    /**
     * @param bool $isAdmin
     * @return IsUserAdminResponse
     */
    public function setIsAdmin(bool $isAdmin): IsUserAdminResponse
    {
        $this->isAdmin = $isAdmin;
        return $this;
    }
}
