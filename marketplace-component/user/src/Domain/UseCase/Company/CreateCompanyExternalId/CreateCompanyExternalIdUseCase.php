<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\UseCase\Company\CreateCompanyExternalId;

use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Service\CompanyExternalIdGeneratorInterface;
use Marketplace\Component\User\Domain\Presenter\Company\CreateCompanyExternalIdPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\Company\CreateCompanyExternalId\DTO\CreateCompanyExternalIdRequest;
use Marketplace\Component\User\Domain\UseCase\Company\CreateCompanyExternalId\DTO\CreateCompanyExternalIdResponse;

class CreateCompanyExternalIdUseCase
{
    public function __construct(
        private CompanyRepositoryInterface $companyRepository,
        private CompanyExternalIdGeneratorInterface $companyExternalIdGenerator
    ) {
    }

    public function execute(
        CreateCompanyExternalIdRequest $request,
        CreateCompanyExternalIdPresenterInterface $presenter
    ): void {
        $response = new CreateCompanyExternalIdResponse();
        $company = $this->companyRepository->getCompanyById($request->companyId);

        if (!$company instanceof Company) {
            $response->success = false;
            $response->getNotification()->addError('company', 'create.company_external_id.company');
            $presenter->present($response);
            return;
        }

        if ($company->getExternalId() !== null) {
            $response->success = true;
            $presenter->present($response);
            return;
        }

        if (trim($request->postalCode) === '') {
            $response->success = false;
            $response->getNotification()->addError('postalCode', 'create.company_external_id.postalCode');
            $presenter->present($response);
            return;
        }

        do {
            $externalId = $this->companyExternalIdGenerator->generateUniqueExternalId(
                $company->getName(),
                $request->postalCode
            );
            $isExternalIdExist = $this->companyRepository->isExternalIdExist($externalId);
        } while ($isExternalIdExist);

        $company->setExternalId($externalId);
        $this->companyRepository->update($company);

        $response->success = true;
        $presenter->present($response);
    }
}
