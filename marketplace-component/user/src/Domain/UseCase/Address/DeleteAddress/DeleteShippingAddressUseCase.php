<?php

namespace Marketplace\Component\User\Domain\UseCase\Address\DeleteAddress;

use Marketplace\Component\User\Domain\Port\Repository\AddressRepositoryInterface;
use Marketplace\Component\User\Domain\Presenter\Address\DeleteShippingAddressPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\Address\DeleteAddress\DTO\DeleteShippingAddressRequest;
use Marketplace\Component\User\Domain\UseCase\Address\DeleteAddress\DTO\DeleteShippingAddressResponse;

class DeleteShippingAddressUseCase
{
    public function __construct(
        protected AddressRepositoryInterface $addressRepository
    ) {
    }

    /**
     * @param DeleteShippingAddressRequest $request
     * @param DeleteShippingAddressPresenterInterface $presenter
     */
    public function execute(DeleteShippingAddressRequest $request, DeleteShippingAddressPresenterInterface $presenter)
    {
        $result = $this->addressRepository->delete($request->getAddressId());
        $response = new DeleteShippingAddressResponse($result);
        $presenter->present($response);
    }
}
