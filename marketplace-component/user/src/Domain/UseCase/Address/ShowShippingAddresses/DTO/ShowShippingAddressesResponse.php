<?php

namespace Marketplace\Component\User\Domain\UseCase\Address\ShowShippingAddresses\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Error\NotificationTrait;
use Marketplace\Component\User\Domain\Model\Address;

class ShowShippingAddressesResponse
{
    use NotificationTrait;

    /**
     * @var Address[]
     */
    private array $addresses;

    /**
     * @return Address[]
     */
    public function getAddresses(): array
    {
        return $this->addresses;
    }

    /**
     * @param Address[] $addresses
     * @return $this
     */
    public function setAddresses(array $addresses): self
    {
        $this->addresses = $addresses;
        return $this;
    }
}
