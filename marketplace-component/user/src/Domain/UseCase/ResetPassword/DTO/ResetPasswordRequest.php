<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\UseCase\ResetPassword\DTO;

use Assert\LazyAssertionException;
use Marketplace\Component\User\Domain\Assert\Assert;

class ResetPasswordRequest
{
    public function __construct(
        public ?string $plainNewPassword = null,
        public ?string $forgottenPasswordToken = null
    ) {
    }

    public function validate(ResetPasswordResponse $response): bool
    {
        try {
            Assert::lazy()
                ->password($this->plainNewPassword, propertyPath: 'plainNewPassword')
                ->that($this->forgottenPasswordToken, 'forgottenPasswordToken')->notBlank('form.value.mandatory')
                ->verifyNow()
            ;

            return true;
        } catch (LazyAssertionException $exception) {
            foreach ($exception->getErrorExceptions() as $errorException) {
                $response
                    ->getNotification()
                    ->addError($errorException->getPropertyPath(), $errorException->getMessage());
            }

            return false;
        }
    }
}
