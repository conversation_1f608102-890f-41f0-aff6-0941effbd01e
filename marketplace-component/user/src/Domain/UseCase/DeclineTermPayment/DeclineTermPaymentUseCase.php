<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\UseCase\DeclineTermPayment;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CurrencyServiceInterface;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\TranslationInterface;
use Marketplace\Component\Mail\Domain\Model\EmailTemplateSlug;
use Marketplace\Component\Mail\Domain\Port\Service\EmailServiceInterface;
use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\TermPayment;
use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\TermPaymentRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use Marketplace\Component\User\Domain\Presenter\DeclineTermPaymentPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\DeclineTermPayment\DTO\DeclineTermPaymentRequest;
use Marketplace\Component\User\Domain\UseCase\DeclineTermPayment\DTO\DeclineTermPaymentResponse;

final class DeclineTermPaymentUseCase
{
    public function __construct(
        private readonly TermPaymentRepositoryInterface $termPaymentRepository,
        private readonly CompanyRepositoryInterface $companyRepository,
        private readonly EmailServiceInterface $emailService,
        private readonly UserRepositoryInterface $userRepository,
        private readonly TranslationInterface $translation,
        private readonly CurrencyServiceInterface $currencyService
    ) {
    }

    public function execute(DeclineTermPaymentRequest $request, DeclineTermPaymentPresenterInterface $presenter): void
    {
        $response = new DeclineTermPaymentResponse();

        if ($request->companyId === null || $request->validate($response) === false) {
            $presenter->present($response);
            return;
        }

        /** @var Company $company */
        $company = $this->companyRepository->getCompanyById($request->companyId);
        $companyName = $company->getName();
        $currency = $this->currencyService->getCurrencyFromRegion($company->getCountry()->getRegionCode());

        $termPayment = $this->termPaymentRepository->getTermPaymentOfCompany($request->companyId);

        if (!$termPayment instanceof TermPayment) {
            $response->errorMessage = 'term_payment.decline.message.notFound';
            $presenter->present($response);
            return;
        }

        if ($termPayment->isRejected()) {
            $response->errorMessage = 'term_payment.decline.message.already_rejected';
            $presenter->present($response);
            return;
        }

        $termPayment->setStatus(TermPayment::TERM_PAYMENT_REJECTED);

        $this->termPaymentRepository->update($termPayment);

        $admins = $this->userRepository->findAdminFromCompany($request->companyId);
        $adminsEmailList = $this->emailService->buildEmailUserList($admins);
        $locale = 'fr';
        if (!empty($admins)) {
            $locale = $admins[0]->getLanguage();
        }
        $type = $termPayment->getType();

        $type = match ($type) {
            TermPayment::TYPE_30_DAYS_EOM => $this->translation->trans('term_payment.email.term_payment_30', $locale),
            TermPayment::TYPE_45_DAYS_EOM => $this->translation->trans('term_payment.email.term_payment_45', $locale),
            default => 'No term payment'
        };

        $priceFormatter = new \NumberFormatter($locale, \NumberFormatter::CURRENCY);
        $threshold = $priceFormatter->formatCurrency($termPayment->getThreshold() ?? 0, $currency);


        if (!empty($adminsEmailList)) {
            $this->emailService->send(
                [$this->emailService->getNoReplyUser()],
                $adminsEmailList,
                EmailTemplateSlug::TERM_PAYMENT_REFUSED_TO_USER,
                $locale,
                [
                    'message' => $request->message,
                    'clientName' => $companyName,
                    'paymentModality' => $type,
                    'authorizedOutstandingAmount' => $threshold,
                ]
            );
        }

        $response->success = true;
        $presenter->present($response);
    }
}
