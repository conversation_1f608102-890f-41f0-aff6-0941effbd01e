<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\UseCase\User\EditUser;

use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use Marketplace\Component\User\Domain\Presenter\User\EditUserPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\User\EditUser\DTO\EditUserRequest;
use Marketplace\Component\User\Domain\UseCase\User\EditUser\DTO\EditUserResponse;

class EditUserUseCase
{
    public function __construct(
        protected UserRepositoryInterface $userRepository,
    ) {
    }
    /**
     * @param EditUserRequest $request
     * @param EditUserPresenterInterface $presenter
     */
    public function execute(EditUserRequest $request, EditUserPresenterInterface $presenter)
    {
        $response = new EditUserResponse();
        if (!$this->checkAddressValidity($request, $response)) {
            $presenter->present($response);
            return;
        }

        /** @var int $userId */
        $userId = $request->getId();
        $user = $this->userRepository->findById($userId);

        if (!$user instanceof User) {
            $response->getNotification()->addError('userId', 'user.not_found');
            $presenter->present($response);
            return;
        }

        $user
            ->setCivility($request->getCivility())
            ->setFirstname($request->getFirstname())
            ->setLastname($request->getLastname())
            ->setEmail($request->getEmail())
            ->setPhone($request->getPhone())
        ;

        $this->userRepository->update($user);

        $response->setSucceed(true);
        $presenter->present($response);
    }

    protected function checkAddressValidity(EditUserRequest $request, EditUserResponse $response): bool
    {
        $isValid = $request->validate($response);
        if (!$isValid) {
            return false;
        }
        return true;
    }
}
