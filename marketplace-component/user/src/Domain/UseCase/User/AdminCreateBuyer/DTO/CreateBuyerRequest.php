<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\UseCase\User\AdminCreateBuyer\DTO;

use Assert\AssertionFailedException;
use Assert\LazyAssertionException;
use Marketplace\Component\User\Domain\Assert\Assert;
use Marketplace\Component\User\Domain\Assert\Assertion;
use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Service\CaptchaServiceInterface;
use Marketplace\Component\User\Domain\Port\Service\VatNumberValidatorInterface;

class CreateBuyerRequest
{
    private const MIN_LENGTH_PASSWORD = 10;
    private const IDENTIFICATION_LENGTH = 14;

    public string $plainPassword;

    public Company $company;

    public bool $batchMode = false;

    public array $roles = [];

    public function __construct(
        public ?string $captcha = null,
        public ?string $email = null,
        public ?string $firstname = null,
        public ?string $lastname = null,
        public ?string $phone = null,
        public ?string $civility = null,
    ) {
    }

    /**
     * Return true if no errors on the current request, if false, errors are stored in notification response.
     *
     * @param CreateBuyerResponse $response
     * @return bool
     */
    public function validate(CreateBuyerResponse $response): bool
    {
        $identification = $this->company->getIdentification();
        if ($identification !== null) {
            $identification = str_replace(' ', '', $identification);
            $this->company->setIdentification($identification);
        }
        try {
            Assert::lazy()
                ->that($this->email, 'email')->notBlank('buyer_creation.value.mandatory')
                ->email('buyer_creation.email.invalid')
                ->that($this->firstname, 'firstname')->notBlank('buyer_creation.value.mandatory')
                ->that($this->lastname, 'lastname')->notBlank('buyer_creation.value.mandatory')
                ->that($this->company->getCountry()->getCode(), 'company.country')
                ->notBlank('buyer_creation.value.mandatory')
                ->that($this->civility, 'civility')->notBlank('buyer_creation.value.mandatory')
                ->that($this->phone, 'phone')
                ->notBlank('buyer_creation.value.mandatory')
                ->regex('/^[\+]?[0-9]+$/', 'buyer_registration.email.invalid')
                ->that($this->company->getVatNumber(), 'company.vatNumber')
                ->notBlank('buyer_creation.value.mandatory')
                ->that($this->company->getIdentification(), 'company.identification')
                ->nullOr()
                ->digit('buyer_creation.identification.invalid_format')
                ->verifyNow();

            return true;
        } catch (LazyAssertionException $exception) {
            foreach ($exception->getErrorExceptions() as $errorException) {
                $response
                    ->getNotification()
                    ->addError($errorException->getPropertyPath(), $errorException->getMessage());
            }

            return false;
        }
    }

    public function validateVatNumber(CreateBuyerResponse $response, VatNumberValidatorInterface $validator): bool
    {
        if ($this->batchMode === true) {
            return true;
        }
        try {
            $vatNumber = $this->company->getVatNumber();
            $vatNumber = $vatNumber !== null ? str_replace(' ', '', $vatNumber) : null;
            $this->company->setVatNumber($vatNumber);
            Assertion::vatNumber(
                $vatNumber,
                'buyer_creation.vatnumber.invalid_format',
                $validator,
                'company.vatNumber'
            );

            return true;
        } catch (AssertionFailedException $failedException) {
            $response->getNotification()->addError($failedException->getPropertyPath(), $failedException->getMessage());

            return false;
        }
    }

    public function validateUniqueEmail(CreateBuyerResponse $response, UserRepositoryInterface $repository): bool
    {
        try {
            Assertion::nonUniqueEmail(
                $this->email,
                'buyer_creation.email.nonUnique',
                $repository,
                'email'
            );

            return true;
        } catch (AssertionFailedException $failedException) {
            $response->getNotification()->addError($failedException->getPropertyPath(), $failedException->getMessage());

            return false;
        }
    }
}
