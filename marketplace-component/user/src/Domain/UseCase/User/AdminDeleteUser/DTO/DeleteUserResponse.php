<?php

namespace Marketplace\Component\User\Domain\UseCase\User\AdminDeleteUser\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Error\NotificationTrait;

class DeleteUserResponse
{
    use NotificationTrait;

    private bool $succeed = false;

    /**
     * @return bool
     */
    public function isSucceed(): bool
    {
        return $this->succeed;
    }

    /**
     * @param bool $succeed
     * @return $this
     */
    public function setSucceed(bool $succeed): self
    {
        $this->succeed = $succeed;
        return $this;
    }
}
