<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\UseCase\User\ListUsers\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Model\Interface\PaginatedRequestInterface;

class ListUsersRequest implements PaginatedRequestInterface
{
    public function __construct(
        private int $userId,
        private int $requestedPage = self::DEFAULT_PAGE,
        private int $itemPerPage = self::DEFAULT_VENDORS_PER_PAGE,
    ) {
    }

    public static function create(...$params): self
    {
        return new self(...$params);
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function getRequestedPage(): int
    {
        return $this->requestedPage;
    }

    public function getItemPerPage(): int
    {
        return $this->itemPerPage;
    }
}
