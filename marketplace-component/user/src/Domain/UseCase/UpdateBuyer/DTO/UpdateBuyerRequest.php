<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\UseCase\UpdateBuyer\DTO;

use Assert\LazyAssertionException;
use Marketplace\Component\User\Domain\Assert\Assert;
use Marketplace\Component\User\Domain\Model\Country;

class UpdateBuyerRequest
{
    private int $id;
    private ?string $name = null;
    private ?string $accountingEmail = null;
    private ?string $vatNumber = null;
    private ?Country $country = null;
    private ?string $accountingPhone = null;
    private ?string $identification = null;
    private ?int $threshold = null;
    private ?string $externalId = null;
    private ?float $inProgressTermPaymentAmount = null;
    private bool $addExtraFees = false;


    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    public function getAccountingEmail(): ?string
    {
        return $this->accountingEmail;
    }

    public function setAccountingEmail(?string $accountingEmail): void
    {
        $this->accountingEmail = $accountingEmail;
    }

    public function getVatNumber(): ?string
    {
        return $this->vatNumber;
    }

    public function setVatNumber(?string $vatNumber): void
    {
        $this->vatNumber = $vatNumber;
    }

    public function getCountry(): ?Country
    {
        return $this->country;
    }

    public function setCountry(?Country $country): void
    {
        $this->country = $country;
    }

    public function getAccountingPhone(): ?string
    {
        return $this->accountingPhone;
    }

    public function setAccountingPhone(?string $accountingPhone): void
    {
        $this->accountingPhone = $accountingPhone;
    }

    public function getIdentification(): ?string
    {
        return $this->identification;
    }

    public function setIdentification(?string $identification): void
    {
        $this->identification = $identification;
    }

    /**
     * @return int|null
     */
    public function getThreshold(): ?int
    {
        return $this->threshold;
    }

    /**
     * @param int|null $threshold
     * @return $this
     */
    public function setThreshold(?int $threshold): self
    {
        $this->threshold = $threshold;
        return $this;
    }



    public function validate(UpdateBuyerResponse $response): bool
    {
        try {
            $assert = Assert::lazy()
                ->that($this->name, 'name')->notBlank('validator.mandatory')
                ->that($this->vatNumber, 'vatNumber')->notBlank('validator.mandatory')
                ->that($this->accountingEmail, 'accountingEmail')
                    ->notBlank('validator.mandatory')
                    ->email('validator.email_invalid')
                ->that($this->country?->getCode(), 'country')->notBlank('validator.mandatory')
                ->that($this->threshold, 'threshold')->nullOr()->notBlank('validator.mandatory');
            $assert->verifyNow();

            return true;
        } catch (LazyAssertionException $exception) {
            foreach ($exception->getErrorExceptions() as $errorException) {
                $response->getNotification()
                    ->addError($errorException->getPropertyPath(), $errorException->getMessage());
            }

            return false;
        }
    }

    /**
     * @return string|null
     */
    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    /**
     * @param string|null $externalId
     */
    public function setExternalId(?string $externalId): void
    {
        $this->externalId = $externalId;
    }

    /**
     * @return float|null
     */
    public function getInProgressTermPaymentAmount(): ?float
    {
        return $this->inProgressTermPaymentAmount;
    }

    /**
     * @param float|null $inProgressTermPaymentAmount
     * @return $this
     */
    public function setInProgressTermPaymentAmount(?float $inProgressTermPaymentAmount): self
    {
        $this->inProgressTermPaymentAmount = $inProgressTermPaymentAmount;
        return $this;
    }

    /**
     * @return bool
     */
    public function isAddExtraFees(): bool
    {
        return $this->addExtraFees;
    }

    /**
     * @param bool $addExtraFees
     * @return $this
     */
    public function setAddExtraFees(bool $addExtraFees): self
    {
        $this->addExtraFees = $addExtraFees;

        return $this;
    }
}
