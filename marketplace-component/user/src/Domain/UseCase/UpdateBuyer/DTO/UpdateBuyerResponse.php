<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\UseCase\UpdateBuyer\DTO;

use Marketplace\Component\User\Domain\Error\NotificationTrait;
use Marketplace\Component\User\Domain\Model\Company;

class UpdateBuyerResponse
{
    use NotificationTrait;

    private ?Company $company = null;
    private bool $succeed = false;

    public function setCompany(?Company $company): void
    {
        $this->company = $company;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    /**
     * @return bool
     */
    public function isSucceed(): bool
    {
        return $this->succeed;
    }

    /**
     * @param bool $succeed
     * @return UpdateBuyerResponse
     */
    public function setSucceed(bool $succeed): UpdateBuyerResponse
    {
        $this->succeed = $succeed;
        return $this;
    }
}
