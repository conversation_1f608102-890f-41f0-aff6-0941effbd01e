<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\UseCase\UpdateCompanyUser\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Error\NotificationTrait;
use Marketplace\Component\User\Domain\Model\User;

class UpdateCompanyUserResponse
{
    use NotificationTrait;

    private ?User $user = null;
    public bool $updated = false;
    public ?int $companyId = null;

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;
        return $this;
    }
}
