<?php

namespace Marketplace\Component\User\Domain\UseCase\AskTermPayment\DTO;

class AskTermPaymentRequest
{
    public function __construct(
        private int $companyId,
        private string $userName,
        private string $clientName
    ) {
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function setCompanyId(int $companyId): self
    {
        $this->companyId = $companyId;
        return $this;
    }

    public function getUserName(): string
    {
        return $this->userName;
    }

    public function setUserName(string $userName): self
    {
        $this->userName = $userName;
        return $this;
    }

    /**
     * @return string
     */
    public function getClientName(): string
    {
        return $this->clientName;
    }

    public function setClientName(string $clientName): self
    {
        $this->clientName = $clientName;
        return $this;
    }
}
