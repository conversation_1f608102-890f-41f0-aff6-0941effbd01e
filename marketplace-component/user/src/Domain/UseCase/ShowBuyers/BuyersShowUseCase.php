<?php

namespace Marketplace\Component\User\Domain\UseCase\ShowBuyers;

use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\MerchantRepositoryInterface;
use Marketplace\Component\User\Domain\Presenter\BuyersShowPresenterInterface;
use Marketplace\Component\User\Domain\Presenter\MerchantsShowPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\ShowBuyers\DTO\BuyersShowRequest;
use Marketplace\Component\User\Domain\UseCase\ShowBuyers\DTO\BuyersShowResponse;
use Marketplace\Component\User\Domain\UseCase\ShowMerchants\DTO\MerchantsShowRequest;
use Marketplace\Component\User\Domain\UseCase\ShowMerchants\DTO\MerchantsShowResponse;

/**
 * Class BuyersShowUseCase
 * @package Marketplace\Component\User\Domain\UseCase\ShowBuyers
 */
class BuyersShowUseCase
{
    /**
     * BuyersShowUseCase constructor.
     * @param CompanyRepositoryInterface $BuyerRepository
     */
    public function __construct(private CompanyRepositoryInterface $BuyerRepository)
    {
    }

    /**
     * @param BuyersShowRequest $request
     * @param BuyersShowPresenterInterface $presenter
     */
    public function execute(
        BuyersShowRequest $request,
        BuyersShowPresenterInterface $presenter
    ): void {
        $filteredName = $request->getFilteredName();
        $filteredVat = $request->getFilteredVat();
        $filteredCountry = $request->getFilteredCountry();
        $filteredStatus = $request->getFilteredStatus();
        $filteredDate = $request->getFilteredCreationDate();
        $buyers = $this->BuyerRepository->findBuyerCompanies(
            $filteredName,
            $filteredVat,
            $filteredCountry,
            $filteredStatus,
            $filteredDate
        );
        $response = new BuyersShowResponse(
            $buyers,
            $request->getRequestedPage(),
            $request->getItemPerPage(),
            $filteredName,
            $filteredVat,
            $filteredCountry,
            $filteredStatus,
            $filteredDate
        );

        $presenter->present($response);
    }
}
