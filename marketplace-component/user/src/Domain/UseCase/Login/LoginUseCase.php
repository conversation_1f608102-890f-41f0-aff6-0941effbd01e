<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\UseCase\Login;

use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Service\PasswordHashInterface;
use Marketplace\Component\User\Domain\Presenter\LoginPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\Login\DTO\LoginRequest;
use Marketplace\Component\User\Domain\UseCase\Login\DTO\LoginResponse;

class LoginUseCase
{
    public function __construct(
        private UserRepositoryInterface $userRepository,
        private PasswordHashInterface $passwordHash,
    ) {
    }

    public function execute(LoginRequest $request, LoginPresenterInterface $presenter): void
    {
        $response = new LoginResponse();
        if ($request->validate($response)) {
            $user = $this->userRepository->findByEmail($request->email);

            if ($user) {
                $passwordValid = $this->passwordHash->checkPassword($request->password, $user->getPassword());
            }

            $response->setUser($user);
            $response->setPasswordValid($passwordValid ?? false);
        }

        $presenter->present($response);
    }
}
