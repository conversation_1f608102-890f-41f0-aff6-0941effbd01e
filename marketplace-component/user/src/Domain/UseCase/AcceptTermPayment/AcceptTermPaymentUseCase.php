<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\UseCase\AcceptTermPayment;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CurrencyServiceInterface;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\TranslationInterface;
use Marketplace\Component\Mail\Domain\Model\EmailTemplateSlug;
use Marketplace\Component\Mail\Domain\Port\Service\EmailServiceInterface;
use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\TermPayment;
use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\TermPaymentRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use Marketplace\Component\User\Domain\Presenter\AcceptTermPaymentPresenterInterface;
use Marketplace\Component\User\Domain\UseCase\AcceptTermPayment\DTO\AcceptTermPaymentRequest;
use Marketplace\Component\User\Domain\UseCase\AcceptTermPayment\DTO\AcceptTermPaymentResponse;

final class AcceptTermPaymentUseCase
{
    public function __construct(
        private readonly TermPaymentRepositoryInterface $termPaymentRepository,
        private readonly EmailServiceInterface $emailService,
        private readonly UserRepositoryInterface $userRepository,
        private readonly CompanyRepositoryInterface $companyRepository,
        private readonly TranslationInterface $translation,
        private readonly CurrencyServiceInterface $currencyService
    ) {
    }

    public function execute(
        AcceptTermPaymentRequest $request,
        AcceptTermPaymentPresenterInterface $presenter
    ): void {
        $response = new AcceptTermPaymentResponse();
        $companyId = $request->getCompanyId();

        $response->setCompanyId($companyId);

        /** @var Company $company */
        $company = $this->companyRepository->getCompanyById($companyId);
        $companyName = $company->getName();
        $currency = $this->currencyService->getCurrencyFromRegion($company->getCountry()->getRegionCode());

        $threshold = $request->getThreshold();
        if ($threshold < 0) {
            $response->setSuccess(false);
            $presenter->present($response);
            return;
        }


        $type = $request->getType();
        if ($type !== TermPayment::TYPE_30_DAYS_EOM && $type !== TermPayment::TYPE_45_DAYS_EOM) {
            $response->setSuccess(false);
            $presenter->present($response);
            return;
        }

        $this->termPaymentRepository->acceptTermPayment($companyId, $request->getThreshold(), $type);

        $admins = $this->userRepository->findAdminFromCompany($companyId);
        $adminsEmailList = $this->emailService->buildEmailUserList($admins);
        $locale = 'fr';
        if (!empty($admins)) {
            $locale = $admins[0]->getLanguage();
        }

        $type = match ($type) {
            TermPayment::TYPE_30_DAYS_EOM => $this->translation->trans('term_payment.email.term_payment_30', $locale),
            TermPayment::TYPE_45_DAYS_EOM => $this->translation->trans('term_payment.email.term_payment_45', $locale)
        };

        $priceFormatter = new \NumberFormatter($locale, \NumberFormatter::CURRENCY);
        $threshold = $priceFormatter->formatCurrency($threshold, $currency);

        if (!empty($adminsEmailList)) {
            $this->emailService->send(
                [$this->emailService->getNoReplyUser()],
                $adminsEmailList,
                EmailTemplateSlug::TERM_PAYMENT_ACCEPTED_TO_USER,
                $locale,
                [
                    'clientName' => $companyName,
                    'paymentModality' => $type,
                    'authorizedOutstandingAmount' => $threshold,
                ]
            );
        }

        $response->setSuccess(true);
        $presenter->present($response);
    }
}
