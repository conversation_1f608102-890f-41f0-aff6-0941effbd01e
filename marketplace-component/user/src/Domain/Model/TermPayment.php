<?php

namespace Marketplace\Component\User\Domain\Model;

class TermPayment
{
    public const TERM_PAYMENT_REQUESTED = "TERM_PAYMENT_REQUESTED";
    public const TERM_PAYMENT_ACCEPTED = "TERM_PAYMENT_ACCEPTED";
    public const TERM_PAYMENT_REJECTED = "TERM_PAYMENT_REJECTED";

    public const TYPE_30_DAYS_EOM = "30-days-eom";
    public const TYPE_45_DAYS_EOM = "45-days-eom";

    /**
     * @var ?int
     */
    private ?int $id;

    /**
     * @var string
     */
    private string $status;

    private ?int $threshold = null;

    private ?string $type = null;

    /**
     * @return ?int
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param ?int $id
     * @return $this
     */
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return $this
     */
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function isRejected(): bool
    {
        return $this->status === self::TERM_PAYMENT_REJECTED;
    }

    /**
     * @return int|null
     */
    public function getThreshold(): ?int
    {
        return $this->threshold;
    }

    /**
     * @param int|null $threshold
     * @return $this
     */
    public function setThreshold(?int $threshold): self
    {
        $this->threshold = $threshold;
        return $this;
    }

    public function isAccepted(): bool
    {
        return $this->status === self::TERM_PAYMENT_ACCEPTED;
    }

    public function isRequested(): bool
    {
        return $this->status === self::TERM_PAYMENT_REQUESTED;
    }

    /**
     * @return string|null
     */
    public function getType(): ?string
    {
        return $this->type;
    }

    /**
     * @param string|null $type
     * @return $this
     */
    public function setType(?string $type): self
    {
        $this->type = $type;
        return $this;
    }
}
