<?php

declare(strict_types=1);

namespace Marketplace\Component\User\Domain\Assert;

use Assert\Assertion as BaseAssertion;
use Marketplace\Component\User\Domain\Exception\InvalidVatNumberException;
use Marketplace\Component\User\Domain\Exception\NonUniqueEmailException;
use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use Marketplace\Component\User\Domain\Port\Service\VatNumberValidatorInterface;

class Assertion extends BaseAssertion
{
    private const INVALID_VAT_NUMBER = 231;
    private const VAT_NUMBER_CHECK_SERVICE_DOWN = 233;

    private const EXISTING_EMAIL = 232;

    public static function vatNumber(
        ?string $value,
        string $message,
        VatNumberValidatorInterface $numberValidator,
        string $propertyPath = null,
        string $messageServiceDown = 'buyer_registration.vatnumber.3rd_party_service_down'
    ): bool {
        if ($value === null || $numberValidator->isValid($value) === false) {
            if (isset($numberValidator->isServiceDown) && $numberValidator->isServiceDown) {
                throw new InvalidVatNumberException($messageServiceDown, self::VAT_NUMBER_CHECK_SERVICE_DOWN, $propertyPath);
            } else {
                throw new InvalidVatNumberException($message, self::INVALID_VAT_NUMBER, $propertyPath);
            }
        }

        return true;
    }

    public static function nonUniqueEmail(
        ?string $email,
        string $message,
        UserRepositoryInterface $userRepository,
        string $propertyPath = null,
    ): bool {
        if ($userRepository->isEmailUnique($email) === false) {
            throw new NonUniqueEmailException($message, self::EXISTING_EMAIL, $propertyPath);
        }

        return true;
    }

    public static function nonUniqueAccountingEmail(
        ?string $email,
        string $message,
        CompanyRepositoryInterface $companyRepository,
        string $propertyPath = null,
    ): bool {
        if ($companyRepository->isEmailUnique($email) === false) {
            throw new NonUniqueEmailException($message, self::EXISTING_EMAIL, $propertyPath);
        }

        return true;
    }
}
