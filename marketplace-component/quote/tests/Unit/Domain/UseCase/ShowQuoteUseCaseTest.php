<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Tests\Unit\Domain\UseCase;

use Marketplace\Component\Quote\Domain\Model\QuoteCatalogue;
use Marketplace\Component\Quote\Domain\Model\QuoteStatus;
use Marketplace\Component\Quote\Domain\Port\Repository\QuoteRepositoryInterface;
use Marketplace\Component\Quote\Domain\Presenter\ShowQuotePresenterInterface;
use Marketplace\Component\Quote\Domain\UseCase\ShowQuote\DTO\ShowQuoteRequest;
use Marketplace\Component\Quote\Domain\UseCase\ShowQuote\DTO\ShowQuoteResponse;
use Marketplace\Component\Quote\Domain\UseCase\ShowQuote\ShowQuoteUseCase;
use Marketplace\Component\Quote\Infrastructure\Adapter\Repository\QuoteCatalogueRepository;
use Prophecy\Argument;
use Prophecy\Prophet;
use PHPUnit\Framework\TestCase;

final class ShowQuoteUseCaseTest extends TestCase
{
    private ShowQuoteUseCase $useCase;

    private ShowQuotePresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements ShowQuotePresenterInterface {
            public ShowquoteResponse $response;

            public function present(ShowquoteResponse $response): void
            {
                $this->response = $response;
            }
        };

        $prophecy = new Prophet();
        $quoteCatalogueRepository = $prophecy->prophesize(QuoteCatalogueRepository::class);
        $quoteCatalogueRepository->findById(
            Argument::type("int")
        )->will(function ($args) {
            $quoteId = $args[0];
            $quote = new QuoteCatalogue();
            $quote->setQuoteId($quoteId)
                ->setStatus(QuoteStatus::STATUS_IN_PROGRESS);
            return $quote;
        });

        $this->useCase = new ShowQuoteUseCase(
            $this->createMock(QuoteRepositoryInterface::class),
            $quoteCatalogueRepository->reveal()
        );
    }

    /**
     * @param ShowQuoteRequest $request
     * @param ShowQuoteResponse $expected
     * @dataProvider provideExecute
     */
    public function testExecute(ShowQuoteRequest $request, ShowQuoteResponse $expected)
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($expected, $this->presenter->response);
    }

    public function provideExecute()
    {
        // Given: I want to show open quote with id = 1
        $quote = (new QuoteCatalogue())
            ->setQuoteId(1)
            ->setStatus(QuoteStatus::STATUS_IN_PROGRESS);
        // When: I summit the request
        $request = new ShowQuoteRequest(1, ShowQuoteRequest::USER_BUYER);
        $response = new ShowQuoteResponse($quote);
        // Then: I get successful response with the quote id 1
        yield 'Successful response' => [$request, $response];

        // Given: I want to show an unexisting open quote
        $quote = (new QuoteCatalogue())
            ->setQuoteId(0)
            ->setStatus(QuoteStatus::STATUS_IN_PROGRESS);
        // When: I summit the request
        $request = new ShowQuoteRequest(0, ShowQuoteRequest::USER_BUYER);
        $response = new ShowQuoteResponse($quote);
        // Then: I get unexisting quote error
        yield 'unexisting quote' => [$request, $response];
    }
}
