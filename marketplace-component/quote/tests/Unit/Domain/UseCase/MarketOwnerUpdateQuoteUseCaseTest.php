<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Tests\Unit\Domain\UseCase;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CheckMerchantServiceInterface;
use Marketplace\Component\Quote\Domain\Enum\QuoteActionEnum;
use Marketplace\Component\Quote\Domain\Exception\QuoteException;
use Marketplace\Component\Quote\Domain\Model\Quote;
use Marketplace\Component\Quote\Domain\Model\QuoteMerchant;
use Marketplace\Component\Quote\Domain\Port\Repository\QuoteRepositoryInterface;
use Marketplace\Component\Quote\Domain\Port\Service\QuoteWorkFlowServiceInterface;
use Marketplace\Component\Quote\Domain\Presenter\MarketOwnerUpdateQuotePresenterInterface;
use Marketplace\Component\Quote\Domain\Strategy\SyncMkOwnerQuoteUpdateContext;
use Marketplace\Component\Quote\Domain\UseCase\MarketOwnerUpdateQuoteUseCase\DTO\MarketOwnerUpdateQuoteResponse;
use Marketplace\Component\Quote\Domain\UseCase\MarketOwnerUpdateQuoteUseCase\DTO\MarketOwnerUpdateQuoteRequest;
use Marketplace\Component\Quote\Domain\UseCase\MarketOwnerUpdateQuoteUseCase\MarketOwnerUpdateQuoteUseCase;
use Marketplace\Component\Quote\Infrastructure\Strategy\Action\QuoteUpdateStatusContext;
use PhpParser\Node\Arg;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Psr\Log\LoggerInterface;

final class MarketOwnerUpdateQuoteUseCaseTest extends TestCase
{
    private MarketOwnerUpdateQuoteUseCase $useCase;

    private MarketOwnerUpdateQuotePresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements MarketOwnerUpdateQuotePresenterInterface {
            public MarketOwnerUpdateQuoteResponse $response;
            public function present(MarketOwnerUpdateQuoteResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        $checkMerchantService = $prophet->prophesize(CheckMerchantServiceInterface::class);
        $checkMerchantService->isMKPOwnerMerchant(Argument::type('int'))->will(function ($args) {
            $merchantId = $args[0];
            return $merchantId !== 0;
        });
        $quoteRepository = $prophet->prophesize(QuoteRepositoryInterface::class);
        $quoteRepository->findById(Argument::type('int'))->will(function ($args) {
            $quoteId = $args[0];
            if ($quoteId === 1) {
                throw new QuoteException('error');
            }
            $merchant = new QuoteMerchant();
            $merchant->setDistantId($quoteId);
            return (new Quote())->setMerchant($merchant);
        });
        $context = $prophet->prophesize(SyncMkOwnerQuoteUpdateContext::class);
        $context->process(Argument::type(MarketOwnerUpdateQuoteRequest::class), Argument::type(Quote::class))
            ->willReturn(true);
        /** @var QuoteWorkFlowServiceInterface|ObjectProphecy $quoteWService */
        $quoteWService = $prophet->prophesize(QuoteWorkFlowServiceInterface::class);
        $quoteWService->getActionFromStatus(Argument::type('string'), Argument::type(Quote::class))->willReturn(QuoteActionEnum::VENDOR_SUBMIT);
        $this->useCase = new MarketOwnerUpdateQuoteUseCase(
            $quoteRepository->reveal(),
            $checkMerchantService->reveal(),
            $context->reveal(),
            $this->createMock(LoggerInterface::class),
            $quoteWService->reveal()
        );
    }

    /**
     * @param MarketOwnerUpdateQuoteRequest $request
     * @param MarketOwnerUpdateQuoteResponse $response
     * @return void
     * @dataProvider provideExecute
     */
    public function testExecute(MarketOwnerUpdateQuoteRequest $request, MarketOwnerUpdateQuoteResponse $response): void
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($response, $this->presenter->response);
    }

    public function provideExecute()
    {
        //given : sync an unexisting quote
        $request = new MarketOwnerUpdateQuoteRequest(
            '1',
            1,
            '',
            [],
            "percent",
            "10",
            "12",
            "",
            ""
        );
        //when : I submit the request
        $response = new MarketOwnerUpdateQuoteResponse();
        $response->success = false;
        $response->getNotification()->addError('quote', 'The 1 quote does not exist.');
        //then : I've got an error, saying that the quote does not exist
        yield [$request, $response];
        //given : sync a quote who does not belong to the MKOwner
        $request = new MarketOwnerUpdateQuoteRequest(
            '0',
            0,
            '',
            [],
            "percent",
            "10",
            "12",
            "",
            ""
        );
        //when : I submit the request
        $response = new MarketOwnerUpdateQuoteResponse();
        $response->success = false;
        $response->getNotification()->addError('merchant', 'The 0 merchant id is not MKP owner.');
        //then : I've got an error, saying that the merchant is not the MKOwner
        yield [$request, $response];
        //given : sync a quote
        $request = new MarketOwnerUpdateQuoteRequest(
            '2',
            2,
            '',
            [],
            "percent",
            "10",
            "12",
            "",
            ""
        );
        //when: I submit the request
        $response = new MarketOwnerUpdateQuoteResponse();
        $response->action = QuoteActionEnum::VENDOR_SUBMIT;
        //then : I've got a success
        yield [$request, $response];
    }
}
