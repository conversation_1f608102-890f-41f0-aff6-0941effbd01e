<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Tests\Unit\Domain\UseCase;

use DateTimeImmutable;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CheckMerchantServiceInterface;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\TranslationInterface;
use Marketplace\Component\CleanArchiCore\Utils\Date\DateNowInterface;
use Marketplace\Component\Discussion\Domain\Model\MessageDetail;
use Marketplace\Component\Discussion\Domain\Model\Thread;
use Marketplace\Component\Discussion\Domain\Port\Service\CreateThreadServiceInterface;
use Marketplace\Component\Discussion\Domain\Port\Service\ReplyThreadBuyerVendor;
use Marketplace\Component\Mail\Domain\Port\Service\EmailServiceInterface;
use Marketplace\Component\Quote\Domain\Model\Quote;
use Marketplace\Component\Quote\Domain\Model\QuoteBuyer;
use Marketplace\Component\Quote\Domain\Model\QuoteCatalogue;
use Marketplace\Component\Quote\Domain\Model\QuoteMerchant;
use Marketplace\Component\Quote\Domain\Model\QuoteStatus;
use Marketplace\Component\Quote\Domain\Port\Repository\QuoteRepositoryInterface;
use Marketplace\Component\Quote\Domain\Port\Service\QuoteContactServiceInterface;
use Marketplace\Component\Quote\Domain\Port\Service\QuoteWorkFlowServiceInterface;
use Marketplace\Component\Quote\Domain\Port\Service\SyncQuoteCreationInterface;
use Marketplace\Component\Quote\Domain\Port\Service\SyncQuoteUpdateInterface;
use Marketplace\Component\Quote\Domain\Presenter\QuoteExpirationPresenterInterface;
use Marketplace\Component\Quote\Domain\UseCase\QuoteActionUseCaseHelper;
use Marketplace\Component\Quote\Domain\UseCase\QuoteExpiration\DTO\QuoteExpirationResponse;
use Marketplace\Component\Quote\Domain\UseCase\QuoteExpiration\QuoteExpirationUseCase;
use Marketplace\Component\User\Domain\Port\Repository\AddressRepositoryInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Psr\Log\LoggerInterface;

final class QuoteExpirationTest extends TestCase
{
    private QuoteExpirationUseCase $useCase;

    private QuoteExpirationPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements QuoteExpirationPresenterInterface {
            public QuoteExpirationResponse $response;

            public function present(QuoteExpirationResponse $response): void
            {
                $this->response = $response;
            }
        };

        $prophecy = new Prophet();
        /** @var QuoteRepositoryInterface|ObjectProphecy $quoteRepositoryMock */
        $quoteRepositoryMock = $prophecy->prophesize(QuoteRepositoryInterface::class);
        $quoteRepositoryMock->findExpiredQuoteId()->willReturn([1, 2]);

        $this->useCase = new QuoteExpirationUseCase(
            $quoteRepositoryMock->reveal(),
            $this->mockQuoteActionUseCaseHelper()
        );
    }

    public function testSuccessful(): void
    {
        $this->useCase->execute($this->presenter);
        $response = $this->presenter->response;
        $this->assertInstanceOf(QuoteExpirationResponse::class, $response);
        $this->assertEquals([1], $response->expiredQuotes);
        $error = [];
        $error [2] = "transition unauthorised";
        $this->assertEquals($error, $response->errors);
    }

    private function mockQuoteActionUseCaseHelper(): QuoteActionUseCaseHelper
    {
        $prophecy = new Prophet();
        /** @var ObjectProphecy $quoteRepositoryMock */
        $quoteRepositoryMock = $prophecy->prophesize(QuoteRepositoryInterface::class);


        $quoteRepositoryMock->findById(Argument::type('int'))
            ->will(function (array $args) {
                [$id] = $args;
                if ($id == 1) {
                    $status = QuoteStatus::STATUS_SUBMIT;
                }
                if ($id == 2) {
                    $status = QuoteStatus::STATUS_IN_PROGRESS; // error status
                }
                $buyer = (new QuoteBuyer())->setLocale("fr");
                $merchant = (new QuoteMerchant())->setId(1)->setLocale("fr");
                return (new Quote())
                    ->setBuyer($buyer)
                    ->setMerchant($merchant)
                    ->setQuoteId($id)
                    ->setStatus($status)
                    ->setThreadId($id);
            });
        $quoteRepositoryMock->persist(Argument::type(Quote::class));

        $translation = $prophecy->prophesize(TranslationInterface::class);
        $translation->trans(Argument::type('string'))->will(function ($args) {
            return $args[0];
        });

        $createThreadService = $prophecy->prophesize(CreateThreadServiceInterface::class);
        $createThreadService->setupMessage(
            Argument::type('string'),
            Argument::type('string'),
            Argument::type('string'),
            Argument::type('array'),
            Argument::type('int'),
            Argument::type('int')
        )->willReturn(new MessageDetail());
        $createThreadService->createThread(
            Argument::type(MessageDetail::class),
            Argument::type('int')
        )->willReturn((new Thread())->setCreatedAt(new \DateTimeImmutable('12/12/12'))->setId(1));

        $replyThreadBuyerVendor = $prophecy->prophesize(ReplyThreadBuyerVendor::class);
        $replyThreadBuyerVendor->replyThreadBuyerVendor(
            Argument::type('int'),
            Argument::type('string'),
            Argument::type('array'),
            Argument::type('string'),
            Argument::type('string'),
            Argument::type('string')
        )->willReturn((new Thread())->setCreatedAt(new \DateTimeImmutable('12/12/12')));

        $workflowService = $prophecy->prophesize(QuoteWorkFlowServiceInterface::class);
        $workflowService->can(
            Argument::type(Quote::class),
            Argument::type('string')
        )->will(function (array $args) {
            [$quote] = $args;
            /** @var QuoteCatalogue $quote */
            if ($quote->getQuoteId() === 2) {
                return false;
            }
            return true;
        });
        $syncQuoteUpdate = $prophecy->prophesize(SyncQuoteUpdateInterface::class);
        $syncQuoteCreation = $prophecy->prophesize(SyncQuoteCreationInterface::class);

        $logger = $prophecy->prophesize(LoggerInterface::class);
        $dateNowService = $prophecy->prophesize(DateNowInterface::class);
        $dateNowService->getDateTimeImmutableNow()->willReturn((new DateTimeImmutable('10/12/11')));

        return new QuoteActionUseCaseHelper(
            $workflowService->reveal(),
            $quoteRepositoryMock->reveal(),
            $translation->reveal(),
            $replyThreadBuyerVendor->reveal(),
            $this->createMock(EmailServiceInterface::class),
            $syncQuoteUpdate->reveal(),
            $syncQuoteCreation->reveal(),
            $this->createMock(CheckMerchantServiceInterface::class),
            $this->createMock(QuoteContactServiceInterface::class),
            $this->createMock(AddressRepositoryInterface::class),
            $dateNowService->reveal(),
            $logger->reveal()
        );
    }
}
