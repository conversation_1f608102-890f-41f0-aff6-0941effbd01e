<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Tests\Domain\UseCase;

use Closure;
use Generator;
use Marketplace\Component\Quote\Domain\Enum\StatusFilterEnum;
use Marketplace\Component\Quote\Domain\Model\Quote;
use Marketplace\Component\Quote\Domain\Model\QuoteStatus;
use Marketplace\Component\Quote\Domain\Port\Repository\QuoteRepositoryInterface;
use Marketplace\Component\Quote\Domain\Presenter\ShowListingQuotesPresenterInterface;
use Marketplace\Component\Quote\Domain\UseCase\ShowListingQuotes\DTO\ShowListingQuotesRequest;
use Marketplace\Component\Quote\Domain\UseCase\ShowListingQuotes\DTO\ShowListingQuotesResponse;
use Marketplace\Component\Quote\Domain\UseCase\ShowListingQuotes\ShowListingQuotesUseCase;
use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\User;
use Marketplace\Component\User\Domain\Port\Repository\UserRepositoryInterface;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;

final class ShowListingQuotesUseCaseTest extends TestCase
{
    private ShowListingQuotesUseCase $useCase;
    private ShowListingQuotesPresenterInterface $presenter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements ShowListingQuotesPresenterInterface {
            public ShowListingQuotesResponse $response;

            public function present(ShowListingQuotesResponse $response): void
            {
                $this->response = $response;
            }
        };
        $prophet = new Prophet();
        /** @var QuoteRepositoryInterface|ObjectProphecy $quoteRepositoryMock */
        $quoteRepositoryMock = $prophet->prophesize(QuoteRepositoryInterface::class);
        $quoteModel = $this->makeQuoteModel();
        $quoteRepositoryMock->getBuyerQuotes(
            Argument::type('int'),
            Argument::type('string'),
            Argument::type('string'),
            Argument::type('string'),
            Argument::type('string'),
            Argument::type(StatusFilterEnum::class)
        )
            ->shouldBeCalledTimes(1)
            ->will(function (array $args) use ($quoteModel): array {
                [$userId, $filteredSubject,$filteredStatus] = $args;
                if ($filteredSubject === "subject A" && $filteredStatus === QuoteStatus::STATUS_SENT) {
                    return [$quoteModel("subject A", QuoteStatus::STATUS_SENT)];
                }
                return [$quoteModel()];
            });

        $this->useCase = new ShowListingQuotesUseCase(
            $quoteRepositoryMock->reveal(),
        );
    }

    /**
     * @dataProvider provideShowListingQuotes
     */
    public function testExecute(ShowListingQuotesRequest $request, ShowListingQuotesResponse $expected): void
    {
        $this->useCase->execute($request, $this->presenter);

        $this->assertEquals($expected, $this->presenter->response);
    }

    public function provideShowListingQuotes(): Generator
    {
        // Given: I want to show all quotes in "in_progress" tab
        $request = new ShowListingQuotesRequest(
            1,
            null,
            "",
            "",
            "",
            "",
            1,
            5,
            "in_progress"
        );
        // When: I access the page
        $response = new ShowListingQuotesResponse(
            [$this->makeQuoteModel()()],
            1,
            5,
            "",
            "",
            "",
            "",
            "in_progress"
        );
        // Then: I get all quotes with "in_progress" status
        yield 'Successful response' => [$request, $response];

        // Given: I want to show only quotes with subject "subject A" and "sent" status
        $request = new ShowListingQuotesRequest(
            1,
            null,
            "subject A",
            QuoteStatus::STATUS_SENT,
            "",
            "",
            1,
            5,
            "in_progress"
        );
        // When: I fill the filter subject by "subject A" and status by "sent"
        $response = new ShowListingQuotesResponse(
            [$this->makeQuoteModel()("subject A", QuoteStatus::STATUS_SENT)],
            1,
            5,
            "subject A",
            QuoteStatus::STATUS_SENT,
            "",
            "",
            "in_progress"
        );
        // Then: I get only quotes with subject "subject A" and "sent" status
        yield 'Successful response with filter' => [$request, $response];
    }

    private function makeQuoteModel(): Closure
    {
        return static function (
            string $filteredSubject = "subject",
            string $filteredStatus = QuoteStatus::STATUS_IN_PROGRESS,
        ): Quote {
            $quote = new Quote();
            $quote->setQuoteId(2);
            $quote->setTitle($filteredSubject);
            $quote->setStatus($filteredStatus);
            return $quote;
        };
    }
}
