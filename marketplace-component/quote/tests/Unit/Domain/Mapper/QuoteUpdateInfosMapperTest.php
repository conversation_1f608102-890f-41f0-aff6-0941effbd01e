<?php

namespace Marketplace\Component\Quote\Domain\Port\Mapper;

use Marketplace\Component\Quote\Domain\Model\Quote;
use Marketplace\Component\Quote\Domain\Model\QuoteUpdateRecord;
use PHPUnit\Framework\TestCase;

class QuoteUpdateInfosMapperTest extends TestCase
{
    public QuoteUpdateInfosMapper $quoteUpdateInfosMapper;

    protected function setUp(): void
    {
        parent::setUp();
        $this->quoteUpdateInfosMapper = new QuoteUpdateInfosMapper();
    }


    public function testMapQuoteUpdateItemInfos()
    {
        $quote = new Quote();
        $quote->setQuoteId(1);
        $quote->setStatus('sent');
        $quote->setQuoteNumber(12);
        $result = $this->quoteUpdateInfosMapper->mapQuoteUpdateItemInfos($quote);

        $expected = new QuoteUpdateRecord(
            'w1',
            1,
            'sent',
            12
        );
        $this->assertEquals($expected, $result);
    }
}
