<?php

namespace Marketplace\Component\Quote\Infrastructure\Adapter\Service;

use Marketplace\Component\Quote\Domain\Model\Quote;
use Marketplace\Component\Quote\Domain\UseCase\SyncQuoteCreationUseCase\DTO\SyncQuoteCreationRequest;
use Marketplace\Component\Quote\Infrastructure\Mapper\QuoteMapper;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

class SyncQuoteCreationServiceTest extends TestCase
{
    private SyncQuoteCreationService $syncQuoteCreationService;
    protected function setUp(): void
    {
        parent::setUp();
        $prophet = new Prophet();
        $messageBus = $prophet->prophesize(MessageBusInterface::class);
        $messageBus->dispatch(Argument::type('object'))->willReturn(new Envelope(new SyncQuoteCreationRequest(1, '')));
        $this->syncQuoteCreationService = new SyncQuoteCreationService($messageBus->reveal());
    }

    public function testSyncQuoteCreation()
    {
        $quote = new Quote();
        $quote->setQuoteId(1);
        $quote->setType(QuoteMapper::OPEN_TYPE);
        $this->syncQuoteCreationService->syncQuoteCreation($quote);
        $this->assertTrue(true);
    }
}
