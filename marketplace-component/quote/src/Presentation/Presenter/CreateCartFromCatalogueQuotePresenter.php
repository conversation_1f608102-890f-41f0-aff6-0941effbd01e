<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Presentation\Presenter;

use Marketplace\Component\Quote\Domain\Presenter\CreateCartFromQuoteCataloguePresenterInterface;
use Marketplace\Component\Quote\Domain\UseCase\Catalogue\CreateCartFromQuoteCatalogue\DTO\CreateCartFromQuoteCatalogueResponse;
use Marketplace\Component\Quote\Presentation\ViewModel\CreateCartFromCatalogueQuoteViewModel;

class CreateCartFromCatalogueQuotePresenter implements CreateCartFromQuoteCataloguePresenterInterface
{
    private CreateCartFromCatalogueQuoteViewModel $viewModel;

    public function __construct()
    {
        $this->viewModel = new CreateCartFromCatalogueQuoteViewModel();
    }

    public function present(CreateCartFromQuoteCatalogueResponse $response): void
    {
        $this->viewModel->succeed = $response->succeed;
        $this->viewModel->errors = $response->errors;
        $this->viewModel->offerIds = $response->offerIds;
        $this->viewModel->errorCode = $response->getErrorCode();
    }

    public function viewModel(): CreateCartFromCatalogueQuoteViewModel
    {
        return $this->viewModel;
    }
}
