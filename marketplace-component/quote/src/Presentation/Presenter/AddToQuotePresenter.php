<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Presentation\Presenter;

use Marketplace\Component\Quote\Domain\Presenter\AddToQuotePresenterInterface;
use Marketplace\Component\Quote\Domain\UseCase\AddToQuote\DTO\AddToQuoteResponse;
use Marketplace\Component\Quote\Presentation\ViewModel\AddToQuoteViewModel;

class AddToQuotePresenter implements AddToQuotePresenterInterface
{
    private AddToQuoteViewModel $viewModel;

    public function __construct()
    {
        $this->viewModel = new AddToQuoteViewModel();
    }

    public function present(AddToQuoteResponse $response): void
    {
        $this->viewModel->succeed = $response->succeed;
        $this->viewModel->errors = $response->errors;
    }

    public function viewModel(): AddToQuoteViewModel
    {
        return $this->viewModel;
    }
}
