<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Presentation\ViewModel;

use Marketplace\Component\Quote\Domain\Model\QuoteOpen;

final class ShowOpenQuoteViewModel
{
    private ?QuoteOpen $quoteOpen = null;

    public function getQuoteOpen(): ?QuoteOpen
    {
        return $this->quoteOpen;
    }

    public function setQuoteOpen(?QuoteOpen $quoteOpen): void
    {
        $this->quoteOpen = $quoteOpen;
    }
}
