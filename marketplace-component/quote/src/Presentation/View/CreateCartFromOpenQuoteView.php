<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Presentation\View;

use Marketplace\Component\CleanArchiCore\Utils\Flash\FlashMessageInterface;
use Marketplace\Component\Order\Presentation\ViewModel\CreateMerchantOrderReviewViewModel;
use Marketplace\Component\Quote\Presentation\ViewModel\CreateCartFromOpenQuoteViewModel;
use Marketplace\Component\Quote\Presentation\ViewModel\CreateOpenQuoteViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class CreateCartFromOpenQuoteView
{

    public function __construct(private SerializerInterface $serializer)
    {
    }

    public function generateJson(CreateCartFromOpenQuoteViewModel $viewModel): Response
    {
        $status = 201;
        if ($viewModel->succeed === false) {
            $status = 400;
        }

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $viewModel,
                'json'
            ),
            $status
        );
    }
}
