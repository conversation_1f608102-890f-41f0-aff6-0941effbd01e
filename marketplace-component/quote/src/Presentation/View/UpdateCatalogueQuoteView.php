<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Presentation\View;

use Marketplace\Component\Quote\Presentation\ViewModel\UpdateCatalogueQuoteViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class UpdateCatalogueQuoteView
{

    public function __construct(private SerializerInterface $serializer)
    {
    }

    public function generateJson(UpdateCatalogueQuoteViewModel $viewModel): Response
    {
        $status = 200;
        if ($viewModel->updated === false) {
            $status = 400;
        }

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $viewModel,
                'json'
            ),
            $status
        );
    }
}
