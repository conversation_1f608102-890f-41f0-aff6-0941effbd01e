<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Presentation\View;

use Marketplace\Component\CleanArchiCore\Presentation\View\AbstractView;
use Marketplace\Component\Quote\Presentation\ViewModel\NegociateQuoteViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class NegociateQuoteView extends AbstractView
{
    public function __construct(private SerializerInterface $serializer)
    {
    }

    public function generateJson(NegociateQuoteViewModel $viewModel): Response
    {
        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $viewModel,
                'json'
            )
        );
    }
}
