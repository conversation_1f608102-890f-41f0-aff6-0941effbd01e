<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Presentation\View;

use Marketplace\Component\CleanArchiCore\Presentation\View\AbstractView;
use Marketplace\Component\Quote\Presentation\ViewModel\AddToQuoteViewModel;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

class AddToQuoteView extends AbstractView
{
    public function __construct(
        private SerializerInterface $serializer
    ) {
    }

    public function generateJson(AddToQuoteViewModel $viewModel): Response
    {
        $status = 201;
        if ($viewModel->succeed === false) {
            $status = 400;
        }

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                $viewModel,
                'json'
            ),
            $status
        );
    }
}
