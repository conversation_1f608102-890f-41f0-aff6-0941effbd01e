<?php

namespace Marketplace\Component\Quote\Infrastructure\Strategy\Action;

use Marketplace\Component\Quote\Domain\Enum\QuoteActionEnum;
use Marketplace\Component\Quote\Domain\UseCase\QuoteChangeStatus\DTO\QuoteChangeStatusRequest;
use Marketplace\Component\Quote\Domain\UseCase\QuoteChangeStatus\QuoteChangeStatusUseCase;
use Marketplace\Component\Quote\Presentation\Presenter\QuoteChangeStatusPresenter;
use Marketplace\Component\Quote\Presentation\View\QuoteChangeStatusView;
use Symfony\Component\HttpFoundation\Response;

class QuoteAcceptStrategy implements QuoteUpdateStatusStrategyInterface
{
    public function __construct(
        private QuoteChangeStatusUseCase $quoteChangeStatusUseCase,
        private QuoteChangeStatusPresenter $presenter,
        private QuoteChangeStatusView $view
    ) {
    }

    public function process(QuoteChangeStatusRequest $request): Response
    {
        $this->quoteChangeStatusUseCase->execute(
            $request,
            $this->presenter
        );
        return $this->view->generateJson($this->presenter->viewModel());
    }

    public function canProcess(QuoteChangeStatusRequest $request): bool
    {
        return $request->quoteActionEnum === QuoteActionEnum::BUYER_ACCEPT;
    }
}
