<?php

namespace Marketplace\Component\Quote\Infrastructure\Strategy\Action;

use Marketplace\Component\Quote\Domain\UseCase\QuoteChangeStatus\DTO\QuoteChangeStatusRequest;
use Symfony\Component\HttpClient\Exception\InvalidArgumentException;
use Symfony\Component\HttpFoundation\Response;

class QuoteUpdateStatusContext
{
    /**
     * @var QuoteUpdateStatusStrategyInterface[] $strategies
     */
    private array $strategies;

    /**
     *
     * @param iterable $strategies
     */
    public function __construct(iterable $strategies)
    {
        foreach ($strategies as $strategy) {
            $this->addStrategy($strategy);
        }
    }

    /**
     * This function will allow to add new strategy.
     *
     * @param QuoteUpdateStatusStrategyInterface $strategy
     */
    public function addStrategy(QuoteUpdateStatusStrategyInterface $strategy)
    {
        $this->strategies[] = $strategy;
    }

    /**
     * @param QuoteChangeStatusRequest $request
     */
    public function process(
        QuoteChangeStatusRequest $request
    ): Response {
        foreach ($this->strategies as $strategy) {
            if ($strategy->canProcess($request)) {
                return $strategy->process($request);
            }
        }
        throw new InvalidArgumentException("no strategy for this action");
    }
}
