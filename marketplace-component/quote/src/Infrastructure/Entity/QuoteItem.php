<?php

namespace Marketplace\Component\Quote\Infrastructure\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="QuoteItemRepository::class")
 */
class QuoteItem
{

    /**
     * @ORM\Column(type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private int $id;

    /**
     * @ORM\Column(type="string",length=255, nullable=false)
     */
    private string $description;

    /**
     * @ORM\Column(type="integer", nullable=false)
     */
    private int $quantity;

    /**
     * Many quote items have One quote.
     * @ORM\ManyToOne(targetEntity="Marketplace\Component\Quote\Infrastructure\Entity\Quote", inversedBy="quoteItems")
     * @ORM\JoinColumn(name="quote_id", referencedColumnName="quote_id")
     */
    private Quote $quote;

    /**
     * @ORM\Column( type="float", nullable=false)
     */
    private float $totalPrice = 0.0;

    /**
     * @ORM\Column( type="float", nullable=false)
     */
    private float $totalPriceWithVat = 0.0;

    /**
     * @ORM\Column( type="float", nullable=false)
     */
    private float $unitPrice = 0.0;

    /**
     * @ORM\Column( type="string", nullable=true)
     */
    private ?string $vatGroupName;

    /**
     * @ORM\Column( type="text", nullable=true)
     */
    private ?string $commentary;

    /**
     * @ORM\Column( type="string", nullable=true)
     */
    private ?string $sku;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return QuoteItem
     */
    public function setId(int $id): QuoteItem
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * @param string $description
     * @return QuoteItem
     */
    public function setDescription(string $description): QuoteItem
    {
        $this->description = $description;
        return $this;
    }

    /**
     * @return int
     */
    public function getQuantity(): int
    {
        return $this->quantity;
    }

    /**
     * @param int $quantity
     * @return QuoteItem
     */
    public function setQuantity(int $quantity): QuoteItem
    {
        $this->quantity = $quantity;
        return $this;
    }

    /**
     * @return Quote
     */
    public function getQuote(): Quote
    {
        return $this->quote;
    }

    /**
     * @param Quote $quote
     * @return QuoteItem
     */
    public function setQuote(Quote $quote): QuoteItem
    {
        $this->quote = $quote;
        return $this;
    }

    /**
     * @return float
     */
    public function getTotalPrice(): float
    {
        return $this->totalPrice;
    }

    /**
     * @param float $totalPrice
     * @return QuoteItem
     */
    public function setTotalPrice(float $totalPrice): QuoteItem
    {
        $this->totalPrice = $totalPrice;
        return $this;
    }



    /**
     * @return float
     */
    public function getUnitPrice(): float
    {
        return $this->unitPrice;
    }

    /**
     * @param float $unitPrice
     * @return QuoteItem
     */
    public function setUnitPrice(float $unitPrice): QuoteItem
    {
        $this->unitPrice = $unitPrice;
        return $this;
    }


    /**
     * @return string|null
     */
    public function getVatGroupName(): ?string
    {
        return $this->vatGroupName;
    }

    /**
     * @param string|null $vatGroupName
     * @return QuoteItem
     */
    public function setVatGroupName(?string $vatGroupName): QuoteItem
    {
        $this->vatGroupName = $vatGroupName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCommentary(): ?string
    {
        return $this->commentary;
    }

    /**
     * @param string|null $commentary
     * @return $this
     */
    public function setCommentary(?string $commentary): self
    {
        $this->commentary = $commentary;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSku(): ?string
    {
        return $this->sku;
    }

    /**
     * @param string|null $sku
     * @return $this
     */
    public function setSku(?string $sku): self
    {
        $this->sku = $sku;
        return $this;
    }

    /**
     * @return float
     */
    public function getTotalPriceWithVat(): float
    {
        return $this->totalPriceWithVat;
    }

    /**
     * @param float $totalPriceWithVat
     * @return QuoteItem
     */
    public function setTotalPriceWithVat(float $totalPriceWithVat): QuoteItem
    {
        $this->totalPriceWithVat = $totalPriceWithVat;
        return $this;
    }
}
