<?php

namespace Marketplace\Component\Quote\Infrastructure\Mapper;

use Marketplace\Component\Quote\Domain\Exception\QuoteException;
use Marketplace\Component\Quote\Domain\Model\Quote;
use Marketplace\Component\Quote\Domain\Model\QuoteBuyer;
use Marketplace\Component\Quote\Domain\Model\QuoteMerchant;
use Marketplace\Component\Quote\Domain\Model\QuoteShipping;
use Marketplace\Component\Quote\Domain\Model\QuoteVersion;
use Marketplace\Component\Quote\Infrastructure\Entity\Quote as DoctrineQuote;
use Marketplace\Component\Quote\Infrastructure\Entity\QuoteCatalogue;
use Marketplace\Component\Quote\Infrastructure\Entity\QuoteOpen;
use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\Country;
use Marketplace\Component\User\Infrastructure\Entity\Address;
use Marketplace\Component\User\Infrastructure\Mapper\CountryMapper;

class QuoteMapper
{
    public const CATALOGUE_TYPE = "offer";
    public const OPEN_TYPE = "open";

    /**
     * @throws QuoteException
     */
    public static function mappingDoctrineToModel(
        DoctrineQuote $doctrineQuote,
        ?Quote $quote = null,
        ?int $orderNumber = null
    ): Quote {
        if ($quote == null) {
            $quote = new Quote();
        }

        $quote->setQuoteId($doctrineQuote->getQuoteId());
        $quote->setStatus($doctrineQuote->getStatus());
        $quote->setTitle($doctrineQuote->getTitle());
        $quote->setVersionNumber($doctrineQuote->getVersion());
        $quote->setQuoteNumber($doctrineQuote->getQuoteNumber());
        $quote->setCreationDate($doctrineQuote->getCreatedAt());
        $quote->setSendDate($doctrineQuote->getSendAt());
        $quote->setValidationDate($doctrineQuote->getValidationDate());
        $quote->setBuyerUnread($doctrineQuote->isBuyerUnread());
        $quote->setVendorUnread($doctrineQuote->isVendorUnread());
        $quote->setTotalPrice($doctrineQuote->getTotalPrice());
        $quote->setTotalPriceWithVat($doctrineQuote->getTotalPriceWithVat());
        $quote->setDiscount($doctrineQuote->getDiscount());
        $quote->setDiscountType($doctrineQuote->getDiscountType());
        $quote->setCurrency($doctrineQuote->getCurrency());
        $buyer = $doctrineQuote->getBuyer();
        if ($buyer === null) {
            throw new QuoteException("Buyer should not be null");
        }
        $quote->setBuyerId($buyer->getId());
        $quoteMerchant = (new QuoteMerchant())
            ->setId($doctrineQuote->getVendor()->getId())
            ->setDistantId($doctrineQuote->getVendor()->getDistantId())
            ->setName($doctrineQuote->getVendor()->getCompanyName())
            ->setCountry($doctrineQuote->getVendor()->getCountry()->getCode())
            ->setLocale($doctrineQuote->getVendor()->getLanguage() ??
                $doctrineQuote->getVendor()->getCountry()->getLocale())
            ->setFirstName($doctrineQuote->getVendor()->getFirstname())
            ->setLastName($doctrineQuote->getVendor()->getLastname())
            ->setEmail($doctrineQuote->getVendor()->getEmail())
            ->setCompanyName($doctrineQuote->getVendor()->getCompanyName());


        $quoteBuyer = (new QuoteBuyer())
            ->setBuyerId($buyer->getId() ?? -1)
            ->setEmail($buyer->getEmail() ?? "")
            ->setFirstName($buyer->getFirstname() ?? "")
            ->setLastName($buyer->getLastname() ?? "")
            ->setCompanyName($doctrineQuote->getCompany()->getName())
            ->setLocale($buyer->getLanguage());
        $quote->setBuyer($quoteBuyer);
        $shipping = $doctrineQuote->getShippingAdress();

        $quoteShipping =
            $shipping instanceof Address ?
                (new QuoteShipping())
                    ->setIsoCode($shipping->getCountry()->getIsoCode())
                    ->setZipCode($shipping->getZipCode())
                    ->setIzbergCode($shipping->getCountry()->getIzbergCountryCode())
                    ->setId($shipping->getId() ?? 0) : null; //impossible here
        $quote->setMerchant($quoteMerchant);
        $country = CountryMapper::mappingDoctrineToDomain($doctrineQuote->getCompany()->getCountry());
        $company = $doctrineQuote->getCompany();
        $quote->setCompany(
            (new Company())
                ->setId($company->getId())
                ->setName($company->getName())
                ->setCountry(
                    ($country)
                )
                ->setExternalId($company->getExternalId())
                ->setAccountingEmail($company->getAccountingEmail())
        );
        $quote->setShipping($quoteShipping);
        $quote->setMessage($doctrineQuote->getMessage());
        $quote->setExpirationDate($doctrineQuote->getExpirationDate());
        $quote->setThreadId($doctrineQuote->getThreadId());
        $quote->setReceiptDate($doctrineQuote->getReceiptDate());
        $type = "";

        if ($doctrineQuote instanceof QuoteCatalogue) {
            $type = QuoteMapper::CATALOGUE_TYPE;
        } elseif ($doctrineQuote instanceof QuoteOpen) {
            $type = QuoteMapper::OPEN_TYPE;
        }

        $versions = [];

        /** @var \Marketplace\Component\Quote\Infrastructure\Entity\QuoteVersion $doctrineQuoteVersion */
        foreach ($doctrineQuote->getQuoteVersions() as $doctrineQuoteVersion) {
            $quoteVersion = (new QuoteVersion())
                ->setId($doctrineQuoteVersion->getId())
                ->setPdfName($doctrineQuoteVersion->getPdfName())
                ->setCreatedAt($doctrineQuoteVersion->getCreatedAt());
            $versions[] = $quoteVersion;
        }
        $quote->setVersions($versions);
        $quote->setType($type);
        $quote->setOrderNumber($orderNumber);
        return $quote;
    }

    public static function domainToDoctrine(
        Quote $quote,
        DoctrineQuote $doctrineQuote = null
    ): DoctrineQuote {
        if (!$doctrineQuote instanceof DoctrineQuote) {
            $doctrineQuote = new DoctrineQuote();
        }

        $doctrineQuote
            ->setStatus($quote->getStatus() ?? "")
            ->setMessage($quote->getMessage())
            ->setTitle($quote->getTitle())
            ->setExpirationDate($quote->getExpirationDate())
            ->setQuoteNumber($quote->getQuoteNumber())
            ->setThreadId($quote->getThreadId())
            ->setReceiptDate($quote->getReceiptDate())
            ->setTotalPrice($quote->getTotalPrice())
            ->setTotalPriceWithVat($quote->getTotalPriceWithVat())
            ->setDiscount($quote->getDiscount())
            ->setDiscountType($quote->getDiscountType())
            ->setValidationDate($quote->getValidationDate())
            ->setSendAt($quote->getSendDate())
            ->setCurrency($quote->getCurrency());

        if ($quote->isBuyerUnread() !== null) {
            $doctrineQuote->setBuyerUnread($quote->isBuyerUnread() ?? false);
        }
        if ($quote->isVendorUnread() !== null) {
            $doctrineQuote->setVendorUnread($quote->isVendorUnread() ?? false);
        }

        return $doctrineQuote;
    }
}
