<?php

namespace Marketplace\Component\Quote\Infrastructure\Mapper;

use Marketplace\Component\Quote\Domain\Model\QuoteItem;
use Marketplace\Component\Quote\Infrastructure\Entity\QuoteItem as DoctrineQuoteItem;

class QuoteItemMapper
{
    public static function mappingDoctrineToModel(DoctrineQuoteItem $doctrineQuoteItem): QuoteItem
    {
        $quoteItem = (new QuoteItem())
            ->setId($doctrineQuoteItem->getId())
            ->setQuantity($doctrineQuoteItem->getQuantity())
            ->setTitle($doctrineQuoteItem->getDescription())
            ->setTotalPrice($doctrineQuoteItem->getTotalPrice())
            ->setVatGroupName($doctrineQuoteItem->getVatGroupName())
            ->setUnitPrice($doctrineQuoteItem->getUnitPrice())
            ->setId($doctrineQuoteItem->getId())
            ->setCommentary($doctrineQuoteItem->getCommentary())
            ->setSku($doctrineQuoteItem->getSku());
        return $quoteItem;
    }

    public static function domainToDoctrine(
        QuoteItem $quoteItem,
        ?DoctrineQuoteItem $doctrineQuoteItem = null
    ): DoctrineQuoteItem {
        if ($doctrineQuoteItem == null) {
            $doctrineQuoteItem = new DoctrineQuoteItem();
        }
        $doctrineQuoteItem
            ->setQuantity($quoteItem->getQuantity())
            ->setDescription($quoteItem->getTitle())
            ->setTotalPrice($quoteItem->getTotalPrice() ?? 0)
            ->setTotalPriceWithVat($quoteItem->getTotalPriceWithVat() ?? 0)
            ->setVatGroupName($quoteItem->getVatGroupName())
            ->setUnitPrice($quoteItem->getUnitPrice() ?? 0)
            ->setCommentary($quoteItem->getCommentary())
            ->setSku($quoteItem->getSku());
        return $doctrineQuoteItem;
    }
}
