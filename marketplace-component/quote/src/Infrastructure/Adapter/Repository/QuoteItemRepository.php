<?php

namespace Marketplace\Component\Quote\Infrastructure\Adapter\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use Marketplace\Component\Quote\Infrastructure\Entity\QuoteItem;

class QuoteItemRepository extends ServiceEntityRepository
{
    public function __construct(
        ManagerRegistry $registry
    ) {
        parent::__construct($registry, QuoteItem::class);
    }
}
