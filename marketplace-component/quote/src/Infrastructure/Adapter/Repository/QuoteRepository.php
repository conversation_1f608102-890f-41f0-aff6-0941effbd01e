<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Infrastructure\Adapter\Repository;

use DateTime;
use DateTimeImmutable;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Marketplace\Component\Cart\Domain\Port\Repository\CartRepositoryInterface;
use Marketplace\Component\Quote\Domain\Enum\StatusFilterEnum;
use Marketplace\Component\Quote\Domain\Exception\QuoteException;
use Marketplace\Component\Quote\Domain\Model\Quote;
use Marketplace\Component\Quote\Domain\Model\QuoteStatus;
use Marketplace\Component\Quote\Domain\Model\QuoteVersion;
use Marketplace\Component\Quote\Domain\Port\Repository\QuoteRepositoryInterface;
use Marketplace\Component\Quote\Infrastructure\Entity\Quote as DoctrineQuote;
use Marketplace\Component\Quote\Infrastructure\Entity\QuoteCatalogue;
use Marketplace\Component\Quote\Infrastructure\Entity\QuoteOpen;
use Marketplace\Component\Quote\Infrastructure\Entity\QuoteVersion as DoctrineQuoteVersion;
use Marketplace\Component\Quote\Infrastructure\Mapper\QuoteCatalogueMapper;
use Marketplace\Component\Quote\Infrastructure\Mapper\QuoteMapper;
use Marketplace\Component\Quote\Infrastructure\Mapper\QuoteOpenMapper;
use Symfony\Component\Serializer\SerializerInterface;

class QuoteRepository extends ServiceEntityRepository implements QuoteRepositoryInterface
{
    private const PDF_EXTENSION = "pdf";

    public function __construct(
        ManagerRegistry $registry,
        private SerializerInterface $serializer,
        private CartRepositoryInterface $cartRepository
    ) {
        parent::__construct($registry, DoctrineQuote::class);
    }

    /**
     * @throws QuoteException
     */
    public function findById(int $quoteId): Quote
    {
        /** @var DoctrineQuote $doctrineQuote */
        $doctrineQuote = $this->find($quoteId);
        if (!$doctrineQuote instanceof DoctrineQuote) {
            throw new QuoteException("quote not found", "quote.not.found", "quoteId");
        }

        if ($doctrineQuote instanceof QuoteOpen) {
            return QuoteOpenMapper::mappingDoctrineToModel($doctrineQuote);
        } elseif ($doctrineQuote instanceof QuoteCatalogue) {
            return QuoteCatalogueMapper::mappingDoctrineToModel($doctrineQuote);
        }
        throw new QuoteException("quote type is unknown");
    }


    public function persist(Quote $quote): void
    {
        $doctrineQuote = $this->find($quote->getQuoteId());
        /** @var DoctrineQuote $doctrineQuote */
        $doctrineQuote = QuoteMapper::domainToDoctrine($quote, $doctrineQuote);
        $this->getEntityManager()->persist($doctrineQuote);
        $this->getEntityManager()->flush();
    }

    public function getBuyerQuotes(
        int $companyId,
        string $filteredSubject,
        string $filteredStatus,
        string $filteredDateStart,
        string $filteredDateEnd,
        StatusFilterEnum $tabStatus,
    ): array {
        $quotes = $this->getQuotesQuery(
            $filteredSubject,
            $filteredStatus,
            $filteredDateStart,
            $filteredDateEnd,
            $tabStatus
        )
            ->
            andWhere('q.company = :companyId')
            ->setParameter('companyId', $companyId)
            ->getQuery()
            ->getResult();
        $quotesResumed = [];
        foreach ($quotes as $quote) {
            $order = $this->cartRepository->getOrderFromQuoteId($quote->getQuoteId());
            $quotesResumed[] = QuoteMapper::mappingDoctrineToModel($quote, null, $order ? $order->getIzbergId() : null);
        }
        return $quotesResumed;
    }

    public function getBuyerQuotesQB(
        int $companyId,
        string $filteredSubject,
        string $filteredStatus,
        string $filteredDateStart,
        string $filteredDateEnd,
        StatusFilterEnum $tabStatus,
    ): QueryBuilder {
        return $this->getQuotesQuery(
            $filteredSubject,
            $filteredStatus,
            $filteredDateStart,
            $filteredDateEnd,
            $tabStatus
        )
            ->andWhere('q.company = :companyId')
            ->setParameter('companyId', $companyId);
    }

    public function getVendorQuotes(
        int $merchantId,
        string $filteredSubject,
        string $filteredStatus,
        string $filteredDateStart,
        string $filteredDateEnd,
        StatusFilterEnum $tabStatus,
    ): array {
        $quotes = $this->getQuotesQuery(
            $filteredSubject,
            $filteredStatus,
            $filteredDateStart,
            $filteredDateEnd,
            $tabStatus
        )
            ->andWhere('q.vendor = :vendorId')
            ->setParameter('vendorId', $merchantId)
            ->orderBy('q.sendAt', 'DESC')
            ->getQuery()
            ->getResult();
        $quotesResumed = [];
        foreach ($quotes as $quote) {
            $order = $this->cartRepository->getOrderFromQuoteId($quote->getQuoteId());
            $quotesResumed[] = QuoteMapper::mappingDoctrineToModel($quote, null, $order ? $order->getIzbergId() : null);
        }
        return $quotesResumed;
    }

    public function getVendorQuotesQB(
        int $merchantId,
        string $filteredSubject,
        string $filteredStatus,
        string $filteredDateStart,
        string $filteredDateEnd,
        StatusFilterEnum $tabStatus,
    ): QueryBuilder {
        return $this->getQuotesQuery(
            $filteredSubject,
            $filteredStatus,
            $filteredDateStart,
            $filteredDateEnd,
            $tabStatus
        )
            ->andWhere('q.vendor = :vendorId')
            ->setParameter('vendorId', $merchantId)
            ->orderBy('q.sendAt', 'DESC');
    }

    private function getQuotesQuery(
        string $filteredSubject,
        string $filteredStatus,
        string $filteredDateStart,
        string $filteredDateEnd,
        StatusFilterEnum $tabStatus
    ): QueryBuilder {
        $doctrineQuotes = $this->createQueryBuilder('q');
        if ($filteredSubject !== "") {
            $doctrineQuotes->innerJoin('q.vendor', 'v');
            $doctrineQuotes->where('q.title LIKE :filteredSubject')
                ->orWhere('q.quoteNumber LIKE :filteredSubject')
                ->orWhere('v.companyName LIKE :filteredSubject');
            $doctrineQuotes->setParameter('filteredSubject', "%$filteredSubject%");
        }
        if ($filteredStatus !== "") {
            $doctrineQuotes->andWhere('q.status = :filteredStatus')
                ->setParameter('filteredStatus', $filteredStatus);
        }
        $dateName = $tabStatus->getDateName($tabStatus);
        if ($filteredDateStart !== "") {
            $doctrineQuotes->andWhere('q.' . $dateName . ' >= :creationDateStart');
            $doctrineQuotes->setParameter('creationDateStart', new DateTimeImmutable($filteredDateStart));
        }
        if ($filteredDateEnd !== "") {
            $doctrineQuotes->andWhere('q.' . $dateName . ' <= :creationDateEnd');
            $doctrineQuotes->setParameter('creationDateEnd', new DateTimeImmutable($filteredDateEnd));
        }
        return $doctrineQuotes
            ->andWhere('q.status in (:tabStatus)')
            ->setParameter('tabStatus', $tabStatus->getStatus())
            ->orderBy('q.createdAt', 'DESC');
    }

    public function updateQuoteStatus(Quote $quote): void
    {
        $this->createQueryBuilder('q')
            ->update('MarketplaceQuoteBundle:Quote', 'q')
            ->set('q.status', ':status')
            ->where('q.quoteId = :quoteId')
            ->setParameter('quoteId', $quote->getQuoteId())
            ->setParameter('status', $quote->getStatus())
            ->getQuery()
            ->execute();
    }

    public function findQuoteByVendorAndAddress(int $vendorDistantId, int $addressId): ?DoctrineQuote
    {
        return $this->createQueryBuilder('q')
            ->leftJoin('q.vendor', 'v')
            ->where('v.distantId = :distantId')
            ->andWhere('q.shippingAdress = :addressId')
            ->setParameter('distantId', $vendorDistantId)
            ->setParameter('addressId', $addressId)
            ->setMaxResults(1)
            ->getQuery()->getOneOrNullResult();
    }

    /**
     * @param $quoteId
     * @param bool $buyerOrMerchant true for buyer false for merchant
     */
    public function updateMarkAsRead($quoteId, bool $buyerOrMerchant): void
    {
        $qb = $this->createQueryBuilder('q')
            ->update('MarketplaceQuoteBundle:Quote', 'q');

        if ($buyerOrMerchant) {
            $qb->set('q.buyerUnread', ':unread');
        } else {
            $qb->set('q.vendorUnread', ':unread');
        }

        $qb
            ->where('q.quoteId = :quoteId')
            ->setParameter('quoteId', $quoteId)
            ->setParameter('unread', false)
            ->getQuery()
            ->execute();
    }

    public function findExpiredQuoteId(): array
    {
        return $this->createQueryBuilder('q')
            ->select('q.quoteId')
            ->where('q.status not in (:statuses)')
            ->andWhere('q.expirationDate < :dateNow')
            ->setParameter("statuses", [
                QuoteStatus::STATUS_CANCELLED,
                QuoteStatus::STATUS_EXPIRED,
                QuoteStatus::STATUS_VALIDATED,
                QuoteStatus::STATUS_REFUSED
            ])
            ->setParameter("dateNow", new DateTime('now'))
            ->getQuery()
            ->getSingleColumnResult();
    }

    public function addVersion(Quote $quote): Quote
    {
        $doctrineQuote = $this->find($quote->getQuoteId());
        $quoteVersion = new DoctrineQuoteVersion();
        $quoteVersion->setPdfName($quote->getPdfName());
        $doctrineQuote->addVersion($quoteVersion);
        $doctrineQuote->setVersion($quote->getVersionNumber() + 1);
        $quoteVersion->setQuoteDataJson(
            $this->serializer->serialize($quote, 'json')
        );
        $this->getEntityManager()->persist($quoteVersion);
        $this->getEntityManager()->flush();

        $versions = $quote->getVersions();
        $versions[] = (new QuoteVersion())->setId($quoteVersion->getId())->setPdfName($quoteVersion->getPdfName());
        $quote->setVersions($versions);
        $quote->setVersionNumber($doctrineQuote->getVersion());
        return $quote;
    }

    public function findByThreadId(int $threadId): ?Quote
    {
        $doctrineQuote =  $this->findOneBy(['threadId' => $threadId]);
        if (!$doctrineQuote instanceof DoctrineQuote) {
            return null;
        }

        return QuoteMapper::mappingDoctrineToModel($doctrineQuote);
    }

    private function getPdfName(QuoteOpen|QuoteCatalogue $quote)
    {
        $name = join('_', [$quote->getQuoteNumber(), $quote->getTitle(), $quote->getVersion()]);

        return sprintf('%s.%s', $name, self::PDF_EXTENSION);
    }

    public function getQuoteNumberByQuoteId(int $quoteId): ?string
    {
        return $this->findById($quoteId)->getQuoteNumber();
    }
}
