<?php

namespace Marketplace\Component\Quote\Infrastructure\Adapter\Service;

use Marketplace\Component\CleanArchiCore\Infrastructure\Service\CustomAttributes;
use Marketplace\Component\Quote\Domain\Exception\QuoteException;
use Marketplace\Component\Quote\Domain\Model\QuoteItem;
use Marketplace\Component\Quote\Domain\Port\Service\GenerateOfferServiceInterface;
use Open\Izberg\Client\BuyerClient;
use Open\Izberg\Client\OperatorClient;
use Open\Izberg\Exception\ApiException;
use Open\Izberg\Model\ApplicationCategory;
use Open\Izberg\Model\Product;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class GenerateOfferService implements GenerateOfferServiceInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;

    public function __construct(
        private int $offerTemplateId,
        private OperatorClient $operatorClient,
        private BuyerClient $buyerClient,
        private CustomAttributes $customAttributes
    ) {
    }

    /**
     * @throws ApiException
     * @throws QuoteException
     */
    public function generateOffer(QuoteItem $quoteItem, int $merchantId, string $locale): int
    {
        try {
            $originOffer = $this->buyerClient->offerApi()->fetchOfferRaw(
                $this->offerTemplateId,
                $locale
            );
        } catch (ApiException $ex) {
            throw new QuoteException("can't found template offer:" . $ex->getMessage());
        }

        $productId = $originOffer['product']['id'];
        $currency = $originOffer['currency'];

        // create new Product
        $product = $this->duplicateProduct($productId, $merchantId);

        //create offer
        $offerId = $this->createOffer(
            $product->getId(),
            $merchantId,
            $quoteItem,
            $originOffer,
            $currency
        );
        return $offerId;
    }


    /**
     * @throws QuoteException
     */
    private function duplicateProduct(int $productId, int $merchantId): Product
    {
        try {
            $product = $this->operatorClient->productApi()->getProduct($productId);

            $keywords = $product->getKeywords();
            if (!in_array("Devis", $keywords)) {
                $keywords[] = "Devis"; //TODO: is it good keyword ?
            }
            $categories = $product->getApplicationCategories() ?? [];

            $categories = array_map(
                function (ApplicationCategory $category) {
                    return $category->getId();
                },
                $categories
            );
            return $this->operatorClient->productApi()->createProduct(
                $merchantId,
                $product,
                $keywords,
                $categories,
                1
            );
        } catch (ApiException $ex) {
            $this->logger->error("can't duplicate product:" . $ex->getMessage());
            throw new QuoteException("can't duplicate product:" . $ex->getMessage());
        }
    }

    /**
     * @throws ApiException
     * @throws QuoteException
     */
    private function createOffer(
        int $productId,
        int $merchantId,
        QuoteItem $quoteItem,
        $originOffer,
        string $currency
    ): int {
        try {
            $description = $this->generateOfferDescription($quoteItem);

            $customAttributes = $originOffer['attributes'];
            //as quoteItems as group by vat, all the list as same vat.
            $customAttributes[$this->customAttributes->getTvaGroupName()] = $quoteItem->getVatGroupName();
            $language = $originOffer['language'];
            $pictureUrl = $originOffer['default_image'];


            $offerName = $quoteItem->getTitle();

            $price = $quoteItem->getUnitPrice();
            if ($price === null || $price === 0.0) {
                throw new QuoteException("quote item has no price value");
            }
            $sku = $quoteItem->getSku();
            if ($sku === null) {
                throw new QuoteException("quote item has no sku");
            }

            // create offer in izberg
            $productOffer = $this->operatorClient->offerApi()->createProductOffer(
                $productId,
                $merchantId,
                $offerName,
                $sku,
                $price,
                $quoteItem->getQuantity(),
                $description,
                $pictureUrl,
                $customAttributes,
                $language
            );

            //assign an image
            $imageId = $this->getImageId($originOffer);
            if ($imageId) {
                $this->operatorClient->offerApi()->assignImage($productOffer->getId(), $imageId);
            }

            // activate offer in izberg
            $this->operatorClient->offerApi()->activateProduct($productOffer->getId());

            return $productOffer->getId();
        } catch (ApiException $ex) {
            $this->logger->error("can't create or activate offer:" . $ex->getMessage());
            throw new QuoteException($ex->getMessage(), 'offer.creation');
        }
    }

    private function getImageId(array $productOffer): ?int
    {
        $images = $productOffer['assigned_images'];
        if ($images && is_array($images) && count($images) > 0) {
            return $images[0]['id'];
        }
        return null;
    }

    private function generateOfferDescription(QuoteItem $quoteItem): string
    {
        return $quoteItem->getTitle();
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
