<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Infrastructure\Adapter\Service;

use Marketplace\Component\CleanArchiCore\Utils\Department\DepartmentUtils;
use Marketplace\Component\Quote\Domain\Model\QuoteContact;
use Marketplace\Component\Quote\Domain\Port\Repository\QuoteContactRepositoryInterface;
use Marketplace\Component\Quote\Domain\Port\Service\QuoteContactServiceInterface;
use Marketplace\Component\User\Domain\Model\Country;
use Marketplace\Component\User\Domain\Port\Repository\CountryRepositoryInterface;
use Psr\Log\LoggerInterface;

final class QuoteContactService implements QuoteContactServiceInterface
{
    public function __construct(
        private readonly CountryRepositoryInterface $countryRepository,
        private readonly QuoteContactRepositoryInterface $quoteContactRepository,
        private readonly LoggerInterface $logger
    ) {
    }


    public function getQuoteContact(int $countryId, ?string $zipCode): ?QuoteContact
    {
        $country = $this->countryRepository->findById($countryId);
        if (!$country instanceof Country) {
            $this->logger->error(sprintf('getQuoteContact error, no country with id %s', $countryId));
            return null;
        }
        $department = null;
        if ($country->getIzbCountryCode() === 'FR') {
            $department = DepartmentUtils::getDepartment($zipCode);
        }

        return $this->quoteContactRepository->getContactByCountryAndDepartment($countryId, $department);
    }
}
