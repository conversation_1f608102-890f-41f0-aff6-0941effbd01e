<?php

namespace Marketplace\Component\Quote\Domain\Port\Service;

use Marketplace\Component\Quote\Domain\Exception\QuoteException;
use Marketplace\Component\Quote\Domain\Model\Discount;

interface DiscountServiceInterface
{
    /**
     * @throws QuoteException
     **/
    public function createDiscount(
        int $merchantId,
        string $name,
        string $discountCode,
        string $type,
        int $value,
        ?int $quantity = 100
    ): Discount;

    /**
     * @throws QuoteException
     **/
    public function activateDiscount(int $discountId): void;

    /**
     * @throws QuoteException
     **/
    public function addDiscountCodeToCart(int $cartId, string $discountCode): void;
}
