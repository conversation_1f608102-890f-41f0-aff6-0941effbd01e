<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Domain\Port\Repository;

use Marketplace\Component\Quote\Domain\Exception\QuoteException;
use Marketplace\Component\Quote\Domain\Model\QuoteCatalogue;

interface QuoteCatalogueRepositoryInterface
{
    public function getCurrentQuote(int $distantMerchantId, int $companyId): ?QuoteCatalogue;

    public function persist(QuoteCatalogue $quoteCatalogue): QuoteCatalogue;

    /** @throws QuoteException */
    public function findById(int $quoteCatalogueId): QuoteCatalogue;
}
