<?php

namespace Marketplace\Component\Quote\Domain\Exception;

use Exception;

class QuoteException extends Exception
{
    public function __construct(
        string $message,
        private ?string $errorKey = null,
        private ?string $fieldName = null,
        private ?int $errorCode = null,
    ) {
        parent::__construct($message);
    }

    /**
     * @return string|null
     */
    public function getErrorKey(): ?string
    {
        return $this->errorKey;
    }

    /**
     * @param string|null $errorKey
     * @return QuoteException
     */
    public function setErrorKey(?string $errorKey): QuoteException
    {
        $this->errorKey = $errorKey;
        return $this;
    }

    public function getError()
    {
        if ($this->errorKey == null) {
            return $this->message;
        }
        return $this->errorKey;
    }

    /**
     * @return string|null
     */
    public function getFieldName(): ?string
    {
        return $this->fieldName;
    }

    /**
     * @param string|null $fieldName
     * @return QuoteException
     */
    public function setFieldName(?string $fieldName): QuoteException
    {
        $this->fieldName = $fieldName;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getErrorCode(): ?int
    {
        return $this->errorCode;
    }

    /**
     * @param int|null $errorCode
     * @return QuoteException
     */
    public function setErrorCode(?int $errorCode): QuoteException
    {
        $this->errorCode = $errorCode;
        return $this;
    }
}
