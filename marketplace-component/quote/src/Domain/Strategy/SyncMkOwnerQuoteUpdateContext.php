<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Domain\Strategy;

use Marketplace\Component\Quote\Domain\Model\Quote;
use Marketplace\Component\Quote\Domain\UseCase\MarketOwnerUpdateQuoteUseCase\DTO\MarketOwnerUpdateQuoteRequest;

class SyncMkOwnerQuoteUpdateContext
{
    /**
     * @var SyncMkOwnerQuoteUpdateStrategyInterface[]
     */
    private array $strategies = [];

    public function __construct(iterable $strategies)
    {
        foreach ($strategies as $strategy) {
            $this->addStrategy($strategy);
        }
    }

    private function addStrategy(SyncMkOwnerQuoteUpdateStrategyInterface $strategy): void
    {
        $this->strategies[] = $strategy;
    }

    public function process(MarketOwnerUpdateQuoteRequest $request, Quote $quote): bool
    {
        foreach ($this->strategies as $strategy) {
            if ($strategy->canProcess($quote)) {
                return $strategy->process($request);
            }
        }

        return false;
    }
}
