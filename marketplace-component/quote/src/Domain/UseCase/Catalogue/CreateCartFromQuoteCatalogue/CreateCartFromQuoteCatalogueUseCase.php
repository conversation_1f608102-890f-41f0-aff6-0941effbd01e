<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Domain\UseCase\Catalogue\CreateCartFromQuoteCatalogue;

use Marketplace\Component\Cart\Domain\Model\AddToCartOffer;
use Marketplace\Component\Cart\Domain\Port\Repository\CartRepositoryInterface;
use Marketplace\Component\Cart\Domain\Port\Service\AddOfferToCartServiceInterface;
use Marketplace\Component\Cart\Domain\Port\Service\CartCleanServiceInterface;
use Marketplace\Component\Quote\Domain\Enum\QuoteActionEnum;
use Marketplace\Component\Quote\Domain\Exception\QuoteException;
use Marketplace\Component\Quote\Domain\Model\Quote;
use Marketplace\Component\Quote\Domain\Model\QuoteCatalogue;
use Marketplace\Component\Quote\Domain\Model\QuoteItemOffer;
use Marketplace\Component\Quote\Domain\Port\ActionUseCaseInterface;
use Marketplace\Component\Quote\Domain\Port\Repository\QuoteCatalogueRepositoryInterface;
use Marketplace\Component\Quote\Domain\Presenter\CreateCartFromQuoteCataloguePresenterInterface;
use Marketplace\Component\Quote\Domain\UseCase\Catalogue\CreateCartFromQuoteCatalogue\DTO\CreateCartFromQuoteCatalogueRequest;
use Marketplace\Component\Quote\Domain\UseCase\Catalogue\CreateCartFromQuoteCatalogue\DTO\CreateCartFromQuoteCatalogueResponse;

use function PHPUnit\Framework\assertNotNull;

final class CreateCartFromQuoteCatalogueUseCase implements ActionUseCaseInterface
{
    private const  ACTION = QuoteActionEnum::BUYER_BUY_CATALOGUE;

    public function __construct(
        private QuoteCatalogueRepositoryInterface $quoteCatalogueRepository,
        private CartCleanServiceInterface $cartCleanService,
        private AddOfferToCartServiceInterface $addOfferToCartService,
        private ActionUseCaseInterface $actionUseCase,
        private CartRepositoryInterface $cartRepository
    ) {
    }

    public function execute(
        CreateCartFromQuoteCatalogueRequest $request,
        CreateCartFromQuoteCataloguePresenterInterface $presenter
    ): void {
        $response = new CreateCartFromQuoteCatalogueResponse();


        try {
            $quoteCatalogue = $this->quoteCatalogueRepository->findById($request->quoteId);
            $this->actionAuthorised($quoteCatalogue, self::ACTION);
            $this->assert($quoteCatalogue);

            //let's clean the cart
            $this->cartCleanService->cartClean($request->userId);

            $reducePercent = $quoteCatalogue->getReductionInPercent();

            $addToCartOffers = $this->getAddToCartOffers($quoteCatalogue->getQuoteItemOffers(), $reducePercent);

            $cartResult = $this->addOfferToCartService->addItemsToCart(
                $addToCartOffers,
                $request->userId
            );
            if ($cartResult->getCode() != 200) {
                throw new QuoteException(
                    message: $cartResult->getMessage() ?? "",
                    errorCode: $cartResult->getCode() ?? 400
                );
            }
            $cart = $cartResult->getCart();
            assertNotNull($cart, "cart is null");

            $cart->setContainQuote(true);
            $cart->setQuoteId($quoteCatalogue->getQuoteId());
            $this->cartRepository->update($cart);
            $this->executeAction($quoteCatalogue->getQuoteId() ?? -1, self::ACTION);

            $response->succeed = true;
            $response->offerIds = $this->getOfferIds($quoteCatalogue->getQuoteItemOffers());
        } catch (QuoteException $ex) {
            $response->succeed = false;
            $response->addError($ex->getMessage());
            $response->setErrorCode($ex->getErrorCode());
            $presenter->present($response);
        }

        $presenter->present($response);
    }


    private function getOfferIds(array $quoteItemOffers): array
    {
        return array_map(function (QuoteItemOffer $quoteItemOffer) {
            return $quoteItemOffer->getOfferId();
        }, $quoteItemOffers);
    }

    /**
     * @param QuoteItemOffer[] $quoteItemOffers
     * @return AddToCartOffer[]
     */
    private function getAddToCartOffers(array $quoteItemOffers, float $reducePercent): array
    {
        $response = [];
        foreach ($quoteItemOffers as $quoteItemOffer) {
            $price = $this->getDiscountedPrice($quoteItemOffer->getUnitPrice(), $reducePercent);
            $response[] = new AddToCartOffer(
                $quoteItemOffer->getOfferId(),
                $quoteItemOffer->getVariantId(),
                $quoteItemOffer->getQuantity(),
                $price
            );
        }
        return $response;
    }

    private function getDiscountedPrice(?float $price, float $percent): float
    {
        if ($price == null) {
            throw new QuoteException("price is not set");
        }
        return ($price - ($price * ($percent / 100)));
    }


    private function assert(QuoteCatalogue $quoteCatalogue)
    {
        assertNotNull($quoteCatalogue->getQuoteId(), "quote has no quoteIds");
        assertNotNull($quoteCatalogue->getDiscountType(), "quote reduction type is null");
        assertNotNull($quoteCatalogue->getDiscount(), "quote reduction is null");
        assertNotNull($quoteCatalogue->getMerchant()->getDistantId(), "merchant has no distantId");
    }

    public function doTransition(Quote $quote, QuoteActionEnum $action): void
    {
        $this->actionUseCase->doTransition($quote, $action);
    }

    public function executeAction(int $quoteId, QuoteActionEnum $actionEnum): quote
    {
        return $this->actionUseCase->executeAction($quoteId, $actionEnum);
    }

    public function actionAuthorised(Quote $quote, QuoteActionEnum $action): void
    {
        $this->actionUseCase->actionAuthorised($quote, $action);
    }
}
