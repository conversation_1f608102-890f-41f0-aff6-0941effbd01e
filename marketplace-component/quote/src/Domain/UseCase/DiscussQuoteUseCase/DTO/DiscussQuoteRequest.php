<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Domain\UseCase\DiscussQuoteUseCase\DTO;

final class DiscussQuoteRequest
{

    /**
     * DiscussQuoteRequest constructor.
     * @param int $quoteId
     * @param string $message
     * @param array $files
     * @param string $recipient
     */
    public function __construct(
        public int $quoteId = 0,
        public string $message = '',
        public array $files = [],
        public string $recipient = ''
    ) {
    }
}
