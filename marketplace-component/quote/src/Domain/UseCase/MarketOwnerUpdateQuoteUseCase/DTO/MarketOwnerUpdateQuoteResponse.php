<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Domain\UseCase\MarketOwnerUpdateQuoteUseCase\DTO;

use Marketplace\Component\CleanArchiCore\Domain\Error\NotificationTrait;
use Marketplace\Component\Quote\Domain\Enum\QuoteActionEnum;

final class MarketOwnerUpdateQuoteResponse
{
    use notificationTrait;

    public bool $success = true;
    public ?QuoteActionEnum $action = null;
}
