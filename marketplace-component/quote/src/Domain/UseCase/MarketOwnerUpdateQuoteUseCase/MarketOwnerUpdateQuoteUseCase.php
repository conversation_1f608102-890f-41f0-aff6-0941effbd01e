<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Domain\UseCase\MarketOwnerUpdateQuoteUseCase;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CheckMerchantServiceInterface;
use Marketplace\Component\Quote\Domain\Exception\QuoteException;
use Marketplace\Component\Quote\Domain\Port\Repository\QuoteRepositoryInterface;
use Marketplace\Component\Quote\Domain\Port\Service\QuoteWorkFlowServiceInterface;
use Marketplace\Component\Quote\Domain\Presenter\MarketOwnerUpdateQuotePresenterInterface;
use Marketplace\Component\Quote\Domain\Strategy\SyncMkOwnerQuoteUpdateContext;
use Marketplace\Component\Quote\Domain\UseCase\MarketOwnerUpdateQuoteUseCase\DTO\MarketOwnerUpdateQuoteRequest;
use Marketplace\Component\Quote\Domain\UseCase\MarketOwnerUpdateQuoteUseCase\DTO\MarketOwnerUpdateQuoteResponse;
use Psr\Log\LoggerInterface;

final class MarketOwnerUpdateQuoteUseCase
{

    public function __construct(
        private QuoteRepositoryInterface $quoteRepository,
        private CheckMerchantServiceInterface $checkMerchantService,
        private SyncMkOwnerQuoteUpdateContext $context,
        private LoggerInterface $logger,
        private QuoteWorkFlowServiceInterface $quoteWorkFlowService
    ) {
    }

    public function execute(
        MarketOwnerUpdateQuoteRequest $request,
        MarketOwnerUpdateQuotePresenterInterface $presenter
    ): void {
        $response = new MarketOwnerUpdateQuoteResponse();
        try {
            $quote = $this->quoteRepository->findById($request->getQuoteId());
        } catch (QuoteException $e) {
            $response->getNotification()
                ->addError('quote', sprintf('The %d quote does not exist.', $request->getQuoteId()));
            $response->success = false;
            $presenter->present($response);
            return;
        }
        $requestStatus = QuoteImportStatusMapper::mapStatus($request->getStatus());
        $action = $this->quoteWorkFlowService->getActionFromStatus(
            $requestStatus,
            $quote
        );

        if ($action === null) {
            $this->logger->error(
                'the quote ' . $request->getQuoteId() . ' can not reach the status ' . $request->getStatus()
            );
            $response->success = false;
            $presenter->present($response);
            return;
        }
        /**
         * @var int $merchantId
         */
        $merchantId = $quote->getMerchant()->getDistantId();
        if (!$this->checkMerchantService->isMKPOwnerMerchant($merchantId)) {
            $response->getNotification()
                ->addError('merchant', sprintf('The %d merchant id is not MKP owner.', $merchantId));
            $response->success = false;
            $presenter->present($response);
            return;
        }
        $response->success = $this->context->process($request, $quote);
        $response->action = $action;
        $presenter->present($response);
    }
}
