<?php

namespace Marketplace\Component\Quote\Domain\UseCase\MarketOwnerUpdateQuoteUseCase;

use Marketplace\Component\Quote\Domain\Model\QuoteStatus;

class QuoteImportStatusMapper
{
    private const MAP = [
        "confirmé" => QuoteStatus::STATUS_SUBMIT
    ];

    public static function mapStatus(string $value): string
    {
        $key = strtolower(trim($value));
        if (array_key_exists($key, self::MAP)) {
            return self::MAP[$key];
        }
        return $value;
    }
}
