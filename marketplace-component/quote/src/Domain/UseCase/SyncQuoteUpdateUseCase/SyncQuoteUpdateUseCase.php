<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Domain\UseCase\SyncQuoteUpdateUseCase;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CheckMerchantServiceInterface;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\ExportServiceInterface;
use Marketplace\Component\CleanArchiCore\Domain\Record\RecordCollection;
use Marketplace\Component\Quote\Domain\Exception\QuoteException;
use Marketplace\Component\Quote\Domain\Port\Mapper\QuoteUpdateInfosMapper;
use Marketplace\Component\Quote\Domain\Port\Repository\QuoteRepositoryInterface;
use Marketplace\Component\Quote\Domain\Presenter\SyncQuoteUpdatePresenterInterface;
use Marketplace\Component\Quote\Domain\UseCase\SyncQuoteUpdateUseCase\DTO\SyncQuoteUpdateRequest;
use Marketplace\Component\Quote\Domain\UseCase\SyncQuoteUpdateUseCase\DTO\SyncQuoteUpdateResponse;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

final class SyncQuoteUpdateUseCase implements LoggerAwareInterface
{
    private LoggerInterface $logger;
    public function __construct(
        private CheckMerchantServiceInterface $checkMerchantService,
        private QuoteRepositoryInterface $quoteRepository,
        private ExportServiceInterface $exportService,
        private QuoteUpdateInfosMapper $infosMapper
    ) {
    }
    public function execute(SyncQuoteUpdateRequest $request, SyncQuoteUpdatePresenterInterface $presenter): void
    {
        $this->logger->info('SyncQuoteUpdateUseCase : Start Sync of ' . $request->getQuoteId());
        $response = new SyncQuoteUpdateResponse();
        try {
            $quote = $this->quoteRepository->findById($request->getQuoteId());
        } catch (QuoteException $e) {
            $response->getNotification()
                ->addError('quote', sprintf('The %d quote does not exist.', $request->getQuoteId()));
            $this->logger->error(sprintf('SyncQuoteUpdateUseCase : The %d quote does not exist.', $request->getQuoteId()));
            $response->success = false;
            $presenter->present($response);
            return;
        }
        /**
         * @var int $merchantId
         */
        $merchantId = $quote->getMerchant()->getDistantId();
        if (!$this->checkMerchantService->isMKPOwnerMerchant($merchantId)) {
            $response->getNotification()
                ->addError('merchant', sprintf('The %d merchant id is not MKP owner.', $merchantId));
            $this->logger->error(sprintf('SyncQuoteUpdateUseCase : The %d merchant id is not MKP owner.', $merchantId));
            $response->success = false;
            $presenter->present($response);
            return;
        }
        $recordCollection = new RecordCollection([$this->infosMapper->mapQuoteUpdateItemInfos($quote)]);
        $this->exportService->export($recordCollection);
        $this->logger->info('SyncQuoteUpdateUseCase : success.');
        $presenter->present($response);
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
