<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Domain\UseCase\AddToQuote\DTO;

final class AddToQuoteResponse
{
    public function __construct(
        public bool $succeed = false,
        public bool $quoteCreation = false,
        public ?int $quoteId = null,
        public array $errors = []
    ) {
    }


    public function addError(string $error)
    {
        $this->errors[] = $error;
    }
}
