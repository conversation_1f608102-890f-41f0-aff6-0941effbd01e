<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Domain\Enum;

use Marketplace\Component\CleanArchiCore\Domain\Enum\StringEnumTrait;
use Marketplace\Component\Quote\Domain\Model\MailRequest;

/** @codingStandardsIgnoreStart */
enum QuoteActionEnum: string implements DistantKeyInterface, MailRequestInterface
{

    use StringEnumTrait;

    case BUYER_SENT = "buyer_sent";
    case BUYER_EDIT = "buyer_edit";
    case VENDOR_EDIT = "vendor_edit";
    case VENDOR_REFUSE = "vendor_refuse";
    case VENDOR_SUBMIT = "vendor_submit";
    case BUYER_ACCEPT = "buyer_accept";
    case VENDOR_READY = "vendor_ready";
    case BUYER_CANCEL = "buyer_cancel";
    case BUYER_NEGOTIATE = "buyer_negotiate";
    case BUYER_VALIDATE = "buyer_validation";
    case EXPIRATION = "expiration";
    case BUYER_BUY_CATALOGUE = "buyer_buy_catalogue";

    public function getDistantKey(): ?string
    {
        return match ($this) {
            QuoteActionEnum::BUYER_SENT => "TLC_Quote-sent",
            QuoteActionEnum::VENDOR_REFUSE => "TLC_Quote-refused",
            QuoteActionEnum::VENDOR_SUBMIT => "TLC_Quote-submit",
            QuoteActionEnum::BUYER_ACCEPT => "TLC_Quote-waiting",
            QuoteActionEnum::VENDOR_READY => "TLC_Quote-ready",
            QuoteActionEnum::BUYER_CANCEL => "TLC_Quote-cancelled",
            QuoteActionEnum::BUYER_NEGOTIATE => "TLC_Quote-redraft",
            QuoteActionEnum::BUYER_VALIDATE,
            QuoteActionEnum::BUYER_BUY_CATALOGUE => "TLC_Quote-validated",
            QuoteActionEnum::EXPIRATION => "TLC_Quote-expired",
            QuoteActionEnum::BUYER_EDIT,
            QuoteActionEnum::VENDOR_EDIT => null
        };
    }

    public function getMailRequest(): ?MailRequest
    {
        return match ($this) {
            QuoteActionEnum::BUYER_SENT =>
            new MailRequest(templateSlugMerchant: "QUOTE_NEW_TO_MERCHANT", toBuyer: false),
            QuoteActionEnum::VENDOR_REFUSE =>
            new MailRequest(templateSlugBuyer: "QUOTE_REFUSE_TO_USER", toMerchant: false),
            QuoteActionEnum::VENDOR_SUBMIT =>
            new MailRequest(templateSlugBuyer: "QUOTE_SUBMIT_TO_USER", toMerchant: false),
            QuoteActionEnum::BUYER_CANCEL =>
            new MailRequest(templateSlugMerchant: "QUOTE_CANCEL_TO_MERCHANT", toBuyer: false),
            QuoteActionEnum::BUYER_NEGOTIATE =>
            new MailRequest(templateSlugMerchant: "QUOTE_NEGOCIATE_TO_MERCHANT", toBuyer: false),
            QuoteActionEnum::BUYER_VALIDATE, QuoteActionEnum::BUYER_BUY_CATALOGUE =>
            new MailRequest(templateSlugMerchant: "QUOTE_VALIDATE_TO_MERCHANT", toBuyer: false),
            QuoteActionEnum::BUYER_ACCEPT,
            QuoteActionEnum::EXPIRATION,
            QuoteActionEnum::VENDOR_READY,
            QuoteActionEnum::BUYER_EDIT,
            QuoteActionEnum::VENDOR_EDIT => null
        };
    }
}
/** @codingStandardsIgnoreEnd */
