<?php

namespace Marketplace\Component\Quote\Domain\Model;

class QuoteBuyer
{
    private int $buyerId;
    private string $email;
    private string $firstName;
    private string $lastName;
    private string $locale;
    private string $companyName;

    /**
     * @return int
     */
    public function getBuyerId(): int
    {
        return $this->buyerId;
    }

    /**
     * @param int $buyerId
     * @return QuoteBuyer
     */
    public function setBuyerId(int $buyerId): QuoteBuyer
    {
        $this->buyerId = $buyerId;
        return $this;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * @param string $email
     * @return QuoteBuyer
     */
    public function setEmail(string $email): QuoteBuyer
    {
        $this->email = $email;
        return $this;
    }

    /**
     * @return string
     */
    public function getFirstName(): string
    {
        return $this->firstName;
    }

    /**
     * @param string $firstName
     * @return QuoteBuyer
     */
    public function setFirstName(string $firstName): QuoteBuyer
    {
        $this->firstName = $firstName;
        return $this;
    }

    /**
     * @return string
     */
    public function getLastName(): string
    {
        return $this->lastName;
    }

    /**
     * @param string $lastName
     * @return QuoteBuyer
     */
    public function setLastName(string $lastName): QuoteBuyer
    {
        $this->lastName = $lastName;
        return $this;
    }

    /**
     * @return string
     */
    public function getLocale(): string
    {
        return $this->locale;
    }

    /**
     * @param string $locale
     * @return QuoteBuyer
     */
    public function setLocale(string $locale): QuoteBuyer
    {
        $this->locale = $locale;
        return $this;
    }

    /**
     * @return string
     */
    public function getCompanyName(): string
    {
        return $this->companyName;
    }

    /**
     * @param string $companyName
     * @return QuoteBuyer
     */
    public function setCompanyName(string $companyName): QuoteBuyer
    {
        $this->companyName = $companyName;
        return $this;
    }
}
