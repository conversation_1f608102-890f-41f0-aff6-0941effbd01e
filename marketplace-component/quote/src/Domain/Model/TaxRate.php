<?php

namespace Marketplace\Component\Quote\Domain\Model;

class TaxRate
{

    private float $taxRate;
    private string $keyName;

    /**
     * @return float
     */
    public function getTaxRate(): float
    {
        return $this->taxRate;
    }

    /**
     * @param float $taxRate
     * @return TaxRate
     */
    public function setTaxRate(float $taxRate): TaxRate
    {
        $this->taxRate = $taxRate;
        return $this;
    }

    /**
     * @return string
     */
    public function getKeyName(): string
    {
        return $this->keyName;
    }

    /**
     * @param string $keyName
     * @return TaxRate
     */
    public function setKeyName(string $keyName): TaxRate
    {
        $this->keyName = $keyName;
        return $this;
    }
}
