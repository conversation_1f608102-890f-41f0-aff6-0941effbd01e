<?php

declare(strict_types=1);

namespace Marketplace\Component\Quote\Domain\Model;

class QuoteContact
{
    private int $id;
    private string $department;
    private string $fieldSalesmanTrigram;
    private string $fieldSalesmanName;
    private string $sedentarySalesmanTrigram;
    private string $sedentarySalesmanName;
    private array $emails;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return $this
     */
    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string
     */
    public function getDepartment(): string
    {
        return $this->department;
    }

    /**
     * @param string $department
     * @return $this
     */
    public function setDepartment(string $department): self
    {
        $this->department = $department;
        return $this;
    }

    /**
     * @return string
     */
    public function getFieldSalesmanTrigram(): string
    {
        return $this->fieldSalesmanTrigram;
    }

    /**
     * @param string $fieldSalesmanTrigram
     * @return $this
     */
    public function setFieldSalesmanTrigram(string $fieldSalesmanTrigram): self
    {
        $this->fieldSalesmanTrigram = $fieldSalesmanTrigram;
        return $this;
    }

    /**
     * @return string
     */
    public function getFieldSalesmanName(): string
    {
        return $this->fieldSalesmanName;
    }

    /**
     * @param string $fieldSalesmanName
     * @return $this
     */
    public function setFieldSalesmanName(string $fieldSalesmanName): self
    {
        $this->fieldSalesmanName = $fieldSalesmanName;
        return $this;
    }

    /**
     * @return string
     */
    public function getSedentarySalesmanTrigram(): string
    {
        return $this->sedentarySalesmanTrigram;
    }

    /**
     * @param string $sedentarySalesmanTrigram
     * @return $this
     */
    public function setSedentarySalesmanTrigram(string $sedentarySalesmanTrigram): self
    {
        $this->sedentarySalesmanTrigram = $sedentarySalesmanTrigram;
        return $this;
    }

    /**
     * @return string
     */
    public function getSedentarySalesmanName(): string
    {
        return $this->sedentarySalesmanName;
    }

    /**
     * @param string $sedentarySalesmanName
     * @return $this
     */
    public function setSedentarySalesmanName(string $sedentarySalesmanName): self
    {
        $this->sedentarySalesmanName = $sedentarySalesmanName;
        return $this;
    }

    /**
     * @return array
     */
    public function getEmails(): array
    {
        return $this->emails;
    }

    /**
     * @param array $emails
     * @return QuoteContact
     */
    public function setEmails(array $emails): QuoteContact
    {
        $this->emails = $emails;
        return $this;
    }
}
