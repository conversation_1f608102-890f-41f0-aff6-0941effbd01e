<?php

namespace Marketplace\Component\Quote\Domain\Model;

abstract class AbstractQuoteItem
{
    protected ?int $id = null;
    protected ?string $sku = '';
    protected string $title;
    protected int $quantity = 0;
    protected ?float $totalPrice = 0.0;
    protected ?float $unitPrice = 0.0;
    protected ?string $commentary = '';

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSku(): ?string
    {
        return $this->sku;
    }

    /**
     * @param string|null $sku
     * @return $this
     */
    public function setSku(?string $sku): self
    {
        $this->sku = $sku;
        return $this;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @param string $title
     * @return $this
     */
    public function setTitle(string $title): self
    {
        $this->title = $title;
        return $this;
    }

    /**
     * @return int
     */
    public function getQuantity(): int
    {
        return $this->quantity;
    }

    /**
     * @param int $quantity
     * @return $this
     */
    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;
        return $this;
    }

    /**
     * @return float|null
     */
    public function getTotalPrice(): ?float
    {
        return $this->getUnitPrice() * $this->getQuantity();
    }

    /**
     * @param float|null $totalPrice
     * @return $this
     */
    public function setTotalPrice(?float $totalPrice): self
    {
        $this->totalPrice = $totalPrice;
        return $this;
    }

    /**
     * @return float|null
     */
    public function getUnitPrice(): ?float
    {
        return $this->unitPrice;
    }

    /**
     * @param float|null $unitPrice
     * @return $this
     */
    public function setUnitPrice(?float $unitPrice): self
    {
        $this->unitPrice = $unitPrice;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCommentary(): ?string
    {
        return $this->commentary;
    }

    /**
     * @param string|null $commentary
     * @return $this
     */
    public function setCommentary(?string $commentary): self
    {
        $this->commentary = $commentary;
        return $this;
    }
}
