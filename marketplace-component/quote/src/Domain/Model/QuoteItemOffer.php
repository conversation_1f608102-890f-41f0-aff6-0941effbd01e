<?php

namespace Marketplace\Component\Quote\Domain\Model;

class QuoteItemOffer extends AbstractQuoteItem
{
    private ?int $stock;
    private int $offerId;
    private int $reduction;
    private ?float $totalPriceWithVat = 0.0;
    private ?string $vatRate = null;
    private ?string $vatGroupName = null;
    private ?int $variantId = null;
    private int $batchsize = 1;
    private ?int $moq;
    private string $offerCategorySlug;
    private string $offerSlug;

    public function getOfferId(): int
    {
        return $this->offerId;
    }

    public function setOfferId(int $offerId): QuoteItemOffer
    {
        $this->offerId = $offerId;
        return $this;
    }

    public function getReduction(): int
    {
        return $this->reduction;
    }

    public function setReduction(int $reduction): QuoteItemOffer
    {
        $this->reduction = $reduction;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getVatRate(): ?string
    {
        return $this->vatRate;
    }

    /**
     * @param string|null $vatRate
     * @return $this
     */
    public function setVatRate(?string $vatRate): self
    {
        $this->vatRate = $vatRate;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getStock(): ?int
    {
        return $this->stock;
    }

    /**
     * @param int|null $stock
     * @return $this
     */
    public function setStock(?int $stock): self
    {
        $this->stock = $stock;
        return $this;
    }

    public function addQuantity(int $quantity): void
    {
        $this->quantity += $quantity;
    }
    /**
     * @return float|null
     */
    public function getTotalPriceWithVat(): ?float
    {
        return
            $this->getTotalPrice() + floatval(
                $this->vatRate
            ) * $this->getTotalPrice() / 100;
    }

    /**
     * @param float|null $totalPriceWithVat
     * @return QuoteItemOffer
     */
    public function setTotalPriceWithVat(?float $totalPriceWithVat): QuoteItemOffer
    {
        $this->totalPriceWithVat = $totalPriceWithVat;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getVariantId(): ?int
    {
        return $this->variantId;
    }

    /**
     * @param int|null $variantId
     * @return QuoteItemOffer
     */
    public function setVariantId(?int $variantId): QuoteItemOffer
    {
        $this->variantId = $variantId;
        return $this;
    }

    /**
     * @return int
     */
    public function getBatchsize(): int
    {
        return $this->batchsize;
    }

    /**
     * @param int $batchsize
     * @return QuoteItemOffer
     */
    public function setBatchsize(int $batchsize): QuoteItemOffer
    {
        $this->batchsize = $batchsize;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getMoq(): ?int
    {
        return $this->moq;
    }

    /**
     * @param int|null $moq
     * @return QuoteItemOffer
     */
    public function setMoq(?int $moq): QuoteItemOffer
    {
        $this->moq = $moq;
        return $this;
    }

    /**
     * @return string
     */
    public function getOfferCategorySlug(): string
    {
        return $this->offerCategorySlug;
    }

    /**
     * @param string $offerCategorySlug
     * @return QuoteItemOffer
     */
    public function setOfferCategorySlug(string $offerCategorySlug): QuoteItemOffer
    {
        $this->offerCategorySlug = $offerCategorySlug;
        return $this;
    }

    /**
     * @return string
     */
    public function getOfferSlug(): string
    {
        return $this->offerSlug;
    }

    /**
     * @param string $offerSlug
     * @return QuoteItemOffer
     */
    public function setOfferSlug(string $offerSlug): QuoteItemOffer
    {
        $this->offerSlug = $offerSlug;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getVatGroupName(): ?string
    {
        return $this->vatGroupName;
    }

    /**
     * @param string|null $vatGroupName
     * @return QuoteItemOffer
     */
    public function setVatGroupName(?string $vatGroupName): QuoteItemOffer
    {
        $this->vatGroupName = $vatGroupName;
        return $this;
    }
}
