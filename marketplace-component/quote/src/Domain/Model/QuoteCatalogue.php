<?php

namespace Marketplace\Component\Quote\Domain\Model;

use Marketplace\Component\Quote\Domain\Exception\QuoteException;

class QuoteCatalogue extends Quote
{
    /**
     * @var QuoteItemOffer[]
     */
    private array $quoteItemOffers = [];

    /**
     * @return QuoteItemOffer[]
     */
    public function getQuoteItemOffers(): array
    {
        return $this->quoteItemOffers;
    }

    /**
     * @param QuoteItemOffer[] $quoteItemOffers
     * @return $this
     */
    public function setQuoteItemOffers(array $quoteItemOffers): QuoteCatalogue
    {
        $this->quoteItemOffers = $quoteItemOffers;
        return $this;
    }


    public function computeTotalPrice(): float
    {
        $price = 0;
        foreach ($this->getQuoteItemOffers() as $quoteItemOffer) {
            $price += $quoteItemOffer->getTotalPrice();
        }
        return $price;
    }

    public function computeTotalPriceWithVat(): float
    {
        $price = 0;
        foreach ($this->getQuoteItemOffers() as $quoteItemOffer) {
            $price += $quoteItemOffer->getTotalPriceWithVat();
        }
        return $price;
    }

    /**
     * @throws QuoteException
     */
    public function getReductionInPercent(): float
    {
        if ($this->getDiscountType() == QuoteCatalogue::DISCOUNT_PURCENT) {
            return floatval($this->getDiscount());
        }
        if ($this->getDiscountType() == QuoteCatalogue::DISCOUNT_SET) {
            return ($this->getDiscount() / $this->getTotalPrice()) * 100;
        }
        return 0;
    }

    public function getTemplate(): string
    {
        return '@MarketplaceQuote/pdf/quote_catalogue_version.html.twig';
    }
}
