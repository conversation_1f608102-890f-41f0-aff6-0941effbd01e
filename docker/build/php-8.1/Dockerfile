FROM php:8.1-fpm

# php extension installed with mlocati/docker-php-extension-installer https://github.com/mlocati/docker-php-extension-installer
ADD https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions /usr/local/bin/

RUN set -x &&\
    chmod +x /usr/local/bin/install-php-extensions && sync && \
    install-php-extensions redis &&\
    install-php-extensions yaml &&\
    install-php-extensions gd &&\
    install-php-extensions pdo_mysql &&\
    install-php-extensions intl &&\
    install-php-extensions mysqli &&\
    install-php-extensions zip &&\
    install-php-extensions soap &&\
    install-php-extensions bcmath &&\
    install-php-extensions sockets &&\
    install-php-extensions @composer &&\
    install-php-extensions apcu &&\
    install-php-extensions opcache &&\
    install-php-extensions xdebug &&\
    install-php-extensions amqp

RUN mkdir /usr/local/composer
ENV COMPOSER_HOME="/usr/local/composer"

# Install PHPCS
RUN composer global require 'squizlabs/php_codesniffer ~3.6'

# Install Psalm
RUN composer global require 'vimeo/psalm ^4.13' -W
RUN composer global require 'psalm/plugin-symfony ^3' -W

# Register composer vendor bin directory.
ENV PATH=$PATH:$COMPOSER_HOME/vendor/bin/

RUN set -x &&\
  apt-get -y update &&\
  apt-get -y install gnupg zip gzip libzip-dev wget &&\
  wget https://get.symfony.com/cli/installer -O - | bash && \
  mv /root/.symfony/bin/symfony /usr/local/bin/symfony

RUN cp /usr/local/etc/php/php.ini-development /usr/local/etc/php/php.ini \
    && sed -i 's/memory_limit = 128M/memory_limit = 256M/' /usr/local/etc/php/php.ini

# Add user
RUN usermod -u 1000 www-data

RUN mkdir -p /var/cache/symfony \
    && chown -R www-data:www-data /var/cache/symfony \
    && chown -R www-data:www-data /var/www \
    && chown -R www-data:www-data /usr/local/composer

# Expose port 9000 and start php-fpm server
EXPOSE 9000
CMD ["php-fpm"]



