<?php

namespace Open\Webhelp;

use Psr\Log\LoggerInterface;

class WebHelpClientFactory
{
    public function __construct(private WebhelpConfiguration $webhelpConfiguration, private LoggerInterface $logger)
    {
    }

    public function buildWebhelpClient(): WebhelpClient
    {
        $webhelpClient = new WebhelpClient($this->webhelpConfiguration);
        $webhelpClient->setLogger($this->logger);

        return $webhelpClient;
    }
}
