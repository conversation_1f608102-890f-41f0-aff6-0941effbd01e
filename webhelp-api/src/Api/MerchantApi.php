<?php

namespace Open\Webhelp\Api;

use Open\Webhelp\Api;

/***
 *
 * Peu de controle, car cette API ne devrait pas être utilisée, développée uniquement pour certains tests
 *
 * Class MerchantApi
 *
 * @package Open\Webhelp\Api
 */
class MerchantApi extends Api
{
    const CREATE_MERCHANT = "createMerchant";
    const CALL_SUCCESS = "WEBHELP API Call Successfull";

    public function createMerchant(
        $merchantCode,
        $apeCode,
        $companyCreationDate,
        $corporateName,
        $legalForm,
        $shareCapital,
        $shareCapitalCurrency,
        $signatoryCivility,
        $signatoryFirstName,
        $signatoryLastName,
        $signatoryEmail,
        $signatoryPhone,
        $signatoryNationality,
        $signatoryBirthDate,
        $signatoryBirthPlace,
        $vatNumber,
        $siret,
        $rcs,
        $fiscalCode,
        $customerPhysicalMeeting,
        $listedOnTheStockExchange,
        $autOrPublicOrFinInst,
        $thirdPartyOrNonUE,
        $fatfOrEtnc
    ) {
        $data = [
            'merchantCode' => $merchantCode,
            'apeCode' => $apeCode,
            'companyCreationDate' => $companyCreationDate,
            'corporateName' => $corporateName,
            'legalForm' => $legalForm,
            'shareCapital' => $shareCapital,
            'shareCapitalCurrency' => $shareCapitalCurrency,
            'signatory' => [
                'civility' => $signatoryCivility,
                'firstName' => $signatoryFirstName,
                'lastName' => $signatoryLastName,
                'email' => $signatoryEmail,
                'phone' => $signatoryPhone,
                'nationality' => $signatoryNationality,
                'birthDate' => $signatoryBirthDate,
                'birthPlace' => $signatoryBirthPlace,
            ],
            'vatNumber' => $vatNumber,
            'siret' => $siret,
            'rcs' => $rcs,
            'fiscalCode' => $fiscalCode,
            'customerPhysicalMeeting' => $customerPhysicalMeeting,
            'listedOnTheStockExchange' => $listedOnTheStockExchange,
            'autOrPublicOrFinInst' => $autOrPublicOrFinInst,
            'thirdPartyOrNonUE' => $thirdPartyOrNonUE,
            'fatfOrEtnc' => $fatfOrEtnc,
        ];

        $response = $this->httpClient->request(
            'POST',
            'createMerchant',
            [
                'json' => $data
            ]
        );

        $content = $response->getContent();

        $payload = json_decode($content, true);

        if ($payload === null) { // WRONG JSON
            $this->throwGenericError(
                'WRONG JSON RESPONSE',
                __CLASS__ . '::' . __METHOD__,
                $content
            );
            return null;
        }

        $this->writeGenericInfoLog(
            self::CALL_SUCCESS,
            __CLASS__ . '::' . __METHOD__,
            $data,
            $payload
        );

        return $payload['idMerchantWPS'];
    }
}
