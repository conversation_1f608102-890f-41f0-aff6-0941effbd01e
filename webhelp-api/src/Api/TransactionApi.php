<?php

namespace Open\Webhelp\Api;

use DateTime;
use Open\Webhelp\Api;
use Open\Webhelp\Model\ChargeResponse;
use Open\Webhelp\Model\Request;
use Open\Webhelp\Model\SubTransactionResponse;
use Open\Webhelp\Model\TransactionResponse;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class TransactionApi extends Api
{
    const CALL_SUCCESS = "WEBHELP API Call Successfull";

    const STATUS_CREATED = 'CREATED';
    const STATUS_CAPTURED = 'CAPTURED';
    const STATUS_CANCELLED = 'CANCELLED';
    const STATUS_CHARGED = 'CHARGED';

    const PAYMENT_CONDITION_PP_CB = 'PP_CB';
    const PAYMENT_CONDITION_PE_SEPA = 'PE_SEPA';
    const PAYMENT_CONDITION_PE_VIREMENT = 'PE_VIREMENT';

    /***
     * @param $amount
     * @param $codeSubTransactionWps
     * @param null $invoiceNumberWPS
     *
     * @return string|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     */
    public function refund($amount, $codeSubTransactionWps, $invoiceNumberWPS = null): ?string
    {
        $data = [
            'amount' => (string)$amount,
            'codeSubTransactionWps' => $codeSubTransactionWps,
            'invoiceNumberWPS' => $invoiceNumberWPS
        ];

        $content = $this->post((new Request('refund'))->setData($data));
        $payload = json_decode($content, true);

        if ($payload === null) { // WRONG JSON
            $this->throwGenericError(
                'WRONG JSON RESPONSE',
                __CLASS__ . '::' . __METHOD__,
                $content
            );
        }

        $this->writeGenericInfoLog(
            self::CALL_SUCCESS,
            __CLASS__ . '::' . __METHOD__,
            $data,
            $payload
        );

        return $payload['codeRefundWPS'];
    }


    /**
     *
     *  charge a sub-transaction if payment with card.
     *
     * @param $amount
     * @param $codeSubTransactionWps
     *
     * @return ChargeResponse|null
     */
    public function charge($amount, $codeSubTransactionWps): ?ChargeResponse
    {
        $data = [
            'amount' => (string)$amount,
            'codeSubTransactionWps' => $codeSubTransactionWps,
        ];

        $content = $this->post((new Request('charge'))->setData($data));
        $payload = json_decode($content, true);

        if ($payload === null) { // WRONG JSON
            $this->throwGenericError(
                'WRONG JSON RESPONSE',
                __CLASS__ . '::' . __METHOD__,
                $content
            );
        }

        // should return CHARGED
        return new ChargeResponse($payload);
    }


    /***
     *
     * cancel a sub-transaction if payment with card
     *
     * @param $amount
     * @param $codeTransactionWps
     *
     * @return SubTransactionResponse|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     */
    public function cancelSubTransaction($amount, $codeTransactionWps): ?SubTransactionResponse
    {
        $data = [
            'amount' => (string)$amount,
            'codeSubTransactionWps' => $codeTransactionWps,
        ];

        $content = $this->post((new Request('cancelSubTransaction'))->setData($data));
        $payload = json_decode($content, true);

        if ($payload === null) { // WRONG JSON
            $this->throwGenericError(
                'WRONG JSON RESPONSE',
                __CLASS__ . '::' . __METHOD__,
                $content
            );
        }

        return new SubTransactionResponse($payload);
    }


    /***
     *
     * Create a sub-transaction : a part of the transaction, one for each merchant. Charge if payment without card.
     *
     * @param float $amount
     * @param string $code Must be unique per transaction, will not be used elsewhere. (max length 255)
     * @param string $codeTransactionWps The transaction code used for the communication with WPS. (length max 35, alphanumeric)
     * @param string $idMerchantWps The merchant code used for the communication with WPS
     * @param string|null $orderId The order id. (max length 255)
     *
     * @return SubTransactionResponse|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     */
    public function createSubTransaction(
        float $amount,
        string $code,
        string $codeTransactionWps,
        string $idMerchantWps,
        ?string $orderId
    ): ?SubTransactionResponse {
        $data = [
            'amount' => (string)$amount,
            'code' => $code,
            'codeTransactionWps' => $codeTransactionWps,
            'idMerchantWps' => $idMerchantWps,
        ];

        if ($orderId !== null) {
            $data['orderId'] = $orderId;
        }

        $content = $this->post((new Request('createSubTransaction'))->setData($data));

        $payload = json_decode($content, true);

        if ($payload === null) { // WRONG JSON
            $this->throwGenericError(
                'WRONG JSON RESPONSE',
                __CLASS__ . '::' . __METHOD__,
                $content
            );
        }

        return new SubTransactionResponse($payload);
    }

    /**
     *
     * Create a transaction corresponding to the customer order.
     *
     * @param $amount
     * @param $code
     * @param $currency
     * @param $idCustomerWPS
     * @param $customerLanguage
     * @param array $installments (could be an array of DateTime)
     * @param $paymentCondition (PE_SEPA, PP_CB, PE_VIREMENT)
     * @param $subTransationsNumber (minimum 1)
     * @param string|null $normalReturnUrl The url to which the customer will be redirected after a payment with SIPS, required if CB
     *
     * @return TransactionResponse|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function createTransaction(
        $amount,
        $code,
        $currency,
        $idCustomerWPS,
        $customerLanguage,
        array $installments,
        $paymentCondition,
        $subTransationsNumber,
        ?string $normalReturnUrl = null
    ): ?TransactionResponse {
        $cancelRule = true;

        $data = [
            'amount' => (string)$amount,
            'cancelRule' => $cancelRule,
            'code' => $code,
            'currency' => $currency,
            'idCustomerWPS' => $idCustomerWPS,
            'customerLanguage' => $customerLanguage,
            'paymentCondition' => $paymentCondition,
            'subTransationsNumber' => $subTransationsNumber,
            'installmentsNumber' => 0,
        ];

        $formattedInstallments = $this->array2Installments($installments);

        if (count($formattedInstallments) >= 1) {
            $data['installmentsNumber'] = count($formattedInstallments);
            $data['installments'] = $formattedInstallments;
        }

        if ($paymentCondition == self::PAYMENT_CONDITION_PP_CB) {
            $data['normalReturnUrl'] = $normalReturnUrl;
        }

        $content = $this->post((new Request('createTransaction'))->setData($data));
        $payload = json_decode($content, true);

        if ($payload === null) { // WRONG JSON
            $this->throwGenericError(
                'WRONG JSON RESPONSE',
                __CLASS__ . '::' . __METHOD__,
                $content
            );
        }

        return new TransactionResponse($payload);
    }

    /**
     * conversion vers le format demandé
     * @param $elem
     *
     * @return string
     */
    private function toInstallment($elem)
    {
        if ($elem instanceof DateTime) {
            return $elem->format("Y-m-d");
        }

        // Assume it is a string (well formatted)
        return $elem;
    }

    /***
     * Avec ça on pourra prendre un tableau de date ou de string
     * @param $tab
     *
     * @return array
     */
    private function array2Installments($tab): array
    {
        $tab = (is_array($tab)) ? $tab : [$tab];

        return array_map(
            function ($elem) {
                return $this->toInstallment($elem);
            },
            $tab
        );
    }
}
