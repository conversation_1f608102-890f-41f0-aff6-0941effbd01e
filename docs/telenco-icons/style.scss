@import "variables";

@font-face {
  font-family: '#{$icomoon-font-family}';
  src:
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.ttf?ncepqf') format('truetype'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.woff?ncepqf') format('woff'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.svg?ncepqf##{$icomoon-font-family}') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: '#{$icomoon-font-family}' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-shipping {
  &:before {
    content: $icon-shipping; 
  }
}
.icon-close-circle {
  &:before {
    content: $icon-close-circle; 
  }
}
.icon-forum {
  &:before {
    content: $icon-forum; 
  }
}
.icon-logout {
  &:before {
    content: $icon-logout; 
  }
}
.icon-chevron-down {
  &:before {
    content: $icon-chevron-down; 
  }
}
.icon-chevron-left {
  &:before {
    content: $icon-chevron-left; 
  }
}
.icon-chevron-right {
  &:before {
    content: $icon-chevron-right; 
  }
}
.icon-chevron-up {
  &:before {
    content: $icon-chevron-up; 
  }
}
.icon-ajout-comparateur {
  &:before {
    content: $icon-ajout-comparateur; 
  }
}
.icon-arrow-right {
  &:before {
    content: $icon-arrow-right; 
  }
}
.icon-big-arrow {
  &:before {
    content: $icon-big-arrow; 
  }
}
.icon-big-arrow-old {
  &:before {
    content: $icon-big-arrow-old; 
  }
}
.icon-check {
  &:before {
    content: $icon-check; 
  }
}
.icon-round-arrow {
  &:before {
    content: $icon-round-arrow; 
  }
}
.icon-save {
  &:before {
    content: $icon-save; 
  }
}
.icon-user {
  &:before {
    content: $icon-user; 
  }
}
.icon-noconnected {
  &:before {
    content: $icon-noconnected; 
  }
}
.icon-connected {
  &:before {
    content: $icon-connected; 
  }
}
.icon-favori {
  &:before {
    content: $icon-favori; 
  }
}
.icon-chat {
  &:before {
    content: $icon-chat; 
  }
}
.icon-colis {
  &:before {
    content: $icon-colis; 
  }
}
.icon-facebook {
  &:before {
    content: $icon-facebook; 
  }
}
.icon-linkedin {
  &:before {
    content: $icon-linkedin; 
  }
}
.icon-twitter {
  &:before {
    content: $icon-twitter; 
  }
}
.icon-youtube {
  &:before {
    content: $icon-youtube; 
  }
}
.icon-produit {
  &:before {
    content: $icon-produit; 
  }
}
.icon-metier {
  &:before {
    content: $icon-metier; 
  }
}
.icon-marque {
  &:before {
    content: $icon-marque; 
  }
}
.icon-promo {
  &:before {
    content: $icon-promo; 
  }
}
.icon-delete {
  &:before {
    content: $icon-delete; 
  }
}
.icon-dots-vertical {
  &:before {
    content: $icon-dots-vertical; 
  }
}
.icon-edit {
  &:before {
    content: $icon-edit; 
  }
}
.icon-entreprise {
  &:before {
    content: $icon-entreprise; 
  }
}
.icon-euro {
  &:before {
    content: $icon-euro; 
  }
}
.icon-export-pdf {
  &:before {
    content: $icon-export-pdf; 
  }
}
.icon-eye {
  &:before {
    content: $icon-eye; 
  }
}
.icon-information {
  &:before {
    content: $icon-information; 
  }
}
.icon-cb {
  &:before {
    content: $icon-cb; 
  }
}
.icon-list {
  &:before {
    content: $icon-list; 
  }
}
.icon-localisation {
  &:before {
    content: $icon-localisation; 
  }
}
.icon-loupe {
  &:before {
    content: $icon-loupe; 
  }
}
.icon-message {
  &:before {
    content: $icon-message; 
  }
}
.icon-minus {
  &:before {
    content: $icon-minus; 
  }
}
.icon-plus {
  &:before {
    content: $icon-plus; 
  }
}
.icon-transport {
  &:before {
    content: $icon-transport; 
  }
}
.icon-new-order {
  &:before {
    content: $icon-new-order; 
  }
}
.icon-panier {
  &:before {
    content: $icon-panier; 
  }
}
.icon-point-interrogation {
  &:before {
    content: $icon-point-interrogation; 
  }
}
.icon-star {
  &:before {
    content: $icon-star; 
  }
}
.icon-star-alt {
  &:before {
    content: $icon-star-alt; 
  }
}
.icon-tel {
  &:before {
    content: $icon-tel; 
  }
}
.icon-avis {
  &:before {
    content: $icon-avis; 
  }
}
.icon-tel-alt {
  &:before {
    content: $icon-tel-alt; 
  }
}
.icon-date {
  &:before {
    content: $icon-date; 
  }
}
.icon-bloc {
  &:before {
    content: $icon-bloc; 
  }
}
.icon-attachment {
  &:before {
    content: $icon-attachment; 
  }
}
.icon-cadena {
  &:before {
    content: $icon-cadena; 
  }
}
.icon-world {
  &:before {
    content: $icon-world; 
  }
}

