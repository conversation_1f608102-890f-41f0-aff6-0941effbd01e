@startuml
'https://plantuml.com/component-diagram

package "marketplace component" {
    [payment domain]
    [payment infra pres]
    [cart]
    [discussion]
    [mail]
    [offer]
    [order]
    [clean archi core]
    [user]
}

[payment infra pres] --> [clean archi core]
[payment infra pres] --> [webhelp api]
[payment infra pres] --> [izberg api]
[payment infra pres] --> [order]
[payment infra pres] --> [user]
[payment infra pres] --> [payment domain]

[cart] --> [offer]
[cart] --> [clean archi core]
[cart] --> [order]
[cart] --> [user]
[cart] --> [izberg api]

[discussion] --> [user]
[discussion] --> [izberg api]
[discussion] --> [clean archi core]
[discussion] --> [payment domain]
[discussion] --> [order]

[mail] --> [clean archi core]
[mail] --> [user]

[offer] --> [clean archi core]
[offer] --> [user]
[offer] --> [izberg api]


[order] --> [payment domain]
[order] --> [user]
[order] --> [clean archi core]
[order] --> [cart] : OrderRepository::createOrder(Cart $cart)
[order] --> [izberg api]

[clean archi core] --> [user]

[user] --> [izberg api]
[user] --> [clean archi core]

[izberg api] --> [user]
[izberg api] --> [clean archi core]

package "telenco component" {
    [contact us]
    [news]
    [shop in shop]
    [widget]
}

[contact us] --> [mail]
[contact us] --> [user]
[contact us] --> [clean archi core]

[shop in shop] --> [widget]
[shop in shop] --> [clean archi core]

[widget] --> [clean archi core]
[widget] --> [news]
[widget] --> [offer]



@enduml
