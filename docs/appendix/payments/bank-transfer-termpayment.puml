@startuml
title Bank transfert term payment workflow
participant Buyer as B
participant Seller as S
participant <PERSON><PERSON><PERSON> as I
participant Front as F
participant WebHelp as W
participant PSP as P
autonumber 1
note right of B: Cart validation and order creation
B->F: validate his cart to pay it
F-->B:show page to choose payment mode and show term payment mode option if buyer have it
B->F: buyer choose term payment mode and validate it
F->I: PUT /v1/cart/{{cart_id}}/ \nselected_payment_type: term_payment\nselected_payment_method:bank_transfer\nselected_payment_term:id
I-->F:
Loop for each cart item
F->I:PATCH /v1/cart_item/{{cart_item_id}}/ \n Body:{"selected_payment_term":\n"/v1/payment_term/{payment_term_id=buyerTermPaymentId}/"}
I-->F:
end
F->I: create order from cart
I->I: create merchantOrder
hnote over I: order status: initial \nmerchandOrder status: initial
I-->F:
F->I: POST /v1/order/{orderID}/autorized
hnote over I: order status: autorized \nmerchandOrder status: autorized
I-->F:
alt if create order process is success
F-->B: Display confirmation page
else else
F-->B: Display failled page and then redirect to cart to retry with same or another payment mode
end
'include "04 Seller confirm or refuse order v3"
'include "05_create_invoices_and_credit_notes_workflow v3"
'include "07_bank_transfer_payment_reconciliation v3"
'include "08_merchant_order_automatic_cancellation v3"
@enduml
