@startuml

title Bank transfert payment reconciliation
participant Buyer as B
participant Seller as S
participant <PERSON><PERSON><PERSON> as I
participant Front as F
participant WebHelp as W
participant PSP as P
autonumber 1
note right of B: Bank transfer reminder

loop Every day at 7h
F->I:GET /v1/customer_invoice/?status=emmitted\n&payment_status=not_paid
I-->F: response: invoice object list with \n all info about each invoice \nincluded reconciliationKey (field payement_detail)
Loop For each term payment invoice which have to be paid in 5 days
F->B:Send 5 day term payment bank transfer reminder mail notif
end
Loop For each term payment invoice which have to be paid for more than 5 days
F->B:Send 5 day late term payment bank transfer reminder mail notif
end
end

loop Every day at 8h
F->I: GET /v1/merchant_order/?status=initial
I-->F: list of merchant_order_id and cart_id
Loop For each merchant_order
F->I: GET /v1/cart/{cart_id}
I-->F: cart object with payment_type and payment_method
Alt if bank_transfer_prepayment and payment is not received after 5 days
F->B:Send 5 day late pre payment bank transfer reminder mail notif with payment instructions
end
end
end
note right of B: Bank transfer payment reconciliation process
Loop for each bank transfer payment sent by a buyer
B->W: buyer make a bank transfer with reconciliationKey
W->W : Funds reception
Alt if prepayment and amountReceived < GlobalTxAmount
W->W: cancel Tx and all subTx
W->B: refund payment
W->F: WPS will send manual notif\nto alert OPEN to cancel Tx and refund\nwith TxId and reason
Note over F: Need to launch next commands manualy in front console \nbecause WPS send notif manualy by mail
F->I: GET /v1/payment/?external_id={codeTransactionWps}
I-->F: payment object with order_id and merchant_order_id list
F->I: POST /v1/order/{id}/cancel \n this will cancel merchantOrder linked to Order \n orderId is get when createOrder
hnote over I: Order status: cancelled\nmerchant_order status: cancelled\npayment status:cancelled
I-->F:
F->B: mail notif to alert cancellation and refund because \n payment amount sent inferior to order amount
else if (prepayment and amountReceived > GlobalTxAmount) or (term_payement  and amount_received > subTx amount)
W->B: refund too much paid amount
W->F: WPS will send manual notif\nto alert OPEN to cancel Tx and refund\nwith TxId and reason
Note over F: Need to launch next commands manualy in front console \nbecause WPS send notif manualy by mail
F->B: mail notif to alert refund over payment because \n payment amount sent superior to order amount \n(attention : notif is maybe different in prepayment and in term_payment)
else if (prepayment and amountReceived >= GlobalTxAmount) or term_payement
W->W : allocation/split into Vendors paiement account
hnote over W: subTX status: Captured
Loop For each reconciliation push received
W->F: push WPS to notify money is received for subTx \n POST /reconciliationDetail(amountReconciled, \ncodeSubTransactionWps, creationDate, \nreconciliationDate, status, statusTransaction)\nreconciliationDate is filled only if left to pay = 0
F->I: GET /v1/psp/gateway/{subTx}
I-->F: pspgateway object with getway_type and \nmerchant_order_id for prepayment\nand invoice_id for term_payement
alt if gateway_type=prepayment
F->I: GET /v1/merchant_oder/{merchant_order_id}/
I-->F: merchant_order object with order id
Alt if merchant_order stauts = "inital" (to know if merchant_order is already authorized because all merchant_order are authorized when we receive the first push for the first merchant_order)
F->I: POST /v1/order/{id}/authorize
hnote over I: order status: autorized \nmerchandOrer status: autorized
I-->F:
F->B: Mail notif to alert payment was receive and each seller have 48h to confirm or refuse their order
end
F->W: charge(subTxId, amount={merchant_order_amount})
W-->F:
Alt if charge error
F->F: send admin notif
end
F-->W:Code 200 if ok or 500 if problem
else  if gateway_type=term_payement
F->I: GET /v1/customer_invoice/{InvoiceId}/
I-->F:  invoice object with expected_amount, remain_amount, \npaid_amount, total_amount
Alt if paid_amount = WPS subTx received amount (payment already saved)
F-->W:Code 200 (Ok)
Else if paid_amount < WPS subTx amoubt (payment not saved)
F->I : POST /v1/psp/gateway/<external_id>/pay/ \n amount={WPS subTx received amount}-{paid_amount}
opt if remaining amont = 0
I->I: update invoice payment statut to paid
hnote over I: Invoice Payment statut: paid
end
I-->F: response: pspgateway with invoice_id
F-->W:code 200 if ok else code 500
end
F->I: GET /v1/customer_invoice/{InvoiceId}/
I-->F:  invoice object with expected_amount, remain_amount, \npaid_amount, total_amount
alt if invoice remaining_amont = 0
F->W : charge(id SubTX, amount)
hnote over W: subTX status: Paid
Alt charge error
W-->F: result: NOK
F->F: mail notif alert to OPEN and WPS
else charge success
W-->F: result: OK
F->B: Mail notif to confirm we receive full payment for the merchantId
F->S: total payment received mail notif to vendor
end
else Remain amont > 0
F->B: Mail notif to confirm we receive partial payment for the merchantId \n and there left x€ + payment instruction
F->S: partial payment received mail notif to vendor
end
end
end
end
end
@enduml
