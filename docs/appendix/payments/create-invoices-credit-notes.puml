@startuml

title create invoices and credit notes workflow
participant Buyer as B
participant Seller as S
participant <PERSON><PERSON><PERSON> as I
participant Front as F
participant WebHelp as W
participant PSP as P
autonumber 1
note right of B: Front prepare invoice
Loop Every day at 21h
F->I: GET /v1/customer_invoice/?status=pending&externalStatut=none
I-->F:
Loop For each pending invoicing object
Alt If payment_type=prepayment
F->I: PATCH /v1/psp/customer_invoice/{invoiceID}/ \nexternal_status: ready
I-->F:
else If payment_type=term_payment
F->I:  GET /v1/merchant/{merchantId}/attribute/
I-->F: merchant object with WPSmerchantId
Alt if customer have not CustomerWPSid
F->W: POST createCustomer(code="customer"_customerId(Front),\nemail,corporateName,\nlanguage,billingAddress)
W-->F:
End
Note over I: #IZB manque la currency
F->W: createTransaction(amount, code="invoice_"{invoice_id(IZB)}, \ncurrency={USD/EUR},idCustomerWPS, \n codeCondition=PE_VIREMENT, \nsubTransationsNumber=1)
hnote over W: Global TX status: Created
W-->F: 201 - return(codeTransactionWps,reconciliationKey,reason,status) \nstatus and reason are used if for example tx is refused
F->W: createSubTransaction(codeTransactionWps, \ncode="invoice_"{invoice_id (IZB)}, idMerchantWps)
hnote over W: subTX status: Created
W-->F: code 201 - return amountReconciled, \ncodeSubTransactionWps, creationDate, reason, \nreconciliationDate, status, statusTransaction
F->I: Create PSPgateway \n POST /v1/psp/gateway/ \nexternal_id: subTxId\ninvoice (resource_uri)\ngateway_type:term_payment
I-->F:
F->I: PATCH /v1/customer_invoice/{invoiceID}/ \nexternal_status: ready \n payment_details:ReconcilationKey
I-->F:
end
end
end
note right of B: WPS Create invoice and credit notes
Note over I: #IZB WPS need subTx_id
loop Every day at 23h
W->I: GET /v1/customer_invoice/?status=pending&externalStatus=ready
I-->W:List of invoice Id
loop For each invoicing object
W->I: GET /v1/customer_invoice/{{id}}/?full_order_item=true
I-->W:invoice object with merchantOrderId, \n issuer(merchantId) info (billing address, VTA_number, SIREN), invoice line with SKU, \n name, price, TVA, quantity
loop for each SKU
W->I: GET /v1/productoffer/?sku={sku}&only=attributes
I-->W:one offer object list custom_code, made_in
end
W->I: GET /v1/merchant/{merchantId}/attributes/
I-->W:get merchant attributes with \n  legal_form, share_capital, rcs_number
W->I: GET /v1/merchantOrder/{id}
I-->W:get client shipping address and orderId
W->W: Invoices generation
W->I: PATCH /v1/customer_invoice/{customer_invoice_id}/ \nfile_url : url of the pdf file \n id_number : legal invoice number \nexternal_status:Emitted
I-->W:
W->I: POST /v1/customer_invoice/{{customer_invoice_id}}/emit/
hnote over I: invoicing status : Emitted
I-->W:
end
W->I: GET /v1/credit_notes/?status=pending
I-->W:List of credit notes Id
Loop For each credit note object
W->I: GET /v1/credit_note/{{id}}/
I-->W:credit_note object with \n issuer(merchantId) info (billing address), VTA_number, SIREN, credit note lines with SKU, \n name, price, TVA, quantity
loop for each SKU
W->I: GET /v1/productoffer/?sku={sku}&only=attributes
I-->W:one offer object list custom_code, made_in
end
W->I: GET /v1/merchant/{merchantId}/attributes/
I-->W:get merchant attributes with \n  legal_form, share_capital, rcs_number
W->I: GET /v1/customer_invoice/{{id}}/?full_order_item=true
I-->W:invoice object with merchantOrderId
W->I: GET /v1/merchantOrder/{id}
I-->W:get client shipping address and orderId
W->W: Credit notes generation
W->I: PATCH /v1/credit_note/{{customer_invoice_id}}/ \npdf_file : url of the pdf file \n id_number : legal invoice number \nexternalStatus:Emitted \n check request - API not provided ???
I-->W:
W->I: POST /v1/credit_notes/{{customer_invoice_id}}/emit/ \n check request - API not provided ???
hnote over I: credit note status : Emitted
I-->W:
end
end
Note right of B: Front send invoice and credit note to buyer
loop Everyday at 2h
F->I: GET /v1/customer_invoice/?status=emmitted&externalStatus=emmitted
I-->F:
Loop For each invoice object
F->B: send new invoice mail notif
F->I: PATCH /v1/customer_invoice/{{customer_invoice_id}}/ \nexternalStatus:sent
I-->F:
end
F->I: GET /v1/credit_note/?status=emmitted&externalStatus=emmitted
I-->F:
Loop For each credit note object
F->B: send new credit note mail notif
F->I: PATCH /v1/credit_note/{{customer_invoice_id}}/ \nexternalStatus:sent
I-->F:
end
end

@enduml
