<?php

namespace App\ParamConverter;

use Marketplace\Component\Discussion\Domain\UseCase\CreateOfferThreadUseCase\DTO\CreateOfferThreadRequest;
use Marketplace\Component\Discussion\Infrastructure\Adapter\Repository\ThreadRepository;
use Marketplace\Component\Offer\Domain\Model\Merchant;
use Marketplace\Component\Offer\Domain\Model\Offer;
use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Marketplace\Component\User\Infrastructure\Entity\User;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Translation\Exception\NotFoundResourceException;

class SendMerchantMessageParamConverterTest extends TestCase
{
    private SendMerchantMessageParamConverter $paramConverter;
    private ParamConverter|ObjectProphecy $paramConverterConfigurationMock;
    private Security|ObjectProphecy $security;

    protected function setUp(): void
    {
        parent::setUp();

        $prophecy = new Prophet();
        $threadRepository = $prophecy->prophesize(ThreadRepository::class);
        $this->security = $prophecy->prophesize(Security::class);
        $offerRepository = $prophecy->prophesize(OfferRepositoryInterface::class);
        $offerRepository->find(Argument::type('int'))->will(
            function ($args) {
                $id = $args[0];
                if ($id === 1) {
                    $merchant = new Merchant();
                    $merchant->setName('Merchant')
                        ->setId(1);
                    $offer = new Offer();
                    $offer->setMerchant($merchant);
                    return $offer;
                }

                return null;
            }
        );

        $this->paramConverterConfigurationMock = $prophecy->prophesize(ParamConverter::class);
        $this->paramConverterConfigurationMock->getName()->willReturn('request');
        $this->paramConverter = new SendMerchantMessageParamConverter(
            $threadRepository->reveal(),
            $this->security->reveal(),
            $offerRepository->reveal()
        );
    }

    public function testExecute(): void
    {
        $request = new Request([], ['offerId' => 1, 'subject' => 'Sujet message', 'message' => 'Message', 'files' => []]);
        $user = (new User())->setId(1);
        $this->security->getUser()->willReturn($user);
        $this->paramConverter->apply($request, $this->paramConverterConfigurationMock->reveal());
        $this->assertArrayHasKey('request', $request->attributes->all());
        $this->assertEquals(
            new CreateOfferThreadRequest(1, 1, "Message", 'Sujet message', []),
            $request->attributes->get('request')
        );
    }

    public function testThrowAccessDenied()
    {
        $this->expectException(AccessDeniedHttpException::class);
        $this->expectExceptionMessage("Authentication is required");
        $request = new Request();
        $this->security->getUser()->willReturn(null);
        $this->paramConverter->apply($request, $this->paramConverterConfigurationMock->reveal());
    }

    public function testThrowOfferNotFound()
    {
        $this->expectException(NotFoundResourceException::class);
        $this->expectExceptionMessage("Offer not found");
        $request = new Request([], ['offerId' => 0]);
        $this->security->getUser()->willReturn((new User())->setId(1));
        $this->paramConverter->apply($request, $this->paramConverterConfigurationMock->reveal());
    }
}
