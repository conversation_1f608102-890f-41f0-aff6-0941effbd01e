<?php

namespace App\Domain\UseCase\ValidateBuyer;

use App\Domain\Presenter\ValidateBuyerPresenterInterface;
use App\Domain\UseCase\ValidateBuyer\DTO\ValidateBuyerRequest;
use App\Domain\UseCase\ValidateBuyer\DTO\ValidateBuyerResponse;
use App\Tests\Unit\Fake\CompanyRepositoryFake;
use App\Tests\Unit\Fake\OrderRepositoryFake;
use PHPUnit\Framework\TestCase;

class ValidateBuyerUseCaseTest extends TestCase
{
    private ValidateBuyerUseCase $useCase;
    private ValidateBuyerPresenterInterface $presenter;
    protected function setUp(): void
    {
        parent::setUp();

        $this->presenter = new class implements ValidateBuyerPresenterInterface {
            public ValidateBuyerResponse $response;

            public function present(ValidateBuyerResponse $response): void
            {
                $this->response = $response;
            }
        };

        $this->useCase = new ValidateBuyerUseCase(new CompanyRepositoryFake(), new OrderRepositoryFake());
    }

    /**
     * @param ValidateBuyerRequest $request
     * @param ValidateBuyerResponse $expected
     * @dataProvider executeProvide
     */
    public function testExecute(ValidateBuyerRequest $request, ValidateBuyerResponse $expected)
    {
        $this->useCase->execute($request, $this->presenter);
        $this->assertEquals($expected, $this->presenter->response);
    }

    public function executeProvide()
    {
        //company does not exist
        $request = new ValidateBuyerRequest(0);
        $expected = new ValidateBuyerResponse(false);
        yield [$request,$expected];

        //company does not reach 1000 total amount
        $request = new ValidateBuyerRequest(2);
        $expected = new ValidateBuyerResponse(false);
        yield [$request,$expected];

        //company status invalid
        $request = new ValidateBuyerRequest(3);
        $expected = new ValidateBuyerResponse(false);
        yield [$request,$expected];

        //company ready to be validated
        $request = new ValidateBuyerRequest(1);
        $expected = new ValidateBuyerResponse(true);
        yield [$request,$expected];
    }
}
