{% extends 'base.html.twig' %}
{% trans_default_domain 'translations' %}

{% block title %}{{ viewModel.title|formattingMetaTitle }}{% endblock %}

{% block description %}{{ viewModel.description|formattingMetaDescription }}{% endblock %}

{% block breadcrumb %}
    {% if viewModel.parentCategory is not null %}
        {{ include("base-components/category-breadcrumb.html.twig", {items:
            {
                'current': viewModel.name,
                'parents': {
                    (viewModel.parentCategory.name) : (viewModel.parentCategory.slug),
                }
            }
        })
        }}
    {% else %}
        {{ include("base-components/category-breadcrumb.html.twig", {items:
            {
                'current': viewModel.name,
            }
        })
        }}
    {% endif %}
{% endblock %}

{% block body %}
{% set defaultParams =  app.request.query.all()|merge({'categorySlug':viewModel.slug, 'page':viewModel.page}) %}

<div class="container-fluid search-container">
    {% include 'pages/category/components/category-description.html.twig' with {'viewModel': viewModel} %}
    {% if viewModel.offers.offers|length > 0 %}
        <div>
            <div class="row bg-light-grey-30">
                <div class="col-auto col-lg-3 col-xl-2 pl-2 pl-lg-4 pr-0 pr-lg-4 facets-sidebar d-flex align-items-center">
                    <div class="facet-fieldname">
                        <i id="hideFacets" class="fas fa-minus displayFacets" data-show="{{ false }}"></i>
                        <i id="showFacets" class="fas fa-plus displayFacets d-none" data-show="{{ true }}"></i>
                        <span class="text-uppercase">{{ 'ui.search.filters'|trans }}</span>
                    </div>
                </div>

                <div class="col d-flex pl-1 pl-lg-4 align-items-center justify-content-end">
                    <div class="d-none d-lg-block flex-grow-1">
                        <span class="badge badge-light" style="background-color: white;">{{viewModel.offers.nbHits}} {{ 'ui.search.product'|trans({'%count%': viewModel.offers.offers|length}) }}</span>
                    </div>

                    {% set options = [
                        {value: 'score', text: 'ui.search.sort.relevance'},
                        {value: 'price_a', text: 'ui.search.sort.asc_price'},
                        {value: 'price_d', text: 'ui.search.sort.desc_price'}
                    ] %}
                    <div class="d-flex nowrap align-items-center ">
                        <span class="text-nowrap">{{ 'ui.search.sort_by'|trans }}</span>
                        <div class="">
                                <select class="selectOptionCategory form-control form-control-mb pl-1 w-auto bg-transparent border-0 font-weight-bold" id="sortSelect" name="sortSelect">
                                    {% for option in options %}
                                        <option {% if viewModel.sort == option.value %}selected{%endif%} value="{{option.value}}" data-url="{{path('category_slug.search', defaultParams|merge({'sort':option.value, 'page': 1}))}}">
                                            {{ option.text|trans }}
                                        </option>
                                    {% endfor %}
                                </select>
                        </div>
                    </div>

                    <div class="px-0 btn-search" role="group">
                        <a href="{{path('category_slug.search', defaultParams|merge({'displayGrid': 'false'}))}}" class="btn px-2 {% if not viewModel.displayGrid %}active{%endif%}" ><i class="icon-list circle-icon-ice circle-icon-inline"></i></a>
                        <a href="{{path('category_slug.search', defaultParams|merge({'displayGrid': 'true'}))}}" class="btn pr-0 pl-2 ml-md-2 {% if viewModel.displayGrid%}active{%endif%}"><i class="icon-bloc circle-icon-ice circle-icon-inline"></i></a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}

    {% include 'pages/category/components/category-results-list.html.twig' with {'viewModel': viewModel, 'defaultParams': defaultParams} %}

    {% if viewModel.offers.offers|length > 0 %}
    <div class="row bg-blue-green px-5 pt-5 pb-4 justify-content-center">
        {% include 'pages/category/components/category-buttons-list.html.twig' with {'categories': viewModel.categories} %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('soft-highlight-widget') }}
    {{ encore_entry_link_tags('listing-highlight-widget') }}
    {{ encore_entry_link_tags('facets-container') }}
    {{ encore_entry_link_tags('show-more-component') }}
{% endblock %}


{% block javascripts %}
{{ parent() }}
{{ encore_entry_script_tags('category') }}
{{ encore_entry_script_tags('general-review-detail') }}
{{ encore_entry_script_tags('stock-label-component') }}
{{ encore_entry_script_tags('soft-highlight-widget') }}
{{ encore_entry_script_tags('listing-highlight-widget') }}
{{ encore_entry_script_tags('facets-container') }}
{{ encore_entry_script_tags('show-more-component') }}
{{ ui_translation('search.') }}
{{ ui_translation('facets.') }}
{{ ui_translation('offer.') }}
{% endblock %}

