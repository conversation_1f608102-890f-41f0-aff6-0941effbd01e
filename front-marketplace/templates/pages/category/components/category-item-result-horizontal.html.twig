{% trans_default_domain 'translations' %}

{% block stylesheets %}
<style>
    .badge-warning {
        color: black;
    }

    .card {
        border: 0;
        border-bottom: 1px solid rgba(0,0,0,0.125);
    }
</style>
{% endblock %}

<div class="card py-3 col-12">
    <div class="row no-gutters justify-content-center">

        <div class="col-2 col-lg-1 d-flex align-items-center">
            <a href="{{ path('offer_detail', {categorySlug: offer.categorySlug, offerSlug: offer.nameSlug, offerId:offer.id}) }}"
               class="show-offer-detail"
               data-offer="{{ offer.id }}"
            >
                <img class="d-block w-100" src="{{ offer.images[0] }}">
            </a>
        </div>

        <div class="col d-flex flex-column">
            <div class="card-body py-0">
                <div class="row">
                    <div class="col-12 col-lg-6">
                        <h3 class="card-title font-montserrat h5 mb-2"><a
                                href="{{ path('offer_detail', {categorySlug: offer.categorySlug, offerSlug: offer.nameSlug, offerId:offer.id}) }}"
                                class="text-black show-offer-detail"
                                data-offer="{{ offer.id }}"
                            >{{ offer.name }}</a></h3>

                        {% if not offer.variationParent %}
                        <div>
                            {{ offer.sku }}
                        </div>
                        {% endif %}
                        {% if not offer.noPrice %}
                            {% if offer.variationParent %}
                            <div class="mt-2 font-italic">
                                {{ 'offer.variation_has_spec_price'|trans }}
                            </div>
                            {% else %}
                            <div class="mt-2">
                                <span class="h3 font-montserrat font-weight-semi-bold">
                                  {{ offer.price|format_currency(offer.currency, locale=currentLanguage) }} <small class="font-weight-semi-bold">{{ 'search.excl_tax'|trans }}</small>
                                </span>
                                {% if offer.previousPrice %}
                                    <span class="h4 font-montserrat font-weight-normal ml-3 d-inline-block"><del>{{ offer.previousPrice }} {{ 'search.excl_tax'|trans }}</del></span>
                                {% endif %}

                                <div>
                                    <stock-label-component availability="{{ offer.disponibility }}"></stock-label-component>
                                </div>
                            </div>
                            {% endif %}
                        {% endif %}
                    </div>

                    <div class="pt-2 pt-lg-0 col-12 col-lg-3">
                        {% if offer.merchant.topVendor %}
                            <span
                                class="badge badge-pill badge-warning font-weight-semi-bold px-3 py-1">{{ 'offer.top_merchant'|trans }}</span>
                        {% endif %}
                        {% if offer.merchant.partnerVendor %}
                            <span
                                class="badge badge-pill badge-secondary font-weight-semi-bold px-3 py-1">{{ 'offer.certified_merchant'|trans }}</span>
                        {% endif %}
                        {% for labelOffer in offer.customAttributeInfosTypes %}
                            {% set label = 'offer.badge-promo-'~ labelOffer %}
                            <span
                                class="badge badge-pill font-weight-semi-bold px-3 py-1 my-1 text-white mr-2 badge-offer-promo-{{ labelOffer }}">{{ label|trans }}</span>
                        {% endfor %}
                        <p class="h6 font-weight-normal mt-4 mb-0">
                            {{ 'offer.merchant'|trans }} : <span
                                class="text-blue font-weight-bold">{{ offer.merchant.name }}</span>
                        </p>
                        <general-review-detail
                            score="{{ offer.merchant.reviewScore }}"
                            reviews-count="{{ offer.merchant.reviewCount }}"
                        ></general-review-detail>
                    </div>

                    <div
                        class="pt-2 pt-lg-0 col-6 col-lg-3 d-flex justify-content-end align-self-end align-items-center">
                        <!--
                        <a href="" class="btn h3 px-1 mr-2 mb-0 d-block" v-b-tooltip.top :title="$t('offer.add_to_wishlist')">
                          <i class="icon-favori circle-icon-ice"></i>
                        </a>
                        -->

                        <a href="{{ path('offer_detail', {categorySlug: offer.categorySlug, offerSlug: offer.nameSlug, offerId:offer.id}) }}"
                           class="btn h3 px-1 mr-2 mb-0 show-offer-detail" v-b-tooltip.top :title="{{ 'offer.see_detail'|trans }}"
                           data-offer="{{ offer.id }}"
                        >
                            <i class="icon-big-arrow circle-icon-ice"></i>
                        </a>
                    </div>

                </div>
            </div>
        </div>
    </div>

    {% include 'base-components/target-path-form.html.twig' %}
</div>

