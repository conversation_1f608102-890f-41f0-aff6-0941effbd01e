{% extends 'pages/user-account-pages.html.twig' %}

{% trans_default_domain 'translations' %}

{% block breadcrumb %}
    {{ include("base-components/breadcrumb.html.twig", {items:
        {
            'current': 'user_account.profile.title',
            'parents': {'user_account.title' : 'my-profile'}
        }
    })
    }}
{% endblock %}

{% block content %}
    <div class="px-4 py-3 bg-green-10 text-black h5 font-montserrat font-weight-semi-bold mb-4">
        {{ 'user_account.profile.personal_information'|trans }}
    </div>

    <div class="container-fluid px-0">
        <div class="row m-0 p-0 my-4 mx-md-n4">
            <div class="col-12 col-md-6 mb-5 pr-0 px-4 d-flex flex-column">
                {% if viewModel.user %}
                    <div class="bg-light-grey-20 p-4 d-flex flex-column flex-grow-1">
                        <p class="font-weight-semi-bold">{{ viewModel.user.civility }} {{ viewModel.user.lastName }} {{ viewModel.user.firstName }}</p>
                        <p>{{ viewModel.user.email }}</p>
                        <p>{{ viewModel.user.phone }}</p>

                        <div class="text-right">
                            <a href="{{ path('user-profile-edit', {id: viewModel.user.id}) }}" class="text-green font-size-11px font-weight-bold text-uppercase">
                                <i class="icon-edit circle-icon-ice text-black mr-2"></i>
                                {{ 'btn.edit'|trans }}
                            </a>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}

