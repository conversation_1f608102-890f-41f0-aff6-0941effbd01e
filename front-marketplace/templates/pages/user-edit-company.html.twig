{% extends 'pages/user-account-pages.html.twig' %}

{% trans_default_domain 'translations' %}

{% block breadcrumb %}
    {{ include("base-components/breadcrumb.html.twig", {items:
        {
            'current': 'user_account.company.title',
            'parents': {'user_account.title' : 'my-profile'}
        }
    }) }}
{% endblock %}

{% block content %}
    {% for error in viewModel.formErrors %}
        <div class="alert alert-danger mt-4 mb-0">
            {{ error.message }}
        </div>
    {% endfor %}

        {{ form_start(viewModel.form) }}
        <div class="container-fluid pl-0">
            <h2 class="h5 px-3 py-2 bg-green-10 font-montserrat">{{ 'user_account.company.section_title.company'|trans }}</h2>
            <div class="row">
                <div class="col-12 col-md-6 col-lg-6">{{ form_row(viewModel.form.name) }}</div>
                <div class="col-12 col-md-6 col-lg-3">{{ form_row(viewModel.form.country) }}</div>
            </div>
            <div class="row">
                <div class="col-12 col-md-6 col-lg-6">{{ form_row(viewModel.form.vatNumber) }}</div>
                <div class="col-12 col-md-6 col-lg-3">{{ form_row(viewModel.form.identification) }}</div>
            </div>
            <h2 class="h5 px-3 py-2 bg-green-10 font-montserrat">{{ 'user_account.company.section_title.contact'|trans }}</h2>
            <div class="row">
                <div class="col-12 col-md-6 col-lg-5">{{ form_row(viewModel.form.accountingEmail) }}</div>
                <div class="col-12 col-md-6 col-lg-2">{{ form_row(viewModel.form.accountingPhone) }}</div>
            </div>

            <div class="row">
                <div class="col-12">
                    {{ form_row(viewModel.form.submit) }}
                </div>
            </div>
        </div>
        {{ form_end(viewModel.form) }}

{% endblock %}
