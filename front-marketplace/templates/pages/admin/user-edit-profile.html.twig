{% extends 'pages/user-account-pages.html.twig' %}

{% trans_default_domain 'translations' %}

{% block breadcrumb %}
    {{ include("base-components/breadcrumb.html.twig", {items:
        {
            'current': 'user_account.admin.edit.title',
            'parents': {'user_account.title' : 'my-profile',
            'user_account.users.title' : 'users-list'}
        }
    })
    }}
{% endblock %}

{% block content %}

    <a href="{{ path('users-list') }}" title=" {{ 'user_account.users.btn_back_users_list'|trans }}" class="d-flex align-items-center bg-green-10 text-green px-4 py-3 font-montserrat font-weight-bold text-uppercase font-size-11px">
        <i class="icon-chevron-left mr-2" style="font-size: 1em"></i>
        {{ 'user_account.users.btn_back_users_list'|trans }}
    </a>

    {% for error in viewModel.formErrors %}
        <div class="alert alert-danger mt-4 mb-0">
            {{ error.message }}
        </div>
    {% endfor %}

    <div class="container-fluid pl-md-0 py-4">
        {{ form_start(viewModel.form) }}

        <div class="row">
            <div class="col-12 col-md-6 col-lg-5">{{ form_row(viewModel.form.lastName) }}</div>
            <div class="col-12 col-md-6 col-lg-5">{{ form_row(viewModel.form.firstName) }}</div>
        </div>

        <div class="row">
            <div class="col-12 col-md-6 col-lg-5">{{ form_row(viewModel.form.role) }}</div>
        </div>
        <div class="row">
            <div class="col-12 col-md-10 d-flex justify-content-center justify-content-md-end">
                {{ form_row(viewModel.form.submit) }}
            </div>
        </div>
        {{ form_end(viewModel.form) }}
    </div>
{% endblock %}
