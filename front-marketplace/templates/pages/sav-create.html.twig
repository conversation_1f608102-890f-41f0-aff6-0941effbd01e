{% extends 'base.html.twig' %}
{% trans_default_domain 'translations' %}

{% block breadcrumb %}
    {{ include("base-components/breadcrumb.html.twig", {items:
        {
            'current': 'sav'
        }
    })
    }}
{% endblock %}

{% block body %}
    <div id="sav-create">
        <div class="container-fluid mb-3 mt-4 mt-md-0 d-flex flex-column flex-md-row justify-content-center justify-content-md-between align-items-center">
            <h1 class="d-flex align-items-center mb-2 mb-md-0 text-center text-md-left mr-5">
                <i class="icon-save circle-icon-ice mr-3" style="font-size: .5em !important;"></i>
                {{ 'after_sales.create.quality_service'|trans }}
            </h1>
        </div>
        <div class="sav-bg py-5">
            <div class="col-12 justify-content-center d-flex">
                <div class="row" style="max-width: 1100px">
                    {% if showSav == true %}
                    <div class="col">
                        <div class="card text-center rounded-0 p-5 mt-4 border border-light-grey-60">
                            <h3 class="mb-4">{{ 'after_sales.create.header_title'|trans }}</h3>
                            <p>{{ 'after_sales.create.validity_info_1'|trans|raw }}</p>
                            <p>{{ 'after_sales.create.validity_info_2'|trans|raw }}</p>
                            <div class="text-center">
                                <a href="{{ path('sav.create.type', {orderId: orderId, type: 'warranty'}) }}" class="btn btn-primary mt-4 sav-create" data-type="sav" data-id="{{ orderId }}">
                                    <i data-v-3a5edbb0="" class="icon-big-arrow circle-icon-ice circle-icon-inline text-black mr-3"></i>
                                    {{ 'after_sales.create.start'|trans }}
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    <div class="col">
                        <div class="card text-center rounded-0 p-5 mt-4 border border-light-grey-60">
                            <h3 class="mb-4">{{ 'after_sales.create.claiming_request'|trans }}</h3>
                            <p>{{ 'after_sales.create.request.validity_info_1'|trans|raw }}</p>
                            <p>{{ 'after_sales.create.request.validity_info_2'|trans|raw }}</p>
                            <div class="text-center">
                                <a href="{{ path('sav.create.type', {orderId: orderId, type: 'complaint'}) }}" class="btn btn-primary mt-4 sav-create" data-type="reclamation" data-id="{{ orderId }}">
                                    <i data-v-3a5edbb0="" class="icon-big-arrow circle-icon-ice circle-icon-inline text-black mr-3"></i>
                                    {{ 'after_sales.create.start'|trans }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
{% endblock %}
