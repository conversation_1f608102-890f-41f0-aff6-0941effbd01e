{% extends 'base.html.twig' %}
{% trans_default_domain 'translations' %}

{% block title %}{{ 'reset_pwd.reset.title'|trans }}{% endblock %}

{% block breadcrumb %}
    {{ include("base-components/breadcrumb.html.twig", {items:
        {
            'current': 'reset_pwd.page.title',
        }
    }) }}
{% endblock %}

{% block body %}
    <div class="container mb-3">
        <h1>{{ 'reset_pwd.reset.title'|trans }}</h1>
        {{ form_start(viewModel.form) }}
            {% for message in app.flashes('success') %}
                <div class="alert alert-success">
                    {{ message }}
                </div>
            {% endfor %}
            {% for message in app.flashes('error') %}
                <div class="alert alert-danger">
                    {{ message }}
                </div>
            {% endfor %}
            {{ form_row(viewModel.form.plainNewPassword)|nl2br }}
            <button class="btn btn-primary">{{ 'reset_pwd.reset.button'|trans }}</button>
        {{ form_end(viewModel.form) }}
    </div>
{% endblock %}
