{% trans_default_domain 'translations' %}
{% set wishlist = {
    'Ma liste numéro 1': 1,
    'Ma liste numéro 2': 2,
    'Ma liste numéro 3': 3,
    'Ma liste numéro 5': 5,
} %}
<div class="d-none" id="wishlist-menu">
    <section class="wishlist-menu px-4 py-3">
        <h1 class="m-0 font-montserrat font-weight-semi-bold h4">{{ 'navigation.wishlist.title'|trans }}</h1>
        <div class="dropdown-divider mt-3 mb-0"></div>
        {% if app.user %}
            {% if wishlist is defined  %}
                <ul class="list-group-flush m-0 p-0 ">
                    {% for label, id in wishlist %}
                        <li class="list-group-item px-1 py-3 d-flex justify-content-between align-items-center">
                            <div class="mr-4">{{ label }}</div>
                            <div class="d-flex">
                                <form action="" method="POST" class="mr-3">
                                    <input type="hidden" name="id" value="{{ label }}" />
                                    <button class="btn p-0"  type="submit">
                                        <i class="icon-edit circle-icon-green-10"></i>
                                    </button>
                                </form>
                                <form action="" method="POST">
                                    <input type="hidden" name="id" value="{{ label }}" />
                                    <button class="btn p-0" type="submit">
                                        <i class="icon-delete circle-icon-green-10"></i>
                                    </button>
                                </form>
                            </div>
                        </li>
                    {% endfor %}
                </ul>
            {% else %}
                <p class="pt-3 m-0">{{ 'navigation.wishlist.menu.no_wishlist'|trans }}</p>
            {% endif %}
        {% else %}
            <p class="pt-3 m-0">{{ 'navigation.wishlist.menu.need_connection'|trans }}</p>
        {% endif %}

    </section>
</div>

