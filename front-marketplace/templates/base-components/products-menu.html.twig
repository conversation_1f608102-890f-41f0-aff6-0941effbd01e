{# @var navItem \App\Model\View\Base\NavigationItem #}
{% trans_default_domain 'translations' %}
<li class="nav-item">
    <a href="#" title="{{ navItem.label }}" class="nav-link py-4 mx-1 my-0 h3 font-weight-semi-bold d-flex align-items-center has-children" data-target="subMenu">
        <i class="{{ navItem.icon }} text-black mr-3"></i> {{ navItem.label }}
    </a>
    {% if navItem.children %}
    <div class="menu-lv1">
        <a href="#" class="close-btn btn btn-sm btn-outline-green border-0 font-montserrat">
            <span class="i-close">&times;</span> <span>{{ 'btn.close'|trans }}</span>
        </a>
        <ul class="menu-lv1-submenu">
            {% for lv1 in navItem.children %}
                {% if lv1.children %}
                    <li class="menu-lv1-submenu-item">
                        <a href="#" class="menu-lv1-submenu-item-label h4 font-montserrat {{ loop.first ? 'active' : null }}">
                            <span class="menu-lv1-submenu-item-label-image" style="background-image: url('{{ lv1.image }}')"></span>
                            <span class="menu-lv1-submenu-item-label-text">{{ lv1.label }}</span>
                        </a>
                        <div class="menu-lv2">
                            <ul class="menu-lv2-container">
                                {% for lv2 in lv1.children %}
                                    <li class="menu-lv2-item">
                                        <div class="menu-lv2-link">
                                            <a href="{{ path('category_slug.search', {'categorySlug': lv2.slug}) }}" class="menu-lv3-link">{{ lv2.label }}</a>
                                        </div>
                                        <ul class="menu-lv3-container">
                                            {% for lv3 in lv2.children %}
                                                <li class="menu-lv3-item">
                                                    {% set searchParams = {'product.application_categories': [lv3.id]} %}
                                                    <a href="{{ path('category_slug.search', {'categorySlug': lv3.slug}) }}" class="menu-lv3-link">{{ lv3.label }}</a>
                                                </li>
                                            {% endfor %}
                                        </ul>
                                    </li>
                                {% endfor %}
                                <li class="menu-lv1-item-link">
                                    {% set searchParams = {'product.application_categories': [lv1.id]} %}
                                    {# Todo #}
                                    <a href={{ path('search.offer')}}#/filters={{ searchParams | json_encode() | url_encode  }} ">
                                        {{ 'btn.see_all_products'|trans }}
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                {% endif %}
            {% endfor %}
        </ul>
    </div>
    {% endif %}
</li>
