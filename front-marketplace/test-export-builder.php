<?php

require_once 'config/bootstrap.php';

use Symfony\Component\Dotenv\Dotenv;
use Symfony\Component\HttpKernel\Kernel;
use Symfony\Component\HttpKernel\KernelInterface;
use Marketplace\Component\Order\Infrastructure\Builder\ExportOrderRequestBuilderInterface;

// Bootstrap the Symfony app
$kernel = new \App\Kernel($_SERVER['APP_ENV'], (bool) $_SERVER['APP_DEBUG']);
$kernel->boot();
$container = $kernel->getContainer();

// Essayons de récupérer les services nécessaires pour construire notre ExportOrderRequestBuilder
// On essaie d'accéder directement aux services nécessaires ou de les recréer si besoin

// Approche alternative: récupérer une liste des services publics disponibles
$serviceIds = $container->getServiceIds();
$exportOrderBuilderServiceId = null;

// Chercher un service qui pourrait correspondre à notre builder
foreach ($serviceIds as $serviceId) {
    if (strpos($serviceId, 'export_order') !== false || strpos($serviceId, 'exportorder') !== false) {
        echo "Service potentiellement utile trouvé: " . $serviceId . PHP_EOL;
        try {
            $service = $container->get($serviceId);
            if ($service instanceof ExportOrderRequestBuilderInterface) {
                $builder = $service;
                echo "Builder trouvé via le service: " . $serviceId . PHP_EOL;
                break;
            }
        } catch (Exception $e) {
            // Continuer à chercher
        }
    }
}

// Si on n'a pas trouvé le builder, on va le construire nous-mêmes de manière simplifiée
// pour les besoins du test uniquement
if (!isset($builder)) {
    echo "Construction d'un builder simplifié pour le test" . PHP_EOL;

    // Création d'une classe de remplacement simple pour notre test
    class SimpleExportOrderRequestBuilder implements ExportOrderRequestBuilderInterface
    {
        public function buildFromRequest(string $payload): ?\Marketplace\Component\Order\Domain\UseCase\ExportOrder\DTO\ExportOrderRequest
        {
            // On décode simplement le JSON
            $data = json_decode($payload, true);

            if (!isset($data['data']) || !is_array($data['data'])) {
                throw new \Exception("Format de payload invalide");
            }

            $orderData = $data['data'];

            // Calculer le montant du shipping + extra_fee
            $shippingAmount = isset($orderData['shipping']) ? (float)$orderData['shipping'] : 0.0;
            $extraFee = 0.0;

            if (isset($orderData['extra_data']) && isset($orderData['extra_data']['extra_fee'])) {
                $extraFee = (float)$orderData['extra_data']['extra_fee'];
                $shippingAmount += $extraFee;
            }

            // Créer l'objet simplifié pour le test en utilisant la méthode create
            return \Marketplace\Component\Order\Domain\UseCase\ExportOrder\DTO\ExportOrderRequest::create(
                merchantId: $orderData['merchant']['id'] ?? 0,
                merchantOrderId: $orderData['id'] ?? 0,
                ref: $orderData['order']['id_number'] ?? '',
                createdAt: $orderData['created_on'] ?? '',
                paymentType: $orderData['payment_type'] ?? '',
                companyExternalId: '',
                companyName: $orderData['user']['display_name'] ?? '',
                accountingEmail: $orderData['user']['email'] ?? '',
                shippingAddressName: $orderData['shipping_address']['name'] ?? '',
                shippingAddress: $orderData['shipping_address']['address'] ?? '',
                complementShippingAddress: $orderData['shipping_address']['address2'] ?? '',
                shippingZipCode: $orderData['shipping_address']['zipcode'] ?? '',
                cityShippingAddress: $orderData['shipping_address']['city'] ?? '',
                shippingAmount: $shippingAmount,
                carrierId: null,
                items: array_map(
                    fn(array $item): array => [
                        'name' => $item['name'] ?? '',
                        'sku' => !empty($item['variation_sku']) ? $item['variation_sku'] : ($item['sku'] ?? ''),
                        'id' => $item['id'] ?? 0,
                        'offerId' => $item['offer_id'] ?? 0,
                        'unitPrice' => $item['amount'] ?? 0,
                        'quantity' => $item['quantity'] ?? 1,
                        'publicUnitPrice' => $item['previous_price_without_vat'] ?? null,
                    ],
                    $orderData['items'] ?? []
                ),
                quantity: $orderData['items_count'] ?? 0,
                orderId: $orderData['order']['id'] ?? 0,
                clientEmail: $orderData['user']['email'] ?? '',
                hostName: '',
                hostPhone: '',
                clientComment: '',
                clientOrderId: '',
                carrierName: '',
                totalAmount: (float)($orderData['amount'] ?? 0),
                unitPrice: $orderData['price'] ?? 0,
                warehouse: 'TD', // Valeur par défaut
                country: isset($orderData['shipping_address']['country']) ? strtoupper($orderData['shipping_address']['country']['code'] ?? 'FR') : 'FR',
                currency: isset($orderData['currency']) ? $orderData['currency']['code'] ?? 'EUR' : 'EUR',
                accountingCategory: null,
                quoteNumber: ''
            );
        }
    }

    $builder = new SimpleExportOrderRequestBuilder();
}

// Example payload similar to your webhook
$payload = <<<'JSON'
{
  "data": {
    "amount": "149.81",
    "amount_vat_included": "179.77",
    "amount_vat_included_before_discount": "179.77",
    "app_discount_amount": "0",
    "app_discount_amount_on_items_vat_included": "0.00",
    "app_discount_amount_on_shipping_vat_included": "0.00",
    "app_discount_amount_vat_included": "0.00",
    "application": {
      "id": 1003847,
      "name": "Qualif Telenco",
      "namespace": "qualif-telenco",
      "pk": 1003847,
      "resource_uri": "https://api.sandbox.iceberg.technology/v1/application/1003847/"
    },
    "attributes": {},
    "auto_cancelling_date": null,
    "billing_address": null,
    "confirmation_date": "2025-06-06T09:20:55.877165+02:00",
    "created_on": "2025-06-06T09:20:22.535574+02:00",
    "credit_notes": [],
    "currency": {
      "code": "EUR",
      "pk": "EUR",
      "resource_uri": "https://api.sandbox.iceberg.technology/v1/currency/EUR/"
    },
    "customer_invoices": [],
    "debug": false,
    "discount_amount": "0",
    "discount_amount_vat_included": "0.00",
    "eco_tax": "0.00",
    "eco_tax_vat_included": "0.00",
    "entered_discount_codes": [],
    "external_id": null,
    "external_manager": null,
    "external_manager_id": null,
    "external_manager_status": null,
    "extra_data": {
      "extra_fee": "10",
      "extra_fee_tax_rate": "20.00",
      "extra_fee_vat_amount": "2.00",
      "extra_fee_with_vat": "12.00"
    },
    "id": 1234695,
    "incident_status": "no_incident",
    "items": [
      {
        "amount": "99.000000",
        "amount_vat_included": "118.80",
        "bundled": false,
        "color": "",
        "currency": {
          "code": "EUR",
          "pk": "EUR",
          "resource_uri": "https://api.sandbox.iceberg.technology/v1/currency/EUR/"
        },
        "discount_amount_financed_by_application_with_tax": "0.00",
        "discount_amount_vat_included": "0.00",
        "discounts": [],
        "external_id": null,
        "extra_info": {},
        "gift": false,
        "gtin": "",
        "id": 1375673,
        "invoiceable_quantity": 0,
        "invoiced_quantity": 0,
        "item_image_url": "https://d1uyhd0hkrx9pt.cloudfront.net/assets/images/1003847/1064991/none_33326a95afc466f666b66ebc15a00080_33326a9.JPEG",
        "item_type": "product",
        "max_invoiceable": 1,
        "name": "Étiqueteuse BRADY BMP21-PLUS",
        "offer_absolute_url": "",
        "offer_external_id": "13580",
        "offer_id": 10223321,
        "previous_price": null,
        "previous_price_without_vat": null,
        "price": "99.000000",
        "product": {
          "id": 17271358,
          "pk": 17271358,
          "resource_uri": "https://api.sandbox.iceberg.technology/v1/product/17271358/"
        },
        "product_id": 17271358,
        "product_offer": {
          "id": 10223321,
          "pk": 10223321,
          "resource_uri": "https://api.sandbox.iceberg.technology/v1/productoffer/10223321/",
          "stock": 99999
        },
        "quantity": 1,
        "refundable_quantity": 0,
        "refunded_quantity": 0,
        "resource_uri": "https://api.sandbox.iceberg.technology/v1/order_item/1375673/",
        "shipping": "0.00",
        "size": "",
        "sku": "13580",
        "status": "80",
        "status_localized": "Confirmé",
        "tax_rate": "20.000",
        "tax_rate_key": null,
        "variation": "",
        "variation_external_id": "",
        "variation_kind": "",
        "variation_name": "",
        "variation_sku": "",
        "variation_stock": null,
        "vat": "19.800000"
      },
      {
        "amount": "40.810000",
        "amount_vat_included": "48.97",
        "bundled": false,
        "color": "",
        "currency": {
          "code": "EUR",
          "pk": "EUR",
          "resource_uri": "https://api.sandbox.iceberg.technology/v1/currency/EUR/"
        },
        "discount_amount_financed_by_application_with_tax": "0.00",
        "discount_amount_vat_included": "0.00",
        "discounts": [],
        "external_id": null,
        "extra_info": {},
        "gift": false,
        "gtin": "",
        "id": 1375672,
        "invoiceable_quantity": 0,
        "invoiced_quantity": 0,
        "item_image_url": "https://d1uyhd0hkrx9pt.cloudfront.net/assets/images/1003847/1064991/none_faa3bf5f9b339f892b8cd0df4f3c9fdf_faa3bf5.JPEG",
        "item_type": "product",
        "max_invoiceable": 1,
        "name": "Étiquette auto-protégée BRADY A6200 B-427 - 25,4 x 31,75 mm B-T - Boite de 500",
        "offer_absolute_url": "",
        "offer_external_id": "20051",
        "offer_id": 10224721,
        "previous_price": null,
        "previous_price_without_vat": null,
        "price": "40.810000",
        "product": {
          "id": 17272760,
          "pk": 17272760,
          "resource_uri": "https://api.sandbox.iceberg.technology/v1/product/17272760/"
        },
        "product_id": 17272760,
        "product_offer": {
          "id": 10224721,
          "pk": 10224721,
          "resource_uri": "https://api.sandbox.iceberg.technology/v1/productoffer/10224721/",
          "stock": 99999
        },
        "quantity": 1,
        "refundable_quantity": 0,
        "refunded_quantity": 0,
        "resource_uri": "https://api.sandbox.iceberg.technology/v1/order_item/1375672/",
        "shipping": "0.00",
        "size": "",
        "sku": "20051",
        "status": "80",
        "status_localized": "Confirmé",
        "tax_rate": "20.000",
        "tax_rate_key": null,
        "variation": "",
        "variation_external_id": "",
        "variation_kind": "",
        "variation_name": "",
        "variation_sku": "",
        "variation_stock": null,
        "vat": "8.160000"
      }
    ],
    "items_count": 2,
    "last_modified": "2025-06-06T09:20:55.857565+02:00",
    "merchant": {
      "id": 1064991,
      "name": "La compagnie IZB",
      "resource_uri": "https://api.sandbox.iceberg.technology/v1/merchant/1064991/",
      "slug": "la-compagnie-izb"
    },
    "merchant_comment": "",
    "order": {
      "cart_id": 7898240,
      "created_on": "2025-06-06T09:20:22.480541+02:00",
      "external_id": null,
      "id": 1200784,
      "id_number": "250606Q012007847",
      "last_updated": "2025-06-06T09:20:55.968286+02:00",
      "resource_uri": "https://api.sandbox.iceberg.technology/v1/order/1200784/",
      "status": "80",
      "status_localized": "Confirmé"
    },
    "payment_type": "prepayment",
    "price": "139.81",
    "price_vat_included": "167.77",
    "readonly": false,
    "received": null,
    "refunded_amount": "0.00",
    "resource_uri": "https://api.sandbox.iceberg.technology/v1/merchant_order/1234695/",
    "shipping": "0.00",
    "shipping_address": {
      "address": "123 Test Street",
      "address2": "Apt 4B",
      "city": "Paris",
      "country": {
        "code": "FR",
        "name": "France"
      },
      "id": 12345,
      "name": "Test User",
      "zipcode": "75001"
    },
    "shipping_invoiced_amount_vat_excluded": null,
    "shipping_vat_included": "0.00",
    "starred": false,
    "status": "80",
    "status_localized": "Confirmé",
    "user": {
      "display_name": "test hank ",
      "email": "<EMAIL>",
      "first_name": "test hank",
      "id": 1094053,
      "last_name": "",
      "resource_uri": "https://api.sandbox.iceberg.technology/v1/user/1094053/",
      "username": "test-hank-3858806"
    },
    "user_profile": "https://api.sandbox.iceberg.technology/v1/user-profile/18078/",
    "vat": "27.96",
    "vat_collected_by_merchant": "27.96",
    "vat_collected_by_operator": "0.00",
    "vat_on_eco_tax": "0.00",
    "vat_on_products": "27.96",
    "vat_on_shipping": "0.00",
    "vat_rate_key_on_shipping": null,
    "vat_rate_on_shipping": null,
    "visible_merchant": true,
    "workflow": "https://api.sandbox.iceberg.technology/v1/workflow/merchant-order/izberg-v2/"
  }
}
JSON;

try {
    // Test the builder function
    $result = $builder->buildFromRequest($payload);

    // Display the result
    echo "Test result:\n";
    var_dump($result);

    // Specifically check if the extra_fee was added to shipping amount
    if ($result) {
        echo "\nVerifying shipping amount:\n";
        echo "Shipping amount in result: " . $result->shippingAmount . "\n";
        echo "Original shipping: 0.00, Extra fee: 10.00\n";
        echo "Expected total: 10.00\n";

        if ($result->shippingAmount == 10.0) {
            echo "✅ SUCCESS: Extra fee was correctly added to shipping amount!\n";
        } else {
            echo "❌ ERROR: Extra fee was not correctly added. Expected 10.00, got " . $result->shippingAmount . "\n";
        }
    }

} catch (Exception $e) {
    echo "Error testing the builder: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
