import PasswordView from "./elements/PasswordView.js";
import axios from 'axios';
import i18n from "./vuejs/i18n/i18n";
// import bootstrap
import "bootstrap";

// import Utils
import * as utils from "./javascript/utils";

import 'daterangepicker';
import 'daterangepicker/daterangepicker.css';
import moment from 'moment';

import './javascript/menu';

// import custom styles
import "./styles/app.scss";

import './javascript/sav';
import {showModal, showAlert} from "./javascript/bootbox-modals";

// import jQuery
const $ = require('jquery');
global.$ = global.jQuery = $;

// Add custom elements
customElements.define('password-view', PasswordView, {extends: "input"})

const Routing = require('./javascript/Routing');
// JS code to execute
$(document).ready(function () {
    // Sticky Header
    utils.stickyHeader();
    utils.closeMessage();
    utils.showFooterCaptcha();

    const badgeCountMessage = document.getElementById("countOperatorMessages");
    const userConnected = badgeCountMessage ? badgeCountMessage.dataset.authenticated : false;

    if (userConnected) {
        axios
            .get(Routing.generate('api_message_operator_count'))
            .then((response) => {
                if (response.data > 0) {
                    badgeCountMessage.textContent = response.data;
                    badgeCountMessage.classList.remove("invisible");
                    document.title = "(" + response.data + ") " + document.title;
                }
            })
            .catch(() => console.error('error'));
    }

    // Spinner fade out
    utils.hideSpinner();

    // Change the value of the "show" attribute of t-messager
    // Note that the real hide and show of t-messenger is done by css
    var showMessenger = document.getElementById("show-messager");
    if (showMessenger) {
        showMessenger.addEventListener("change", function (e) {
            document.getElementById("operator-messenger").setAttribute("show", e.target.checked);
        });
    }

    //Listen to the close-messenger event
    document.addEventListener("close-messenger-event", function (e) {
        document.getElementById("show-messager").checked = false;
        document.getElementById("operator-messenger").setAttribute("show", "false");
    });

    // Bootstrap popover init
    $('[data-toggle="popover"]').popover({
        html: true,
        trigger: 'click',
        content: function () {
            if ($(this).data("target")) {
                return $($($(this).data("target")).html());
            }

            if ($(this).data("content")) {
                return $($($(this).data("content")).html());
            }

            return 'Aucun contenu';
        },
    });

    let lang = document.documentElement.lang;
    switch (lang) {
        case 'fr':
            moment.locale('fr');
            break;
        case 'en':
            moment.locale('en-GB');
            break;
        case 'de':
            moment.locale('de');
            break;
        default:
            moment.locale('en-GB');
    }

    let clear = i18n.t('filter.date.clear');
    let apply = i18n.t('filter.date.apply');
    let format = i18n.t('filter.date.format');
    $('[data-toggle="daterange"]').daterangepicker({
        opens: 'left',
        autoUpdateInput: false,
        locale: {
            format: format,
            cancelLabel: clear,
            applyLabel: apply,
        }
    });

    $('[data-toggle="daterange"]').on('apply.daterangepicker', function (ev, picker) {
        $(this).data('start', picker.startDate.format('MM/DD/YYYY'));
        $(this).data('end', picker.endDate.format('MM/DD/YYYY'));
        $(this).val(picker.startDate.format(format) + ' - ' + picker.endDate.format(format));
    });

    $('[data-toggle="daterange"]').on('cancel.daterangepicker', function (ev, picker) {
        $(this).data('start', '');
        $(this).data('end', '');
        $(this).val('');
    });

    // Close popover on click on dropdown
    $('[data-toggle="dropdown"]').on('click', function () {
        $('[data-toggle="popover"]').popover('hide');
    })

    // Close popover on click outside
    $('body').on('click', function (e) {
      if (e.target.matches(".triggerUserMenuButton")) {
        return
      }
      $('[data-toggle="popover"]').each(function () {
        //the 'is' for buttons that trigger popups
        //the 'has' for icons within a button that triggers a popup
        if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.popover').has(e.target).length === 0) {
          $(this).popover('hide');
        }
      });
    });


    $('#cartAddOffer').on('click', function (e) {
        e.preventDefault();
        let dataApi = $(this).data("api");
        let dataOrders = $(this).data("orders");

        let dataOrdersMapped = dataOrders.map(({offerId, quantity, variationId}) => ({offerId, quantity, variationId}));
        $.ajax({
            url: dataApi,
            method: "POST",
            data: JSON.stringify({"offers": dataOrdersMapped}),
            contentType: "application/json; charset=utf-8",
            beforeSend: function () {
                $('#cartAddOffer').prop('disabled', true);
                $("#cart-add-offer-spinner").removeClass('invisible');
            }
        }).done(function () {
            window.location.href= Routing.generate('cart.show');
        }).catch((error) => {
            $('#cartAddOffer').prop('disabled', false);
            $("#cart-add-offer-spinner").addClass('invisible');
            if (error.status === 400 && error.responseJSON.success === false && error.responseJSON.message) {
              showAlert(error.responseJSON.message)
            } else {
              showAlert(i18n.t('order.list.reorder.error'))
            }
        })
    });

    $(".back-to-top").on("click", function () {
        $("html, body").animate({scrollTop: 0}, 500);
    });

    $("#newsletter_form").on("submit", function (e) {
      e.preventDefault();
      let self = this;
      let newsletterButton = document.querySelector('#newsletter_form button');
      let newsletterInput = document.querySelector('#newsletter_form input[type="email"]');
      let newsletterEmail = newsletterInput.value;
      let captchaBlock = document.getElementById('reCaptchaNewsletter');
      let captchaId = captchaBlock.dataset.widgetId;
      $.ajax({
        url: $(self).attr("action"),
        method: "POST",
        data: {
          'email': newsletterEmail,
          'g-recaptcha-response': grecaptcha.getResponse(captchaId)
        },
        beforeSend: () => {
          newsletterInput.disabled = true;
          newsletterButton.disabled = true;
        }
      }).done(function (response) {
        if (response.success) {
          showModal($(self).attr("title"), $(self).attr("msgdatasuccess"));
          newsletterInput.value = '';
        }

        if (!response.success) {
          let message = ' '
          for (const [key, value] of Object.entries(response.errors)) {
            message += value + '<br>';
          }
          showModal($(self).attr("title"), message);
        }

        grecaptcha.reset(captchaId);
        newsletterInput.disabled = false;
        newsletterButton.disabled = false;
      }).fail(function (xhr, textStatus, errorThrown){
        showModal($(self).attr("title"), errorThrown);
        $("input[name='email']", self).val('');
        newsletterInput.disabled = false;
        newsletterButton.disabled = false;
        grecaptcha.reset(captchaId);
      });
    });

});
