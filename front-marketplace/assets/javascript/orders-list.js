import bootbox from "bootbox";
import axios from 'axios';
import i18n from "../vuejs/i18n/i18n";
import {hideSpinner, showSpinner} from "./utils";

const $ = require('jquery');
const Routing = require('./Routing');

$(document).ready(function() {
  filterStatusShow();
  $('#tabs a').click(function (){
    filterStatusShow($(this).attr('id'));
  });
	$('#orders-list-filters').on('submit', function(e) {
		e.preventDefault();
    let current_tab = $('#tabs a.active').attr('id');
    let currentPanel = $("#pane-in-progress .orders-table");

    let filteredStatus = null;
    switch (current_tab) {
      case  "tab-in-progress" :
        filteredStatus = $('#filteredStatusInProgress').val();
        break;
      case "tab-completed" :
        filteredStatus = $('#filteredStatusCompleted').val();
        currentPanel = $("#pane-completed .orders-table");
        break;
      case "tab-cancelled" :
        filteredStatus = $('#filteredStatusCancelled').val();
        currentPanel = $("#pane-cancelled .orders-table");
        break;
    }
		const filteredNumber = $('#filteredNumber').val();

		const filteredCreationDateStart = $('#filteredCreationDate').data('start');
		const filteredCreationDateEnd = $('#filteredCreationDate').data('end');

    const filters = JSON.parse(currentPanel.attr('filters'));
    filters.filteredNumber = filteredNumber;
    filters.filteredStatus = filteredStatus;
    filters.filteredCreationDateStart = filteredCreationDateStart;
    filters.filteredCreationDateEnd = filteredCreationDateEnd;
    currentPanel.attr('filters', JSON.stringify(filters));

	});
});


document.addEventListener("reorder_items", function(e) {
	const offers = JSON.stringify({"offers": e.detail});
	document.activeElement.blur();
	showSpinner();

	axios.post(Routing.generate('cart_add_offers'), offers)
	.then((response) => {
		if (response.status === 200 && response.data.succeeded === true) {
			window.location.href= Routing.generate('cart.show');
		} else {
			hideSpinner();
			bootbox.alert({message: i18n.t(response.data.message), centerVertical: true, container: 'body'})
		}
	}).catch((error) => {
		hideSpinner();
    let messageKey = 'order.list.reorder.error';
    if (error.response && error.response.data.message) {
      messageKey = error.response.data.message;
    }
		bootbox.alert({message: i18n.t(messageKey), centerVertical: true, container: 'body'})
	})
});

function filterStatusShow(current_tab){
  current_tab = current_tab ? current_tab : $('#tabs a.active').attr('id');
  $('.filtered-status').hide();
  switch (current_tab) {
    case "tab-in-progress" :
      $('#filteredStatusInProgress').show();
      break;
    case "tab-completed" :
      $('#filteredStatusCompleted').show();
      break;
    case "tab-cancelled":
      $('#filteredStatusCancelled').show();
      break;
  }
}
