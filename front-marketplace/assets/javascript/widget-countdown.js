// import Utils
import {formatTwoDigit} from "./utils";


const MINUTES = 60;
const HOURS = 60 * MINUTES;
const DAYS = 24 * HOURS;
// Get all countdown widget
const widgetList = document.querySelectorAll('.widget-triple-promo [data-time]');

widgetList.forEach( (widget) => {
  widgetCountdown(widget);
})

/**
 * @param {HTMLElement} widget
 * @returns {void}
 */
function widgetCountdown(widget) {
  const expiredOffer = widget.querySelector('.triple-promo-item-expired-offer');
  const timeBloc = widget.querySelector('.triple-promo-item-remaining-time-bloc');
  const daysBloc = widget.querySelector('.triple-promo-item-remaining-time-days');
  const daysValue = daysBloc.querySelector('.triple-promo-item-remaining-time-value');
  const hoursValue = widget.querySelector('.triple-promo-item-remaining-time-hours .triple-promo-item-remaining-time-value');
  const minutesValue = widget.querySelector('.triple-promo-item-remaining-time-minutes .triple-promo-item-remaining-time-value');
  const secondsBloc = widget.querySelector('.triple-promo-item-remaining-time-seconds');
  const secondsValue = secondsBloc.querySelector(' .triple-promo-item-remaining-time-value');
  const separatorDays = widget.querySelector('.separator-days');
  const separatorMinutes = widget.querySelector('.separator-minutes');
  const endDate = widget.dataset.time
  let previousDiff = {};

  function refreshCountdown () {
    const now = Date.now() / 1000;
    const remainingSeconds = endDate - now;

    const diff = {
      days: Math.floor(remainingSeconds / DAYS),
      hours: Math.floor(remainingSeconds % DAYS / HOURS),
      minutes: Math.floor(remainingSeconds % HOURS / MINUTES),
      seconds: Math.floor(remainingSeconds % MINUTES )
    }

    updateDom(diff);

    if (remainingSeconds > DAYS) { // remaining time > 1 day -> refresh each minutes
      setTimeout(() => {
        window.requestAnimationFrame(refreshCountdown) //refresh only if page is visible
      }, 1000*60);
    } else if (remainingSeconds > 1) { // remaining time < 1 day but > 1 second -> refresh each second
      setTimeout(() => {
        window.requestAnimationFrame(refreshCountdown) //refresh only if page is visible
      }, 1000);
    } else {
      expiredOffer.style.height = timeBloc.offsetHeight + 'px';
      expiredOffer.classList.remove('d-none');
      timeBloc.classList.add('d-none');
    }
  }

  refreshCountdown();

  /**
   * Update HTML DOM
   * @param {{days: number, hours: number, minutes: number, seconds: number}} diff
   */
  function updateDom(diff) {
    if (previousDiff.days !== diff.days) {
      daysValue.innerText = formatTwoDigit(diff.days);

      if (diff.days <= 0) {
        daysBloc.classList.add('d-none');
        separatorDays.classList.add('d-none');
        secondsBloc.classList.remove('d-none');
        separatorMinutes.classList.remove('d-none');
      }
    }

    if (previousDiff.hours !== diff.hours) {
      hoursValue.innerText = formatTwoDigit(diff.hours);
    }

    if (previousDiff.minutes !== diff.minutes) {
      minutesValue.innerText = formatTwoDigit(diff.minutes);
    }

    if (diff.days < 1) {
      if (previousDiff.seconds !== diff.seconds) {
        secondsValue.innerText = formatTwoDigit(diff.seconds);
      }
    }

    previousDiff = diff;
  }
}
