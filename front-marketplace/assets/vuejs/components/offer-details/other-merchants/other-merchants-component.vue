<template>
  <div v-if="vendors.length > 0">
    <div class="h5 font-montserrat border-top py-3 mb-0">
      {{ $t('offer.other_merchants.title') }}
    </div>
    <div class="border-bottom py-3" v-for="vendor in [...vendors.slice(0,showElem)]">
      <div class="d-flex align-items-center">
        <span><span class="h3 font-montserrat font-weight-semi-bold">{{ priceFormatter(vendor.price, vendor.currency) }}</span> <small class="font-weight-semi-bold ">{{ $t("cart.excl_tax") }}</small></span>
        <span class="badge badge-pill border border-blue-green text-blue-green gb-white ml-3" v-if="vendor.cheapest">{{ $t('offer.other_merchants.lower_price') }}</span>
      </div>
      <div class="h6 font-montserrat d-flex justify-content-between align-items-center font-weight-normal">
        <div>{{ $t('offer.other_merchants.selling_by') }}<span class="text-blue font-weight-semi-bold">{{ vendor.merchantName }}</span></div>
        <div class="font-size-11px">
          <a :href="vendor.productLink" class="text-uppercase text-green text-decoration-none">{{ $t('offer.other_merchants.see_product') }}</a>
        </div>
      </div>
    </div>
    <div v-if="vendors.length > 2">
      <button class="btn btn-link btn-sm text-green font-weight-bold text-uppercase text-decoration-none font-montserrat" @click="toggleMore">
        <span v-if="!showMore">{{ $t('offer.other_merchants.show_more') }}</span>
        <span v-else>{{ $t('offer.other_merchants.show_less') }}</span>
      </button>
    </div>
  </div>
</template>

<script>
import axios from "axios";
const Routing = require('../../../../javascript/Routing');

export default {
  name: "other-merchants-component",
  data: () => {
    return {
      showMore: false,
      maxShow: 2,
      vendors: []
    }
  },
  props: ['offerId'],
  created() {
    const vendors = []
    axios.get(Routing.generate('api.offer.similar', {offerId: this.offerId})).then(response => {
      for (const [key, value] of Object.entries(response.data.similarOffers)) {
        vendors.push(value);
      }
      this.vendors = vendors;
    });
  },
  computed: {
    showElem() {
      return this.showMore ? this.vendors.length : this.maxShow;
    }
  },
  methods: {
    toggleMore() {
      this.showMore = !this.showMore;
    }
  }
}
</script>

