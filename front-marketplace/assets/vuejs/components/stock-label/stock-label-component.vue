<template>
    <span v-if="formatedAvailability" class="status__availablity" :class="formatedAvailability.class">{{ $t(formatedAvailability.label) }}</span>
</template>


<script>
export default {
  name: "stock-label-component",
  props: {
    availability: {
      type: String,
      default: null
    },
  },
  data: () => {
    return {
      dispo: {
        'in_restock' : {label: "offer.in_restock", class:"availablity__restock"},
        'in_stock' : {label: "offer.on_stock", class:"availablity__stock"},
        'on_order' : {label: "offer.on_order", class:"availablity__on-order"},
        'on_demand' : {label: "offer.on_order", class:"availablity__on-order"},
        'soon_out_of_stock' : {label: "offer.soon_out_of_stock", class:"availablity__soon-out-of-stock"},
        'not_homogeneous' : {label: "offer.not_homogeneous", class:"availablity__not-homogeneous"},
      },
    }
  },
  computed: {
      formatedAvailability() {
        if (this.availability !== null) {
          const key = this.availability.toLowerCase().replaceAll(' ', '_');
          if(this.dispo[key]) {
              return this.dispo[key];
          }
        }
        return false;
      },
  },
}
</script>
