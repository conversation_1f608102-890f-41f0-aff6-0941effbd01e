<template>
  <div class="container-fluid p-0">

    <div class="row m-0 align-items-center">
      <template v-if="this.locale === 'en'"><!-- En langue EN la currency est devant le champs -->
        <div class="col pr-1 pl-0">
          <div class="input-group align-items-center">
            <span class="pr-1">{{currencySymbol}}</span>
            <input type="number" class="form-control p-1" v-model="minPrice" placeholder="Min" :class="{red: !validated}">
          </div>
        </div>
        <div class="col p-0 ">
          <div class="input-group align-items-center">
            <span class="pr-1">{{currencySymbol}}</span>
            <input type="number" class="form-control p-1" v-model="maxPrice" placeholder="Max" :class="{red: !validated}">
          </div>
        </div>
      </template>
      <template v-else><!-- Sinon la currency est derrière le champs -->
        <div class="col pr-1 pl-0">
          <div class="input-group align-items-center">
            <input type="number" class="form-control p-1" v-model="minPrice" placeholder="Min" :class="{red: !validated}">
            <span class="pl-1">{{currencySymbol}}</span>
          </div>
        </div>
        <div class="col p-0 ">
          <div class="input-group align-items-center">
            <input type="number" class="form-control p-1" v-model="maxPrice" placeholder="Max" :class="{red: !validated}">
            <span class="pl-1">{{currencySymbol}}</span>
          </div>
        </div>
      </template>

      <div class="col-auto p-0 text-center">
        <button class="btn btn-link btn-sm" @click="getResults">
          <i class="fa fa-check"></i>
        </button>
      </div>
    </div>

  </div>
</template>

<script>
export default {
  name: "facets-bloc-price",
  components: {},
  props: ['facet','facetIndex', 'currency', 'fromCategory', 'locale'],
  data() {
      return {
        visible: true,
        value : [],
        validated: true,
        currencySymbol: ""
      }
  },
  created() {
    if (this.fromCategory) {
        const searchParams = new URLSearchParams(window.location.search);
        searchParams.getAll('filter').map(facet => {
            const parsedFacet = JSON.parse(facet);
            Object.keys(parsedFacet).map(name => {
                if (name === 'price') {
                    this.$store.commit('searchStore/SET_PRICE_RANGE', [parsedFacet[name].from, parsedFacet[name].to]);
                    this.$store.commit('searchStore/SET_MIN_PRICE', parsedFacet[name].from);
                    this.$store.commit('searchStore/SET_MAX_PRICE', parsedFacet[name].to);
                }
            })
        });
    }

  },
    mounted() {
    this.value = [this.minPrice, this.maxPrice];
    let currencySymbolTab = this.$i18n.n("0","currency", this.currency ).split("");
    this.currencySymbol = currencySymbolTab[currencySymbolTab.length-1];
  },
  computed: {
    minPrice: {
      get() {
        return this.$store.state.searchStore.minPrice
      },
      set(minPrice) {
        this.$store.commit('searchStore/SET_MIN_PRICE', minPrice);
      }
    },
    maxPrice: {
      get() {
        return this.$store.state.searchStore.maxPrice
      },
      set(maxPrice) {
        this.$store.commit('searchStore/SET_MAX_PRICE', maxPrice);
      }
    },
  },
  methods: {
    getResults() {
      if(this.minPrice ==="" || this.maxPrice ==="" || this.maxPrice === 0 || parseInt(this.maxPrice) < parseInt(this.minPrice)) {
        this.validated = false
      } else {
        this.validated = true
        this.value = [this.minPrice, this.maxPrice];
        this.$store.commit('searchStore/SET_PRICE_RANGE', this.value);
        this.$store.commit('searchStore/SET_MIN_PRICE', this.minPrice);
        this.$store.commit('searchStore/SET_MAX_PRICE', this.maxPrice);

        if (this.fromCategory) {
            this.$store.dispatch('searchStore/setSearchParamsUrlAndReloadPage');
        } else {
            this.$store.dispatch('searchStore/updateUrl');
            this.$store.dispatch('searchStore/search');
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.merge-tooltip {
  position: absolute;
  left: 50%;
  bottom: 100%;
  transform: translate(-50%, -15px);
}

.facets-bloc {
  input[type="number"] {
    min-height: 1rem;
  }
}
.red {
  border-color: red;
}
</style>
