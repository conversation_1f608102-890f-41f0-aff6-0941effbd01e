<template>
  <div class="row">
    <div class="col-auto mb-2" v-for="variation in listVariation">
      <div class="h5 font-montserrat mb-2">
        {{ variation.name }}
      </div>
      <select class="custom-select" @change="selectVariation">
        <option :selected="!selectedVariationId" disabled hidden>Select...</option>
        <option
            class="px-2"
            v-for="value in variation.values"
            :value="value.value"
            :selected="value.ids.includes(selectedVariationId)"
            :data-variation-ids="JSON.stringify(value.ids)"
            :data-variation-sku="JSON.stringify(value.sku)"
            :data-variation-prices="JSON.stringify(value.prices)"
            :data-variation-no-prices="JSON.stringify(value.noPrices)"
            :data-variation-pictures="JSON.stringify(value.pictures)"
            :data-variation-description="JSON.stringify(value.description)"
            :data-variation-short-description="JSON.stringify(value.shortDescription)"
            :data-variation-documentations="JSON.stringify(value.documentations)"
            :data-variation-technical-datas="JSON.stringify(value.technicalDatas)"
            :data-variation-agencies="JSON.stringify(value.agencies)"
            :data-variation-infos-types-labels="JSON.stringify(value.infosTypesLabels)"
            :data-variation-availability-tag="JSON.stringify(value.availabilityTag)"
            :data-variation-batch-size="JSON.stringify(value.batchSize)"
        >
          {{ variation.key === 'color' ? $t('offer.variations.color.' + value.value.toLowerCase()) : value.value }}
        </option>
      </select>
    </div>
  </div>
</template>

<script>

const groupBy = function (xs, key) {
  return xs.reduce(function (rv, x) {
    (rv[x[key]] = rv[x[key]] || []).push(x);
    return rv;
  }, {});
};

export default {
  name: 'cart-variations',
  props: ['variationsList'],
  data() {
    return {
      selectedVariationId: null,
      listVariation: []
    }
  },
  beforeMount() {
    this.listVariation = this.getVariationsList();
  },
  methods: {
    selectVariation(event) {
      const variationIds = JSON.parse(event.target.selectedOptions[0].dataset.variationIds);
      const variationSku = JSON.parse(event.target.selectedOptions[0].dataset.variationSku);
      const variationPrices = JSON.parse(event.target.selectedOptions[0].dataset.variationPrices);
      const variationNoPrices =  JSON.parse(event.target.selectedOptions[0].dataset.variationNoPrices);
      const variationPictures = JSON.parse(event.target.selectedOptions[0].dataset.variationPictures);
      const variationAgencies = JSON.parse(event.target.selectedOptions[0].dataset.variationAgencies);
      const variationDescription = JSON.parse(event.target.selectedOptions[0].dataset.variationDescription);
      const variationShortDescription = JSON.parse(event.target.selectedOptions[0].dataset.variationShortDescription);
      const variationDocumentations = JSON.parse(event.target.selectedOptions[0].dataset.variationDocumentations);
      const variationTechnicalDatas = JSON.parse(event.target.selectedOptions[0].dataset.variationTechnicalDatas);
      const variationInfosTypesLabels = JSON.parse(event.target.selectedOptions[0].dataset.variationInfosTypesLabels);
      const variationAvailabilityTag = JSON.parse(event.target.selectedOptions[0].dataset.variationAvailabilityTag);
      const variationBatchSize = JSON.parse(event.target.selectedOptions[0].dataset.variationBatchSize);

      const variation = {
        id: null,
        sku: null,
        price: null,
        noPrice: false,
        pictures: [],
        description: null,
        shortDescription: null,
        documentations: [],
        technicalDatas: {},
        agencies: [],
        infosTypesLabels: [],
        availabilityTag: null,
        batchSize: null,
      };

      if (variationIds.length > 0) {
        this.selectedVariationId = variationIds[0];
        variation.id = variationIds[0];
      }

      if (variationSku.length > 0) {
        variation.sku = variationSku[0];
      }

      if (variationPrices.length > 0) {
        variation.price = variationPrices[0];
      }

      if (variationNoPrices.length > 0) {
        variation.noPrice = variationNoPrices[0];
      }

      if (variationIds.length > 0 && variationPrices.length > 0) {
        variation.pictures = variationPictures[0];
      }

      if (variationIds.length > 0 && variationAgencies.length > 0) {
        variation.agencies = variationAgencies[0];
      }

      if (variationIds.length > 0 && variationDescription.length > 0) {
        variation.description = variationDescription[0];
      }

      if (variationIds.length > 0 && variationShortDescription.length > 0) {
        variation.shortDescription = variationShortDescription[0];
      }

      if (variationIds.length > 0 && Object.keys(variationTechnicalDatas).length > 0) {
        variation.technicalDatas = variationTechnicalDatas;
      }

      if (variationIds.length > 0 && Object.keys(variationDocumentations).length > 0) {
        variation.documentations = variationDocumentations;
      }

      if (variationIds.length > 0 && variationInfosTypesLabels.length > 0) {
        variation.infosTypesLabels = variationInfosTypesLabels;
      }

      if (variationIds.length > 0 && variationAvailabilityTag.length > 0) {
        variation.availabilityTag = variationAvailabilityTag;
      }

      if (variationBatchSize.length > 0) {
        variation.batchSize = variationBatchSize[0];
      }

      this.$emit('variation-id-selected', variation);
    },
    getVariationsList: function () {
      const explodedVariationsWithVaryingAttr = this.variationsList.map(
          ({id, sku, price, noPrice, pictures, varryingAttributes, description, shortDescription, technicalDatas, documentations, agencies, infosTypesLabels, availabilityTag, batchSize}) => {
            return varryingAttributes.map(
                ({key, name, value}) => ({id, sku, price, noPrice, pictures, key, name, value, description, shortDescription, technicalDatas, documentations, agencies, infosTypesLabels, availabilityTag, batchSize})
            );
          }
      );

      let allExplodedVariations = [];
      explodedVariationsWithVaryingAttr.forEach(variation => {
        allExplodedVariations = [...allExplodedVariations, ...variation];
      });

      const variationsByKey = groupBy(allExplodedVariations, 'key');
      const variationsByKeyAndValue = [];

      for (const variation in variationsByKey) {
        if (variationsByKey[variation].length > 0) {
          const {name, key} = variationsByKey[variation][0];
          const variationGroupedByValue = groupBy(variationsByKey[variation], 'value');

          const sortedVariations = Object.keys(variationGroupedByValue)
              .sort()
              .reduce((accumulator, key) => {
                  accumulator[key] = variationGroupedByValue[key];
                  return accumulator;
              }, {})
          ;

          variationsByKeyAndValue.push({
            key,
            name,
            values: Object.keys(sortedVariations).map(value => ({
                value,
                ids: sortedVariations[value].map(({id}) => id),
                sku: sortedVariations[value].map(({sku}) => sku),
                prices: sortedVariations[value].map(({price}) => price),
                noPrices: sortedVariations[value].map(({noPrice}) => noPrice),
                pictures: sortedVariations[value].map(({pictures}) => pictures),
                description: sortedVariations[value].map(({description}) => description),
                shortDescription: sortedVariations[value].map(({shortDescription}) => shortDescription),
                technicalDatas: sortedVariations[value].map(({technicalDatas}) => technicalDatas)[0],
                documentations: sortedVariations[value].map(({documentations}) => documentations)[0],
                agencies: sortedVariations[value].map(({agencies}) => agencies),
                infosTypesLabels: sortedVariations[value].map(({infosTypesLabels}) => infosTypesLabels)[0],
                availabilityTag: sortedVariations[value].map(({availabilityTag}) => availabilityTag)[0],
                batchSize: sortedVariations[value].map(({batchSize}) => batchSize)
            })),
          });

        }
      }

      return variationsByKeyAndValue;
    }
  }
}
</script>
