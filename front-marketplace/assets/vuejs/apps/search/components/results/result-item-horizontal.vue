<template>
  <div class="card py-3 col-12">
    <div class="row no-gutters justify-content-center">
      <div class="col-2 col-lg-1 d-flex align-items-center">
        <b-carousel
            :interval="4000"
            :controls="offer.images.length > 1"
            class="d-flex flex-fill"
        >
          <b-carousel-slide :key="image" v-for="image in offer.images" :img-src="image" style="padding-top: 100%;">
            <template #img>
              <a :href="getRoute(offer.categorySlug, offer.nameSlug, offer.id)" @click.prevent="submitShowOfferDetail(offer.id)" class="product-slide" :style="{backgroundImage:`url(${image})`}"></a>
            </template>
          </b-carousel-slide>
        </b-carousel>
      </div>

      <div class="col d-flex flex-column">
        <div class="card-body py-0">
          <div class="row">
            <div class="col-12 col-lg-6">
              <h3 class="card-title font-montserrat h5 mb-2"><a :href="getRoute(offer.categorySlug, offer.nameSlug, offer.id)" @click.prevent="submitShowOfferDetail(offer.id)" class="text-black">{{offer.name}}</a></h3>
              <div v-if="offer.variationParent === false">
                {{ offer.sku }}
              </div>
              <span v-if="offer.reduction" class="badge badge-orange color-white">{{offer.reduction}}</span>
              <div v-if="!offer.noPrice" class="mt-2">
                <div v-if="offer.variationParent" class="mb-2 font-italic">
                  {{ $t('offer.variation_has_spec_price') }}
                </div>
                <span v-else class="h3 font-montserrat font-weight-semi-bold">
                  {{ priceFormatter(offer.price, currency) }} <small class="font-weight-semi-bold">{{ $t('search.excl_tax') }}</small>
                </span>
                <span v-if="offer.previousPrice" class="h4 font-montserrat font-weight-normal ml-3 d-inline-block">
                  <del>
                    {{ priceFormatter(offer.previousPrice, currency) }} {{ $t('search.excl_tax') }}
                  </del>
                </span>
                  <div>
                      <stock-label-component :availability="offer.disponibility"></stock-label-component>
                  </div>
              </div>
            </div>
            <div class="pt-2 pt-lg-0 col-6 col-lg-3">
              <span v-if="offer.merchant.topVendor" class="badge badge-pill badge-warning font-weight-semi-bold px-3 py-1">{{ $t('offer.top_merchant') }}</span>
              <span v-if="offer.merchant.partnerVendor" class="badge badge-pill badge-secondary font-weight-semi-bold px-3 py-1">{{ $t('offer.certified_merchant') }}</span>
              <span v-for="labelOffer in offer.customAttributeInfosTypes" class="badge badge-pill font-weight-semi-bold px-3 py-1 my-1 text-white mr-2" :class="'badge-offer-promo-' + labelOffer">{{ $t('offer.badge-promo-'+ labelOffer) }}</span>
                <p class="h6 font-weight-normal mt-4 mb-0">
                  {{ $t('offer.merchant') }} : <span class="text-blue font-weight-bold">{{offer.merchant.name}}</span>
                </p>
                <general-review-detail-component :score="offer.rating" :reviews-count="offer.countVotes"></general-review-detail-component>
            </div>

            <div class="pt-2 pt-lg-0 col-6 col-lg-3 d-flex justify-content-end align-self-end align-items-center">
              <!--
              <a href="" class="btn h3 px-1 mr-2 mb-0 d-block" v-b-tooltip.top :title="$t('offer.add_to_wishlist')">
                <i class="icon-favori circle-icon-ice"></i>
              </a>
              -->

              <a :href="getRoute(offer.categorySlug, offer.nameSlug, offer.id)" @click.prevent="submitShowOfferDetail(offer.id)" class="btn h3 px-1 mr-2 mb-0" v-b-tooltip.top :title="$t('offer.see_detail')">
                  <i class="icon-big-arrow circle-icon-blue-30"></i>
              </a>
            </div>

          </div>
        </div>
      </div>
    </div>

    <form class="targetPathForm" method="post" :action="getRoute(offer.categorySlug, offer.nameSlug, offer.id)" :data-offer="offer.id">
      <input type="hidden" name="_target_path" class="_target_path" value=""/>
    </form>
  </div>
</template>

<script>
import QuoteOfferAddComponent from "../../../../components/quote-offer-add/quote-offer-add-component";
import GeneralReviewDetailComponent from "../../../../components/general-review-detail/general-review-detail-component";
import StockLabelComponent from "../../../../components/stock-label/stock-label-component"
const Routing = require('../../../../../javascript/Routing');

export default {
    name: "result-item-horizontal",
    components: {GeneralReviewDetailComponent, QuoteOfferAddComponent, StockLabelComponent},
    props: ['offer'],
    computed: {
        currency() {
            return this.offer.currency;
        }
    },
    methods: {
        getRoute(categorySlug, nameSlug, id) {
            return Routing.generate('offer_detail', {categorySlug: categorySlug, offerSlug: nameSlug, offerId: id});
        }
    }
}
</script>

<style scoped>
.product-slide {
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.badge-warning {
  color: black;
}

.card {
  border: 0;
  border-bottom: 1px solid rgba(0,0,0,0.125);
}
</style>
