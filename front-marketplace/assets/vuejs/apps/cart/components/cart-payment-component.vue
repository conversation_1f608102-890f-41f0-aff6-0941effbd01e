<template>
  <div class="my-5 mx-5 mr-md-0"><!-- root element -->

    <div class="row mb-5">
      <div class="col-12 col-lg-auto pre-payment">
        <h2 class="h5 font-weight-semi-bold">{{ $t('cart.payment.pre_payment') }}</h2>
        <template v-for="paymentMethod in paymentMethodsList">
          <div class="custom-control custom-radio text-uppercase d-flex align-items-center flex-wrap py-4" v-if="paymentMethod.type === 'CREDIT_CARD' || paymentMethod.type === 'BANK_TRANSFER'">
            <input
              type="radio"
              name="payment-mode"
              class="custom-control-input"
              :id="paymentMethod.type"
              :value="paymentMethod.type.toLowerCase()"
              @change="setPayment"
            />
            <label class="custom-control-label font-weight-bold" :for="paymentMethod.type">
              {{ paymentMethod.name }}
              <span class="pl-3" v-if="paymentMethod.type === 'CREDIT_CARD'">
                <img src="/imgs/carte_visa.svg" alt="Visa"/>
                <img src="/imgs/carte_mastercard.svg" alt="mastercard"/>
              </span>
            </label>
          </div>
        </template>
      </div><!-- .col-auto -->

      <div class="col-12 col-lg-5 mt-5 mt-lg-0">
        <template v-for="paymentMethod in paymentMethodsList">
          <div v-if="paymentMethod.type === 'TERM_PAYMENT'">
            <h2 class="h5 font-weight-semi-bold">{{ $t('cart.payment.term_payment') }}</h2>
            <div class="custom-control custom-radio text-uppercase d-flex align-items-center flex-wrap py-4" >
              <input
                type="radio"
                name="payment-mode"
                class="custom-control-input"
                :id="paymentMethod.type"
                :value="paymentMethod.type.toLowerCase()"
                @change="setPayment"
              />
              <label class="custom-control-label font-weight-bold" :for="paymentMethod.type">
                {{ paymentMethod.name }}
                <span class="pl-3" v-if="paymentMethod.type === 'CREDIT_CARD'">
                <img src="/imgs/carte_visa.svg" alt="Visa"/>
                <img src="/imgs/carte_mastercard.svg" alt="mastercard"/>
              </span>
              </label>
            </div>

            <progress-bar-component v-if="paymentMethod.currentAmount !== null && paymentMethod.threshold !== null" :current="paymentMethod.currentAmount" :max="paymentMethod.threshold" :min="0" :currency="currency"></progress-bar-component>
          </div>

        </template>
      </div><!-- .col-auto -->
    </div><!-- .row -->

    <!-- Bank Transfert Details -->
    <div class="row" v-if="paymentMethodsList.includes('BANK_TRANSFER')">
      <div class="col-auto">
        <div class="bg-light-grey-20 px-5 py-3">
          <a class="h5 font-montserrat d-flex align-items-center text-decoration-none text-black m-0" data-toggle="collapse" href="#bank-transfert">
            <i class="icon-plus text-green mr-2"></i>
            <i class="icon-minus text-green mr-2"></i>
            <h2 class="h4 mb-0">{{ $t('cart.bank_details.button') }}</h2>
          </a>
          <div class="collapse show" id="bank-transfert">
            <ul class="list-unstyled mt-4 mb-0 ml-4">
              <li class="mb-3">
                <span class="font-weight-bold">{{ $t('cart.bank_details.holder') }}</span>
                {{ bankDetails.holder }}
              </li>
              <li class="mb-3">
                <span class="font-weight-bold">{{ $t('cart.bank_details.domiciliation') }}</span>
                {{ bankDetails.domiciliation }}
              </li>
              <li class="mb-3">
                <span class="font-weight-bold">{{ $t('cart.bank_details.branch_code') }}</span>
                {{ bankDetails.branchCode }}
              </li>
              <li class="mb-3">
                <span class="font-weight-bold">{{ $t('cart.bank_details.bank_code') }}</span>
                {{ bankDetails.bankCode }}
              </li>
              <li class="mb-3">
                <span class="font-weight-bold">{{ $t('cart.bank_details.account_number') }}</span>
                {{ bankDetails.AccountNumber }}
              </li>
              <li class="mb-3">
                <span class="font-weight-bold">{{ $t('cart.bank_details.bank_key') }}</span>
                {{ bankDetails.bankKey }}
              </li>
              <li class="mb-3">
                <span class="font-weight-bold">{{ $t('cart.bank_details.iban') }}</span>
                {{bankDetails.iban }}
              </li>
              <li>
                <span class="font-weight-bold text-uppercase">{{ $t('cart.bank_details.bic') }}</span>
                {{ bankDetails.bic }}
              </li>
            </ul>
          </div>
        </div><!-- .bg-light-grey-20 -->
      </div><!-- .col-12 -->
    </div><!-- .row -->

  </div><!-- root element -->
</template>

<script>
import axios from "axios";
import ProgressBarComponent from "../../../components/progress-bar/progress-bar-component.vue";
const Routing = require('../../../../javascript/Routing');

export default {
  name: "cart-payment-component",
  components: {ProgressBarComponent},
  data: () => {
    return {
      paymentMethodsList: [],
    }
  },
  created() {
    const companyId = this.$store.state.cartStore.billingAddress.companyId;
    const total = this.$store.getters["cartStore/total"];
    axios.get(Routing.generate('api.payments.authorised', {companyId: companyId, orderAmountTTC: total}))
      .then(response => {
        this.paymentMethodsList = response.data.paymentMethods.filter(pm => pm.authorised === true);
      })
    ;
  },
  methods: {
    setPayment(e) {
      this.$store.dispatch("cartStore/setPaymentMode", {paymentMethod: e.target.value});
    }
  },
  computed: {
    bankDetails() {
      return this.$store.state.cartStore.bankDetails;
    },
    currency() {
      return this.$store.state.cartStore.currency;
    }
  }
}
</script>

<style scoped>
.custom-control-label {
  line-height: 25px;
}
</style>
