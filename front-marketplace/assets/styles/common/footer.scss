footer {
  color: black;
  font-size: $h5-font-size;

  a {
    color: inherit;

    &:hover {
        color: $green;
    }
  }

  .btn-link {
    color: $gray-900;
  }

  .sub-footer {
    color: $gray-900;
  }

  .social-icon {
    font-size: $social-icon-font-size;
  }
}

hr.divider {
  height: 2px;
  border-radius: 2px;
  width: 20%;
  background-color: $gray-900;
  margin: 2em auto;
}

//  reinsurance band
.reinsurance {

  i[class*="icon-"] {
    font-size: $h1-font-size;
    margin: 0 0 1.125rem 0;
  }

  .h6 {
    line-height: 1.4;
  }

    span, a {
        color: $black;
        font-size: $btn-font-size;
        font-weight: $font-weight-semibold;

        &:hover {
            text-decoration: none;
        }
    }
}

a.facebook:hover {
  color: #3b5998 !important;
}

a.twitter:hover {
  color: #00acee !important;
}

a.youtube:hover {
  color: #c4302b !important;
}

a.linkedin:hover {
  color: #0e76a8 !important;
}

/* ReCaptcha for newsletter */
#reCaptchaNewsletter {
    overflow: hidden;
    transition: all .3s ease-in-out;
}
@include media-breakpoint-down(sm) {
    #reCaptchaNewsletter > div {
        transform: scale(0.85);
        transform-origin: 0 0;
    }
}

