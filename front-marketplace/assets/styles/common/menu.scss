$lv1Top: 260px;
$lv1Width: 280px;
$lv1Height: 51px;
$ourSectionWidth: 420px;

.nav-link {
	&.active {
		+ .menu-lv1 {
			display: block;
            overflow: hidden;
		}
	}
}

.menu {
	&-lv1 {
		display: none;
		ul {
			list-style: none;
			padding: 0;
		}
		&-submenu {
			&-item {
				&-label {
					border-top: 1px solid getColor('light-grey-60');
					height: $lv1Height;
					display: flex;
					align-items: center;
					padding: 4px 4px 4px 25px;
					margin: 0;
					color: $dark-grey;
					&.active,
					&:active,
					&:focus,
					&:hover {
						font-weight: bold;
						box-shadow: inset 15px 0 getColor('green-20');
						text-decoration: none;
						background: getColor('light-grey-20');
					}
					&:hover {
						color: black;
					}
					&.active {
						+ .menu-lv2 {
							display: block;
						}
					}
					&-image {
						display: inline-block;
						width: 32px;
						height: 32px;
						background-size: contain;
						background-repeat: no-repeat;
						background-position: center;
						margin-right: 15px;
					}
				}
				.menu-lv2 {
					display: none;
					padding: 15px 0;

					&-container {
						list-style: none;
						margin: 0;
						padding: 0;
					}

					&-item {
						padding: 0 15px 40px 0;
					}

					&-link {
						font-weight: bold;
					}

					.menu-lv3 {
						&-container {
							margin: 0;
							padding: 0;
							list-style: none;
						}
					}
				}
			}
		}
		&-item {
			&-link {
				font-weight: bold;
				text-transform: uppercase;
				color: $green;
				padding: 5px;
				border-top: 1px solid $ice;
			}
		}

		.our-selection-section {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
			width: $ourSectionWidth;
			padding: 2rem 0 0 0;
			background: getColor('light-grey-50');
            overflow: auto;

			.selection-container, .selection-title-container {
				width: 330px;
				margin: auto;
			}

			.selection-product {
				width: 100%;
				padding: 4px 15px;
				background: #fff;
				margin: .5rem 0;
				display: flex;
				justify-content: space-between;
				align-items: center;

				&:hover {
					text-decoration: none;
				}


				&-text {
					&:hover {
						font-weight: 600;
						color: $black;
					}
				}

				&-image {
					width: 40px;
                    height: 40px;
                    object-fit: contain;
				}
			}

            .our-selection-highlight {
                display: grid;
                grid-gap: 10px;
                grid-template-areas:
                    'a b'
                    'c c'
                ;

                & > a {
                    display: block;
                    margin: 0;
                    padding: 0;
                    height: 120px;
                    background-color: white;

                    & > img {
                        display: block;
                        padding: 10px;
                        width: 100%;
                        height: inherit;
                        object-fit: contain;
                    }
                }
            }
            .our-selection-highlight a:nth-of-type(1) {
                grid-area: a;
            }
            .our-selection-highlight a:nth-of-type(2) {
                grid-area: b;
            }
            .our-selection-highlight a:nth-of-type(3) {
                grid-area: c;
            }
		}
	}
}
.navbar-nav .nav-item {
    border-bottom: 1px solid black;
}

@include media-breakpoint-down(md) {
	.navbar-nav {
		.nav-link {
			padding-left: 48px;
			padding-right: 10px;
			position: relative;
			i {
				width: 35px;
			}
			&.has-children::after,
			&.has-children::before {
				position: absolute;
				content: '';
				display: block;
				background: $green;
				top: 50%;
				height: 2px;
				width: 22px;
				left: 13px;
				margin-top: -1px;
				transform-origin: center;
			}
			&.has-children::after {
				transform: rotateZ(90deg);
			}
			&.has-children.active::after {
				transform: rotateZ(0deg);
			}
		}
	}

	.menu-lv1 {
		.close-btn, .our-selection-section {
			display: none;
		}

		&-submenu {
			&-item {
                width: 100%;
				padding-left: 30px;
				&-label {
					position: relative;
					&::before {
						content: '';
						position: absolute;
						width: 0;
						height: 0;
						border-style: solid;
						border-width: 0 6px 6px 6px;
						border-color: transparent transparent white transparent;
						transform-origin: center;
						transform: translateY(-50%) rotateZ(180deg);
						filter: drop-shadow(0px -2px 0px $blue);
						top: 50%;
						left: -20px;
					}
					&.active {
						&::before {
							transform: translateY(-50%) rotateZ(0deg);
						}
					}
				}
			}
		}
	}

	.menu-lv2 {
		width: 100%;
	}
}

@include media-breakpoint-up(lg) {
    .main-nav {
		border-bottom: 1px solid getColor('light-grey-60');

        .nav-item {
            border-bottom: none;
        }
	}
	.menu {
		&-lv1 {
			position: absolute;
			right: 0;
			left: 0;
			background: white;
			padding: 0;
			margin: 0;
            height: calc(100vh - #{$lv1Top});
            border-top: 1px solid getColor('light-grey-50');
            border-bottom: 1px solid getColor('light-grey-50');
            box-shadow: 0 12px 12px 0 rgba(0,0,0,.3);

			.close-btn {
				position: absolute;
				top: 10px;
				right: 30px;
				z-index: 999;
				font-size: 11px;
				text-transform: uppercase;
				padding: 4px;
				min-height: 11px;
				background: transparent;
				&:hover {
					color: $green !important;
				}

				.i-close {
					padding-right: 5px;
					font-size: 20px;
					height: 11px;
					overflow: hidden;
					line-height: 11px;
				}
			}

			&-submenu {
				//width: calc(100% - #{$ourSectionWidth});
				box-sizing: border-box;
				position: relative;
				height: inherit;
				list-style: none;
				padding: 0;
				margin: 0;
                overflow-y: auto;

				&-item {
                    width: $lv1Width;

					.menu-lv2 {
						position: absolute;
						top: 25px;
						left: $lv1Width;
                        right: 0;
						bottom: 0;
						padding: 0 15px 15px 15px;

						&-container {
                            column-count: 2;
							column-gap: 35px;
							list-style: none;
							margin: 0;
							padding: 0;
						}

						&-item {
							break-inside: avoid;
							padding: 0 15px 40px;
						}

						&-link {
							font-weight: bold;
						}

						.menu-lv3 {
							&-container {
								margin: 0;
								padding: 0;
								list-style: none;
							}
						}

						a {
							&:hover {
								text-decoration: none;
								color: $green;
							}
						}
					}

					&:first-child {
						.menu-lv1-submenu-item-label {
							border-top: none;
						}
					}
				}
			}
		}
	}
}
@include media-breakpoint-up(xl) {
    .menu {
        &-lv1 {
            &-submenu {
                &-item {
                    .menu-lv2 {
                        &-container {
                            column-count: 4;
                            column-gap: 50px;
                        }
                    }
                }
            }
        }
    }
}


.activeAsideMenu {
    position: relative;
    &::before {
        position: absolute;
        top: 6px;
        left: -10px;
        content: '';
        width: 7px;
        height: 7px;
        border-radius: 50%;
        background-color: $green;
    }
}
