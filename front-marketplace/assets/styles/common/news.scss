.list-news {
	.card {
		border-radius: 0;
		margin-bottom: 2rem;
		.card-block {
			padding: 1rem; 
		}
	}
	&--category {
		float: left;
		color:$dark-grey;
		text-transform: uppercase;
	}
	&--date {
		float: right;
		color:$dark-grey;
		text-transform: uppercase;
	}
}

.last-news {
	background-color: rgba(213, 212, 204, 0.3);
	h3 {
		font-family: $font-family-monospace;
		margin: 0 3rem;
		&::before {
			content:"+";
			display: inline-block;
			color: $green;
			padding-right: 8px;
		}
	}
	.card {
		border-radius: 0;
		border: 0;
		background-color: transparent;
		margin: 0 1rem 0 3rem;
		&-block img {
			max-width: 70px;
		}
	}
	&--category {
		color: $dark-grey;
		text-transform: uppercase;
	}
	&--date {
		color: $dark-grey;
		text-transform: uppercase;
	}
}

@media (min-width: 576px) { 
	.list-news {
		column-count: 2;
		column-gap: 2rem;
	}
}

@include media-breakpoint-down(md) { 
	.last-news {
		h3 {
			margin: 0 1rem; 
		}
		.card {
			margin: 0 1rem; 
		}
	}
}
