framework:
    messenger:
        # Uncomment this (and the failed transport below) to send failed messages to this transport for later handling.
        # failure_transport: failed
        reset_on_message: true
        failure_transport: failed
        transports:
            # https://symfony.com/doc/current/messenger.html#transport-configuration
            async:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                retry_strategy:
                    max_retries: '%env(int:DEFAULT_MAX_RETRY)%'
            specific_price:
                dsn: '%env(MESSENGER_TRANSPORT_DSN_PRICE)%'
                retry_strategy:
                    max_retries: '%env(int:DEFAULT_MAX_RETRY)%'
            order_creation: 
                dsn: '%env(MESSENGER_TRANSPORT_DSN_ORDER_CREATION)%'
                failure_transport: order_failed
                retry_strategy:
                    max_retries: '%env(int:MAX_RETRY_ORDER_CREATION)%'
            chat:
                dsn: '%env(MESSENGER_TRANSPORT_DSN_CHAT)%'
                retry_strategy:
                    max_retries: '%env(int:DEFAULT_MAX_RETRY)%'
            shipping: 
                dsn: '%env(MESSENGER_TRANSPORT_DSN_SHIPPING)%'
                retry_strategy:
                    max_retries: '%env(int:DEFAULT_MAX_RETRY)%'
            failed: '%env(MESSENGER_TRANSPORT_DSN_FAILED)%'
            order_failed: '%env(MESSENGER_TRANSPORT_DSN_ORDER_FAILED)%'
        routing:
            # Route your messages to the transports
            'Telenco\Component\SyncAfterSale\Domain\UseCase\SyncCreation\DTO\AfterSaleSyncRequest': async
            'Marketplace\Component\Order\Domain\UseCase\SyncOrder\DTO\SyncOrderRequest': order_creation
            'Marketplace\Component\Order\Domain\UseCase\SyncOrder\DTO\SyncOrderCreationRequest': order_creation
            'Marketplace\Component\Offer\Domain\UseCase\ImportSpecificPrices\DTO\ImportSpecificPricesRequest': specific_price
            'Marketplace\Component\Order\Domain\UseCase\ExportOrder\DTO\ExportOrderRequest': order_creation
            'App\Message\SendInfosCompanyBuyerToErpUsingOrderIdRequest': async
            'Marketplace\Component\Invoice\Domain\UseCase\SyncParcel\DTO\SyncParcelRequest': shipping
            'Marketplace\Component\Invoice\Domain\UseCase\SyncInvoiceUseCase\DTO\SyncInvoiceRequest': async
            'Marketplace\Component\User\Domain\UseCase\SyncMerchantUseCase\DTO\SyncMerchantRequest': async
            'Marketplace\Component\User\Domain\UseCase\SyncMerchantInfoUseCase\DTO\SyncMerchantInfoRequest': async
            'Marketplace\Component\Payment\Domain\UseCase\Authorize\DTO\AuthorizeRequest': order_creation
            'Open\Webhelp\Model\AutomaticResponse': order_creation
            'Marketplace\Component\Order\Domain\UseCase\SendOrderInfosUseCase\DTO\SendOrderInfosRequest': async
            'Marketplace\Component\Payment\Domain\UseCase\PayMerchantOrder\DTO\PayMerchantOrderRequest': order_creation
            'Marketplace\Component\User\Infrastructure\Message\FailedImportBuyers': async
            'Marketplace\Component\Order\Domain\UseCase\UpdateMerchantOrderShippingDetailsUseCase\DTO\UpdateMerchantOrderShippingDetailsRequest': shipping
            'Marketplace\Component\Order\Domain\UseCase\SendCheckoutEmail\DTO\SendCheckoutEmailRequest': async
            'App\Model\Message\AutoConfirmRequest': order_creation
            'Marketplace\Component\Invoice\Domain\UseCase\ParcelStatusChanged\DTO\ParcelStatusChangedRequest': shipping
            'Marketplace\Component\Discussion\Domain\UseCase\NewMessageUseCase\DTO\NewMessageRequest': chat
            'Marketplace\Component\Payment\Domain\UseCase\CancelMerchantOrder\DTO\CancelMerchantOrderRequest': order_creation
            'App\Model\Order\SendOrderToErpRequest': order_creation
            'App\Model\Order\ExportOrderToErpRequest': order_creation
            'Marketplace\Component\Payment\Domain\Event\OrderCancelledEvent': order_creation
            'Marketplace\Component\Payment\Domain\Event\OrderWaitCancelEvent': order_creation
            'Marketplace\Component\Invoice\Domain\UseCase\SyncCreditNote\DTO\SyncCreditNoteRequest': order_creation
            'Marketplace\Component\Invoice\Domain\UseCase\ExportInvoice\DTO\ExportInvoiceRequest': async
