<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20211125160932 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE after_sales ADD order_id INT DEFAULT NULL, ADD status VARCHAR(50) NOT NULL, ADD created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', ADD modified_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE after_sales ADD CONSTRAINT FK_38EC4C5C8D9F6D38 FOREIGN KEY (order_id) REFERENCES orders (id)');
        $this->addSql('CREATE INDEX IDX_38EC4C5C8D9F6D38 ON after_sales (order_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE after_sales DROP FOREIGN KEY FK_38EC4C5C8D9F6D38');
        $this->addSql('DROP INDEX IDX_38EC4C5C8D9F6D38 ON after_sales');
        $this->addSql('ALTER TABLE after_sales DROP order_id, DROP status, DROP created_at, DROP modified_at');
    }

    public function isTransactional(): bool
    {
        return false;
    }
}
