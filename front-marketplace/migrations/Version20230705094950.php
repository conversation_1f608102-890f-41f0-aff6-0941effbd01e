<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230705094950 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE widget_carousel (id INT NOT NULL, discount_offer_id LONGTEXT NOT NULL COMMENT \'(DC2Type:simple_array)\', news_offer_id LONGTEXT NOT NULL COMMENT \'(DC2Type:simple_array)\', best_sells_offer_id LONGTEXT NOT NULL COMMENT \'(DC2Type:simple_array)\', created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE widget_carousel ADD CONSTRAINT FK_F1BA9A21BF396750 FOREIGN KEY (id) REFERENCES widget_contents (id) ON DELETE CASCADE');
            }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE widget_carousel');

    }

    public function isTransactional(): bool
    {
        return false;
    }
}
