<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220221202034 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE merchant_orders ADD amount_ttc DOUBLE PRECISION DEFAULT NULL');
        $this->addSql('ALTER TABLE order_items ADD status VARCHAR(50) DEFAULT NULL');
        $this->addSql('ALTER TABLE merchant_orders ADD status VARCHAR(50) DEFAULT NULL');
        $this->addSql('DROP TABLE reconciliation_action');
        $this->addSql('DROP TABLE invoice_action');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE merchant_orders DROP amount_ttc');
        $this->addSql('ALTER TABLE order_items DROP status');
        $this->addSql('ALTER TABLE merchant_orders DROP status');
        $this->addSql('CREATE TABLE invoice_action (invoice_id INT NOT NULL, merchant_id INT NOT NULL, payment_type VARCHAR(255) NOT NULL, psp_customer_id VARCHAR(255) NOT NULL, status VARCHAR(255) NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', modified_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', PRIMARY KEY(invoice_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE reconciliation_action (id INT NOT NULL, status VARCHAR(255) NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', modified_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }

    /**
     * @inheritDoc
     */
    public function isTransactional(): bool
    {
        return false;
    }
}
