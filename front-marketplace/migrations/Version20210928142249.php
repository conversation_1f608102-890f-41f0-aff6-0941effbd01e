<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20210928142249 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE cart ADD shipping_address_id INT DEFAULT NULL, ADD billing_address_id INT DEFAULT NULL, ADD status VARCHAR(50) NOT NULL');
        $this->addSql('ALTER TABLE cart ADD CONSTRAINT FK_BA388B74D4CFF2B FOREIGN KEY (shipping_address_id) REFERENCES address (id)');
        $this->addSql('ALTER TABLE cart ADD CONSTRAINT FK_BA388B779D0C0E4 FOREIGN KEY (billing_address_id) REFERENCES address (id)');
        $this->addSql('CREATE INDEX IDX_BA388B74D4CFF2B ON cart (shipping_address_id)');
        $this->addSql('CREATE INDEX IDX_BA388B779D0C0E4 ON cart (billing_address_id)');
        $this->addSql('ALTER TABLE orders CHANGE status status VARCHAR(50) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE cart DROP FOREIGN KEY FK_BA388B74D4CFF2B');
        $this->addSql('ALTER TABLE cart DROP FOREIGN KEY FK_BA388B779D0C0E4');
        $this->addSql('DROP INDEX IDX_BA388B74D4CFF2B ON cart');
        $this->addSql('DROP INDEX IDX_BA388B779D0C0E4 ON cart');
        $this->addSql('ALTER TABLE cart DROP shipping_address_id, DROP billing_address_id, DROP status');
        $this->addSql('ALTER TABLE orders CHANGE status status VARCHAR(50) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`');
    }

    /**
     * @inheritDoc
     */
    public function isTransactional(): bool
    {
        return false;
    }
}
