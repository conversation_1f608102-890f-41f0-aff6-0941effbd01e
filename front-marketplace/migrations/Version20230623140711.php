<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230623140711 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE credit_note (id INT NOT NULL, merchant_order_id INT NOT NULL, number_id VARCHAR(255) NOT NULL, total_amount_with_taxes DOUBLE PRECISION NOT NULL, status VARCHAR(255) NOT NULL, pdf_url LONGTEXT DEFAULT NULL, payment_status VARCHAR(255) DEFAULT \'not_paid\' NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
         $this->addSql('DROP TABLE credit_note');
    }

    public function isTransactional(): bool
    {
        return false;
    }
}
