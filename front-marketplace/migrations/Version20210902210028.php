<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210902210028 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE cart ADD region VARCHAR(255) NOT NULL');
        $this->addSql('ALTER TABLE cart_merchant DROP FOREIGN KEY FK_EA346A8A1AD5CDBF');
        $this->addSql('ALTER TABLE cart CHANGE id id INT NOT NULL');
        $this->addSql('ALTER TABLE company DROP FOREIGN KEY FK_4FBF094F633A1CF5');
        $this->addSql('DROP INDEX UNIQ_4FBF094F633A1CF5 ON company');
        $this->addSql('ALTER TABLE company DROP company_access_token_id');
        $this->addSql('ALTER TABLE company_access_token CHANGE izberg_bearer_expiration_date izberg_bearer_expiration_date DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE cart_item DROP FOREIGN KEY FK_F0FE252734452C14');
        $this->addSql('DROP INDEX IDX_F0FE252734452C14 ON cart_item');
        $this->addSql('ALTER TABLE cart_item ADD cart_id INT DEFAULT NULL, DROP cart_merchant_id, DROP sku, DROP name, DROP imageUrl, DROP unit_price, DROP tax_rate, CHANGE id id INT NOT NULL');
        $this->addSql('ALTER TABLE cart_item ADD CONSTRAINT FK_F0FE25271AD5CDBF FOREIGN KEY (cart_id) REFERENCES cart (id)');
        $this->addSql('CREATE INDEX IDX_F0FE25271AD5CDBF ON cart_item (cart_id)');
        $this->addSql('DROP TABLE cart_merchant');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE cart DROP region');
        $this->addSql('ALTER TABLE cart_merchant ADD CONSTRAINT FK_EA346A8A1AD5CDBF FOREIGN KEY (cart_id) REFERENCES cart (id)');
        $this->addSql('ALTER TABLE cart CHANGE id id INT AUTO_INCREMENT NOT NULL');
        $this->addSql('ALTER TABLE company ADD company_access_token_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE company ADD CONSTRAINT FK_4FBF094F633A1CF5 FOREIGN KEY (company_access_token_id) REFERENCES company_access_token (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_4FBF094F633A1CF5 ON company (company_access_token_id)');
        $this->addSql('ALTER TABLE company_access_token CHANGE izberg_bearer_expiration_date izberg_bearer_expiration_date DATE NOT NULL COMMENT \'(DC2Type:date_immutable)\'');
        $this->addSql('ALTER TABLE cart_item DROP FOREIGN KEY FK_F0FE25271AD5CDBF');
        $this->addSql('DROP INDEX IDX_F0FE25271AD5CDBF ON cart_item');
        $this->addSql('ALTER TABLE cart_item ADD cart_merchant_id INT NOT NULL, ADD sku VARCHAR(50) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, ADD name VARCHAR(50) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, ADD imageUrl VARCHAR(50) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, ADD unit_price DOUBLE PRECISION NOT NULL, ADD tax_rate DOUBLE PRECISION NOT NULL, DROP cart_id, CHANGE id id INT AUTO_INCREMENT NOT NULL');
        $this->addSql('ALTER TABLE cart_item ADD CONSTRAINT FK_F0FE252734452C14 FOREIGN KEY (cart_merchant_id) REFERENCES cart_merchant (id)');
        $this->addSql('CREATE INDEX IDX_F0FE252734452C14 ON cart_item (cart_merchant_id)');
        $this->addSql('CREATE TABLE cart_merchant (id INT AUTO_INCREMENT NOT NULL, cart_id INT NOT NULL, name VARCHAR(50) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, INDEX IDX_EA346A8A1AD5CDBF (cart_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
    }

    /**
     * @inheritDoc
     */
    public function isTransactional(): bool
    {
        return false;
    }
}
