<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210917115512 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE shipping_group (id INT NOT NULL, name VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE address CHANGE `default` by_default TINYINT(1) NOT NULL');
        $this->addSql('ALTER TABLE company_access_token ADD company_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE company_access_token ADD CONSTRAINT FK_486F17A6979B1AD6 FOREIGN KEY (company_id) REFERENCES company (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_486F17A6979B1AD6 ON company_access_token (company_id)');
        $this->addSql('ALTER TABLE email CHANGE created_at created_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', CHANGE updated_at updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE shipping_group');
        $this->addSql('ALTER TABLE address CHANGE by_default `default` TINYINT(1) NOT NULL');
        $this->addSql('ALTER TABLE company_access_token DROP FOREIGN KEY FK_486F17A6979B1AD6');
        $this->addSql('DROP INDEX UNIQ_486F17A6979B1AD6 ON company_access_token');
        $this->addSql('ALTER TABLE company_access_token DROP company_id');
        $this->addSql('ALTER TABLE email CHANGE created_at created_at DATE DEFAULT NULL COMMENT \'(DC2Type:date_immutable)\', CHANGE updated_at updated_at DATE DEFAULT NULL COMMENT \'(DC2Type:date_immutable)\'');
    }

    /**
     * @inheritDoc
     */
    public function isTransactional(): bool
    {
        return false;
    }
}
