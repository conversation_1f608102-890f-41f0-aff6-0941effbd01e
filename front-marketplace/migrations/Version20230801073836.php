<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230801073836 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create Widget Triple Promos table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE widget_triple_promos (id INT NOT NULL, promo1_id INT DEFAULT NULL, promo2_id INT DEFAULT NULL, promo3_id INT DEFAULT NULL, INDEX IDX_8D11F22AFBBE0264 (promo1_id), INDEX IDX_8D11F22AE90BAD8A (promo2_id), INDEX IDX_8D11F22A51B7CAEF (promo3_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE widget_triple_promos ADD CONSTRAINT FK_8D11F22AFBBE0264 FOREIGN KEY (promo1_id) REFERENCES widget_promo_item (id)');
        $this->addSql('ALTER TABLE widget_triple_promos ADD CONSTRAINT FK_8D11F22AE90BAD8A FOREIGN KEY (promo2_id) REFERENCES widget_promo_item (id)');
        $this->addSql('ALTER TABLE widget_triple_promos ADD CONSTRAINT FK_8D11F22A51B7CAEF FOREIGN KEY (promo3_id) REFERENCES widget_promo_item (id)');
        $this->addSql('ALTER TABLE widget_triple_promos ADD CONSTRAINT FK_8D11F22ABF396750 FOREIGN KEY (id) REFERENCES widget_contents (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE credit_note CHANGE payment_status payment_status VARCHAR(255) DEFAULT \'not_paid\' NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE widget_triple_promos');
    }

    public function isTransactional(): bool
    {
        return false;
    }
}
