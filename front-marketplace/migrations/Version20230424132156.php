<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230424132156 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add country AfterSalesContacts';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE after_sales_contacts ADD country_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE after_sales_contacts ADD CONSTRAINT FK_DBFD7C70F92F3E70 FOREIGN KEY (country_id) REFERENCES country (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE after_sales_contacts DROP FOREIGN KEY FK_DBFD7C70F92F3E70');
        $this->addSql('DROP INDEX IDX_DBFD7C70F92F3E70 ON after_sales_contacts');
        $this->addSql('ALTER TABLE after_sales_contacts DROP country_id');
    }

    public function isTransactional(): bool
    {
        return false;
    }
}
