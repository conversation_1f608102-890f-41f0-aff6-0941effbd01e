<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230711142058 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE widget_home_banner (id INT NOT NULL, image01 VARCHAR(255) NOT NULL, url01 VARCHAR(255) NOT NULL, image02 VARCHAR(255) NOT NULL, url02 VARCHAR(255) NOT NULL, image03 VARCHAR(255) NOT NULL, url03 VARCHAR(255) NOT NULL, image04 VARCHAR(255) NOT NULL, url04 VARCHAR(255) NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE widget_home_banner ADD CONSTRAINT FK_A1835BD6BF396750 FOREIGN KEY (id) REFERENCES widget_contents (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE widget_home_banner');
    }
}
