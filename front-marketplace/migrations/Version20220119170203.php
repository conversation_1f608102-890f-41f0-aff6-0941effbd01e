<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220119170203 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quote_item_offer ADD sku VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE quote ADD mkp_owner_quote_id VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quote_item_offer DROP sku');
        $this->addSql('ALTER TABLE quote DROP mkp_owner_quote_id');
    }
    public function isTransactional(): bool
    {
        return false;
    }
}
