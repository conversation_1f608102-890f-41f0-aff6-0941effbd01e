<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20211222094026 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quote_item ADD commentary VARCHAR(255) DEFAULT NULL, ADD sku VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE quote ADD expiration_date DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE quote ADD day_before_expiration INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quote_item DROP commentary, DROP sku');
        $this->addSql('ALTER TABLE quote DROP expiration_date');
        $this->addSql('ALTER TABLE quote DROP day_before_expiration');
    }

    public function isTransactional(): bool
    {
        return false;
    }
}
