<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220217095140 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add buyer unread after sale';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE after_sales ADD buyer_unread TINYINT(1) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE after_sales DROP buyer_unread');
    }

    /**
     * @inheritDoc
     */
    public function isTransactional(): bool
    {
        return false;
    }
}
