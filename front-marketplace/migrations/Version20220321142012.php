<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220321142012 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create CategoryLvl3 table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE category_lvl3 (id INT AUTO_INCREMENT NOT NULL, izberg_id INT NOT NULL, description VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE category_lvl3');
    }

    /**
     * @inheritDoc
     */
    public function isTransactional(): bool
    {
        return false;
    }
}
