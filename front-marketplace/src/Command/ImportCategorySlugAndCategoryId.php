<?php

namespace App\Command;

use Marketplace\Component\CleanArchiCore\Utils\Str;
use Open\Izberg\Model\Category;
use Open\Izberg\Service\CategoryService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Telenco\Component\Category\Domain\Model\CategorySlug;
use Telenco\Component\Category\Domain\Port\Repository\CategoryDescriptionRepositoryInterface;
use Telenco\Component\Category\Domain\Port\Repository\CategorySlugRepositoryInterface;

final class ImportCategorySlugAndCategoryId extends Command
{
    private const DATE_FORMAT = "d-m-Y G:i:s";
    public const LEVEL2 = "lvl2";
    public const LEVEL3 = "lvl3";

    public function __construct(
        private readonly array $supportedLocales,
        private readonly CategorySlugRepositoryInterface $categorySlugRepository,
        private readonly CategoryService $categoryService,
        private readonly CategoryDescriptionRepositoryInterface $categoryDescriptionRepository,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('open:import:category_slug_and_category_id')
            ->setDescription('Import categories id and categories slug');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $now = new \DateTime();
        $output->writeln('<comment>Start import categories description and categories Slug : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');


        // Category Slug
        $numberCategoriesImported = 1;
        $locales = $this->supportedLocales;

        if ($this->categorySlugRepository->isTableNotEmpty()) {
            $this->categorySlugRepository->truncate();
        }

        $categories = [];
        foreach ($locales as $locale) {
            $categories[$locale] = $this->categoryService->getFullHierarchy($locale);
            /** @var Category $category */
            foreach ($categories[$locale] as $category) {
                foreach ($category->getHydratedChildren() as $lvl2) {
                    $slug = Str::slugify($lvl2->getName());
                    if ($this->categorySlugRepository->isSlugExist($slug)) {
                        continue;
                    }
                    $slugCategory = new CategorySlug();
                    $slugCategory->setIzbergId($lvl2->getId())
                        ->setSlug($slug)
                        ->setLanguage($locale);
                    $this->categorySlugRepository->save($slugCategory);
                    $numberCategoriesImported++;

                    foreach ($lvl2->getHydratedChildren() as $lvl3) {
                        $slug = Str::slugify($lvl3->getName());
                        if ($this->categorySlugRepository->isSlugExist($slug)) {
                            continue;
                        }
                        $slugCategory = new CategorySlug();
                        $slugCategory->setIzbergId($lvl3->getId())
                            ->setSlug($slug)
                            ->setLanguage($locale);
                        $this->categorySlugRepository->save($slugCategory);
                        $numberCategoriesImported++;
                    }
                }
            }
        }


        // Category Level 3
        $numberCategoriesLevel3Imported = 0;
        $numberCategoriesLevel2Imported = 0;

        if ($this->categoryDescriptionRepository->isTableNotEmpty()) {
            $this->categoryDescriptionRepository->truncate();
        }

        $allCategories = $this->categoryService->getFullHierarchy();
        foreach ($allCategories as $category) {
            foreach ($category->getHydratedChildren() as $lvl2) {
                $isSaved = $this->categoryDescriptionRepository->save($lvl2, self::LEVEL2);
                if ($isSaved) {
                    $numberCategoriesLevel2Imported++;
                }

                foreach ($lvl2->getHydratedChildren() as $lvl3) {
                    $isSaved = $this->categoryDescriptionRepository->save($lvl3, self::LEVEL3);
                    if ($isSaved) {
                        $numberCategoriesLevel3Imported++;
                    }
                }
            }
        }

        $now = new \DateTime();
        $output->writeln("\n" . '<comment>Categories slug imported : ' . $numberCategoriesImported . ' ---</comment>');
        $output->writeln("\n" . '<comment>Categories Level 2 imported : ' . $numberCategoriesLevel2Imported . ' ---</comment>');
        $output->writeln("\n" . '<comment>Categories Level 3 imported : ' . $numberCategoriesLevel3Imported . ' ---</comment>');
        $output->writeln("\n" . '<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>' . "\n");

        return 0;
    }
}
