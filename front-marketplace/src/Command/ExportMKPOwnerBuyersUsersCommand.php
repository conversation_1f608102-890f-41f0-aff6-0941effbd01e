<?php

declare(strict_types=1);

namespace App\Command;

use DateTime;
use DateTimeImmutable;
use Marketplace\Component\User\Domain\UseCase\ExportMKPOwnerBuyersUsers\DTO\ExportMKPOwnerBuyersUsersRequest;
use Marketplace\Component\User\Domain\UseCase\ExportMKPOwnerBuyersUsers\ExportMKPOwnerBuyersUsersUseCase;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Marketplace\Component\User\Domain\Presenter\ExportMKPOwnerBuyersUsersPresenterInterface;

class ExportMKPOwnerBuyersUsersCommand extends Command
{
    private const DATE_FORMAT = "d-m-Y H:i:s";

    protected static $defaultName = 'export:mkpownerbuyers:users';

    public function __construct(
        private ExportMKPOwnerBuyersUsersUseCase $useCase,
        private ExportMKPOwnerBuyersUsersPresenterInterface $presenter,
        private string $lastExportUserFile
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $now = new DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        $dateTimeLastExportUser = null;
        if (file_exists($this->lastExportUserFile)) {
            $dateTimeLastExportUser = file_get_contents($this->lastExportUserFile);
        }
        $request = new ExportMKPOwnerBuyersUsersRequest($dateTimeLastExportUser);
        $this->useCase->execute($request, $this->presenter);
        file_put_contents($this->lastExportUserFile, (new DateTimeImmutable())->format('Y-m-d H:i:s'));

        $now = new DateTime();
        $output->writeln("\n" . '<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        return 0;
    }
}
