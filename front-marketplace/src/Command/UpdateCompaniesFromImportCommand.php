<?php

declare(strict_types=1);

namespace App\Command;

use Marketplace\Component\User\Domain\UseCase\UpdateCompaniesFromExport\UpdateCompaniesFromImportUseCase;
use Marketplace\Component\User\Presentation\Presenter\UpdateCompaniesFromImportPresenter;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class UpdateCompaniesFromImportCommand extends Command
{
    protected static $defaultName = 'open:import:companies';

    public function __construct(
        private UpdateCompaniesFromImportUseCase $useCase,
        private UpdateCompaniesFromImportPresenter $presenter,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setDescription('Update companies from import');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->useCase->execute($this->presenter);

        if ($this->presenter->viewModel()->noData === true) {
            $output->writeln('<comment>No data---</comment>');
            return Command::SUCCESS;
        }

        $output->writeln('<comment>Update companies is done<comment>');

        return Command::SUCCESS;
    }
}
