<?php

namespace App\Command\Expiration;

use Marketplace\Component\Offer\Domain\Presenter\ExpireSpecificPricesPresenterInterface;
use Marketplace\Component\Offer\Domain\UseCase\ExpireSpecificPrices\ExpireSpecificPricesUseCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class SpecificPriceExpirationCommand extends Command
{
    protected static $defaultName = 'open:specific_price:expiration';

    public function __construct(
        private ExpireSpecificPricesUseCase $useCase,
        private ExpireSpecificPricesPresenterInterface $presenter,
        private LoggerInterface $logger
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setDescription('expire specific price');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->useCase->execute($this->presenter);

        $response = $this->presenter->getResponse();

        $output->writeln(sprintf('<comment>expired:%d</comment>', count($response->expiredSpecificPrice)));
        foreach ($response->expiredSpecificPrice as $expired) {
            $message = sprintf(
                "merchant:%s, sku:%s, vat:%s",
                $expired->getMerchant()->getCompanyName() ?? "",
                $expired->getSku(),
                $expired->getVatNumber()
            );
            $output->writeln(sprintf("<info>%s</info>", $message));
            $this->logger->info("[SPECIFIC_PRICE][EXPIRATION] expired: " . $message);
        }
        return Command::SUCCESS;
    }
}
