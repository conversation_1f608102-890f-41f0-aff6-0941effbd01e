<?php

namespace App\Command\Migration;

use Marketplace\Component\Offer\Domain\Model\Offer;
use Marketplace\Component\Offer\Infrastructure\Adapter\Elasticsearch\OfferRepositoryElasticsearch;
use Marketplace\Component\Quote\Infrastructure\Adapter\Repository\QuoteItemOfferRepository;
use Marketplace\Component\Quote\Infrastructure\Entity\QuoteItemOffer;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class MigrateQuoteItemVatCommand extends Command implements LoggerAwareInterface
{
    //ONE SHOT FOR TLC-1960, update vatGroupName for quoteItem
    protected static $defaultName = 'open:migrate:quote-item';
    private LoggerInterface $logger;

    public function __construct(
        private QuoteItemOfferRepository $repository,
        private OfferRepositoryElasticsearch $offerRepository,
        string $name = null
    ) {
        parent::__construct($name);
    }

    protected function configure(): void
    {
        $this->setDescription('Migrate a quote item vat group name');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<comment>Update quote item start</comment>');
        $page = 1;
        $nb = 0;
        do {
            $itemPresence = false;
            $paginatedResult = $this->repository->findAllVatGroupNameNullPaginated($page, $pageSize = 10);

            /** @var QuoteItemOffer $quoteItem */
            foreach ($paginatedResult as $quoteItem) {
                $quoteItem = $this->updateQuoteItem($quoteItem);
                $this->repository->persist($quoteItem);
                $itemPresence = true;
                $nb++;
            }
            $this->repository->flush();
            $page++;
        } while ($itemPresence);

        $output->writeln('<comment>Update quote item stop</comment>');
        return self::SUCCESS;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    private function updateQuoteItem(QuoteItemOffer $quoteItemOffer): QuoteItemOffer
    {
        $offer = $this->offerRepository->find($quoteItemOffer->getOfferId());
        if (!$offer instanceof Offer) {
            return $quoteItemOffer;
        }

        $quoteItemOffer->setVatGroupName($offer->getTaxGroupName());
        return $quoteItemOffer;
    }
}
