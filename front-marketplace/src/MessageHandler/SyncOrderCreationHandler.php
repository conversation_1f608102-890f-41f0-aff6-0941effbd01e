<?php

namespace App\MessageHandler;

use App\Message\SendInfosCompanyBuyerToErpUsingOrderIdRequest;
use App\Model\Message\UpdateMerchantOrderCustomRequest;
use Marketplace\Component\Cart\Infrastructure\Adapter\Repository\CartRepository;
use Marketplace\Component\Cart\Infrastructure\Entity\Cart;
use Marketplace\Component\Order\Domain\UseCase\SyncOrder\DTO\SyncOrderCreationRequest;
use Marketplace\Component\Order\Domain\UseCase\SyncOrder\DTO\SyncOrderRequest;
use Marketplace\Component\Order\Domain\UseCase\SyncOrder\SyncOrderUseCase;
use Marketplace\Component\Order\Domain\UseCase\UpdateMerchantOrderShippingDetailsUseCase\DTO\UpdateMerchantOrderShippingDetailsRequest;
use Marketplace\Component\Order\Presentation\Presenter\SyncOrderPresenter;
use Marketplace\Component\Order\Presentation\View\SyncOrderView;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class SyncOrderCreationHandler implements MessageHandlerInterface
{
    public function __construct(
        private SyncOrderView $view,
        private SyncOrderUseCase $useCase,
        private SyncOrderPresenter $presenter,
        private MessageBusInterface $messageBus,
        private CartRepository $cartRepository
    ) {
    }

    public function __invoke(SyncOrderCreationRequest $syncOrderRequest): Response
    {
        $this->useCase->execute(new SyncOrderRequest($syncOrderRequest->orderId), $this->presenter);

        $this->messageBus->dispatch(
            new SendInfosCompanyBuyerToErpUsingOrderIdRequest(
                $syncOrderRequest->merchantId,
                $syncOrderRequest->orderId
            )
        );

        $this->messageBus->dispatch(new UpdateMerchantOrderCustomRequest($syncOrderRequest->merchantOrderId));

        $cart = $this->cartRepository->find($syncOrderRequest->cartId);
        if ($cart instanceof Cart) {
            $request = new UpdateMerchantOrderShippingDetailsRequest(
                $syncOrderRequest->merchantOrderId,
                $cart->getContactName(),
                $cart->getContactPhone(),
                $cart->getOrderNumber(),
                $cart->getShippingComment(),
                $cart->isEarlyDelivery()
            );

            $this->messageBus->dispatch($request);
        }

        return $this->view->generateView($this->presenter->viewModel());
    }
}
