<?php

namespace App\MessageHandler;

use Marketplace\Component\User\Domain\UseCase\Address\EditAddress\DTO\EditShippingAddressRequest;
use Marketplace\Component\User\Domain\UseCase\Address\EditAddress\EditShippingAddressUseCase;
use Marketplace\Component\User\Presentation\Presenter\Address\EditBillingAddressPresenter;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;

class ChangeCompanyNameShipping<PERSON>andler implements MessageHandlerInterface
{
    public function __construct(private EditShippingAddressUseCase $useCase, private EditBillingAddressPresenter $presenter)
    {
    }

    public function __invoke(EditShippingAddressRequest $request)
    {
        $this->useCase->execute($request, $this->presenter);
    }
}
