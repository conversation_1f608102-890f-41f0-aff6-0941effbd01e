<?php

declare(strict_types=1);

namespace App\MessageHandler;

use Marketplace\Component\Order\Domain\Presenter\ExportOrderPresenterInterface;
use Marketplace\Component\Order\Domain\UseCase\ExportOrder\DTO\ExportOrderRequest;
use Marketplace\Component\Order\Domain\UseCase\ExportOrder\ExportOrderUseCase;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;

final class ExportOrderHandler implements MessageHandlerInterface
{
    public function __construct(
        private ExportOrderUseCase $useCase,
        private ExportOrderPresenterInterface $presenter,
    ) {
    }

    public function __invoke(ExportOrderRequest $exportOrderRequest): void
    {
        $this->useCase->execute($exportOrderRequest, $this->presenter);
    }
}
