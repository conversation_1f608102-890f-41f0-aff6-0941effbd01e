<?php

namespace App\MessageHandler;

use Marketplace\Component\User\Domain\UseCase\Address\EditAddress\DTO\EditBillingAddressRequest;
use Marketplace\Component\User\Domain\UseCase\Address\EditAddress\EditBillingAddressUseCase;
use Marketplace\Component\User\Presentation\Presenter\Address\EditBillingAddressPresenter;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;

class ChangeCompanyNameBillingHandler implements MessageHandlerInterface
{
    public function __construct(private EditBillingAddressUseCase $useCase, private EditBillingAddressPresenter $presenter)
    {
    }

    public function __invoke(EditBillingAddressRequest $request)
    {
        $this->useCase->execute($request, $this->presenter);
    }
}
