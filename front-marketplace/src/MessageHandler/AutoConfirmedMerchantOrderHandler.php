<?php

declare(strict_types=1);

namespace App\MessageHandler;

use App\Model\Message\AutoConfirmRequest;
use App\Service\MerchantAutoConfirmedService;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;

class AutoConfirmedMerchantOrderHandler implements MessageHandlerInterface
{
    public function __construct(private MerchantAutoConfirmedService $autoConfirmedService)
    {
    }

    public function __invoke(AutoConfirmRequest $autoConfirmRequest)
    {
        if ($autoConfirmRequest->getAction() === AutoConfirmRequest::CONFIRM) {
            $this->autoConfirmedService->confirmMerchant(
                $autoConfirmRequest->getMerchantId(),
                $autoConfirmRequest->getMerchantOrderId()
            );
        } elseif ($autoConfirmRequest->getAction() === AutoConfirmRequest::PROCESS) {
            $this->autoConfirmedService->processMerchantOrder(
                $autoConfirmRequest->getMerchantId(),
                $autoConfirmRequest->getMerchantOrderId()
            );
        }
    }
}
