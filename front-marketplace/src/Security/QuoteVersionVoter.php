<?php

namespace App\Security;

use Marketplace\Component\Quote\Infrastructure\Adapter\Repository\QuoteVersionRepository;
use Marketplace\Component\Quote\Infrastructure\Entity\QuoteVersion;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

class QuoteVersionVoter extends Voter
{
    public const EDIT = 'edit';
    public const VIEW = 'view';

    public function __construct(
        private readonly QuoteVersionRepository $quoteVersionRepository
    ) {
    }

    protected function supports(string $attribute, $subject): bool
    {
        if (!in_array($attribute, [self::VIEW, self::EDIT])) {
            return false;
        }
        if ($subject === null) {
            return true;
        }

        if (is_integer($subject)) {
            $subject = $this->quoteVersionRepository->find($subject);
        }

        if (!$subject instanceof QuoteVersion) {
            return false;
        }

        return true;
    }

    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        $currentUser = $token->getUser();
        if ($subject === null && $currentUser instanceof User) {
            return true;
        }

        $quoteVersion = $this->quoteVersionRepository->find($subject);
        if (!$quoteVersion instanceof QuoteVersion) {
            return false;
        }

        if ($currentUser instanceof User) {
            return match ($attribute) {
                self::VIEW, self::EDIT => $this->canViewOrEditBuyer($quoteVersion, $currentUser),
                default => false,
            };
        }

        return false;
    }

    private function canViewOrEditBuyer(QuoteVersion $quoteVersion, User $currentUser): bool
    {
        return $quoteVersion->getQuote()->getCompany()->getId() === $currentUser->getCompany()?->getId();
    }
}
