<?php

namespace App\Security;

use Marketplace\Component\Order\Infrastructure\Adapter\Repository\OrderRepository;
use Marketplace\Component\Order\Infrastructure\Entity\Order;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

class OrderVoter extends Voter
{
    public const EDIT = 'edit';
    public const VIEW = 'view';


    public function __construct(
        private readonly OrderRepository $orderRepository
    ) {
    }

    protected function supports(string $attribute, $subject): bool
    {
        if (!in_array($attribute, [self::VIEW, self::EDIT])) {
            return false;
        }
        if ($subject === null) {
            return true;
        }

        if (is_integer($subject)) {
            $subject = $this->orderRepository->find($subject);
        }

        if (!$subject instanceof Order) {
            return false;
        }

        return true;
    }

    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        /** @var User $currentUser */
        $currentUser = $token->getUser();
        if ($subject === null && $currentUser instanceof User) {
            return true;
        }

        $order = $this->orderRepository->find($subject);

        if (!$order instanceof Order) {
            return false;
        }

        if ($currentUser instanceof User) {
            return match ($attribute) {
                self::VIEW, self::EDIT => $this->canViewOrEdit($order, $currentUser),
                default => false,
            };
        }

        return false;
    }

    private function canViewOrEdit(Order $order, User $currentUser): bool
    {
        return $order->getCompany()?->getId() === $currentUser->getCompany()?->getId();
    }
}
