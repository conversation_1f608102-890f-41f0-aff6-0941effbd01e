<?php

namespace App\Validator\Constraints;

use Symfony\Component\Validator\Constraint;

/**
 * @Annotation
 */
class CompanyIdentification extends Constraint
{
    public string $message = 'form.company.ident_number.invalid';

    /**
     * @return string
     */
    public function getMessage(): string
    {
        return $this->message;
    }

    public function validatedBy(): string
    {
        return static::class . 'Validator';
    }
}
