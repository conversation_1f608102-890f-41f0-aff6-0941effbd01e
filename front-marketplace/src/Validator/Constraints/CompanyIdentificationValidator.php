<?php

namespace App\Validator\Constraints;

use <PERSON><PERSON><PERSON><PERSON>\Vatin\Validator;
use <PERSON>ymfony\Component\Validator\Constraint;
use S<PERSON><PERSON>ny\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use <PERSON>ymfony\Component\Validator\Exception\UnexpectedValueException;
use Symfony\Component\Validator\Exception\ValidatorException;

class CompanyIdentificationValidator extends ConstraintValidator
{
    private Validator $validator;

    /**
     * CompanyIdentificationValidator constructor.
     * @param Validator $validator
     */
    public function __construct(Validator $validator)
    {
        $this->validator = $validator;
    }


    public function validate($value, Constraint $constraint)
    {
        if (!$constraint instanceof CompanyIdentification) {
            throw new UnexpectedTypeException($constraint, CompanyIdentification::class);
        }

        // custom constraints should ignore null and empty values to allow
        // other constraints (NotBlank, NotNull, etc.) to take care of that
        if (null === $value || '' === $value) {
            return;
        }

        if (!is_string($value)) {
            // throw this exception if your validator cannot handle the passed type so that it can be marked as invalid
            throw new UnexpectedValueException($value, 'string');

            // separate multiple types using pipes
            // throw new UnexpectedValueException($value, 'string|int');
        }

        try {
            if (!$this->validator->isValid($value, true)) {
                // the argument must be a string or an object implementing __toString()
                $this->context->buildViolation($constraint->message)
                    ->setParameter('{{ string }}', $value)
                    ->addViolation();
            }
        } catch (ValidatorException $exception) {
            //we don't want to block the process here: only add a log
            // VAT could not be validated because VIES service is unreachable
        }
    }
}
