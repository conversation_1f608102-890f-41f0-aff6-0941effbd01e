<?php

declare(strict_types=1);

namespace App\Form\User;

use App\Form\RecaptchaType;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetLocaleInterface;
use Marketplace\Component\User\Domain\UseCase\BuyerRegistration\DTO\BuyerRegistrationRequest;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class BuyerRegistrationFormType extends AbstractType
{
    public function __construct(private GetLocaleInterface $getLocale)
    {
    }

    /**
     * @inheritDoc
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $locale = $this->getLocale->getLocale();
        $builder
            ->add('civility', ChoiceType::class, [
                'label' => 'registration.form.civility.label',
                'choices' => [
                    'registration.form.civility.miss' => 'Mme',
                    'registration.form.civility.mister' => 'Mr'
                ],
                'translation_domain' => 'translations',
                'expanded' => true,
                'attr' => ['class' => 'd-flex h-form'],
                'label_attr' => ['class' => 'radio-custom']
            ])
            ->add('lastname', TextType::class, [
                'label' => 'form.label.lastname',
                'attr' => ['placeholder' => 'form.label.lastname'],
                'translation_domain' => 'translations'
            ])
            ->add('firstname', TextType::class, [
                'label' => 'form.label.firstname',
                'attr' => ['placeholder' => 'form.label.firstname'],
                'translation_domain' => 'translations'
            ])
            ->add('email', EmailType::class, [
                'label' => 'form.label.email',
                'attr' => ['placeholder' => 'form.label.email'],
                'translation_domain' => 'translations'
            ])
            ->add('phone', TextType::class, [
                'label' => 'registration.form.phone_label',
                'attr' => ['placeholder' => 'registration.form.phone_placeholder'],
                'translation_domain' => 'translations'
            ])
            ->add('plainPassword', PasswordType::class, [
                'label' => 'registration.form.password',
                'attr' => ['placeholder' => 'registration.form.password', 'is' => 'password-view'],
                'translation_domain' => 'translations',
                'help' => 'form.label.password_help',
                'help_html' => true
            ])
            ->add('cg', CheckboxType::class, [
                'label' => 'registration.form.cg',
                'label_translation_parameters' => [
                    '%locale%' => $locale,
                ],
                'label_html' => true,
                'mapped' => false,
                'translation_domain' => 'translations',
                'label_attr' => ['class' => 'checkbox-custom']
            ])
            ->add('pc', CheckboxType::class, [
                'label' => 'registration.form.pc',
                'label_translation_parameters' => [
                    '%locale%' => $locale,
                ],
                'label_html' => true,
                'mapped' => false,
                'translation_domain' => 'translations',
                'label_attr' => ['class' => 'checkbox-custom']
            ])
            ->add('company', CompanyFormType::class, [
                'label' => false,
                'attr' => ['placeholder' => 'registration.form.company'],
                'translation_domain' => 'translations',
            ])
            ->add('captcha', RecaptchaType::class, ['mapped' => true])
        ;
    }

    /**
     * @inheritDoc
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefault('data_class', BuyerRegistrationRequest::class);
    }
}
