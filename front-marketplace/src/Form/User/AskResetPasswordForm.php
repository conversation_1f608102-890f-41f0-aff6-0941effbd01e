<?php

declare(strict_types=1);

namespace App\Form\User;

use Marketplace\Component\User\Domain\UseCase\AskResetPassword\DTO\AskResetPasswordRequest;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class AskResetPasswordForm extends AbstractType
{
    /**
     * @inheritDoc
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->add('email', EmailType::class, [
            'label' => 'form.reset_password.label.email',
            'attr' => ['placeholder' => '<EMAIL>'],
            'translation_domain' => 'translations'
        ]);
    }

    /**
     * @inheritDoc
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('data_class', AskResetPasswordRequest::class);
    }
}
