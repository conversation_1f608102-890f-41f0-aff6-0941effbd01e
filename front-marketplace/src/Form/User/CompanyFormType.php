<?php

declare(strict_types=1);

namespace App\Form\User;

use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\Country as DomainCountry;
use Marketplace\Component\User\Domain\Port\Repository\CountryRepositoryInterface;
use Marketplace\Component\User\Infrastructure\Entity\Country;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CompanyFormType extends AbstractType
{
    public function __construct(private CountryRepositoryInterface $countryRepository)
    {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'registration.form.company.name',
                'translation_domain' => 'translations',
                'attr' => ['placeholder' => 'registration.form.company.name'],
            ])
            ->add('selfEmployed', ChoiceType::class, [
                'label' => 'registration.form.selfEmployed',
                'label_attr' => ['class' => 'radio-custom'],
                'translation_domain' => 'translations',
                'choice_translation_domain' => true,
                'expanded' => true,
                'mapped' => false,
                'choices' => [
                    'yes' => true,
                    'no' => false
                ]
            ])
            ->add('country', ChoiceType::class, [
                'label' => 'registration.form.country',
                'placeholder' => 'registration.form.country_placeholder',
                'translation_domain' => 'translations',
                'attr' => ['class' => 'custom-select'],
                'choices' => $this->countryRepository->getCountries(['buyer' => true]),
                'choice_label' => fn(DomainCountry $country) => $country->getCode(),
            ])
            ->add(
                'vatNumber',
                TextType::class,
                [
                    'label' => 'registration.form.company.vatNumber',
                    'translation_domain' => 'translations',
                    'attr' => ['placeholder' => 'registration.form.company.vatNumber'],
                ]
            )
            ->add('identification', TextType::class, [
                'label' => 'registration.form.company.identification',
                'translation_domain' => 'translations',
                'attr' => ['placeholder' => 'registration.form.company.identification_placeholder'],
            ])
            ->add('accountingEmail', EmailType::class, [
                'label' => 'registration.form.accounting_email.label',
                'translation_domain' => 'translations',
                'attr' => ['placeholder' => 'registration.form.accounting_email.placeholder'],
            ])
            ->add('accountingPhone', TextType::class, [
                'label' => 'registration.form.accounting_phone.label',
                'attr' => ['placeholder' => 'registration.form.accounting_phone.placeholder'],
                'required' => false,
                'translation_domain' => 'translations'
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Company::class,
        ]);
    }
}
