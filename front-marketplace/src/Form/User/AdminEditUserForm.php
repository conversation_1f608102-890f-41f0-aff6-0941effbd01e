<?php

namespace App\Form\User;

use App\Form\SubmitIconType;
use Marketplace\Component\User\Domain\UseCase\User\AdminCreateUser\DTO\CreateUserRequest;
use Marketplace\Component\User\Domain\UseCase\User\AdminEditUser\DTO\AdminEditUserRequest;
use Marketplace\Component\User\Domain\UseCase\User\EditUser\DTO\EditUserRequest;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class AdminEditUserForm extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->setMethod('PUT');

        $builder
            ->add('lastName', TextType::class, [
                'label' => 'form.label.lastname',
                'attr' => ['placeholder' => 'form.label.lastname'],
            ])
            ->add('firstName', TextType::class, [
                'label' => 'form.label.firstname',
                'attr' => ['placeholder' => 'form.label.firstname'],
            ])
            ->add('role', ChoiceType::class, [
                'label' => 'form.label.roles',
                'choices' => [
                    'user_roles.ROLE_BUYER_ADMIN' => User::ROLE_ADMIN,
                    'user_roles.ROLE_BUYER_CONSULT' => User::ROLE_BUYER_CONSULT,
                    'user_roles.ROLE_BUYER_STANDARD' => User::ROLE_BUYER_STANDARD
                ],
                'attr' => ['class' => 'd-flex h-form']
            ])
            ->add('submit', SubmitIconType::class, [
                'label' => 'btn.edit',
                'icon' => 'arrow-right',
                'attr' => [
                    'class' => 'btn btn-primary',
                ]
            ])
        ;
    }

    /**
     * @inheritDoc
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => AdminEditUserRequest::class,
            'translation_domain' => 'translations',
        ]);
    }
}
