<?php

declare(strict_types=1);

namespace App\Form\User;

use Marketplace\Component\User\Domain\UseCase\ResetPassword\DTO\ResetPasswordRequest;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class ResetPasswordForm extends AbstractType
{
    /**
     * @inheritDoc
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('plainNewPassword', PasswordType::class, [
            'label' => 'resetPassword.form.label',
            'attr' => ['placeholder' => 'resetPassword.form.placeholder', 'is' => 'password-view'],
            'help' => 'form.label.password_help'
        ]);
    }

    /**
     * @inheritDoc
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => ResetPasswordRequest::class,
            'translation_domain' => 'translations'
        ]);
    }
}
