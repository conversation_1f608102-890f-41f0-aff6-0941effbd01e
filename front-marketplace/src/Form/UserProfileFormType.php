<?php

namespace App\Form;

use Marketplace\Component\User\Infrastructure\Entity\User;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserProfileFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'firstname',
                TextType::class,
                array(
                    'label' => 'form.label.firstname',
                    'attr' => array(
                        'placeholder' => 'profile.form.firstname'
                    )
                )
            )
            ->add(
                'lastname',
                TextType::class,
                array(
                    'label' => 'form.label.lastname',
                    'attr' => array(
                        'placeholder' => 'profile.form.lastname'
                    ),
                    'required' => true
                )
            )
            ->add(
                'email',
                TextType::class,
                array(
                    'label' => 'form.label.email',
                    'attr' => array(
                        'placeholder' => 'profile.form.email'
                    ),
                    'disabled' => true
                )
            );
        $builder
            ->add(
                'submit',
                SubmitType::class,
                array(
                    "attr" => array(
                        "value" => "Save",
                        "class" => "btn btn-lg btn-primary"
                    ),
                )
            )
            ->setMethod('POST');
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'data_class' => User::class,
            ]
        );
    }
}
