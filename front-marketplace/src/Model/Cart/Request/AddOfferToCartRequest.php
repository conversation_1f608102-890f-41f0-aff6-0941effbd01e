<?php

namespace App\Model\Cart\Request;

class AddOfferToCartRequest
{
    public function __construct(private int $offerId, private int $quantity, private ?int $variationId = null)
    {
    }

    public function getOfferId(): int
    {
        return $this->offerId;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    /**
     * @return int|null
     */
    public function getVariationId(): ?int
    {
        return $this->variationId;
    }
}
