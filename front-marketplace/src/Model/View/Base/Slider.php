<?php

namespace App\Model\View\Base;

class Slider
{
    private string $previousText;
    private string $nextText;
    private array $sliderItems = [];

    public function getPreviousText(): string
    {
        return $this->previousText;
    }

    public function setPreviousText(string $previousText): Slider
    {
        $this->previousText = $previousText;
        return $this;
    }

    public function getNextText(): string
    {
        return $this->nextText;
    }

    public function setNextText(string $nextText): Slider
    {
        $this->nextText = $nextText;
        return $this;
    }

    public function getSliderItems(): array
    {
        return $this->sliderItems;
    }

    public function setSliderItems(array $sliderItems): Slider
    {
        $this->sliderItems = $sliderItems;
        return $this;
    }
}
