<?php

namespace App\Model\View;

class AutocompleteItem
{
    public function __construct(private string $action = '', private string $label = '')
    {
    }

    public function getAction(): string
    {
        return $this->action;
    }

    public function setAction(string $action): AutocompleteItem
    {
        $this->action = $action;
        return $this;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function setLabel(string $label): AutocompleteItem
    {
        $this->label = $label;
        return $this;
    }
}
