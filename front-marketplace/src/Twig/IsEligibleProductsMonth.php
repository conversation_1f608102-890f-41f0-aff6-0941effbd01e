<?php

namespace App\Twig;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetRegionServiceInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

final class IsEligibleProductsMonth extends AbstractExtension
{
    public function __construct(private GetRegionServiceInterface $regionService)
    {
    }

    public function getFunctions(): array
    {
        return [
            new TwigFunction('isEligibleProductsMonth', [$this, 'isEligibleProductsMonth'])
        ];
    }

    public function isEligibleProductsMonth(): bool
    {
        $eligibleCountries = ['france', 'belgium', 'luxembourg', 'switzerland'];
        return in_array($this->regionService->getRegion(), $eligibleCountries);
    }
}
