<?php

namespace App\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

final class ConvertCurrencyExtension extends AbstractExtension
{
    public function getFunctions(): array
    {
        return [
            new TwigFunction('iconCurrency', [$this, 'convertCurrencyToIcon'])
        ];
    }

    public function convertCurrencyToIcon(?string $currency): string
    {
        return match ($currency) {
            "GBP" => "£",
            "USD" => "$",
            default => "€"
        };
    }

    //TODO : Ajouter un log en cas de currency null
}
