<?php

namespace App\ParamConverter;

use App\Security\AfterSaleVoter;
use Marketplace\Component\Discussion\Domain\UseCase\ReplyAfterSaleThread\DTO\ReplyAfterSaleThreadRequest;
use Marketplace\Component\Discussion\Infrastructure\Adapter\Repository\AfterSalesRepository;
use Marketplace\Component\Order\Infrastructure\Adapter\Repository\MerchantOrderRepository;
use Marketplace\Component\Order\Infrastructure\Entity\MerchantOrder;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Translation\Exception\NotFoundResourceException;
use Telenco\Component\SyncAfterSale\Domain\UseCase\SyncCreation\DTO\AfterSaleSyncRequest;
use Telenco\Component\SyncAfterSale\Infrastructure\Strategy\SyncAfterSaleStrategyInterface;

class ReplyAfterSaleThreadRequestParamConverter implements ParamConverterInterface
{
    public function __construct(
        private AfterSalesRepository $afterSalesRepository,
        private MerchantOrderRepository $merchantOrderRepository,
        private Security $security
    ) {
    }

    public function apply(Request $request, ParamConverter $configuration): void
    {
        /**
         * @var User $user
         */
        $user = $this->security->getUser();
        $afterSaleId = $request->attributes->getInt('afterSaleId');

        $userId = $user->getId();
        if (is_null($userId)) {
            throw new NotFoundResourceException('User not found');
        }

        $message = (string)$request->request->get('message', '');

        $files = $request->files->get('files', []);

        $afterSale = $this->afterSalesRepository->findAfterSale($afterSaleId);

        if (is_null($afterSale)) {
            throw new NotFoundResourceException('after sale not found');
        }

        if (!$this->security->isGranted(AfterSaleVoter::EDIT, $afterSale->getId())) {
            throw new AccessDeniedException();
        }
        /**
         * @var MerchantOrder $merchantOrder
         */
        $merchantOrder = $this->merchantOrderRepository->find($afterSale->getMerchantOrderId());
        $replyRequest = new ReplyAfterSaleThreadRequest($afterSaleId, $message, $files, $merchantOrder->getMerchantDistantId());


        $request->attributes->set($configuration->getName(), $replyRequest);
    }

    public function supports(ParamConverter $configuration): bool
    {
        return $configuration->getClass() === ReplyAfterSaleThreadRequest::class;
    }
}
