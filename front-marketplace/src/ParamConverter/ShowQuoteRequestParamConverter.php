<?php

declare(strict_types=1);

namespace App\ParamConverter;

use App\Security\QuoteVoter;
use Marketplace\Component\Discussion\Domain\Port\Repository\ThreadRepositoryInterface;
use Marketplace\Component\Quote\Domain\Request\ShowQuoteRequestInterface;
use Marketplace\Component\Quote\Domain\UseCase\ShowOpenQuote\DTO\ShowOpenQuoteRequest;
use Marketplace\Component\Quote\Domain\UseCase\ShowQuote\DTO\ShowQuoteRequest;
use Marketplace\Component\Quote\Infrastructure\Adapter\Repository\QuoteRepository;
use Marketplace\Component\Quote\Infrastructure\Entity\QuoteCatalogue;
use Marketplace\Component\Quote\Infrastructure\Entity\QuoteOpen;
use Marketplace\Component\Sso\Infrastructure\Entity\Vendor;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Translation\Exception\NotFoundResourceException;

class ShowQuoteRequestParamConverter implements ParamConverterInterface
{
    public function __construct(
        private QuoteRepository $quoteRepository,
        private Security $security
    ) {
    }

    public function apply(Request $request, ParamConverter $configuration): void
    {
        $user = $this->security->getUser();
        $quoteId = $request->attributes->getInt('quoteId');

        $userId = null;
        $userType = ShowQuoteRequest::USER_BUYER;
        ;
        if ($user instanceof User) {
            $userId = $user->getId();
        } elseif ($user instanceof Vendor) {
            $userId = $user->getId();
            $userType = ShowQuoteRequest::USER_MERCHANT;
        }

        if (is_null($userId)) {
            throw new NotFoundResourceException('User not found');
        }

        $quote = $this->quoteRepository->find($quoteId);
        if ($quote instanceof QuoteOpen) {
            $showRequest = new ShowOpenQuoteRequest($quoteId, $userType);
        } elseif ($quote instanceof QuoteCatalogue) {
            $showRequest = new ShowQuoteRequest($quoteId, $userType);
        } else {
            throw new NotFoundResourceException('Quote not found');
        }

        if (!$this->security->isGranted(QuoteVoter::VIEW, $quote)) {
            throw new AccessDeniedException();
        }

        $request->attributes->set($configuration->getName(), $showRequest);
    }

    public function supports(ParamConverter $configuration): bool
    {
        return $configuration->getClass() === ShowQuoteRequestInterface::class;
    }
}
