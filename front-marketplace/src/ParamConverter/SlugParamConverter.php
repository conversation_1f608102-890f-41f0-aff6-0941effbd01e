<?php

declare(strict_types=1);

namespace App\ParamConverter;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetRegionServiceInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Telenco\Component\Shared\Domain\Attribute\ShowRequest;
use Telenco\Component\Shared\Domain\Model\Region;
use Telenco\Component\Shared\Domain\Model\Slug;
use Telenco\Component\Shared\Domain\Port\Repository\RegionRepositoryInterface;
use Telenco\Component\Shared\Domain\Port\Repository\SlugRepositoryInterface;
use Telenco\Component\Shared\Domain\Request\SlugRequestInterface;

class SlugParamConverter implements ParamConverterInterface
{
    public function __construct(
        private SlugRepositoryInterface $slugRepository,
        private GetRegionServiceInterface $regionService,
        private RegionRepositoryInterface $regionRepository
    ) {
    }

    /**
     * @inheritDoc
     */
    public function apply(Request $request, ParamConverter $configuration): void
    {
        $slugName = $request->attributes->get('slug');
        $slug = $this->slugRepository->findByName($slugName);
        $region = $this->regionRepository->findByName($this->regionService->getRegion());

        if (!$region instanceof Region) {
            throw new NotFoundHttpException('Region not found');
        }

        if (!$slug instanceof Slug) {
            throw new NotFoundHttpException('Slug not found:' . $slugName);
        }

        $request->attributes->set($configuration->getName(), $this->getRequestClass($slug, $request, $region));
    }

    /**
     * @inheritDoc
     */
    public function supports(ParamConverter $configuration): bool
    {
        return $configuration->getClass() === SlugRequestInterface::class;
    }

    private function getRequestClass(Slug $slug, Request $request, Region $region): SlugRequestInterface
    {
        /** @var class-string $className */
        $className = $slug->getRef();
        $reflectionClass = new \ReflectionClass($className);
        $attributes = $reflectionClass->getAttributes(ShowRequest::class);
        if (empty($attributes)) {
            throw new NotFoundHttpException("Model don't have ShowRequest attribute");
        }
        $showRequest = array_shift($attributes);
        $showRequestInstance = $showRequest->newInstance();
        $showRequestName = $showRequestInstance->getName();
        $slugRequest = new $showRequestName($region, $request->getLocale(), $slug->getName());

        if (!$slugRequest instanceof SlugRequestInterface) {
            throw new NotFoundHttpException("Request not found");
        }
        return $slugRequest;
    }
}
