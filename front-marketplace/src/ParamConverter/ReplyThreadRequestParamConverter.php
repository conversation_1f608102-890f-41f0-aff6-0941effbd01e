<?php

namespace App\ParamConverter;

use App\Security\Exception\UserNotAuthorizedException;
use App\Security\ThreadVoter;
use Marketplace\Component\Discussion\Domain\Model\MessageDetail;
use Marketplace\Component\Discussion\Domain\Request\ReplyThreadRequestInterface;
use Marketplace\Component\Discussion\Domain\UseCase\CreateThread\DTO\CreateThreadRequest;
use Marketplace\Component\Discussion\Domain\UseCase\ReplyThread\DTO\ReplyThreadRequest;
use Marketplace\Component\Discussion\Infrastructure\Adapter\Repository\ThreadRepository;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Translation\Exception\NotFoundResourceException;

class ReplyThreadRequestParamConverter implements ParamConverterInterface
{
    public function __construct(
        private ThreadRepository $threadRepository,
        private Security $security
    ) {
    }

    public function apply(Request $request, ParamConverter $configuration): void
    {
        /**
         * @var User $user
         */
        $user = $this->security->getUser();
        $threadId = $request->attributes->getInt('threadId');

        $userId = $user->getId();
        if (is_null($userId)) {
            throw new NotFoundResourceException('User not found');
        }

        // thread will no exist yet for a first message to operator
        $message = (string)$request->request->get('message', '');
        // $threadId = $request->request->get('threadId');
        $files = $request->files->get('files', []);

        if ($threadId === 0) {
            $replyRequest = $this->operatorCase($userId, $message, $files);
        } else {
            $replyRequest = $this->normalCase($threadId, $message, $files);
        }

        $request->attributes->set($configuration->getName(), $replyRequest);
    }

    public function supports(ParamConverter $configuration): bool
    {
        return $configuration->getClass() === ReplyThreadRequestInterface::class;
    }


    private function operatorCase(int $userId, string $message, array $files): ReplyThreadRequestInterface
    {
        $thread = $this->threadRepository->findActiveOperatorThread($userId);
        if (is_null($thread)) {
            $messageDetail = (new MessageDetail())->setBody($message)->setType(MessageDetail::TYPE_OPERATOR);
            $messageDetail->setFiles($files);
            $replyRequest = new CreateThreadRequest(
                $messageDetail,
                $userId
            );
        } else {
            $replyRequest = new ReplyThreadRequest($thread->getId(), $message, $files);
        }
        return $replyRequest;
    }

    private function normalCase(int $threadId, string $message, array $files): ReplyThreadRequestInterface
    {

        $thread = $this->threadRepository->findThreadById($threadId);

        if (is_null($thread)) {
            throw new NotFoundResourceException('Thread not found');
        }

        if ($this->security->isGranted(ThreadVoter::EDIT, $thread)) {
            throw new AccessDeniedException();
        }

        return new ReplyThreadRequest($thread->getId(), $message, $files);
    }
}
