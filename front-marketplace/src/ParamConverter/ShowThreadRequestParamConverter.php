<?php

namespace App\ParamConverter;

use App\Security\Exception\UserNotAuthorizedException;
use App\Security\ThreadVoter;
use Marketplace\Component\Discussion\Domain\Model\MessageDetail;
use Marketplace\Component\Discussion\Domain\Request\ThreadRequestInterface;
use Marketplace\Component\Discussion\Domain\UseCase\CreateEmptyThread\DTO\CreateEmptyThreadRequest;
use Marketplace\Component\Discussion\Domain\UseCase\ShowThread\DTO\ShowThreadRequest;
use Marketplace\Component\Discussion\Infrastructure\Adapter\Repository\ThreadRepository;
use Marketplace\Component\Sso\Infrastructure\Entity\Vendor;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Translation\Exception\NotFoundResourceException;

class ShowThreadRequestParamConverter implements ParamConverterInterface
{
    public function __construct(
        private ThreadRepository $threadRepository,
        private Security $security
    ) {
    }

    public function apply(Request $request, ParamConverter $configuration): void
    {
        /**
         * @var User $user
         */
        $user = $this->security->getUser();
        $userId = null;
        $isVendor = false;
        if ($user instanceof User || $user instanceof Vendor) {
            $userId = $user->getId();
        }

        if ($user instanceof Vendor) {
            $isVendor = true;
        }

        $threadId = $request->attributes->getInt('threadId');

        if (is_null($userId)) {
            throw new NotFoundResourceException('User not found');
        }

        if ($threadId === 0) {
            $threadRequest = $this->operatorCase($userId, $isVendor);
        } else {
            $threadRequest = $this->normalCase($threadId, $isVendor);
        }
        $request->attributes->set($configuration->getName(), $threadRequest);
    }

    public function supports(ParamConverter $configuration): bool
    {
        return $configuration->getClass() === ThreadRequestInterface::class;
    }

    private function operatorCase(int $userId, bool $isVendor): ThreadRequestInterface
    {
        $thread = $this->threadRepository->findActiveOperatorThread($userId);
        if (is_null($thread)) {
            $threadRequest = new CreateEmptyThreadRequest(
                (new MessageDetail())->setType(MessageDetail::TYPE_OPERATOR),
                $userId
            );
        } else {
            $threadRequest = new ShowThreadRequest($thread->getId(), $isVendor);
        }
        return $threadRequest;
    }

    private function normalCase(int $threadId, bool $isVendor): ThreadRequestInterface
    {
        $thread = $this->threadRepository->findThreadById($threadId);

        if (is_null($thread)) {
            throw new NotFoundResourceException('Thread not found');
        }

        if ($this->security->isGranted(ThreadVoter::VIEW, $thread)) {
            throw new AccessDeniedException();
        }
        return  new ShowThreadRequest($thread->getId(), $isVendor);
    }
}
