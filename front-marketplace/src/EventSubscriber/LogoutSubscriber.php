<?php

namespace App\EventSubscriber;

use Marketplace\Component\User\Infrastructure\Entity\User;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Security\Http\Event\LogoutEvent;

class LogoutSubscriber implements EventSubscriberInterface
{
    /**
     * LogoutSubscriber constructor.
     * @param Security $security
     * @param RouterInterface $router
     */
    public function __construct(
        private readonly Security $security,
        private readonly RouterInterface $router
    ) {
    }

    public static function getSubscribedEvents()
    {
        return [
            RequestEvent::class => 'onKernelRequest',
            LogoutEvent::class => 'onLogout'
        ];
    }

    /**
     *  if user is not active anymore redirect to logout
     */
    public function onKernelRequest(RequestEvent $event)
    {
        $user = $this->security->getUser();
        if (!$user instanceof User) {
            return;
        }

        if (!$user->isEnabled()) {
            $event->setResponse(new RedirectResponse($this->router->generate('logout')));
        }
    }

    /**
     * on Logout redirect to home page with good locale
     */
    public function onLogout(LogoutEvent $event)
    {
        $locale = $event->getRequest()->attributes->get("_locale", 'fr');
        $event->setResponse(new RedirectResponse($this->router->generate(
            'home',
            parameters: ["_locale" => $locale]
        )));
    }
}
