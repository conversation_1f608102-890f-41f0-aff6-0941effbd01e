<?php

namespace App\EventSubscriber;

use App\Service\TelencoBaseViewService;
use Marketplace\Component\Cart\Domain\Port\Repository\CartItemRepositoryInterface;
use Marketplace\Component\Cart\Event\FetchCartCountEvent;
use Marketplace\Component\Cart\Event\RefreshCartCountEvent;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Security\Core\Security;

class CartEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private Security $security,
        private TelencoBaseViewService $telencoBaseViewService,
        private CartItemRepositoryInterface $cartItemRepository
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            FetchCartCountEvent::class => 'onFetchCartCount',
            RefreshCartCountEvent::class => 'onRefreshCartCount'
        ];
    }

    public function onFetchCartCount(FetchCartCountEvent $event)
    {
        /** @var User $user */
        $user = $this->security->getUser();

        if ($user instanceof User) {
            $userId = $user->getId();

            if (null !== $userId) {
                $cartCount = $this->cartItemRepository->countByUserCurrentCart($userId);

                // todo use caching system
                $this->telencoBaseViewService->setCartCount($cartCount);
            }
        }
    }

    public function onRefreshCartCount(RefreshCartCountEvent $event)
    {
        /** @var User $user */
        $user = $this->security->getUser();

        if ($user instanceof User) {
            // todo invalid caching system
            // todo set new cache
            $this->telencoBaseViewService->setCartCount($event->getCartCount());
        }
    }
}
