<?php

namespace App\EventSubscriber;

use App\Event\ValidateBuyerEvent;
use App\Domain\UseCase\ValidateBuyer\ValidateBuyerUseCase;
use App\Presentation\Presenter\ValidateBuyerPresenter;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Class ValidateBuyerEventSubscriber
 */
class ValidateBuyerEventSubscriber implements EventSubscriberInterface
{
    /**
     * ValidateBuyerEventSubscriber constructor.
     * @param ValidateBuyerUseCase $useCase
     * @param ValidateBuyerPresenter $presenter
     */
    public function __construct(
        private ValidateBuyerUseCase $useCase,
        private ValidateBuyerPresenter $presenter
    ) {
    }

    /**
     * @return array<string, string>
     */
    public static function getSubscribedEvents(): array
    {
        return [ValidateBuyerEvent::class => 'validateBuyer'];
    }

    /**
     * @param ValidateBuyerEvent $event
     */
    public function validateBuyer(ValidateBuyerEvent $event)
    {
        $this->useCase->execute($event->getRequest(), $this->presenter);
    }
}
