<?php

declare(strict_types=1);

namespace App\Sitemap\Offer;

use App\Port\Service\SitemapProviderInterface;
use Marketplace\Component\Offer\Domain\Port\Repository\OfferRepositoryInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class OfferSitemapProvider implements SitemapProviderInterface
{

    public function __construct(
        private OfferRepositoryInterface $offerRepository,
        private UrlGeneratorInterface $urlGenerator,
    ) {
    }

    public function getUrls(): array
    {
        $offersUrl = [];
        $offers = $this->offerRepository->scrollAllActiveOffer();
        foreach ($offers as $offer) {
            $offerUrl = $this->urlGenerator->generate(
                'offer_detail',
                [
                    'categorySlug' => $offer['categorySlug'],
                    'offerSlug' => $offer['nameSlug'],
                    'offerId' => $offer['id']
                ]
            );
            $offersUrl[] = $offerUrl;
        }
        return $offersUrl;
    }
}
