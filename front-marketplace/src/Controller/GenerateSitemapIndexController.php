<?php

declare(strict_types=1);

namespace App\Controller;

use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Response;
use Twig\Environment;

#[Route('/sitemap_index.xml', name: 'sitemap.index', methods: ['GET'])]
final class GenerateSitemapIndexController
{
    private const PAGE_URL = 'sitemap/sitemap-index.xml.twig';
    public function __construct(
        private readonly Environment $twig,
        private readonly array $supportedLocales
    ) {
    }

    public function __invoke(): Response
    {
        $response =  new Response(
            $this->twig->render(
                self::PAGE_URL,
                [
                    'locales' => $this->supportedLocales,
                ]
            )
        );

        $response->headers->set('Content-Type', 'application/xml; charset=utf-8');
        return $response;
    }
}
