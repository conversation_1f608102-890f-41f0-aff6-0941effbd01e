<?php

declare(strict_types=1);

namespace App\Controller\Category;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Telenco\Component\Category\Domain\UseCase\GetCategoryDescriptionUseCase\DTO\GetCategoryDescriptionRequest;
use Telenco\Component\Category\Domain\UseCase\GetCategoryDescriptionUseCase\GetCategoryDescriptionUseCase;
use Telenco\Component\Category\Presentation\Presenter\GetCategoryDescriptionPresenter;
use Telenco\Component\Category\Presentation\View\GetCategoryDescriptionView;

#[Route('/api/category_description/{categoryId}', name: 'api.category_description', options: ['expose' => true], methods: ['GET'])]
class GetCategoryDescriptionController extends AbstractController
{
    public function __construct(
        private GetCategoryDescriptionView $view
    ) {
    }

    public function __invoke(
        int $categoryId,
        GetCategoryDescriptionUseCase $useCase,
        GetCategoryDescriptionPresenter $presenter
    ): Response {
        $useCase->execute(new GetCategoryDescriptionRequest($categoryId), $presenter);
        return $this->view->generateJson($presenter->viewModel());
    }
}
