<?php

namespace  App\Controller\Webhook\Parcel;

use Marketplace\Component\Invoice\Domain\UseCase\ParcelStatusChanged\DTO\ParcelStatusChangedRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;

#[Route("/hook/parcel/status_changed", name:"webhook.parcel.status_changed", methods:["POST"])]
class ParcelStatusChangedWebHookController extends ParcelWebHookController
{
    public function __invoke(Request $request, MessageBusInterface $messageBus): Response
    {
        parent::__invoke($request, $messageBus);

        $data = json_decode($request->getContent());
        $parcelId = $data->data->id;
        $status = $data->data->status;

        if ($status === "in_transit") {
            $messageBus->dispatch(new ParcelStatusChangedRequest($parcelId));
        }

        return new Response();
    }
}
