<?php

declare(strict_types=1);

namespace App\Controller\Webhook\Order;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\Order\Domain\UseCase\SyncOrder\DTO\SyncOrderRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;

class OrderWebHookController extends AbstractController
{
    protected object $data;
    public function __invoke(Request $request, MessageBusInterface $messageBus)
    {
        $this->data = json_decode($request->getContent());

        $messageBus->dispatch(new SyncOrderRequest($this->data->data->id));

        return new Response();
    }
}
