<?php

declare(strict_types=1);

namespace App\Controller\Webhook\PSP;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Open\Webhelp\Model\AutomaticResponse;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

#[Route("/wps/push/automaticResponse", name:"wps.payment.automatic", methods:["POST"])]
class AutomaticResponseController extends AbstractController implements LoggerAwareInterface
{
    private LoggerInterface $logger;

    public function __construct(private SerializerInterface $serializer)
    {
    }

    public function __invoke(Request $request, MessageBusInterface $messageBus)
    {

        $this->logger->info('WEBHELP AUTOMATIC RESPONSE', ['payload' => json_decode($request->getContent())]);

        $webhelpAutomaticResponse = $this->serializer->deserialize(
            $request->getContent(),
            AutomaticResponse::class,
            'json'
        );

        if (!$webhelpAutomaticResponse instanceof AutomaticResponse) {
            throw new \RuntimeException('cannot parse webhelp automatic response');
        }

        $messageBus->dispatch($webhelpAutomaticResponse);
        return new Response();
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
