<?php

declare(strict_types=1);

namespace App\Controller\Webhook\MerchantOrder;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use App\Message\OrderSyncMessage;
use Marketplace\Component\Order\Domain\UseCase\SyncOrder\DTO\SyncOrderRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;

abstract class MerchantOrderWebHookController extends AbstractController
{
    public function __invoke(Request $request, MessageBusInterface $messageBus)
    {
        $data = json_decode($request->getContent());

        $messageBus->dispatch(new SyncOrderRequest($data->data->order->id));

        return new Response();
    }
}
