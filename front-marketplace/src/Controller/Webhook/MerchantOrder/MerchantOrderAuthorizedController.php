<?php

declare(strict_types=1);

namespace App\Controller\Webhook\MerchantOrder;

use App\Service\MerchantOrderAuthorizedService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route("/hook/merchant-order/authorized", name: "webhook.merchant-order.authorized", methods: ["POST"])]
class MerchantOrderAuthorizedController
{

    public function __construct()
    {
    }

    public function __invoke(Request $request, MerchantOrderAuthorizedService $authorizedService): Response
    {
        $data = json_decode($request->getContent());
        $authorizedService->merchantOrderAuthorized(
            $data->data->id,
            $data->data->merchant->id,
            $data->data->order->id,
            $data->data->order->cart_id
        );

        return new Response();
    }
}
