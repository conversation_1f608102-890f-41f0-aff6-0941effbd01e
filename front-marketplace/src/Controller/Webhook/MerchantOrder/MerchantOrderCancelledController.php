<?php

declare(strict_types=1);

namespace App\Controller\Webhook\MerchantOrder;

use Marketplace\Component\Order\Domain\UseCase\SyncOrder\DTO\SyncOrderRequest;
use Marketplace\Component\Order\Infrastructure\Builder\InfosMerchantOrderCancelledRequestBuilder\InfosMerchantOrderCancelledRequestBuilderInterface;
use Marketplace\Component\Payment\Domain\UseCase\CancelMerchantOrder\DTO\CancelMerchantOrderRequest;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\Exception\NotNormalizableValueException;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

#[Route("/hook/merchant-order/cancelled", name: "webhook.merchant-order.cancelled", methods: ["POST"])]
class MerchantOrderCancelledController
{
    protected LoggerInterface $logger;

    public function __construct(
        private InfosMerchantOrderCancelledRequestBuilderInterface $infosMerchantOrderCancelledRequestBuilder
    ) {
    }

    public function __invoke(Request $request, MessageBusInterface $messageBus)
    {
        $data = json_decode($request->getContent());

        $messageBus->dispatch(new SyncOrderRequest($data->data->order->id));

        $messageBus->dispatch(new CancelMerchantOrderRequest($data->data->id));

        try {
            $infosMerchantOrderCancelledRequest = $this->infosMerchantOrderCancelledRequestBuilder
                ->buildFromRequest($request);
            $messageBus->dispatch($infosMerchantOrderCancelledRequest);
        } catch (NotNormalizableValueException | Exception $exception) {
            $this->logger->info($exception->getMessage(), ['INFOS_MERCHANT_ORDER_CANCELLED_REQUEST_EXCEPTION']);
            $this->logger->error($request->getContent(), ['request.EXPORT_DATA']);
        }

        return new Response();
    }
}
