<?php

declare(strict_types=1);

namespace App\Controller\Webhook\Invoice;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\Invoice\Domain\UseCase\SyncInvoiceUseCase\DTO\SyncInvoiceRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;

abstract class InvoiceWebHookController extends AbstractController
{
    public function __invoke(Request $request, MessageBusInterface $messageBus)
    {
        $data = json_decode($request->getContent());

        $messageBus->dispatch(new SyncInvoiceRequest($data->data->id));

        return new Response();
    }
}
