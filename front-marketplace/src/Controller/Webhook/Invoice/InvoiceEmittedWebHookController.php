<?php

declare(strict_types=1);

namespace App\Controller\Webhook\Invoice;

use Marketplace\Component\Invoice\Domain\UseCase\ExportInvoice\DTO\ExportInvoiceRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;

#[Route("/hook/invoice/emitted", name: "webhook.invoice.emitted", methods: ["POST"])]
class InvoiceEmittedWebHookController extends InvoiceWebHookController
{
    public function __invoke(Request $request, MessageBusInterface $messageBus)
    {
        parent::__invoke($request, $messageBus);
        $data = json_decode($request->getContent());
        $messageBus->dispatch(new ExportInvoiceRequest($data->data->id));

        return new Response();
    }
}
