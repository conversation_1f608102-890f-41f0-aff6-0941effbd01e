<?php

namespace App\Controller\Offer\API;

use Marketplace\Component\Offer\Domain\UseCase\LastSeenProduct\ShowLastSeenProductsUseCase\DTO\ShowLastSeenProductsRequest;
use Marketplace\Component\Offer\Domain\UseCase\LastSeenProduct\ShowLastSeenProductsUseCase\ShowLastSeenProductsUseCase;
use Marketplace\Component\Offer\Presentation\Presenter\ShowLastSeenProductsPresenter;
use Marketplace\Component\Offer\Presentation\View\ShowLastSeenProductsView;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/offer/lastSeenProducts/{offerId}', name: 'last_seen_offers', options: ['expose' => true], methods: ['GET'])]
class GetLastSeenProductsController
{
    public function __construct(private ShowLastSeenProductsView $view)
    {
    }

    public function __invoke(
        int $offerId,
        ShowLastSeenProductsUseCase $useCase,
        ShowLastSeenProductsPresenter $presenter
    ): Response {
        $useCase->execute(new ShowLastSeenProductsRequest($offerId), $presenter);

        return $this->view->generateView($presenter->getViewModel());
    }
}
