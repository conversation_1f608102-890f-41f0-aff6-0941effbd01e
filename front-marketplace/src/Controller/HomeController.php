<?php

namespace App\Controller;

use Marketplace\Component\Cart\Domain\Port\Service\CartCleanServiceInterface;
use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\CleanArchiCore\Utils\Str;
use Marketplace\Component\Offer\Infrastructure\Adapter\Elasticsearch\OfferRepositoryElasticsearch;
use Marketplace\Component\User\Domain\Port\Mapper\RegionMapperInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Telenco\Component\Widget\Domain\UseCase\PublishedWidgetsShow\DTO\PublishedWidgetsShowRequest;
use Telenco\Component\Widget\Domain\UseCase\PublishedWidgetsShow\PublishedWidgetsShowUseCase;
use Telenco\Component\Widget\Presentation\Presenter\PublishedWidgetsShowPresenter;
use Telenco\Component\Widget\Presentation\View\PublishedWidgetsShowView;

#[Route(path: '/home', name: 'home', options: ['expose' => true], methods: ['GET'])]
class HomeController extends AbstractController
{
    public function __construct(
        private PublishedWidgetsShowView $homeView,
        private RegionMapperInterface $regionMapper,
        private CartCleanServiceInterface $cartCleanService,
    ) {
    }

    public function __invoke(
        Request $request,
        PublishedWidgetsShowUseCase $useCase,
        PublishedWidgetsShowPresenter $presenter,
        OfferRepositoryElasticsearch $repositoryElasticsearch,
    ): Response {

        $userLocale = $request->getSession()->get('_locale');
        if ($userLocale !== null && $userLocale !== $request->attributes->get('_locale')) {
            $request->setLocale($userLocale);
            return $this->redirectToRoute('home', ['_locale' => $userLocale]);
        }
        $this->updateCartRegion($request);
        $useCase->execute($presenter, new PublishedWidgetsShowRequest($request->getLocale()));
        return $this->homeView->generateView($presenter->viewModel());
    }

    private function updateCartRegion(Request $request): void
    {
        $domain = Str::getPartFromTail($request->getHost(), '.', $_ENV['DOMAIN_LOOK_UP']);
        $current = $this->regionMapper->getRegionFromDomain($domain);
        $this->cartCleanService->updateCartRegion($current);
    }
}
