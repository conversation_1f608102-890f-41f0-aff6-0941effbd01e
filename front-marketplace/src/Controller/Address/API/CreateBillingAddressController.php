<?php

declare(strict_types=1);

namespace App\Controller\Address\API;

use App\Security\AddressVoter;
use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\User\Domain\UseCase\Address\EditAddress\DTO\EditBillingAddressRequest;
use Marketplace\Component\User\Domain\UseCase\Address\EditAddress\EditBillingAddressUseCase;
use Marketplace\Component\User\Infrastructure\Entity\Address;
use Marketplace\Component\User\Domain\Model\Address as AddressModel;
use Marketplace\Component\User\Presentation\Presenter\Address\CreateBillingAddressApiPresenter;
use Marketplace\Component\User\Presentation\View\Address\CreateBillingAddressViewJson;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/api/address/create_billing_address', name: 'address.create_billing_address.api', options: ['expose' => true], methods: ['POST'])]
class CreateBillingAddressController extends AbstractController
{
    public function __construct(
        private SerializerInterface $serializer,
        private CreateBillingAddressViewJson $view,
    ) {
    }

    public function __invoke(
        Request $request,
        EditBillingAddressUseCase $useCase,
        CreateBillingAddressApiPresenter $presenter,
    ) {
        $editBillingAddressRequest = $this->serializer->deserialize(
            $request->getContent(),
            EditBillingAddressRequest::class,
            'json'
        );

        $attribute = AddressVoter::CREATE;
        $this->denyAccessUnlessGranted($attribute, (new Address())->setType(AddressModel::BILLING));

        $companyId = $this->getCompanyId();
        $editBillingAddressRequest->setCompanyId($companyId);

        $useCase->execute($editBillingAddressRequest, $presenter);

        return $this->view->generateView($presenter->viewModel());
    }
}
