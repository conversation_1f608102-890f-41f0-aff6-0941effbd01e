<?php

namespace App\Controller\Quote;

use App\Security\QuoteVoter;
use App\Service\TelencoBaseViewService;
use Marketplace\Component\Quote\Infrastructure\Entity\Quote;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Telenco\Component\Shared\Presentation\Presenter\DefaultPresenter;
use Telenco\Component\Shared\Presentation\View\DefaultView;

#[Route('/quote/offer/update/{quote}', name: 'offer_quote_update', methods: ['GET'])]
class UpdateOfferQuoteFrontController extends AbstractController
{
    public function __construct(
        private DefaultView $view,
        private TelencoBaseViewService $baseViewService,
    ) {
    }

    public function __invoke(
        Quote $quote,
        DefaultPresenter $presenter,
        Request $request
    ): Response {
        $attribute = QuoteVoter::EDIT;
        $this->denyAccessUnlessGranted($attribute, $quote);

        // On force le chargement de la langue pour que tous les boutons soient traduits
        $this->baseViewService->buildBaseViewHeader($request->getLocale());

        $presenter->present();

        $viewModel = $presenter->viewModel(
            $this->baseViewService->buildUserMenuItems('quotes_list'),
            'euro',
            ['header.title' => 'quote.create.title', 'page.title' => 'quote.create.title'],
            ['quoteId' => $quote->getQuoteId(), 'quote' => $quote]
        );
        return $this->view->generateView($viewModel, 'pages/offer-quote-update.html.twig');
    }
}
