<?php

namespace App\Controller\Quote;

use App\Service\TelencoBaseViewService;
use Marketplace\Component\Quote\Presentation\Presenter\ListQuotesPresenter;
use Marketplace\Component\Quote\Presentation\View\ListQuotesView;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/quotes', name: 'quotes_list', options: ['expose' => true], methods: ['GET'])]
class ListQuotesController extends AbstractController
{
    public function __construct(
        private ListQuotesView $view,
        private TelencoBaseViewService $baseViewService,
    ) {
    }

    public function __invoke(
        Request $request,
        ListQuotesPresenter $presenter,
    ): Response {
        // On force le chargement de la langue pour que tous les boutons soient traduits
        $this->baseViewService->buildBaseViewHeader($request->getLocale());

        $presenter->present();
        return $this->view->generateView($presenter->viewModel($this->baseViewService->buildUserMenuItems('quotes_list')));
    }
}
