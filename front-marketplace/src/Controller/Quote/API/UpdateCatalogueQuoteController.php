<?php

namespace App\Controller\Quote\API;

use App\Security\QuoteVoter;
use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\Quote\Domain\UseCase\UpdateCatalogueQuote\DTO\UpdateCatalogueQuoteRequest;
use Marketplace\Component\Quote\Domain\UseCase\UpdateCatalogueQuote\UpdateCatalogueQuoteUseCase;
use Marketplace\Component\Quote\Presentation\Presenter\UpdateCatalogueQuotePresenter;
use Marketplace\Component\Quote\Presentation\View\UpdateCatalogueQuoteView;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/api/quote/update_catalogue_quote', name: 'quote.update_catalogue_quote.api', options: ['expose' => true], methods: ['PATCH'])]
class UpdateCatalogueQuoteController extends AbstractController
{
    public function __construct(
        private UpdateCatalogueQuoteView $view,
        private SerializerInterface $serializer,
        private Security $security
    ) {
    }

    public function __invoke(
        Request $request,
        UpdateCatalogueQuoteUseCase $useCase,
        UpdateCatalogueQuotePresenter $presenter
    ): Response {


        /** @var UpdateCatalogueQuoteRequest $updateRequest */
        $updateRequest = $this->serializer->deserialize(
            $request->getContent(),
            UpdateCatalogueQuoteRequest::class,
            'json'
        );
        if (!$this->security->isGranted(QuoteVoter::EDIT, $updateRequest->quoteCatalogue->getQuoteId())) {
            throw new AccessDeniedException();
        }

        $useCase->execute($updateRequest, $presenter);

        return $this->view->generateJson($presenter->viewModel());
    }
}
