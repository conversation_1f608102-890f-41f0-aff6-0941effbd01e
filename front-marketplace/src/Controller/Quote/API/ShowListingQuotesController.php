<?php

declare(strict_types=1);

namespace App\Controller\Quote\API;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\Quote\Domain\UseCase\ShowListingQuotes\DTO\ShowListingQuotesRequest;
use Marketplace\Component\Quote\Domain\UseCase\ShowListingQuotes\ShowListingQuotesUseCase;
use Marketplace\Component\Quote\Presentation\Presenter\ShowListingQuotesPresenter;
use Marketplace\Component\Quote\Presentation\View\ShowListingQuotesView;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/vendor/quotes', name: 'vendor.quotes.list.api', methods: ['GET'])]
#[Route('/api/quotes', name: 'quotes.list.api', options: ['expose' => true], methods: ['GET'])]
class ShowListingQuotesController extends AbstractController
{
    public function __construct(
        private ShowListingQuotesView $view
    ) {
    }

    /**
     * @param ShowListingQuotesRequest $showListingQuotesRequest
     * @param ShowListingQuotesUseCase $useCase
     * @param ShowListingQuotesPresenter $presenter
     * @return Response
     *
     * The Request hydrate by ShowListingQuotesRequestParamConverter
     * @see ShowListingQuotesRequestParamConverter
     */
    public function __invoke(
        ShowListingQuotesRequest $showListingQuotesRequest,
        ShowListingQuotesUseCase $useCase,
        ShowListingQuotesPresenter $presenter
    ): Response {
        $useCase->execute($showListingQuotesRequest, $presenter);
        return $this->view->generateJson($presenter->viewModel());
    }
}
