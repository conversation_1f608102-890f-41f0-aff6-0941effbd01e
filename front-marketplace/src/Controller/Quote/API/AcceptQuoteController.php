<?php

declare(strict_types=1);

namespace App\Controller\Quote\API;

use App\Security\QuoteVoter;
use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\Quote\Domain\Enum\QuoteActionEnum;
use Marketplace\Component\Quote\Domain\UseCase\QuoteChangeStatus\DTO\QuoteChangeStatusRequest;
use Marketplace\Component\Quote\Infrastructure\Strategy\Action\QuoteUpdateStatusContext;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Security;

#[Route('/api/quote/accept/{quoteId}', name: 'accept.quote.api', options: ['expose' => true], methods: ['PATCH'])]
class AcceptQuoteController extends AbstractController
{
    public function __construct(
        private QuoteUpdateStatusContext $context,
        private Security $security
    ) {
    }

    public function __invoke(
        int $quoteId,
        Request $request
    ): Response {
        if (!$this->security->isGranted(QuoteVoter::EDIT, $quoteId)) {
            throw new AccessDeniedException();
        }

        return $this->context->process(
            new QuoteChangeStatusRequest(
                $quoteId,
                QuoteActionEnum::BUYER_ACCEPT
            )
        );
    }
}
