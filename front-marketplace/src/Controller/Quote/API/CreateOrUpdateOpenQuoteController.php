<?php

namespace App\Controller\Quote\API;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\Quote\Domain\UseCase\CreateOpenQuote\DTO\CreateOpenQuoteRequest;
use Marketplace\Component\Quote\Infrastructure\Strategy\CrudOpenQuoteContext;
use Marketplace\Component\User\Domain\Model\Company;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/quote/cu_open_quote', name: 'quote.cu_open_quote.api', options: ['expose' => true], methods: ['POST'])]
class CreateOrUpdateOpenQuoteController extends AbstractController
{
    public function __construct(
        private CrudOpenQuoteContext $context
    ) {
    }

    /**
     * @param CreateOpenQuoteRequest $createOpenQuoteRequest
     * @param Request $request
     * @return Response
     *
     * The Request hydrate by CreateOpenQuoteRequestParamConverter
     * @see CreateOpenQuoteRequest
     */
    public function __invoke(
        CreateOpenQuoteRequest $createOpenQuoteRequest,
        Request $request
    ): Response {
        //associate company
        $createOpenQuoteRequest->openQuote->setBuyerId($this->getUserId());
        $createOpenQuoteRequest->openQuote->setCompany((new Company())->setId($this->getCompanyId()));

        return $this->context->process($createOpenQuoteRequest);
    }
}
