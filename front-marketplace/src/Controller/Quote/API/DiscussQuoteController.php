<?php

namespace App\Controller\Quote\API;

use App\Security\QuoteVoter;
use Marketplace\Component\Quote\Domain\UseCase\DiscussQuoteUseCase\DiscussQuoteUseCase;
use Marketplace\Component\Quote\Domain\UseCase\DiscussQuoteUseCase\DTO\DiscussQuoteRequest;
use Marketplace\Component\Quote\Presentation\Presenter\DiscussQuotePresenter;
use Marketplace\Component\Quote\Presentation\View\DiscussQuoteView;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Security;

#[Route('/api/quote/discuss/{quoteId}/{threadId}', name: 'discuss.quote.api', options: ['expose' => true], methods: ['POST'])]
#[Route('/api/vendor/quote/discuss/{quoteId}/{threadId}', name: 'discuss.quote.api.vendor', methods: ['POST'])]
class DiscussQuoteController
{
    public function __construct(
        private DiscussQuoteView $view,
        private Security $security
    ) {
    }

    /**
     * @param DiscussQuoteRequest $discussQuoteRequest
     * @param DiscussQuoteUseCase $useCase
     * @param DiscussQuotePresenter $presenter
     * @return Response
     *
     * The Request hydrate by DiscussQuoteRequestParamConverter
     * @see DiscussQuoteRequestParamConverter
     */
    public function __invoke(
        DiscussQuoteRequest $discussQuoteRequest,
        DiscussQuoteUseCase $useCase,
        DiscussQuotePresenter $presenter
    ): Response {
        if (!$this->security->isGranted(QuoteVoter::EDIT, $discussQuoteRequest->quoteId)) {
            throw new AccessDeniedException();
        }
        $useCase->execute($discussQuoteRequest, $presenter);
        return $this->view->generateJson($presenter->viewModel());
    }
}
