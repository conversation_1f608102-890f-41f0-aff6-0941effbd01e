<?php

namespace App\Controller\Quote\API;

use App\Security\QuoteVoter;
use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\Quote\Domain\UseCase\CreateCartFromQuoteOpen\CreateCartFromQuoteOpenUseCase;
use Marketplace\Component\Quote\Domain\UseCase\CreateCartFromQuoteOpen\DTO\CreateCartFromQuoteOpenRequest;
use Marketplace\Component\Quote\Presentation\Presenter\CreateCartFromOpenQuotePresenter;
use Marketplace\Component\Quote\Presentation\View\CreateCartFromOpenQuoteView;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Security;

#[Route(
    '/api/quote/create_cart_from_open_quote/{quoteId}',
    name: 'quote.create_cart_from_open_quote.api',
    options: ['expose' => true],
    methods: ['POST']
)]
#[IsGranted('ROLE_BUYER_STANDARD')]
class CreateCartFromQuoteOpenController extends AbstractController
{
    public function __construct(
        private CreateCartFromOpenQuoteView $view,
        private Security $security
    ) {
    }

    public function __invoke(
        int $quoteId,
        CreateCartFromQuoteOpenUseCase $useCase,
        CreateCartFromOpenQuotePresenter $presenter
    ): Response {
        $user = $this->security->getUser();


        if (!$user instanceof User || !$this->security->isGranted(QuoteVoter::EDIT, $quoteId)) {
            throw new AccessDeniedException();
        }

        $useCase->execute((new CreateCartFromQuoteOpenRequest($quoteId, $this->getUserId())), $presenter);

        return $this->view->generateJson($presenter->viewModel());
    }
}
