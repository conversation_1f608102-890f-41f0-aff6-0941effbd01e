<?php

namespace App\Controller;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/demo-table', name: 'demo-table', methods: ['GET'])]
class DemoTable extends AbstractController
{
    public function __invoke(): Response
    {
        return $this->render('demo/table.html.twig', []);
    }
}
