<?php

namespace App\Controller\Cart\API;

use Marketplace\Component\Cart\Domain\UseCase\UpdateCartShippingDetailsUseCase\DTO\UpdateCartShippingDetailsRequest;
use Marketplace\Component\Cart\Domain\UseCase\UpdateCartShippingDetailsUseCase\UpdateCartShippingDetailsUseCase;
use Marketplace\Component\Cart\Presentation\Presenter\UpdateCartShippingDetailsPresenter;
use Marketplace\Component\Cart\Presentation\View\UpdateCartShippingDetailsView;
use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/api/cart/contact/details', name: 'api.cart.patch.contact.details', options: ['expose' => true], methods: ['PATCH'])]
class PatchCartContactDetailsController extends AbstractController
{
    public function __construct(
        private UpdateCartShippingDetailsView $view,
        private SerializerInterface $serializer,
    ) {
    }
    public function __invoke(
        Request $request,
        UpdateCartShippingDetailsUseCase $useCase,
        UpdateCartShippingDetailsPresenter $presenter
    ): Response {
        $user = $this->getUser();
        /**
         * @var int $userId
         */
        $userId = $user->getId();
        $patchRequest = $this->serializer->deserialize(
            $request->getContent(),
            UpdateCartShippingDetailsRequest::class,
            'json'
        );
        $patchRequest->setUserId($userId);
        $useCase->execute($patchRequest, $presenter);

        return $this->view->generateJson($presenter->getViewModel());
    }
}
