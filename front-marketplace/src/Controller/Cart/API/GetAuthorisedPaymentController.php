<?php

namespace App\Controller\Cart\API;

use Marketplace\Component\Payment\Domain\UseCase\GetAuthorisedPayment\DTO\GetAuthorisedPaymentRequest;
use Marketplace\Component\Payment\Domain\UseCase\GetAuthorisedPayment\GetAuthorisedPaymentUseCase;
use Marketplace\Component\Payment\Presentation\Presenter\GetAuthorisedPaymentPresenter;
use Marketplace\Component\Payment\Presentation\View\GetAuthorisedPaymentView;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;

#[Route('/api/authorised_payments', name: 'api.payments.authorised', options: ['expose' => true], methods: ['GET'])]
#[Security("is_granted('ROLE_BUYER_CONSULT')")]
class GetAuthorisedPaymentController
{
    public function __construct(
        private GetAuthorisedPaymentView $view,
    ) {
    }

    public function __invoke(
        Request $request,
        GetAuthorisedPaymentUseCase $useCase,
        GetAuthorisedPaymentPresenter $presenter,
    ): Response {
        $companyId = $request->query->getInt('companyId');
        $amount = $request->query->get('orderAmountTTC');
        if ($amount === null) {
            throw new BadRequestException();
        }
        $publishRequest = new GetAuthorisedPaymentRequest($companyId, (float) $amount);
        $useCase->execute($publishRequest, $presenter);
        return $this->view->generateView($presenter->viewModel());
    }
}
