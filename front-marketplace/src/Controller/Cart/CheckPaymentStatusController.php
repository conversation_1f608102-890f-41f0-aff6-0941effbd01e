<?php

namespace App\Controller\Cart;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\Order\Domain\UseCase\SendCheckoutEmail\DTO\SendCheckoutEmailRequest;
use Marketplace\Component\Order\Domain\Model\Order;
use Marketplace\Component\Order\Domain\Port\Repository\OrderRepositoryInterface;
use Marketplace\Component\Order\Infrastructure\ValueObject\IzbergStatus;
use Marketplace\Component\Payment\Domain\Model\Method\BankTransferMethod;
use Marketplace\Component\Payment\Domain\Model\Method\CreditCardMethod;
use Marketplace\Component\Payment\Domain\Model\PaymentAction;
use Marketplace\Component\Payment\Infrastructure\Adapter\Repository\PaymentActionRepository;
use Marketplace\Component\Payment\Presentation\View\CheckPaymentStatusView;
use Marketplace\Component\Payment\Presentation\ViewModel\CheckPaymentStatusViewModel;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\RouterInterface;

#[Route(path: '/payment/{cartId}/check/status/', name: 'payment.check_payment_status', options: ['expose' => true], methods: ['GET'])]
final class CheckPaymentStatusController extends AbstractController
{
    public function __construct(
        private CheckPaymentStatusView $view,
        private OrderRepositoryInterface $orderRepository,
        private PaymentActionRepository $paymentActionRepository,
        private RouterInterface $router,
        private MessageBusInterface $messageBus,
    ) {
    }
    public function __invoke(
        Request $request,
        int $cartId
    ) {
        $pendingStatuses = [
            IzbergStatus::PENDING_CREATION_STATUS,
            IzbergStatus::INITIAL_STATUS,
        ];

        $baseUrl = $request->getSchemeAndHttpHost();
        $viewModel = new CheckPaymentStatusViewModel();
        $order = $this->orderRepository->findByCartId($cartId);
        $locale = $request->getLocale();
        $paymentSuccessful = true;

        if (!$order instanceof Order) {
            $viewModel->url = null;
            return $this->view->generateView($viewModel);
        }

        // order confirmed url
        $viewModel->url = $this->router->generate(
            'cart.order.confirmed',
            ['cartId' => $cartId]
        );



        // order status is pending => url = null
        if (in_array($order->getStatus(), $pendingStatuses)) {
            //CASE bank transfert 24h before authorisation
            if ($order->getPaymentMethod() instanceof BankTransferMethod) {
                $paiement = $this->paymentActionRepository->findByCartId($cartId);
                if (!$paiement instanceof PaymentAction || $paiement->getStatus() !== PaymentAction::STATUS_DONE) {
                    $viewModel->url = null;
                    return $this->view->generateView($viewModel);
                }
            }
        }

        // order status is cancelled => order cancelled url
        if ($order->getStateStatus() === IzbergStatus::CANCELLED || (!$order->getPaymentMethod() instanceof CreditCardMethod && $order->getOrderId() === null)) {
            $paymentSuccessful = false;
        }

        // Send email
        if ($paymentSuccessful === true) {
            $this->messageBus->dispatch(
                new SendCheckoutEmailRequest(
                    baseUrl: $baseUrl,
                    locale: $locale,
                    cartId: $cartId,
                    success: true
                )
            );
        }

        if ($paymentSuccessful === false) {
            $viewModel->url = $this->router->generate(
                'cart.order.cancelled',
                ['cartId' => $cartId]
            );
            $this->messageBus->dispatch(
                new SendCheckoutEmailRequest(
                    baseUrl: $baseUrl,
                    locale: $locale,
                    cartId: $cartId,
                    success: false
                )
            );
        }

        return $this->view->generateView($viewModel);
    }
}
