<?php

declare(strict_types=1);

namespace App\Controller\Cart;

use Marketplace\Component\Cart\Domain\UseCase\ConfirmOrder\ConfirmOrderUseCase;
use Marketplace\Component\Cart\Domain\UseCase\ConfirmOrder\DTO\ConfirmOrderRequest;
use Marketplace\Component\Cart\Infrastructure\Adapter\Service\BankDetailsService;
use Marketplace\Component\Cart\Presentation\Presenter\ConfirmOrderPresenter;
use Marketplace\Component\Cart\Presentation\View\ConfirmOrderView;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/cart/{cartId}/order/confirmed', name: 'cart.order.confirmed', options: ['expose' => true], methods: ['GET'])]
final class OrderConfirmedController
{
    public function __construct(
        private ConfirmOrderView $view,
        private BankDetailsService $bankDetailsService,
    ) {
    }

    public function __invoke(
        int $cartId,
        ConfirmOrderUseCase $useCase,
        ConfirmOrderPresenter $presenter
    ): Response {
        $useCase->execute(new ConfirmOrderRequest($cartId), $presenter);

        return $this->view->generateView($presenter->viewModel($this->bankDetailsService->getBankDetails()));
    }
}
