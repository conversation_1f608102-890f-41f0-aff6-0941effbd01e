<?php

namespace App\Controller\Messenger;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\Discussion\Infrastructure\Adapter\Repository\ThreadRepository;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Translation\Exception\NotFoundResourceException;

#[Route('/thread/attachment/{attachmentId}', name: 'thread.attachment.url', methods: ['GET'])]
#[Route('/api/vendor/thread/attachment/{attachmentId}', name: 'vendor.thread.attachment.url', methods: ['GET'])]
class FetchAttachmentController extends AbstractController
{
    /**
     * @param int attachmentId
     * @return Response
     *
     */
    public function __invoke(string $attachmentId, ThreadRepository $threadRepository): Response
    {
        $url  = $threadRepository->fetchAttachmentUrl($attachmentId);
        if (is_null($url)) {
            throw new NotFoundResourceException('Attachment not found');
        }

        return $this->redirect($url);
    }
}
