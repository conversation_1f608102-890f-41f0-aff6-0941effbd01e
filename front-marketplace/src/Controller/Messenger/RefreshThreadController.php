<?php

declare(strict_types=1);

namespace App\Controller\Messenger;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\Discussion\Domain\UseCase\RefreshThread\DTO\RefreshThreadRequest;
use Marketplace\Component\Discussion\Domain\UseCase\RefreshThread\RefreshThreadUseCase;
use Marketplace\Component\Discussion\Presentation\Presenter\RefreshThreadPresenter;
use Marketplace\Component\Discussion\Presentation\View\RefreshThreadView;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/thread/refresh/{threadId}', name: 'thread.refresh', methods: ['GET'])]
#[Route('/api/vendor/thread/refresh/{threadId}', name: 'vendor.thread.refresh', methods: ['GET'])]
class RefreshThreadController extends AbstractController
{
    public function __construct(
        private RefreshThreadView $view,
    ) {
    }

    /**
     * @param int $threadId
     * @param RefreshThreadRequest $refreshThreadRequest
     * @param RefreshThreadUseCase $useCase
     * @param RefreshThreadPresenter $presenter
     * @return Response
     *
     *  The Request hydrate by RefreshThreadRequestParamConverter with param id
     * @see RefreshThreadRequestParamConverter
     */
    public function __invoke(
        int $threadId,
        RefreshThreadRequest $refreshThreadRequest,
        RefreshThreadUseCase $useCase,
        RefreshThreadPresenter $presenter
    ): Response {
        $useCase->execute($refreshThreadRequest, $presenter);
        return $this->view->generateView($presenter->viewModel());
    }
}
