<?php

namespace App\Controller\Messenger;

use Marketplace\Component\Discussion\Domain\Model\AfterSales;
use Marketplace\Component\Discussion\Domain\UseCase\CreateAfterSales\CreateAfterSalesUseCase;
use Marketplace\Component\Discussion\Domain\UseCase\CreateAfterSales\DTO\CreateAfterSalesRequest;
use Marketplace\Component\Discussion\Presentation\Presenter\CreateAfterSalesPresenter;
use Marketplace\Component\Discussion\Presentation\View\CreateAfterSalesView;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Symfony\Component\Serializer\SerializerInterface;
use Telenco\Component\SyncAfterSale\Domain\UseCase\SyncCreation\DTO\AfterSaleSyncRequest;
use Telenco\Component\SyncAfterSale\Infrastructure\Strategy\SyncAfterSaleStrategyInterface;

#[Route('/api/aftersales/create', name: 'api_after_sales_create', options: ['expose' => true], methods: ['POST'])]
class CreateAfterSalesController extends AbstractController
{
    public function __construct(
        private CreateAfterSalesView $view,
        private MessageBusInterface $messageBus
    ) {
    }

    public function __invoke(
        Request $request,
        CreateAfterSalesUseCase $useCase,
        CreateAfterSalesPresenter $presenter,
        SerializerInterface $serializer
    ): Response {
        $userId = $this->getUserId();
        $createRequest = new CreateAfterSalesRequest(
            $request->request->getInt('merchantId'),
            (string) $request->request->get('afterSalesType'),
            explode(',', (string) $request->request->get('orderItemIds')),
            $request->request->getInt('merchantOrderId'),
            (string) $request->request->get('subject'),
            (string) $request->request->get('message'),
            $request->files->get('attachments', [])
        );
        $createRequest->setUserId($userId);
        $useCase->execute($createRequest, $presenter);
        $afterSales = $presenter->viewModel()->afterSales;
        if ($afterSales instanceof AfterSales) {
            /**
             * @var int $afterSaleId
             */
            $afterSaleId = $afterSales->getId();

            $afterSaleSyncRequest = new AfterSaleSyncRequest(
                $createRequest->getMerchantId(),
                $afterSaleId,
                SyncAfterSaleStrategyInterface::CREATE,
                $createRequest->getMessage()
            );
            $this->messageBus->dispatch($afterSaleSyncRequest);
        }
        return $this->view->generateView($presenter->viewModel());
    }
}
