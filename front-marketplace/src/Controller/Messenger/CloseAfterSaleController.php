<?php

namespace App\Controller\Messenger;

use App\Security\AfterSaleVoter;
use Marketplace\Component\Discussion\Domain\UseCase\CloseAfterSaleUseCase\CloseAfterSaleUseCase;
use Marketplace\Component\Discussion\Domain\UseCase\CloseAfterSaleUseCase\DTO\CloseAfterSaleRequest;
use Marketplace\Component\Discussion\Domain\UseCase\ShowAfterSaleDetails\DTO\ShowAfterSaleDetailsRequest;
use Marketplace\Component\Discussion\Domain\UseCase\ShowAfterSaleDetails\ShowAfterSaleDetailsUseCase;
use Marketplace\Component\Discussion\Presentation\Presenter\CloseAfterSalePresenter;
use Marketplace\Component\Discussion\Presentation\Presenter\ShowAfterSaleDetailsPresenter;
use Marketplace\Component\Discussion\Presentation\View\CloseAfterSaleView;
use Marketplace\Component\Discussion\Presentation\View\ShowAfterSaleDetailsView;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/api/vendor/aftersales/close/{afterSaleId}', name: 'api_after_sales_close', methods: ['POST'])]
class CloseAfterSaleController
{
    public function __construct(private CloseAfterSaleView $view, private Security $security)
    {
    }

    public function __invoke(
        int $afterSaleId,
        CloseAfterSaleUseCase $useCase,
        CloseAfterSalePresenter $presenter,
        SerializerInterface $serializer
    ): Response {
        if (!$this->security->isGranted(AfterSaleVoter::CLOSE, $afterSaleId)) {
            throw new AccessDeniedException();
        }

        $afterSaleRequest = new CloseAfterSaleRequest(
            $afterSaleId
        );
        $useCase->execute($afterSaleRequest, $presenter);
        return $this->view->generateView($presenter->viewModel());
    }
}
