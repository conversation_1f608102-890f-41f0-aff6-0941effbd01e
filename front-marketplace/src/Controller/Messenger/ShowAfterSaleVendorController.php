<?php

namespace App\Controller\Messenger;

use App\Controller\BackOfficeVendor\AbstractVendorController;
use App\Security\AfterSaleVoter;
use Marketplace\Component\Discussion\Domain\UseCase\ShowAfterSaleDetails\DTO\ShowAfterSaleDetailsRequest;
use Marketplace\Component\Discussion\Domain\UseCase\ShowAfterSales\DTO\ShowAfterSalesRequest;
use Marketplace\Component\Discussion\Domain\UseCase\ShowAfterSaleVendorUseCase\ShowAfterSaleVendorUseCase;
use Marketplace\Component\Discussion\Presentation\Presenter\ShowAfterSaleVendorPresenter;
use Marketplace\Component\Discussion\Presentation\View\ShowAfterSaleVendorView;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/api/vendor/aftersales/detail/vendor/{afterSaleId}', name: 'api_after_sales_detail_vendor', methods: ['GET'])]
class ShowAfterSaleVendorController extends AbstractVendorController
{
    public function __construct(private ShowAfterSaleVendorView $view, private Security $security)
    {
    }

    public function __invoke(
        int $afterSaleId,
        ShowAfterSaleVendorUseCase $useCase,
        ShowAfterSaleVendorPresenter $presenter,
        SerializerInterface $serializer
    ): Response {
        if (!$this->security->isGranted(AfterSaleVoter::VIEW, $afterSaleId)) {
            throw new AccessDeniedException();
        }
        $AfterSaleRequest = new ShowAfterSaleDetailsRequest(
            $afterSaleId
        );

        $useCase->execute($AfterSaleRequest, $presenter);
        return $this->view->generateView($presenter->viewModel());
    }
}
