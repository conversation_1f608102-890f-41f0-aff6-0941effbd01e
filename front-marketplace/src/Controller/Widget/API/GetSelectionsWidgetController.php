<?php

declare(strict_types=1);

namespace App\Controller\Widget\API;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Telenco\Component\Widget\Domain\UseCase\ShowSelectionsWidgetUseCase\DTO\ShowSelectionsWidgetRequest;
use Telenco\Component\Widget\Domain\UseCase\ShowSelectionsWidgetUseCase\ShowSelectionsWidgetUseCase;
use Telenco\Component\Widget\Presentation\Presenter\ShowSelectionsWidgetPresenter;
use Telenco\Component\Widget\Presentation\View\ShowSelectionsWidgetView;

#[Route('/api/widget/selections/{locale}', name: 'widget_selections', options: ['expose' => true], methods: ['GET'])]
class GetSelectionsWidgetController
{
    public function __construct(private ShowSelectionsWidgetView $selectionsWidgetView)
    {
    }

    public function __invoke(
        string $locale,
        ShowSelectionsWidgetUseCase $useCase,
        ShowSelectionsWidgetPresenter $presenter
    ): Response {
        $useCase->execute(new ShowSelectionsWidgetRequest($locale), $presenter);

        return $this->selectionsWidgetView->generateView($presenter->getViewModel());
    }
}
