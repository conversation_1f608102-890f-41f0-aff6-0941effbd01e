<?php

declare(strict_types=1);

namespace App\Controller\User;

use App\Form\MerchantRegistrationFormType;
use Marketplace\Component\User\Domain\UseCase\MerchantRegistration\DTO\MerchantRegistrationRequest;
use Marketplace\Component\User\Domain\UseCase\MerchantRegistration\MerchantRegistrationUseCase;
use Marketplace\Component\User\Presentation\Presenter\MerchantRegistrationPresenter;
use Marketplace\Component\User\Presentation\View\MerchantRegistrationView;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route(path: '/merchant/register', name: 'merchant_register', methods: ['POST', 'GET'])]
class MerchantRegistrationController extends AbstractController
{
    public function __construct(
        private FormFactoryInterface $formFactory,
        private MerchantRegistrationView $registrationView
    ) {
    }
    public function __invoke(
        Request $request,
        MerchantRegistrationUseCase $useCase,
        MerchantRegistrationPresenter $presenter,
    ): Response {
        $merchantRegistrationRequest = new MerchantRegistrationRequest();
        $form = $this->formFactory
            ->create(MerchantRegistrationFormType::class, $merchantRegistrationRequest)
            ->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $useCase->execute($merchantRegistrationRequest, $presenter);
        }

        return $this->registrationView->generateView($presenter->viewModel($form));
    }
}
