<?php

declare(strict_types=1);

namespace App\Controller\User;

use App\Form\User\ResetPasswordForm;
use Marketplace\Component\User\Domain\UseCase\ResetPassword\DTO\ResetPasswordRequest;
use Marketplace\Component\User\Domain\UseCase\ResetPassword\ResetPasswordUseCase;
use Marketplace\Component\User\Presentation\Presenter\ResetPasswordPresenter;
use Marketplace\Component\User\Presentation\View\ResetPasswordView;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/reset-password/{resetPasswordToken}', name: 'security_reset_password', methods: ['GET', 'POST'])]
final class ResetPasswordController
{
    public function __construct(private FormFactoryInterface $form, private ResetPasswordView $resetPasswordView)
    {
    }

    public function __invoke(
        string $resetPasswordToken,
        ResetPasswordUseCase $useCase,
        ResetPasswordPresenter $presenter,
        Request $request,
    ): Response {
        $resetPasswordRequest = new ResetPasswordRequest(forgottenPasswordToken: $resetPasswordToken);
        $form = $this->form->create(ResetPasswordForm::class, $resetPasswordRequest)->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $useCase->execute($resetPasswordRequest, $presenter);
        }
        return $this->resetPasswordView->generateView($presenter->viewModel($form));
    }
}
