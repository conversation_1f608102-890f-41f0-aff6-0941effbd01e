<?php

namespace App\Controller\User;

use App\Form\User\EditUserForm;
use App\ParamConverter\EditUserRequestParamConverter;
use App\Security\UserVoter;
use App\Service\TelencoBaseViewService;
use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\User\Domain\UseCase\User\EditUser\DTO\EditUserRequest;
use Marketplace\Component\User\Domain\UseCase\User\EditUser\EditUserUseCase;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Marketplace\Component\User\Presentation\Presenter\User\EditUserPresenter;
use Marketplace\Component\User\Presentation\View\User\EditUserView;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('user/user-profile/edit/{id}', name: 'user-profile-edit', methods: ['GET', 'PUT'])]
class EditUserController extends AbstractController
{
    public function __construct(
        private EditUserView $view,
        private FormFactoryInterface $formFactory,
        private TelencoBaseViewService $baseViewService,
    ) {
    }

    /**
     * @param int $id
     * @param User|null $user
     * @param Request $request
     * @param EditUserRequest $editUserRequest
     * @param EditUserUseCase $useCase
     * @param EditUserPresenter $presenter
     * @return Response
     *
     * The Request hydrate by EditUserRequestParamConverter with param Address
     * @see EditUserRequestParamConverter
     */
    public function __invoke(
        int $id,
        ?User $user,
        Request $request,
        EditUserRequest $editUserRequest,
        EditUserUseCase $useCase,
        EditUserPresenter $presenter,
    ): Response {

        $attribute = UserVoter::EDIT;
        $this->denyAccessUnlessGranted($attribute, $user);

        // On force le chargement de la langue pour que tous les boutons soient traduits
        $this->baseViewService->buildBaseViewHeader($request->getLocale());

        $form = $this->formFactory
            ->create(EditUserForm::class, $editUserRequest)
            ->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $useCase->execute($editUserRequest, $presenter);
        }

        return $this->view->generateView($presenter->viewModel($form, $this->baseViewService->buildUserMenuItems('users-list')));
    }
}
