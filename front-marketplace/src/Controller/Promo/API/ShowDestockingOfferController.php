<?php

namespace App\Controller\Promo\API;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\Offer\Domain\UseCase\ShowDestockingOfferUseCase\DTO\ShowDestockingOfferRequest;
use Marketplace\Component\Offer\Domain\UseCase\ShowDestockingOfferUseCase\ShowDestockingOfferUseCase;
use Marketplace\Component\Offer\Presentation\Presenter\ShowDestockingOfferPresenter;
use Marketplace\Component\Offer\Presentation\View\ShowDestockingOfferView;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/api/promo/destocking', name: 'show.destocking.offer.api', options: ['expose' => true], methods: ['POST'])]
class ShowDestockingOfferController extends AbstractController
{
    public function __construct(
        private ShowDestockingOfferView $view,
        private SerializerInterface $serializer
    ) {
    }

    public function __invoke(
        Request $request,
        ShowDestockingOfferUseCase $useCase,
        ShowDestockingOfferPresenter $presenter
    ): Response {
        /** @var ShowDestockingOfferRequest $showRequest */
        $showRequest = $this->serializer->deserialize(
            $request->getContent(),
            ShowDestockingOfferRequest::class,
            'json'
        );
        $useCase->execute($showRequest, $presenter);

        return $this->view->generateView($presenter->getViewModel());
    }
}
