<?php

declare(strict_types=1);

namespace App\Controller\ShopInShop;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Telenco\Component\ShopInShop\Domain\UseCase\ShowShopInShop\DTO\ShowShopInShopRequest;
use Telenco\Component\ShopInShop\Domain\UseCase\ShowShopInShop\ShowShopInShopUseCase;
use Telenco\Component\ShopInShop\Presentation\Presenter\ShowShopInShopPresenter;
use Telenco\Component\ShopInShop\Presentation\View\ShowShopInShopView;

#[Route('shop-in-shop/{id}/show', name: 'shop_in_shop.show', options: ['expose' => true], methods: ['GET'])]
final class ShowShopInShopController
{
    public function __construct(private ShowShopInShopView $view)
    {
    }

    public function __invoke(
        int $id,
        Request $request,
        ShowShopInShopUseCase $useCase,
        ShowShopInShopPresenter $presenter
    ): Response {
        $useCase->execute(new ShowShopInShopRequest($id, $request->getLocale()), $presenter);
        return $this->view->generateView($presenter->viewModel());
    }
}
