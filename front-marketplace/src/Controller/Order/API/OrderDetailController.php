<?php

declare(strict_types=1);

namespace App\Controller\Order\API;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use App\Service\TelencoBaseViewService;
use Marketplace\Component\Order\Domain\UseCase\ShowOrder\DTO\ShowOrderRequest;
use Marketplace\Component\Order\Domain\UseCase\ShowOrder\ShowOrderUseCase;
use Marketplace\Component\Order\Presentation\Presenter\ShowOrderJsonPresenter;
use Marketplace\Component\Order\Presentation\View\ShowOrderJsonView;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/order/detail/{orderId}', name: 'order.detail.api', options: ['expose' => true], methods: ['GET'])]
class OrderDetailController extends AbstractController
{
    public function __construct(
        private ShowOrderJsonView $showOfferView,
        private TelencoBaseViewService $baseViewService,
    ) {
    }

    public function __invoke(int $orderId, ShowOrderUseCase $useCase, ShowOrderJsonPresenter $presenter): Response
    {
        $useCase->execute(new ShowOrderRequest($orderId), $presenter);
        return $this->showOfferView->generateView($presenter->viewModel());
    }
}
