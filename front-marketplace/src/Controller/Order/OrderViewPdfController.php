<?php

namespace App\Controller\Order;

use App\Security\OrderVoter;
use Marketplace\Component\Order\Domain\UseCase\OrderViewPdfUseCase\DTO\OrderViewPdfRequest;
use Marketplace\Component\Order\Domain\UseCase\OrderViewPdfUseCase\OrderViewPdfUseCase;
use Marketplace\Component\Order\Presentation\Presenter\OrderViewPdfPresenter;
use Marketplace\Component\Order\Presentation\View\OrderViewPdfView;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/order/view_pdf/{orderId}', name: 'order_view_pdf', methods: ['GET'])]
class OrderViewPdfController extends AbstractController
{
    public function __construct(
        private OrderViewPdfView $view,
    ) {
    }

    public function __invoke(
        int $orderId,
        OrderViewPdfUseCase $useCase,
        OrderViewPdfPresenter $presenter
    ): Response {
        $attribute = OrderVoter::VIEW;
        $this->denyAccessUnlessGranted($attribute, $orderId);
        $request = new OrderViewPdfRequest($orderId);
        $useCase->execute($request, $presenter);

        return $this->view->generateView($presenter->viewModel());
    }
}
