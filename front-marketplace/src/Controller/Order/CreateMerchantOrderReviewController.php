<?php

declare(strict_types=1);

namespace App\Controller\Order;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Marketplace\Component\Order\Domain\UseCase\CreateMerchantOrderReview\CreateMerchantOrderReviewUseCase;
use Marketplace\Component\Order\Domain\UseCase\CreateMerchantOrderReview\DTO\CreateMerchantOrderReviewRequest;
use Marketplace\Component\Order\Presentation\Presenter\CreateMerchantOrderReviewPresenter;
use Marketplace\Component\Order\Presentation\View\CreateMerchantOrderReviewView;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

#[Route(path: '/merchant-order/review/{merchantOrderId}', name: 'merchant.order.review', options: ['expose' => true], methods: ['POST'])]
#[IsGranted('ROLE_BUYER_STANDARD')]
final class CreateMerchantOrderReviewController extends AbstractController
{
    public function __construct(
        private CreateMerchantOrderReviewView $view,
        private SerializerInterface $serializer,
    ) {
    }

    public function __invoke(
        int $merchantOrderId,
        Request $request,
        CreateMerchantOrderReviewUseCase $useCase,
        CreateMerchantOrderReviewPresenter $presenter
    ): Response {
        /** @var CreateMerchantOrderReviewRequest $createMerchantOrderReviewRequest */
        $createMerchantOrderReviewRequest = $this->serializer->deserialize(
            $request->getContent(),
            CreateMerchantOrderReviewRequest::class,
            'json'
        );
        $createMerchantOrderReviewRequest->merchantOrderId = $merchantOrderId;

        $useCase->execute($createMerchantOrderReviewRequest, $presenter);

        return $this->view->generateJson($presenter->viewModel());
    }
}
