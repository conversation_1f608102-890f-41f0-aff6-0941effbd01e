<?php

namespace App\Event;

use App\Domain\UseCase\ValidateBuyer\DTO\ValidateBuyerRequest;
use Symfony\Contracts\EventDispatcher\Event;

class ValidateBuyerEvent extends Event
{
    /**
     * ValidateBuyerEvent constructor.
     * @param ValidateBuyerRequest $request
     */
    public function __construct(private ValidateBuyerRequest $request)
    {
    }

    /**
     * @return ValidateBuyerRequest
     */
    public function getRequest(): ValidateBuyerRequest
    {
        return $this->request;
    }
}
