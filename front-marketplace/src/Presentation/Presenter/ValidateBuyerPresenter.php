<?php

declare(strict_types=1);

namespace App\Presentation\Presenter;

use App\Domain\Presenter\ValidateBuyerPresenterInterface;
use App\Domain\UseCase\ValidateBuyer\DTO\ValidateBuyerResponse;

/**
 * Class ValidateBuyerPresenter
 * @package Marketplace\Component\User\Presentation\Presenter
 */
class ValidateBuyerPresenter implements ValidateBuyerPresenterInterface
{
    /**
     * @param ValidateBuyerResponse $response
     */
    public function present(ValidateBuyerResponse $response): void
    {
    }
}
