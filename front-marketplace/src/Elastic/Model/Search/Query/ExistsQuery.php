<?php

namespace App\Elastic\Model\Search\Query;

use App\Elastic\Contract\SearchQueryInterface;

class ExistsQuery implements SearchQueryInterface
{
    public function __construct(private string $field)
    {
    }

    ####################################################################################################################
    ### GETTERS AND SETTERS METHODS
    ####################################################################################################################
    public function getField(): string
    {
        return $this->field;
    }

    public function setField(string $field): self
    {
        $this->field = $field;
        return $this;
    }

    ####################################################################################################################
    ### REQUIRED METHODS
    ####################################################################################################################
    public function getQuery(): array
    {
        return [
            'exists' => [
                'field' => $this->field
            ]
        ];
    }
}
