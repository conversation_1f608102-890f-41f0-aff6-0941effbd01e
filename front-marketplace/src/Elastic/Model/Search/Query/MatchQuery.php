<?php

namespace App\Elastic\Model\Search\Query;

use App\Elastic\Contract\SearchQueryInterface;
use App\Elastic\Enum\AnalyzerEnum;

class MatchQuery implements SearchQueryInterface
{
    private ?string $analyzer = null;

    public function __construct(private string $field, private string $value)
    {
    }

    ####################################################################################################################
    ### GETTERS AND SETTERS METHODS
    ####################################################################################################################

    public function getField(): string
    {
        return $this->field;
    }

    public function setField(string $field): self
    {
        $this->field = $field;
        return $this;
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function setValue(string $value): self
    {
        $this->value = $value;
        return $this;
    }

    public function getAnalyzer(): ?string
    {
        return $this->analyzer;
    }

    public function setAnalyzer(?string $analyzer): self
    {
        $this->analyzer = $analyzer;
        return $this;
    }

    ####################################################################################################################
    ### REQUIRED METHODS
    ####################################################################################################################
    public function getQuery(): array
    {
        return [
            'match' => [
                $this->field => [
                    'query' => $this->value,
                    'analyzer' => $this->analyzer ?? AnalyzerEnum::analyserStandard(),
                ]
            ]
        ];
    }
}
