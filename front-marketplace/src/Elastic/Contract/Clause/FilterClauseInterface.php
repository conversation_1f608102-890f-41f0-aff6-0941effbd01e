<?php

namespace App\Elastic\Contract\Clause;

use App\Elastic\Contract\SearchQueryInterface;

interface FilterClauseInterface
{
    /**
     * Method that allows us to add filter to the filter clause.
     *
     * @param SearchQueryInterface $searchQuery the filter to add as search query (bool, term, exists, etc...)
     *
     * @return FilterClauseInterface
     */
    public function addFilter(SearchQueryInterface $searchQuery): self;

    /**
     * Method that returns the search filter clause with conform array syntax.
     *
     * @return array
     */
    public function getFilterClause(): array;
}
