<?php

declare(strict_types=1);

namespace App\DataFixtures;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Persistence\ObjectManager;
use Telenco\Component\Shared\Infrastructure\Entity\Region;

class RegionFixtures extends Fixture implements FixtureGroupInterface
{
    public const REGION_WIDGET_REFERENCE = 'widget-region';
    /**
     * @inheritDoc
     */
    public function load(ObjectManager $manager): void
    {
        $region = new Region();
        $region->setName('France');
        $region->setCode('FR');
        $region->setEnabled(true);
        $manager->persist($region);
        $this->addReference(self::REGION_WIDGET_REFERENCE, $region);

        $manager->flush();
    }

    /**
     * @inheritDoc
     */
    public static function getGroups(): array
    {
        return ['group2'];
    }
}
