<?php

declare(strict_types=1);

namespace App\DataFixtures;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use Marketplace\Component\CleanArchiCore\Domain\Model\Locale;
use Telenco\Component\ShopInShop\Infrastructure\Adapter\Repository\ShopInShopRepository;
use Telenco\Component\ShopInShop\Infrastructure\Entity\ShopInShop;
use Telenco\Component\ShopInShop\Infrastructure\Entity\ShopInShopContent;

final class ShopInShopContentFixtures extends Fixture implements DependentFixtureInterface, FixtureGroupInterface
{
    /**
     * @inheritDoc
     */
    public function load(ObjectManager $manager): void
    {
        /** @var ShopInShopRepository $shopInShopRepository */
        $shopInShopRepository = $manager->getRepository(ShopInShop::class);
        $shopInShops = $shopInShopRepository->findAll();

        foreach ($shopInShops as $shopInShop) {
            for ($i = 1; $i <= 5; $i++) {
                $shopInShopContent = new ShopInShopContent();
                $shopInShopContent->setLocale(Locale::SUPPORTED_LOCALES[array_rand([Locale::SUPPORTED_LOCALES])]);
                $shopInShopContent->setDescription(
                    'Lorem ipsum dolor sit amet. Et voluptate velit quo omnis perspiciatis in nobis necessitatibus et
                optio odio et accusantium quisquam.
                Quisquam dolor qui voluptatem temporibus hic numquam dolorum et dignissimos ratione non
                aliquid sapiente et galisum repellendus!'
                );
                $shopInShopContent->setShopInShop($shopInShop);
                $manager->persist($shopInShopContent);
            }
        }

        $manager->flush();
    }

    public function getDependencies(): array
    {
        return [ShopInShopFixtures::class, RegionFixtures::class];
    }

    /**
     * @inheritDoc
     */
    public static function getGroups(): array
    {
        return ['group2'];
    }
}
