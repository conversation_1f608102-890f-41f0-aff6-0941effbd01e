{% extends 'base.html.twig' %}
{% trans_default_domain "translations" %}
{% block title viewModel.headerTitle %}

{% block content %}
    <div class="container-fluid py-3">
        <h1 class="my-3">{{ viewModel.pageTitle }}</h1>

        <table class="table table-hover w-100">
            <thead class="thead-dark">
                <tr>
                    <th>{{ 'contact_us.list.label.id'|trans }}</th>
                    <th>{{ 'contact_us.list.label.firstname'|trans }}</th>
                    <th>{{ 'contact_us.list.label.lastname'|trans }}</th>
                    <th>{{ 'contact_us.list.label.email'|trans }}</th>
                    <th>{{ 'contact_us.list.label.company'|trans }}</th>
                    <th>{{ 'contact_us.list.label.subject'|trans }}</th>
                    <th>{{ 'contact_us.list.label.created_at'|trans }}</th>
                    <th></th>
                    <th>{{ 'contact_us.list.label.action'|trans }}</th>
                </tr>
            </thead>
            <tbody>
            {% for message in viewModel.paginatedResult %}
                <tr class="{% if message.read == false %}font-weight-bold{% endif %}">
                    <td class="align-middle">{{ message.id }}</td>
                    <td class="align-middle">{{ message.firstname }}</td>
                    <td class="align-middle">{{ message.lastname }}</td>
                    <td class="align-middle">{{ message.email }}</td>
                    <td class="align-middle">{{ message.companyName }}</td>
                    <td class="align-middle">{{ message.subject }}</td>
                    <td class="align-middle">{{ message.createdAt|date }}</td>
                    <td class="align-middle">{% if message.files %}<i class="fas fa-paperclip"></i>{% endif %}</td>
                    <td class="align-middle">
                        <a href="{{ path('reply-contact-us', {'id': message.id}) }}" class="btn btn-success mr-2">{{ 'button.show'|trans }}</a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
        {% include 'partials/_pagination.html.twig' %}
    </div>
{%  endblock %}

