{% extends 'base.html.twig' %}
{% trans_default_domain "translations" %}
{% block title viewModel.headerTitle %}

{% block content %}
    <div class="container mb-5">
        <h1>{{ viewModel.pageTitle }}</h1>

        {{ form_start(viewModel.form) }}

        {{ form_row(viewModel.form.region) }}

        <div class="card">
            <div class="card-header">
                <nav>
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        {% set active = true %}
                        {% for content in viewModel.form.contents %}
                            <li class="nav-item">
                                <a class="nav-link {% if active %}active{% endif %}" data-toggle="tab" href="#tab-{{ content.vars.value.locale}}" role="tab">{{ content.vars.value.locale }}</a>
                            </li>
                            {% set active = false %}
                        {% endfor %}
                    </ul>
                </nav>
            </div>

            <div class="card-body">
                <div class="tab-content">
                    {% set active = true %}

                    {% for content in viewModel.form.contents %}
                        <div class="tab-pane fade {% if active %}show active{% endif %}" data-lang="{{ content.vars.value.locale }}" id="tab-{{ content.vars.value.locale}}">
                            {{ form_row(content.locale) }}
                            {{ form_row(content.title) }}
                            {{ form_row(content.link) }}


                            {% set index = content.logos|length > 0 ? content.logos|last.vars.name +1 : 0 %}
                            <div class="logos"
                                 data-prototype="{{ form_widget(content.logos.vars.prototype)|e('html_attr') }}"
                                 data-index="{{ index }}"
                                 data-locale="{{ content.vars.value.locale }}"
                                 data-type="image"
                            >
                                {% if content.logos is not empty %}
                                    {% for key, logo in content.logos %}
                                        <div class="mt-3">
                                            <div class="list-group-item">{{ include('widget/form/_logo_form.html.twig', {form: logo, required: false, image: viewModel.displayCurrentImageOfLogo(key, content.vars.value.locale).image}) }}</div>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    {% for logo in content.logos %}
                                        <div class="mt-3">
                                            {{ include('widget/form/_logo_form.html.twig', {form: logo, required: true}) }}
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            </div>

                            <button type="button" class="btn-new btn btn-success mt-3 font-weight-bold" data-list-selector="#tab-{{ content.vars.value.locale}} .logos"><i class="fas fa-plus"></i> {{ 'button.add_logo'|trans }}</button>
                        </div>
                        {% set active = false %}
                    {% endfor %}
                </div>
            </div>
        </div>
        {{ form_widget(viewModel.form._token) }}

        {{ form_row(viewModel.form.submit) }}

        {{ form_end(viewModel.form, {'render_rest': false}) }}
    </div>
{% endblock %}
