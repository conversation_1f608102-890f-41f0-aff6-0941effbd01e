{% extends 'base.html.twig' %}
{% trans_default_domain "translations" %}
{% block content %}
<div class="container pt-5 pb-5">
    <p>
        <a href="{{ path('buyer.create') }}" class="btn btn-outline-info" title="{{ 'buyer_create.button'|trans }}">{{ 'buyer_create.button'|trans }}</a>
    </p>
    <div class="container">
        <table class="table table-hover">
            <thead>
            <tr class="color-header">
                {# sorting of properties based on query components #}
                <th>{{ 'back.commons.id'|trans }}</th>
                <th>{{ 'back.buyer.list.name'|trans }}</th>
                <th>{{ 'back.buyer.list.identification'|trans }}</th>
                <th>{{ 'back.buyer.list.country'|trans }}</th>
                <th>{{ 'back.buyer.list.status'|trans }}</th>
                <th>{{ 'back.buyer.list.creationDate'|trans }}</th>
                <th>{{ 'back.commons.actions'|trans }}</th>
            </tr>
            <tr class="color-header">
                <form action="{{ path('buyers_list')}}" method="get" name="filter_buyer">
                    <th></th>
                    <th><input class="form-control" type="text" name="filteredName" id="filteredName" value="{{ viewModel.filterParameters["filteredName"] }}"></th>
                    <th><input class="form-control" type="text" name="filteredVat" id="filteredVat" value="{{ viewModel.filterParameters["filteredVat"] }}"></th>
                    <th><input class="form-control" type="text" name="filteredCountry" id="filteredCountry" value="{{ viewModel.filterParameters["filteredCountry"] }}"></th>
                    <th>
                        <select class="form-control" name="filteredStatus" id="filteredStatus" aria-label="Status filter">
                            <option value=""></option>
                            {% for key, choice in viewModel.statusChoices %}
                                <option
                                    value="{{ key }}" {{ viewModel.filterParameters["filteredStatus"] == key ? 'selected' : '' }}>{{ choice }}</option>
                            {% endfor %}
                        </select>
                    </th>

                    <th><input type="text" class="form-control" name="filteredCreationDate" id="filteredCreationDate" value="{{ viewModel.filterParameters["filteredCreationDate"] }}"></th>
                    <th>
                        <button class="btn btn-outline-success" type="submit">Filtrer</button>
                        <a class="btn btn-outline-blue" href="{{ path('buyers_list') }}">RAZ</a>
                    </th>
                </form>
            </tr>
            </thead>
            <tbody>
            {% if viewModel.paginatedResult is empty %}
                <tr>
                    <td colspan="7" class="alert-danger align-center">{{ 'search.result.empty'|trans }}</td>
                </tr>
            {% endif %}
            {% for buyer in viewModel.paginatedResult %}
                {% if buyer.status != "deleted" %}
                <tr {% if loop.index is odd %}class="color"{% endif %}>
                    <td><a href="{{ path('buyer_show', {id: buyer.id}) }}">{{ buyer.id }}</a></td>
                    <td>{{ buyer.name }}</td>
                    <td>{{ buyer.vatNumber }}</td>
                    <td>{{ buyer.country }}</td>
                    <td>{{ buyer.status }}</td>
                    <td>{{ buyer.createdAt}}</td>
                    <td>
                        <div class="d-flex">
                            <a href="{{ path('operator_buyer_edit', {id: buyer.id}) }}" class="btn btn-success mr-2">Edit</a>
                            <form action="{{ path('buyer_delete', { id: buyer.id }) }}" method="post" onsubmit="return confirm('{{ 'buyer_delete.confirm_message'|trans }}')">
                                <input type="hidden" name="_method" value="DELETE">
                                <input type="hidden" name="token" value="{{ csrf_token('delete-buyer') }}"/>
                                <button class="btn btn-danger" type="submit">{{ 'buyer_delete.button'|trans }}</button>
                            </form>
                        </div>
                        {% endif %}
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
        {% include 'partials/_pagination.html.twig' %}
    </div>
</div>
{% endblock %}
