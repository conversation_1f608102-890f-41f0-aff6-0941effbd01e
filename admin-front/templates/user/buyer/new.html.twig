{% extends 'base.html.twig' %}
{% trans_default_domain "translations" %}
{% block javascripts %}
    {{ parent() }}
{% endblock %}
{% block content %}

<div class="container mb-3">
    <div class="container-fluid">
        <h1>{{ 'buyer_creation.title'|trans }}</h1>
    </div>

    <div class="container-fluid mb-5">

        {% for message in app.flashes('success') %}
            <div class="alert alert-success">
                {{ message }}
            </div>
        {% endfor %}
        {% for message in app.flashes('error') %}
            <div class="alert alert-danger">
                {{ message }}
            </div>
        {% endfor %}
        {{ form_start(viewModel.form) }}

        <!-- Company Bloc -->
        <div class="row bg-green-10 mb-4">
            <div class="py-3 col">
                <h2 class="p-0 m-0 h4 font-montserrat">{{ 'buyer_creation.company_info'|trans }}</h2>
            </div>
        </div>
        <div class="row">
           <div class="col-4">{{ form_row(viewModel.form.company.name) }}</div>
            <div class="col-1"></div>
           <div class="col-4">{{ form_row(viewModel.form.company.country) }}</div>
            <div class="col-1"></div>
           <div class="col-4">{{ form_row(viewModel.form.company.vatNumber) }}</div>
            <div class="col-1"></div>
            <div class="col-4">{{ form_row(viewModel.form.company.identification) }}</div>
            <div class="col-1"></div>
            <div class="col-4">{{ form_row(viewModel.form.company.externalId) }}</div>
        </div>

        <!-- Main User Bloc -->
        <div class="row bg-green-10 mb-4 mt-2">
            <div class="py-3 col">
                <h3 class="p-0 m-0 h4 font-montserrat">{{ 'buyer_creation.main_user'|trans }}</h3>
            </div>
        </div>
        <div class="row">
            <div class="col-12"> {{ form_row(viewModel.form.civility) }}</div>
            <div class="col-4"> {{ form_row(viewModel.form.lastname) }}</div>
            <div class="col-1"></div>
            <div class="col-4"> {{ form_row(viewModel.form.firstname) }}</div>
            <div class="col-1"></div>
            <div class="col-4"> {{ form_row(viewModel.form.phone) }}</div>
            <div class="col-1"></div>
            <div class="col-4"> {{ form_row(viewModel.form.email) }}</div>
            <div class="col-1"></div>

        </div>

        <!-- Accounting Bloc -->
        <div class="row bg-green-10 mb-4 mt-2">
            <div class="py-3 col">
                <h3 class="p-0 m-0 h4 font-montserrat">{{ 'buyer_creation.accounting_contact'|trans }}</h3>
            </div>
        </div>
        <div class="row">
            <div class="col-4"> {{ form_row(viewModel.form.company.accountingEmail) }}</div>
            <div class="col-1"></div>
            <div class="col-4">{{ form_row(viewModel.form.company.accountingPhone) }}</div>
        </div>


        <div class="row justify-content-end">
            <div class="col d-flex justify-content-end">
                <button type="submit" class="btn btn-primary d-flex align-items-center">
                    <i class="icon-big-arrow circle-icon-ice circle-icon-inline text-black mr-3"></i>
                    {{ 'form.button.create'|trans }}
                </button>
            </div>
            <div class="col-3"></div>
        </div>
        {{ form_end(viewModel.form) }}
    </div>
</div>
{% endblock %}
