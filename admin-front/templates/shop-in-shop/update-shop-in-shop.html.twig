{% extends 'base.html.twig' %}
{% trans_default_domain "translations" %}
{% block title viewModel.headerTitle %}
{% block javascripts %}
    {{ parent() }}
<script type="text/javascript">
    let routes = {
        'getRegion': '{{ path('api.regions.show') }}',
    };
</script>
    {{ encore_entry_script_tags('region') }}
{% endblock %}
{% block content %}

    <h1>{{ viewModel.pageTitle }}</h1>

    {{ form_start(viewModel.form) }}
    <div class="row">
        <div class="col-6">{{ form_row(viewModel.form.name) }}</div>
    </div>
    <div class="row">
        <div class="col-4">{{ form_row(viewModel.form.imagePageBrand) }}</div>
        <div class="col-4">{{ form_row(viewModel.form.imageBrand) }}</div>
        <div class="col-4">{{ form_row(viewModel.form.imageTop) }}</div>
    </div>
    <div class="row">
        <div class="col-6">{{ form_row(viewModel.form.merchantDistantId) }}</div>
        <div class="col-6">{{ form_row(viewModel.form.brandDistantId) }}</div>
    </div>
    <div class="row">
        <div class="col-6">{{ form_row(viewModel.form.regions) }}</div>
    </div>
    <div class="card">
        <div class="card-header">
            <nav>
                <ul class="nav nav-tabs card-header-tabs" role="tablist">
                    {% set active = true %}
                    {% for content in viewModel.form.contents %}
                        <li class="nav-item">
                            <a class="nav-link {% if active %}active{% endif %}" data-toggle="tab" href="#tab-{{ content.vars.value.locale}}" role="tab">{{ content.vars.value.locale }}</a>
                        </li>
                        {% set active = false %}
                    {% endfor %}
                </ul>
            </nav>
        </div>

        <div class="card-body">
            <div class="tab-content">
                {% set active = true %}
                {% for content in viewModel.form.contents %}
                    <div class="tab-pane fade {% if active %}show active{% endif %}" data-lang="{{ content.vars.value.locale }}" id="tab-{{ content.vars.value.locale}}">
                        {{ form_row(content.locale) }}
                        {{ form_row(content.description) }}
                    </div>
                    {% set active = false %}
                {% endfor %}
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 mt-4">
            <button type="submit" class="btn btn-primary">{{ 'form.button.save'|trans }}</button>
            {{ form_end(viewModel.form) }}
        </div>
    </div>
{% endblock %}
