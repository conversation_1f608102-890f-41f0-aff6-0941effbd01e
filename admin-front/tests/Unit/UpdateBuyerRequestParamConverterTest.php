<?php

declare(strict_types=1);

namespace App\Tests\Unit;

use App\ParamConverter\UpdateBuyerRequestParamConverter;
use Generator;
use Marketplace\Component\TermPayment\Domain\Port\Service\CalculateAmountServiceInterface;
use Marketplace\Component\User\Domain\Port\Repository\CompanyRepositoryInterface;
use Marketplace\Component\User\Domain\UseCase\UpdateBuyer\DTO\UpdateBuyerRequest;
use PHPUnit\Framework\TestCase;
use Prophecy\Prophecy\ObjectProphecy;
use Prophecy\Prophet;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;

/**
 * @coversDefaultClass \App\ParamConverter\UpdateBuyerRequestParamConverter
 */
final class UpdateBuyerRequestParamConverterTest extends TestCase
{
    private ObjectProphecy|ParamConverter $configuration;

    private UpdateBuyerRequestParamConverter $paramConverter;

    protected function setUp(): void
    {
        parent::setUp();

        $prophet = new Prophet();

        $this->configuration = $prophet->prophesize(ParamConverter::class);

        /** @var CompanyRepositoryInterface $companyRepository */
        $companyRepository = $prophet->prophesize(CompanyRepositoryInterface::class)->reveal();
        $termPaymentCalculateAmountService = $prophet->prophesize(CalculateAmountServiceInterface::class)->reveal();
        $this->paramConverter = new UpdateBuyerRequestParamConverter($companyRepository, $termPaymentCalculateAmountService);
    }

    /**
     * @covers ::supports
     * @dataProvider requestClassDataProvider
     */
    public function testSupport(string $class, bool $expected): void
    {
        $this->configuration->getClass()->willReturn($class);

        $this->assertEquals($expected, $this->paramConverter->supports($this->configuration->reveal()));
    }

    public function requestClassDataProvider(): Generator
    {
        yield [UpdateBuyerRequest::class, true];
        yield [\stdClass::class, false];
    }
}
