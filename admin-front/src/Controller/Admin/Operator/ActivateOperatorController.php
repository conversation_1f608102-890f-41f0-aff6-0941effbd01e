<?php

declare(strict_types=1);

namespace App\Controller\Admin\Operator;

use Marketplace\Component\User\Domain\UseCase\ActivateOperator\ActivateOperatorUseCase;
use Marketplace\Component\User\Domain\UseCase\ActivateOperator\DTO\ActivateOperatorRequest;
use Marketplace\Component\User\Presentation\Presenter\ActivateOperatorPresenter;
use Marketplace\Component\User\Presentation\View\ActivateOperatorView;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;

#[Route('/operator/activate/{id}', name: 'operator_activate', methods: ['POST'])]
#[Security("is_granted('ROLE_OPERATOR')")]
final class ActivateOperatorController
{
    public function __construct(
        private ActivateOperatorView $view,
        private CsrfTokenManagerInterface $csrfTokenManager
    ) {
    }

    public function __invoke(
        int $id,
        ActivateOperatorUseCase $useCase,
        ActivateOperatorPresenter $presenter,
        Request $request
    ): Response {

        $submittedToken = (string)$request->request->get('token');
        if ($this->csrfTokenManager->isTokenValid(new CsrfToken('activate-operator', $submittedToken))) {
            $useCase->execute(new ActivateOperatorRequest($id), $presenter);
        }

        return $this->view->generateView($presenter->viewModel());
    }
}
