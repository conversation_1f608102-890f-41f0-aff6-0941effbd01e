<?php

declare(strict_types=1);

namespace App\Controller\Admin\Widget;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Telenco\Component\Widget\Domain\UseCase\OperatorWidgetsShow\OperatorWidgetsShowUseCase;
use Telenco\Component\Widget\Presentation\Presenter\OperatorWidgetsShowPresenter;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Telenco\Component\Widget\Presentation\View\WidgetsShowView;

#[Route('/home-widgets', name: 'home.widgets', methods: ['GET'])]
#[Security("is_granted('ROLE_OPERATOR')")]
final class ShowWidgetsController extends AbstractController
{
    public function __invoke(): Response
    {

        return $this->render('widget/home-widgets.html.twig');
    }
}
