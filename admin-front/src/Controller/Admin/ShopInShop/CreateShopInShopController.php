<?php

declare(strict_types=1);

namespace App\Controller\Admin\ShopInShop;

use App\Form\ShopInShop\CreateShopInShopFormType;
use Exception;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Telenco\Component\ShopInShop\Domain\UseCase\CreateShopInShopUseCase\CreateShopInShopUseCase;
use Telenco\Component\ShopInShop\Domain\UseCase\CreateShopInShopUseCase\DTO\CreateShopInShopRequest;
use Telenco\Component\ShopInShop\Presentation\Presenter\CreateShopInShopPresenter;
use Telenco\Component\ShopInShop\Presentation\View\CreateShopInShopView;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;

#[Route('shop-in-shop/create', name: 'shopinshop.create', methods: ['GET', 'POST'])]
#[Security("is_granted('ROLE_OPERATOR')")]
final class CreateShopInShopController
{
    public function __construct(
        private FormFactoryInterface $formFactory,
        private CreateShopInShopView $createShopInShopView
    ) {
    }

    /**
     * @param Request $request
     * @param CreateShopInShopUseCase $useCase
     * @param CreateShopInShopPresenter $presenter
     * @return Response
     * @throws LoaderError
     * @throws RuntimeError
     * @throws SyntaxError
     * @throws Exception
     */
    public function __invoke(
        Request $request,
        CreateShopInShopUseCase $useCase,
        CreateShopInShopPresenter $presenter
    ): Response {
        $createShopInShopRequest = new CreateShopInShopRequest();
        $form = $this->formFactory
            ->create(CreateShopInShopFormType::class, $createShopInShopRequest)
            ->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $useCase->execute($createShopInShopRequest, $presenter);
        }

        return $this->createShopInShopView->generateView($presenter->viewModel($form));
    }
}
