<?php

declare(strict_types=1);

namespace App\Controller\Admin\Cookie;

use Marketplace\Component\CleanArchiCore\Presentation\Controller\AbstractController;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Telenco\Component\Shared\Domain\UseCase\ShowCookiesStatsUseCase\ShowCookiesStatsUseCase;
use Telenco\Component\Shared\Presentation\Presenter\ShowCookiesStatsPresenter;
use Telenco\Component\Shared\Presentation\View\ShowCookiesStatsView;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;

#[Security("is_granted('ROLE_OPERATOR')")]
#[Route(path: '/operator/cookies-stats', name: 'cookies-stats', methods: ['GET'])]
final class ShowCookiesStatsController extends AbstractController
{
    public function __construct(
        private readonly ShowCookiesStatsView $view
    ) {
    }

    /**
     * @throws SyntaxError
     * @throws RuntimeError
     * @throws LoaderError
     */
    public function __invoke(
        ShowCookiesStatsUseCase $useCase,
        ShowCookiesStatsPresenter $presenter
    ): Response {
        $useCase->execute($presenter);

        return $this->view->generateView($presenter->viewModel());
    }
}
