<?php

declare(strict_types=1);

namespace App\Controller\Admin\StaticPage;

use App\Form\StaticPage\CreateStaticPageForm;
use App\ParamConverter\CreateStaticPageRequestParamConverter;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Telenco\Component\StaticPage\Domain\UseCase\CreateStaticPageUseCase\CreateStaticPageUseCase;
use Telenco\Component\StaticPage\Domain\UseCase\CreateStaticPageUseCase\DTO\CreateStaticPageRequest;
use Telenco\Component\StaticPage\Presentation\Presenter\CreateStaticPagePresenter;
use Telenco\Component\StaticPage\Presentation\View\CreateStaticPageView;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;

#[Route('/static-page/create', name: 'static-page.create', methods: ['GET', 'POST'])]
#[Security("is_granted('ROLE_OPERATOR')")]
final class CreateStaticPageController
{
    public function __construct(
        private FormFactoryInterface $formFactory,
        private CreateStaticPageView $createStaticPageView
    ) {
    }

    /**
     * @param CreateStaticPageRequest $createStaticPageRequest
     * @param Request $request
     * @param CreateStaticPageUseCase $useCase
     * @param CreateStaticPagePresenter $presenter
     * @return Response
     * @throws LoaderError
     * @throws RuntimeError
     * @throws SyntaxError
     * @see CreateStaticPageRequestParamConverter
     */
    public function __invoke(
        CreateStaticPageRequest $createStaticPageRequest,
        Request $request,
        CreateStaticPageUseCase $useCase,
        CreateStaticPagePresenter $presenter
    ): Response {
        $form = $this->formFactory
            ->create(CreateStaticPageForm::class, $createStaticPageRequest)
            ->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $useCase->execute($createStaticPageRequest, $presenter);
        }

        return $this->createStaticPageView->generateView($presenter->viewModel($form));
    }
}
