<?php

namespace App\Controller\Admin;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Annotation\Route;

#[Route(path: '/', name: 'home')]
class DashboardController extends AbstractController
{
    public function __invoke(): RedirectResponse
    {
        return $this->redirectToRoute('operator_list');
    }
}
