<?php

declare(strict_types=1);

namespace App\Controller\Admin\News;

use Exception;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Telenco\Component\News\Domain\UseCase\DeleteNewsUseCase\DeleteNewsUseCase;
use Telenco\Component\News\Domain\UseCase\DeleteNewsUseCase\DTO\DeleteNewsRequest;
use Telenco\Component\News\Presentation\Presenter\DeleteNewsPresenter;
use Telenco\Component\News\Presentation\View\DeleteNewsView;

#[Route('/news/delete/{id}', name: 'news.delete', methods: ['DELETE'])]
#[Security("is_granted('ROLE_OPERATOR')")]
final class DeleteNewsController
{
    public function __construct(
        private DeleteNewsView $deleteNewsView,
        private CsrfTokenManagerInterface $csrfTokenManager
    ) {
    }

    /**
     * @throws Exception
     */
    public function __invoke(
        int $id,
        Request $request,
        DeleteNewsUseCase $useCase,
        DeleteNewsPresenter $presenter
    ): Response {
        $submittedToken = (string)$request->request->get('token');
        if ($this->csrfTokenManager->isTokenValid(new CsrfToken('delete-news', $submittedToken))) {
            $useCase->execute(new DeleteNewsRequest($id), $presenter);
        }

        return $this->deleteNewsView->generateView($presenter->viewModel());
    }
}
