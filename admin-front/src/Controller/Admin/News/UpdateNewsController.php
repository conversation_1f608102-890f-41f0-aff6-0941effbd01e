<?php

declare(strict_types=1);

namespace App\Controller\Admin\News;

use App\Form\News\UpdateNewsFormType;
use App\ParamConverter\UpdateNewsRequestParamConverter;
use Exception;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Telenco\Component\News\Domain\UseCase\UpdateNewsUseCase\DTO\UpdateNewsRequest;
use Telenco\Component\News\Domain\UseCase\UpdateNewsUseCase\UpdateNewsUseCase;
use Telenco\Component\News\Presentation\Presenter\UpdateNewsPresenter;
use Telenco\Component\News\Presentation\View\UpdateNewsView;

#[Route('/news/update/{id}', name: 'news.update', methods: ['GET', 'POST'])]
#[Security("is_granted('ROLE_OPERATOR')")]
final class UpdateNewsController
{
    public function __construct(
        private FormFactoryInterface $formFactory,
        private UpdateNewsView $createNewsView
    ) {
    }

    /**
     * @throws Exception
     * @see UpdateNewsRequestParamConverter
     */
    public function __invoke(
        UpdateNewsRequest $updateNewsRequest,
        Request $request,
        UpdateNewsUseCase $useCase,
        UpdateNewsPresenter $presenter
    ): Response {
        $form = $this->formFactory
            ->create(UpdateNewsFormType::class, $updateNewsRequest)
            ->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $useCase->execute($updateNewsRequest, $presenter);
        }

        return $this->createNewsView->generateView($presenter->viewModel($form));
    }
}
