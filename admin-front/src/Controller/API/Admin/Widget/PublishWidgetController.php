<?php

declare(strict_types=1);

namespace App\Controller\API\Admin\Widget;

use App\ParamConverter\PublishWidgetRequestParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Telenco\Component\Widget\Domain\UseCase\OperatorWidgetPublish\DTO\OperatorWidgetPublishRequest;
use Telenco\Component\Widget\Domain\UseCase\OperatorWidgetPublish\OperatorWidgetPublishUseCase;
use Telenco\Component\Widget\Presentation\Presenter\OperatorWidgetPublishPresenter;
use Telenco\Component\Widget\Presentation\View\OperatorWidgetPublishView;

#[Route('/api/widget/publish', name: 'api.widget.publish', methods: ['PATCH'])]
#[Security("is_granted('ROLE_OPERATOR')")]
final class PublishWidgetController
{
    public function __construct(
        private OperatorWidgetPublishView $view,
    ) {
    }

    /**
     * @see PublishWidgetRequestParamConverter
     */
    public function __invoke(
        Request $request,
        SerializerInterface $serializer,
        OperatorWidgetPublishUseCase $useCase,
        OperatorWidgetPublishPresenter $presenter,
        OperatorWidgetPublishRequest $publishRequest,
    ): Response {
        $useCase->execute($publishRequest, $presenter);
        return $this->view->generateView($presenter->viewModel());
    }
}
