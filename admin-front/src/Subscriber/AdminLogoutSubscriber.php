<?php

declare(strict_types=1);

namespace App\Subscriber;

use Marketplace\Component\User\Infrastructure\Entity\User;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Security;

class AdminLogoutSubscriber implements EventSubscriberInterface
{
    /**
     * LogoutSubscriber constructor.
     * @param Security $security
     * @param RouterInterface $router
     */
    public function __construct(
        private readonly Security $security,
        private readonly RouterInterface $router
    ) {
    }

    public static function getSubscribedEvents()
    {
        return [
            RequestEvent::class => 'onKernelRequest'
        ];
    }

    public function onKernelRequest(RequestEvent $event)
    {
        $user = $this->security->getUser();
        if (!$user instanceof User) {
            return;
        }

        if (!$user->isEnabled()) {
            $event->setResponse(new RedirectResponse($this->router->generate('logout')));
        }
    }
}
