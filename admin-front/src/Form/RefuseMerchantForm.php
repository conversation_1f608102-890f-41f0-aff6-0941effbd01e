<?php

declare(strict_types=1);

namespace App\Form;

use Marketplace\Component\User\Domain\UseCase\RefuseMerchant\DTO\RefuseMerchantRequest;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class RefuseMerchantForm extends AbstractType
{
    /**
     * @inheritDoc
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->add('message', TextType::class, ['label' => 'refuse_merchant.message']);
    }

    /**
     * @inheritDoc
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('data_class', RefuseMerchantRequest::class);
    }
}
