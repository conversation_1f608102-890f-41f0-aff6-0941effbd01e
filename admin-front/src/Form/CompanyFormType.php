<?php

declare(strict_types=1);

namespace App\Form;

use Marketplace\Component\User\Domain\Model\Company;
use Marketplace\Component\User\Domain\Model\Country as DomainCountry;
use Marketplace\Component\User\Domain\Port\Repository\CountryRepositoryInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CompanyFormType extends AbstractType
{
    public function __construct(private CountryRepositoryInterface $countryRepository)
    {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'buyer_creation.form.company.name',
                'translation_domain' => 'translations',
                'attr' => ['placeholder' => 'buyer_creation.form.company.name'],
            ])
            ->add('country', ChoiceType::class, [
                'label' => 'buyer_creation.form.country',
                'placeholder' => 'buyer_creation.form.country_placeholder',
                'translation_domain' => 'translations',
                'attr' => ['class' => 'custom-select'],
                'choices' => $this->countryRepository->getCountries(['buyer' => true]),
                'choice_label' => fn (DomainCountry $country) => $country->getCode(),
            ])
            ->add(
                'vatNumber',
                TextType::class,
                [
                    'label' => 'buyer_creation.form.company.vatNumber',
                    'translation_domain' => 'translations',
                    'attr' => ['placeholder' => 'buyer_creation.form.company.vatNumber'],
                ]
            )
            ->add('identification', TextType::class, [
                'label' => 'buyer_creation.form.company.identification',
                'translation_domain' => 'translations',
                'required' => false,
                'attr' => ['placeholder' => 'buyer_creation.form.company.identification_placeholder'],
            ])
            ->add('externalId', TextType::class, [
                'label' => 'buyer_registration.form.externalId',
                'translation_domain' => 'translations',
                'required' => false,
                'attr' => ['placeholder' => 'buyer_creation.form.company.identification_placeholder'],
            ])
            ->add('accountingEmail', EmailType::class, [
                'label' => 'buyer_creation.form.accounting_email.label',
                'translation_domain' => 'translations',
                'attr' => ['placeholder' => 'buyer_creation.form.accounting_email.placeholder'],
            ])
            ->add('accountingPhone', TextType::class, [
                'label' => 'buyer_creation.form.accounting_phone.label',
                'attr' => ['placeholder' => 'buyer_creation.form.accounting_phone.placeholder'],
                'required' => false,
                'translation_domain' => 'translations'
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Company::class,
        ]);
    }
}
