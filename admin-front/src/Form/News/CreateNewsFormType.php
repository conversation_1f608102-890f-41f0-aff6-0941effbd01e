<?php

namespace App\Form\News;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Telenco\Component\News\Domain\UseCase\CreateNewsUseCase\DTO\CreateNewsRequest;
use Telenco\Component\Shared\Domain\Port\Repository\RegionRepositoryInterface;
use Telenco\Component\Shared\Domain\Status\Status;

class CreateNewsFormType extends AbstractType
{
    public function __construct(private RegionRepositoryInterface $regionRepository)
    {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('slug', TextType::class, [
                'label' => 'news.form.create.label.slug',
                'attr' => ['placeholder' => 'news.form.create.label.slug'],
                'translation_domain' => 'translations',
                'required' => false
            ])
            ->add('image', FileType::class, [
                'label' => 'news.form.create.label.image',
                'required' => false,
                'translation_domain' => 'translations'
            ])
            ->add('tag', TextType::class, [
                'label' => 'news.form.create.label.tag',
                'attr' => ['placeholder' => 'news.form.create.label.tag'],
                'translation_domain' => 'translations',
                'required' => false
            ])
            ->add('status', ChoiceType::class, [
                'choices' => [
                    'news.form.create.status_choice.published' => Status::PUBLISHED,
                    'news.form.create.status_choice.incomplete' => Status::INCOMPLETE,
                ],
                'label' => 'news.form.create.label.status',
                'translation_domain' => 'translations'
            ])
            ->add('regions', ChoiceType::class, [
                'choices' => $this->regionRepository->findAllRegions(),
                'choice_value' => 'id',
                'choice_label' => 'name',
                'multiple' => true,
                'required' => false
            ])
            ->add('contents', CollectionType::class, [
                'entry_type' => CreateNewsContentFormType::class,
                'by_reference' => false
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefault('data_class', CreateNewsRequest::class);
    }
}
