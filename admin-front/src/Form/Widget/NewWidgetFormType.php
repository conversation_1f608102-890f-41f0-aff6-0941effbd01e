<?php

namespace App\Form\Widget;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\UseCase\OperatorWidgetCreation\DTO\OperatorWidgetCreationRequest;

class NewWidgetFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'widget.form.new.label.name',
                'attr' => ['placeholder' => 'widget.form.new.label.name'],
                'translation_domain' => 'translations'
            ])
            ->add('type', ChoiceType::class, [
                'label' => 'widget.form.new.label.type',
                'choices' => [
                    'widget.form.new.label.type_' . Widget::TYPE_HIGHLIGHT => Widget::TYPE_HIGHLIGHT,
                    'widget.form.new.label.type_' . Widget::TYPE_PRODUCT_CAROUSEL => Widget::TYPE_PRODUCT_CAROUSEL,
                    'widget.form.new.label.type_' . Widget::TYPE_CAROUSEL => Widget::TYPE_CAROUSEL,
                    'widget.form.new.label.type_' . Widget::TYPE_TOP_BRAND => Widget::TYPE_TOP_BRAND,
                    'widget.form.new.label.type_' . Widget::TYPE_NEWS_WIDGET => Widget::TYPE_NEWS_WIDGET,
                    'widget.form.new.label.type_' . Widget::TYPE_HOME_BANNER => Widget::TYPE_HOME_BANNER,
                    // Remove this widget type for the time being
                    // 'widget.form.new.label.type_' . Widget::TYPE_SELECTIONS => Widget::TYPE_SELECTIONS,
                    'widget.form.new.label.type_' . Widget::TYPE_HIGHLIGHT_LISTING => Widget::TYPE_HIGHLIGHT_LISTING,
                    'widget.form.new.label.type_' . Widget::TYPE_SOFT_HIGHLIGHT => Widget::TYPE_SOFT_HIGHLIGHT,
                    'widget.form.new.label.type_' . Widget::TYPE_PROMO_ITEM => Widget::TYPE_PROMO_ITEM,
                    'widget.form.new.label.type_' . Widget::TYPE_TRIPLE_PROMOS => Widget::TYPE_TRIPLE_PROMOS,
                ],
                'translation_domain' => 'translations',
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('data_class', OperatorWidgetCreationRequest::class);
    }
}
