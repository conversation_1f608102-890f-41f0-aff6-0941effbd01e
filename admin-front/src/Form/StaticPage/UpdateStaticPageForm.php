<?php

namespace App\Form\StaticPage;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Telenco\Component\Shared\Domain\Model\Region;
use Telenco\Component\Shared\Domain\Port\Repository\RegionRepositoryInterface;
use Telenco\Component\Shared\Domain\Status\Status;
use Telenco\Component\StaticPage\Domain\Model\StaticPage;
use Telenco\Component\StaticPage\Domain\Port\Repository\StaticPageRepositoryInterface;
use Telenco\Component\StaticPage\Domain\UseCase\CreateStaticPageUseCase\DTO\StaticPageSlugDTO;
use Telenco\Component\StaticPage\Domain\UseCase\UpdateStaticPageUseCase\DTO\UpdateStaticPageRequest;

class UpdateStaticPageForm extends AbstractType
{
    public function __construct(
        private readonly RegionRepositoryInterface $regionRepository,
        private readonly StaticPageRepositoryInterface $staticPageRepository
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $regions = $this->regionRepository->findAllRegions();
        $parents = [
            new StaticPageSlugDTO(
                staticPage: (new StaticPage())->setId(0),
                slugName: 'static_page.create.parent.none'
            )
        ];
        foreach ($regions as $region) {
            $parents[$region->getName()] = $this->staticPageRepository->findAllFromRegionWithSlugNameAndNotId(
                region: $region->getName(),
                id: $options['data']->getId()
            );
        }
        $builder
            ->add('slug', TextType::class, [
                'label' => 'static_page.create.slug_label',
            ])
            ->add('status', ChoiceType::class, [
                'choices' => [
                    'static_page.create.status_choices.published' => Status::PUBLISHED,
                    'static_page.create.status_choices.incomplete' => Status::INCOMPLETE,
                ],
                'label' => 'static_page.create.status_label'
            ])
            ->add(
                'staticPageRegions',
                ChoiceType::class,
                [
                    'choices' => $regions,
                    'choice_value' => 'id',
                    'choice_label' => 'name',
                    'multiple' => true,
                    'label' => 'static_page.create.regions_label'
                ]
            )
            ->add(
                child: 'parent',
                type: ChoiceType::class,
                options: [
                    'choices' => $parents,
                    'choice_value' => 'staticPage.id',
                    'choice_label' => 'slugName',
                    'label' => 'static_page.create.parent'
                ]
            )
            ->add(
                'staticPageContents',
                CollectionType::class,
                [
                    'entry_type' => UpdateStaticPageContentForm::class,
                    'by_reference' => false,
                    'label' => false
                ]
            )
            ->add(
                'save',
                SubmitType::class
            );
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => UpdateStaticPageRequest::class,
            'translation_domain' => 'translations',
        ]);
    }
}
