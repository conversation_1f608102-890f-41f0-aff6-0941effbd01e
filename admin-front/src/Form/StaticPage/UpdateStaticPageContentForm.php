<?php

namespace App\Form\StaticPage;

use FOS\CKEditorBundle\Form\Type\CKEditorType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Telenco\Component\StaticPage\Domain\UseCase\UpdateStaticPageUseCase\DTO\UpdateStaticPageContentRequest;

class UpdateStaticPageContentForm extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('locale', HiddenType::class)
            ->add(
                'title',
                TextType::class,
                array(
                    'label' => 'static_page.create.contents.title_label'
                )
            )
            ->add(
                'content',
                CKEditorType::class,
                array(
                    'label' => 'static_page.create.contents.body_label',
                    'attr' => ['class' => 'ckeditor-instance']
                )
            )
            ->add(
                'seoTitle',
                TextType::class,
                array(
                    'label' => 'static_page.create.contents.seo_title_label'
                )
            )
            ->add(
                'seoDescription',
                TextType::class,
                array(
                    'label' => 'static_page.create.contents.seo_description_label'
                )
            );
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => UpdateStaticPageContentRequest::class,
            'translation_domain' => 'translations',
        ]);
    }
}
