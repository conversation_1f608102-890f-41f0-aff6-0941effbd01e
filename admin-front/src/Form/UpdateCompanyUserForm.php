<?php

declare(strict_types=1);

namespace App\Form;

use Marketplace\Component\User\Domain\UseCase\UpdateCompanyUser\DTO\UpdateCompanyUserRequest;
use Marketplace\Component\User\Infrastructure\Entity\User;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UpdateCompanyUserForm extends AbstractType
{
    /**
     * @inheritDoc
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('civility', ChoiceType::class, [
                'label' => 'form.label.civility',
                'choices' => [
                    'form.label.mrs' => 'Mme',
                    'form.label.mr' => 'Mr'
                ],
                'expanded' => true,
                'attr' => ['class' => 'd-flex h-form'],
                'label_attr' => ['class' => 'radio-custom']
            ])
            ->add('lastName', TextType::class, [
                'label' => 'form.label.lastname',
                'attr' => ['placeholder' => 'form.label.lastname'],
            ])
            ->add('firstName', TextType::class, [
                'label' => 'form.label.firstname',
                'attr' => ['placeholder' => 'form.label.firstname'],
            ])
            ->add('email', EmailType::class, [
                'label' => 'form.label.email',
                'attr' => ['placeholder' => 'form.label.email'],
            ])
            ->add('phone', TextType::class, [
                'label' => 'form.label.phone',
                'attr' => ['placeholder' => 'form.label.phone'],
            ])
            ->add('role', ChoiceType::class, [
                'label' => 'form.label.role',
                'attr' => ['placeholder' => 'form.label.role'],
                'choices' => [
                    'form.label.roles.admin' => User::ROLE_ADMIN,
                    'form.label.roles.buyer_consult' => User::ROLE_BUYER_CONSULT,
                    'form.label.roles.standard' => User::ROLE_BUYER_STANDARD
                ],
            ])
            ->add('enabled', CheckboxType::class, [
                'label' => 'form.label.enabled',
                'attr' => ['placeholder' => 'form.label.enabled'],
                'required' => false,
            ])
            ->add('submit', SubmitType::class, [
                'label' => 'button.send',
                'attr' => [
                    'class' => 'btn btn-primary',
                ]
            ])
        ;
    }

    /**
     * @inheritDoc
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => UpdateCompanyUserRequest::class,
            'translation_domain' => 'translations',
        ]);
    }
}
