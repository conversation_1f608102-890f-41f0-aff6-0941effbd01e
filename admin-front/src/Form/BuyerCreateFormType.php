<?php

declare(strict_types=1);

namespace App\Form;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetLocaleInterface;
use Marketplace\Component\User\Domain\UseCase\User\AdminCreateBuyer\DTO\CreateBuyerRequest;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class BuyerCreateFormType extends AbstractType
{
    public function __construct(private GetLocaleInterface $getLocale)
    {
    }

    /**
     * @inheritDoc
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $locale = $this->getLocale->getLocale();
        $builder
            ->add('civility', ChoiceType::class, [
                'label' => 'buyer_creation.form.civility.label',
                'choices' => [
                    'buyer_creation.form.civility.miss' => 'Mme',
                    'buyer_creation.form.civility.mister' => 'Mr'
                ],
                'translation_domain' => 'translations',
                'expanded' => true,
                'attr' => ['class' => 'd-flex h-form'],
                'label_attr' => ['class' => 'radio-custom']
            ])
            ->add('lastname', TextType::class, [
                'label' => 'form.label.lastname',
                'attr' => ['placeholder' => 'form.label.lastname'],
                'translation_domain' => 'translations'
            ])
            ->add('firstname', TextType::class, [
                'label' => 'form.label.firstname',
                'attr' => ['placeholder' => 'form.label.firstname'],
                'translation_domain' => 'translations'
            ])
            ->add('email', EmailType::class, [
                'label' => 'form.label.email',
                'attr' => ['placeholder' => 'form.label.email'],
                'translation_domain' => 'translations'
            ])
            ->add('phone', TextType::class, [
                'label' => 'buyer_creation.form.phone_label',
                'attr' => ['placeholder' => 'buyer_creation.form.phone_placeholder'],
                'translation_domain' => 'translations'
            ])
            ->add('company', CompanyFormType::class, [
                'label' => false,
                'attr' => ['placeholder' => 'buyer_creation.form.company'],
                'translation_domain' => 'translations',
            ])
        ;
    }

    /**
     * @inheritDoc
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefault('data_class', CreateBuyerRequest::class);
    }
}
