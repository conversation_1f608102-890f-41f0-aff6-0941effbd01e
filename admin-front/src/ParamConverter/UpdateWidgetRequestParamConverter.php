<?php

declare(strict_types=1);

namespace App\ParamConverter;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use <PERSON>ymfony\Component\HttpFoundation\Request;
use Telenco\Component\Widget\Domain\Port\Repository\WidgetRepositoryInterface;
use Telenco\Component\Widget\Domain\UseCase\OperatorWidgetEdit\DTO\OperatorWidgetEditRequest;
use Telenco\Component\Widget\Domain\Model\Widget;

class UpdateWidgetRequestParamConverter implements ParamConverterInterface
{
    public function __construct(private WidgetRepositoryInterface $widgetRepository)
    {
    }

    /**
     * @inheritDoc
     */
    public function apply(Request $request, ParamConverter $configuration): void
    {
        $id = $request->query->getInt('id');
        $widget = $this->widgetRepository->findById($id);
        if ($widget instanceof Widget) {
            $operatorWidgetEditRequest = new OperatorWidgetEditRequest($id, $widget->getName());
            $request->attributes->set($configuration->getName(), $operatorWidgetEditRequest);
        }
    }

    /**
     * @inheritDoc
     */
    public function supports(ParamConverter $configuration): bool
    {
        return $configuration->getClass() === OperatorWidgetEditRequest::class;
    }
}
