<?php

declare(strict_types=1);

namespace App\ParamConverter;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;
use Telenco\Component\Widget\Domain\Model\Region;
use Telenco\Component\Shared\Infrastructure\Entity\Region as doctrineRegion;
use Telenco\Component\Widget\Infrastructure\Adapter\Repository\RegionRepository;

class UpdateRegionRequestParamConverter implements ParamConverterInterface
{
    public function __construct(
        private RegionRepository $regionRepository,
    ) {
    }

    /**
     * @inheritDoc
     */
    public function apply(Request $request, ParamConverter $configuration): void
    {
        $doctrineRegion = $this->regionRepository->find($request->attributes->getInt('regionId'));
        if ($doctrineRegion instanceof doctrineRegion) {
            $region = (new Region())->setName($doctrineRegion->getName())->setId($doctrineRegion->getId());
            $request->attributes->set($configuration->getName(), $region);
        }
    }

    /**
     * @inheritDoc
     */
    public function supports(ParamConverter $configuration): bool
    {
        return $configuration->getClass() === Region::class;
    }
}
