<?php

declare(strict_types=1);

namespace App\ParamConverter;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Telenco\Component\Shared\Domain\Model\Slug;
use Telenco\Component\Shared\Domain\Port\Repository\SlugRepositoryInterface;
use Telenco\Component\StaticPage\Domain\Exception\InvalidIdException;
use Telenco\Component\StaticPage\Domain\Model\StaticPage;
use Telenco\Component\StaticPage\Domain\Port\Repository\StaticPageContentRepositoryInterface;
use Telenco\Component\StaticPage\Domain\Port\Repository\StaticPageRepositoryInterface;
use Telenco\Component\StaticPage\Domain\UseCase\UpdateStaticPageUseCase\DTO\UpdateStaticPageContentRequest;
use Telenco\Component\StaticPage\Domain\UseCase\UpdateStaticPageUseCase\DTO\UpdateStaticPageRequest;

class UpdateStaticPageRequestParamConverter implements ParamConverterInterface
{
    public function __construct(
        private readonly StaticPageRepositoryInterface $staticPageRepository,
        private readonly StaticPageContentRepositoryInterface $staticPageContentRepository,
        private readonly SlugRepositoryInterface $slugRepository,
    ) {
    }

    /**
     * @inheritDoc
     */
    public function apply(Request $request, ParamConverter $configuration): void
    {
        $id = $request->attributes->getInt('id');
        $staticPage = $this->staticPageRepository->findById($id);
        $slug = $this->slugRepository->findByRefAndRefId(StaticPage::class, $id);
        $slugName = $slug instanceof Slug ? $slug->getName() : null;

        if (!$staticPage instanceof StaticPage) {
            throw new InvalidIdException('static_page.update.errors.id_notfound', Response::HTTP_BAD_REQUEST);
        }

        $parent = null;
        if ($staticPage->getParent() !== null && $staticPage->getParent()->getId() !== null) {
            $parent = $this->staticPageRepository->findByIdWithSlugName($staticPage->getParent()->getId());
        }

        $updateStaticPageRequest = new UpdateStaticPageRequest(
            status: $staticPage->getStatus(),
            id: $id,
            slug: $slugName,
            staticPageRegions: $staticPage->getStaticPageRegions(),
            parent: $parent
        );

        $staticPageContents = $this->staticPageContentRepository->findByStaticPageId($id);
        foreach ($staticPageContents as $staticPageContent) {
            $updateStaticPageRequest->staticPageContents[$staticPageContent->getLocale()->getLocale()] =
                new UpdateStaticPageContentRequest(
                    locale: $staticPageContent->getLocale()->getLocale(),
                    title: $staticPageContent->getTitle(),
                    id: $staticPageContent->getId(),
                    content: $staticPageContent->getText(),
                    seoTitle: $staticPageContent->getSeoTitle(),
                    seoDescription: $staticPageContent->getSeoMetaDescription()
                )
            ;
        }

        $request->attributes->set($configuration->getName(), $updateStaticPageRequest);
    }

    /**
     * @inheritDoc
     */
    public function supports(ParamConverter $configuration): bool
    {
        return $configuration->getClass() === UpdateStaticPageRequest::class;
    }
}
