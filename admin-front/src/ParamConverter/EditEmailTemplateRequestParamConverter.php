<?php

namespace App\ParamConverter;

use Marketplace\Component\Mail\Domain\UseCase\EditEmailUseCase\DTO\EmailContentRequest;
use Marketplace\Component\Mail\Domain\UseCase\EditEmailUseCase\DTO\EditEmailTemplateRequest;
use Marketplace\Component\Mail\Infrastructure\Adapter\Repository\EmailRepository;
use Marketplace\Component\Mail\Infrastructure\Entity\Email;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;
use Marketplace\Component\Mail\Infrastructure\Entity\EmailContent as DoctrineEmailContent;

class EditEmailTemplateRequestParamConverter implements ParamConverterInterface
{
    /**
     * EditEmailTemplateRequestParamConverter constructor.
     * @param EmailRepository $emailRepository
     */
    public function __construct(
        private EmailRepository $emailRepository
    ) {
    }

    /**
     * @param Request $request
     * @param ParamConverter $configuration
     * @return bool
     */
    public function apply(Request $request, ParamConverter $configuration): bool
    {
        $id = $request->attributes->getInt('id');
        $email = $this->emailRepository->find($id);
        if ($email instanceof Email) {
            $editEmailTemplateRequest = new EditEmailTemplateRequest($email->getId());
            $contents = [];
            /**
             * @var DoctrineEmailContent $content
             */
            foreach ($email->getContents() as $content) {
                $DTOcontent = new EmailContentRequest();
                $DTOcontent->setTitle($content->getTitle());
                $DTOcontent->setBody($content->getBody());
                $DTOcontent->setLocale($content->getLocale());
                $DTOcontent->setId($content->getId());
                $contents [] = $DTOcontent;
            }
            $editEmailTemplateRequest->setContents($contents);
            $editEmailTemplateRequest->setSendTestMail(false);
            $request->attributes->set($configuration->getName(), $editEmailTemplateRequest);
        }
        return true;
    }

    /**
     * @param ParamConverter $configuration
     * @return bool
     */
    public function supports(ParamConverter $configuration)
    {
        return $configuration->getClass() === EditEmailTemplateRequest::class;
    }
}
