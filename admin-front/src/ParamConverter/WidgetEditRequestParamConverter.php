<?php

declare(strict_types=1);

namespace App\ParamConverter;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Translation\Exception\NotFoundResourceException;
use Telenco\Component\Widget\Domain\Model\Widget;
use Telenco\Component\Widget\Domain\Request\WidgetContentRequestInterface;
use Telenco\Component\Widget\Domain\ValueObject\WidgetType;
use Telenco\Component\Widget\Infrastructure\Adapter\Repository\WidgetRepository;
use Telenco\Component\Widget\Infrastructure\Strategy\EditRequest\EditRequestContext;

class WidgetEditRequestParamConverter implements ParamConverterInterface
{
    public function __construct(
        private WidgetRepository $widgetRepository,
        private EditRequestContext $editRequestContext,
    ) {
    }

    /**
     * @inheritDoc
     */
    public function apply(Request $request, ParamConverter $configuration): void
    {
        $regionId = $request->attributes->getInt('regionId');
        $widgetId = $request->attributes->getInt('widgetId');

        $widget = $this->widgetRepository->findById($widgetId);
        if (!$widget instanceof Widget) {
            throw new NotFoundResourceException('Widget not found');
        }

        $widgetType = WidgetType::create($widget->getType());
        $widgetContentRequestClass = $widgetType->loadWidgetContentEditRequest();

        /** @var WidgetContentRequestInterface $widgetContentRequest */
        $widgetContentRequest = new $widgetContentRequestClass($regionId, $widgetId);

        $widgetRequest = $this->editRequestContext->process($widgetContentRequest);

        $request->attributes->set($configuration->getName(), $widgetRequest);
    }

    /**
     * @inheritDoc
     */
    public function supports(ParamConverter $configuration): bool
    {
        return $configuration->getClass() === WidgetContentRequestInterface::class;
    }
}
