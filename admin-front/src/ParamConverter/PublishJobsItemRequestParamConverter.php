<?php

declare(strict_types=1);

namespace App\ParamConverter;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Serializer\SerializerInterface;
use Telenco\Component\JobsMenu\Domain\Port\Service\PublishJobsItemServiceInterface;
use Telenco\Component\JobsMenu\Domain\UseCase\PublishJobsItemUseCase\DTO\PublishJobsItemRequest;

class PublishJobsItemRequestParamConverter implements ParamConverterInterface
{
    public function __construct(
        private SerializerInterface $serializer,
        private PublishJobsItemServiceInterface $jobsItemService,
    ) {
    }

    public function apply(Request $request, ParamConverter $configuration): void
    {
        /** @var PublishJobsItemRequest $publishJobsItemRequest */
        $publishJobsItemRequest = $this->serializer
            ->deserialize(
                $request->getContent(),
                PublishJobsItemRequest::class,
                $request->getContentType() ?? 'json'
            );

        $publishJobsItemRequest->setJobsItemsToPublish(
            $this->jobsItemService->arrayToModel($publishJobsItemRequest->getJobsItemsToPublish())
        );
        $request->attributes->set($configuration->getName(), $publishJobsItemRequest);
    }

    public function supports(ParamConverter $configuration): bool
    {
        return $configuration->getClass() === PublishJobsItemRequest::class;
    }
}
