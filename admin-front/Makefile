analyse:
	phpcs && psalm --no-cache

.PHONY: test
test: vendor/autoload.php
	php ./vendor/bin/phpunit --colors --testdox

.PHONY: testc
testc:
	php -dxdebug.mode=coverage ./vendor/bin/phpunit --colors --testdox --coverage-html=./coverage

.PHONY: tw
tw: vendor/autoload.php ## Lance le watcher phpunit
	php ./vendor/bin/phpunit-watcher watch --colors --testdox

vendor/autoload.php: composer.lock
	composer install
	touch ./vendor/autoload.php

fixtures:
	php bin/console doctrine:fixtures:load -n --env=$(env)

database:
	php bin/console doctrine:database:drop --if-exists --force --env=$(env)
	php bin/console doctrine:database:create --env=$(env)
	php bin/console doctrine:schema:update --force --env=$(env)

prepare:
	make database env=$(env)
	make fixtures env=$(env)
