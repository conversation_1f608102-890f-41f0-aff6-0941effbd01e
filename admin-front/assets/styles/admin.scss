html, body {
    padding: 0;
    margin: 0;
    display: flex;
    height: 100vh;
    flex-direction: column;
    font-size: 14px;
    overflow: hidden;
}

#term_payment {
    .row {
        border-style: solid;
        border-color: grey;
        border-width: 0  0   0  1px;
    }
    .row:first-child {
        border-style: solid;
        border-color: grey;
        border-width: 1px  0   0  1px;
    }

    .row > div {
        border-color: grey;
        border-style: solid;
        border-width: 0  1px 1px  0;
    }
}
.grid-striped .row:nth-of-type(odd) {
    background-color: rgba(0,0,0,.05);
}


#admin-body {
    flex: 1;
    overflow: auto;
    display: flex;
    > .side-bar {
        background: $blue;
        width: $sidebar-width;
        overflow: auto;
    }
    > .content-container {
        display: flex;
        flex: 1;
        flex-direction: column;
        > .content {
            flex: 1;
            overflow: auto;
        }
    }
}

.card.is-invalid {
    border-color: $danger;
}


.buyer_create {
    .custom-control-label.required {
        margin-right: 8px;
    }
    .required:not(.custom-control-label)
    {
        &::after {
            content: '*';
        }
    }
}

/* FORM */
form fieldset:not(:last-child) {
    margin-bottom: 2rem;
}
form fieldset legend {
    transform: translateX(-1em);
}
form fieldset legend span {
    display: inline-block;
    padding: .25em 1em;
    background-color: #343a40;
    color: white;
}
