<template>
  <draggable
      :move="checkMove"
      class="dragArea"
      :list="items"
      group="people"
      tag="ul"
      draggable=".draggable-item"
  >
    <div
        v-for="(item, index) in items"
        :key="item.name"
        class="draggable-item"
    >
      <div class="d-flex align-items-center list-group-item">
        <div class="handle">
          <i class="fa fa-align-justify"></i>
        </div>
        <div class="px-3 flex-grow-1 text-truncate" v-b-tooltip :title="item.name">
                <span
                    class="badge badge-info mr-1"
                    v-for="region in item.regions"
                    :key="region.id"
                >
                  {{region.name}}
                </span> {{ item.name }}
        </div>

        <div class="btn-group">
          <button @click="unpublishedItem(item, index, items)" class="btn btn-warning" v-b-tooltip title="Unpublish item">
            <i class="fa fa-eye-slash"></i>
          </button>
          <a :href="updateItemUrl + item.id" class="btn btn-primary" v-b-tooltip title="Update item">
            <i class="fa fa-pen"></i>
          </a>
        </div>
      </div>

      <nested-draggable :items="item.tasks" :update-item-url="updateItemUrl" :unpublished-items="unpublishedItems"/>
    </div>

  </draggable>
</template>
<script>

import draggable from "vuedraggable";

export default {
  props: {
    items: {
      required: true,
      type: Array
    },
    updateItemUrl: {
      required: true,
      type: String
    },
    unpublishedItems: {
      required: true,
      type: Array
    }
  },
  components: {
    draggable
  },
  name: "nested-draggable",
  methods: {
    unpublishedItem: function (item, index, list) {
      this.unpublishedItems.push(item);
      list.splice(index, 1);
    },
    checkMove: function (evt) {
      return true;
    }
  }
};
</script>

