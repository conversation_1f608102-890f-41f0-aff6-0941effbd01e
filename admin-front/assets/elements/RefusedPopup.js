/**
 * @property {String} route
 * @property {HTMLFormElement} form
 */
export default class RefusedPopup extends HTMLElement {

    constructor() {
        super();

        this.handleRefuse = this.handleRefuse.bind(this)
    }

    connectedCallback() {
        const formSelector = this.getAttribute('form') || '#refusedForm'
        const buttonSelector = this.getAttribute('button') || '#refusedButton'

        this.form = this.querySelector(formSelector)
        const button = this.querySelector(buttonSelector)
        this.route = this.form.getAttribute('action')

        button.addEventListener('click', this.handleRefuse)
    }

    /**
     * @param {EventListener} event
     * @returns {Promise<void>}
     */
    async handleRefuse(event) {
        event.preventDefault()

        const messageInput = this.querySelector('#message')
        const errorMessageInput = this.querySelector('#messageError')
        const message = messageInput.value
        const response = await this.callApi(this.route, { message })
        // Bad request status (validation error)
        if (!response.ok) {
            const { message } = await response.json()
            messageInput.classList.add('is-invalid')
            errorMessageInput.innerHTML = this.errorMessage(message)
        } else {
            const { redirectUrl } = await response.json()
            window.location = redirectUrl;
        }
    }

    /**
     * @param {String} route
     * @param {Object} data The data api
     * @returns {Promise<Response>}
     */
    async callApi(route, data = {}) {
        return await fetch(route, {
            method: this.form.getAttribute('method') || 'POST',
            headers: {
                'Content-type': 'application/json',
                Accept: 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(data),
        })
    }

    /**
     * Return markup html for display error message under the field
     *
     * @param {String} message
     * @returns {string}
     */
    errorMessage(message) {
        return `
            <span class="d-block">
                <span class="form-error-icon badge badge-danger text-uppercase">Error</span>
                <span class="form-error-message">${message}</span>
            </span>
        `
    }
}
