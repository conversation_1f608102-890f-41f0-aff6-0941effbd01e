import { Config } from '@stencil/core';
import { sass } from '@stencil/sass';
import { reactOutputTarget } from '@stencil/react-output-target';

export const config: Config = {
  namespace: 'messager',
  plugins: [
    sass()
  ],
  outputTargets: [
    {
      type: 'dist',
      esmLoaderPath: '../loader'
    },
    {
      type: 'dist',
      buildDir: '../../front-marketplace/public/messager'
    },
    {
      type: 'www',
      serviceWorker: null // disable service workers
    }
  ]
};
