<?php

namespace Open\Izberg\Event;

use Open\Izberg\Client\BuyerClient;
use Open\Izberg\Client\VendorClient;

class VendorCallIzbergApiEvent
{
    public function __construct(private VendorClient $vendorClient)
    {
    }

    /**
     * @return VendorClient
     */
    public function getVendorClient(): VendorClient
    {
        return $this->vendorClient;
    }

    /**
     * @param VendorClient $vendorClient
     * @return $this
     */
    public function setVendorClient(VendorClient $vendorClient): self
    {
        $this->vendorClient = $vendorClient;
        return $this;
    }
}
