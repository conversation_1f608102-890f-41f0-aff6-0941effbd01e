<?php

namespace Open\Izberg\Authentication;

use Doctrine\Common\Annotations\AnnotationReader;
use Open\Izberg\IzbergConfiguration;
use Open\Izberg\Model\User;
use Open\Izberg\Service\EscapeSpecialChar;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\HttpClient\ScopingHttpClient;
use Symfony\Component\PropertyInfo\Extractor\PhpDocExtractor;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory;
use Symfony\Component\Serializer\Mapping\Loader\AnnotationLoader;
use Symfony\Component\Serializer\NameConverter\CamelCaseToSnakeCaseNameConverter;
use Symfony\Component\Serializer\Normalizer\ArrayDenormalizer;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

final class Authenticator
{
    private ScopingHttpClient $client;
    private SerializerInterface $serializer;

    /**
     * Authenticator constructor.
     * @param IzbergConfiguration $izbergConfiguration
     */
    public function __construct(
        private IzbergConfiguration $izbergConfiguration
    ) {
        $client = HttpClient::create();
        $this->client = ScopingHttpClient::forBaseUri($client, $this->izbergConfiguration->getApiUrl(), []);

        $this->initSerializer();
    }

    /**
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function authenticate(AccessToken $accessToken): void
    {
        $firstName = $accessToken->getFirstname();
        $lastName = $accessToken->getLastname();
        $email = $accessToken->getEmail();

        [$firstName, $lastName] = array_map(
            fn(string $element) => EscapeSpecialChar::escapeAuthRawElement($element),
            [$firstName, $lastName]
        );

        // Epoch time
        $timestamp = time();
        $authRaw = implode(';', [ $email, $firstName, $lastName, $timestamp]);

        // Encrypt the signature with our secret key
        $auth = hash_hmac('sha1', $authRaw, $this->izbergConfiguration->getIzbergSecretKey());

        // Build query string
        $query = [
            'message_auth' => $auth,
            'application' => $this->izbergConfiguration->getIzbergApplicationNamespace(),
            'timestamp' => $timestamp,
            'is_staff' => 'false',
            'first_name' => $firstName,
            'last_name' => $lastName,
            'email' => $email,
        ];

        $response = $this->client->request(
            'GET',
            '/v1/user/sso',
            [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                    'Accept' => 'application/json',
                ],
                'query' => $query
            ]
        );

        $content = $response->getContent();
        /** @var UserSsoResponse $userSsoResponse */
        $userSsoResponse = $this->serializer->deserialize($content, UserSsoResponse::class, 'json');

        $izbergUserId = $userSsoResponse->getUser()->getId();
        $bearer = $this->generateBearer($userSsoResponse->getUsername(), $userSsoResponse->getAccessToken());

        $accessToken->setBearer($bearer);
        $accessToken->setIzbergUserId($izbergUserId);
        $accessToken->setExpirationDate(new \DateTimeImmutable('+5hours'));
    }

    /**
     * @param string $operatorUsername
     * @param string $operatorAccessToken
     * @return string Bearer to authentify operator
     */
    public function authenticateOperator(string $operatorUsername, string $operatorAccessToken): string
    {
        return $this->generateBearer($operatorUsername, $operatorAccessToken);
    }

    public function fetchCurrentUser(string $bearer): User
    {
        $response = $this->client->request(
            'GET',
            '/v1/user/me',
            [
                'auth_bearer' => $bearer,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ]
            ]
        );

        $content = $response->getContent();
        /** @var User $user */
        $user = $this->serializer->deserialize($content, User::class, 'json');

        return $user;
    }

    public static function escapeAuthRawElement(string $element): string
    {
        return substr(preg_replace('/[^a-z0-9À-ú\s\-]/i', '', $element), 0, 30);
    }

    private function initSerializer()
    {
        $classMetadataFactory = new ClassMetadataFactory(new AnnotationLoader(new AnnotationReader()));
        $objectNormalizer = new ObjectNormalizer(
            $classMetadataFactory,
            new CamelCaseToSnakeCaseNameConverter(),
            null,
            new PhpDocExtractor()
        );
        $arrayDeNormalizer = new ArrayDenormalizer();
        $jsonEncoder = new JsonEncoder();
        $this->serializer = new Serializer([$arrayDeNormalizer, $objectNormalizer], [$jsonEncoder]);
    }

    private function generateBearer(string $username, string $accessToken): string
    {
        return $username . ':' . $accessToken;
    }
}
