<?php

declare(strict_types=1);

namespace Open\Izberg\Api\Discount;

use Open\Izberg\Exception\ApiException;
use Open\Izberg\Model\Discount;

interface DiscountApiInterface
{
    /**
     * @throws ApiException
     **/
    public function createDiscount(
        int $merchantId,
        string $name,
        string $discountCode,
        int $type,
        int $value,
        ?int $quantity = 100,
        ?int $productFamilyId = null
    ): Discount;

    /**
     * @throws ApiException
     **/
    public function activateDiscount(int $discountId): void;
}
