<?php

declare(strict_types=1);

namespace Open\Izberg\Api;

use Open\Izberg\DTO\Request;
use Open\Izberg\Model\CreditNote;

final class CreditNoteApi extends ApiAbstract
{

    public function fetchCreditNote(int $creditNoteId): CreditNote
    {
        $content = $this->get(
            new Request(sprintf('credit_note/%d/', $creditNoteId))
        );



        return $this->serializer->deserialize($content, CreditNote::class, 'json');
    }
}
