<?php

namespace Open\Izberg\Api;

use DateTime;
use Generator;
use Open\Izberg\DTO\Request;
use Open\Izberg\Exception\ApiException;
use Open\Izberg\Exception\NotFoundException;
use Open\Izberg\Model\IzbergResponseOrders;
use Open\Izberg\Model\MerchantOrder;
use Open\Izberg\Model\Order;
use Open\Izberg\Model\ShippingOption;
use Open\Izberg\Service\AsyncActionIdExtractor;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;

class OrderApi extends ApiAbstract
{
    public function fetchOrders(): Generator
    {
        return $this->fetchAll('order/', [], IzbergResponseOrders::class);
    }

    public function fetchAllOrders(DateTime $lastModifiedStart): Generator
    {
        $date = $lastModifiedStart->format("Y-m-d");
        return $this->fetchAll(sprintf('order/?last_modified__gt=%s&status__in=60%%2C80%%2C85%%2C110&limit=100', $date), [], IzbergResponseOrders::class);
    }

    /**
     * @throws ApiException
     */
    public function fetchOrder(int $orderId): Order
    {

        $content = $this->get(
            (new Request(sprintf('order/%d', $orderId)))
                ->setHeader(['Accept' => 'application/json'])
        );
        return $this->serializer->deserialize($content, Order::class, 'json');
    }

    /**
     * @throws ApiException
     * @throws NotFoundException
     */
    public function fetchOrderByExternalId(string $orderExternalId): Order
    {
        $content = $this->get(
            (new Request((sprintf('order/?external_id=%s', $orderExternalId))))
                ->setHeader(['Accept' => 'application/json'])
        );

        /** @var IzbergResponseOrders $izbergResponseOrders */
        $izbergResponseOrders = $this->serializer->deserialize($content, IzbergResponseOrders::class, 'json');
        $orders = $izbergResponseOrders->getObjects();

        $order = current($orders);
        if (!$order instanceof Order) {
            throw new NotFoundException(sprintf('Order not found with order external ID "%s"', $orderExternalId));
        }

        return $order;
    }

    /**
     * @throws ApiException
     */
    public function fetchMerchantOrder(int $merchantOrderId): ?MerchantOrder
    {

        $content = $this->get(
            (new Request(sprintf('merchant_order/%d', $merchantOrderId)))
                ->setHeader(['Accept' => 'application/json'])
        );
        return $this->serializer->deserialize($content, MerchantOrder::class, 'json');
    }

    public function fetchShippingOptionsOfMerchantOrder(int $merchantOrderId): ?ShippingOption
    {
        $content = $this->get(new Request(sprintf('merchant_order/%d/shipping_options/', $merchantOrderId)));

        /** @var ShippingOption $shippingOption */
        $shippingOption = $this->serializer->deserialize($content, ShippingOption::class, 'json');
        return $shippingOption;
    }

    /**
     * @param int $merchantOrderId
     * @return bool
     * @throws ApiException
     */
    public function confirmMerchantOrder(int $merchantOrderId): bool
    {
        $this->post(new Request(sprintf('merchant_order/%d/confirm/', $merchantOrderId)));
        return true;
    }

    /**
     * @param int $merchantOrderId
     * @throws ApiException
     */
    public function processMerchantOrder(int $merchantOrderId)
    {
        $this->post(
            (new Request(sprintf('merchant_order/%d/process/', $merchantOrderId)))
                ->setHeader(['Accept' => 'application/json'])
        );
    }


    /**
     * returns the async-action id or null if nothing has been created
     * @param int $cartId
     * @param string $orderExternalId
     * @return string
     * @throws ApiException
     */
    public function createOrderFromCart(int $cartId, string $orderExternalId): string
    {
        $response = $this->postWithResponse(
            new Request(sprintf('cart/%d/create_order/', $cartId)),
            ['external_id' => $orderExternalId]
        );

        if ($response->getCode() !== Response::HTTP_ACCEPTED) {
            $content = $response->getContent();
            throw new ApiException(
                sprintf(
                    "Izberg create_order failed - http status code: %d, content:%s",
                    $response->getCode(),
                    $content ?? "emptyContent"
                )
            );
        }

        $headers = $response->getHeader();
        $location = $headers['location'][0] ?? null;

        if (!is_string($location)) {
            throw new RuntimeException(
                'Izberg create_order return a 202 without the async-action resource'
            );
        }

        $asyncActionId = AsyncActionIdExtractor::extract($location);

        if (!is_string($asyncActionId)) {
            throw new RuntimeException(
                sprintf('Could not extract the async-action ID from resource %s', $location)
            );
        }

        return $asyncActionId;
    }


    public function cancelOrder($orderId): Order
    {

        $content = $this->post((new Request(sprintf("order/%d/cancel/", $orderId))));
        $order = $this->serializer->deserialize($content, Order::class, 'json');

        if (!$order instanceof Order) {
            throw new ApiException('Failed canceling izberg order', 500);
        }

        return $order;
    }
}
