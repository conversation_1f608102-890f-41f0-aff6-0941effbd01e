<?php

namespace Open\Izberg\Api\Utils;

class IzbergCustomAttributesUtils
{
    public static function createFullAttributeName($attributeName): string
    {
        return sprintf('attributes.%s', $attributeName);
    }

    public function fetchAllAttributes(): array
    {
        $attributes = [];

        $properties = get_object_vars($this);
        foreach ($properties as $attribute) {
            if (is_array($attribute)) {
                $attributes = array_merge($attributes, $attribute);
            }

            if (is_string($attribute)) {
                $attributes[] = $attribute;
            }
        }

        return $attributes;
    }
}
