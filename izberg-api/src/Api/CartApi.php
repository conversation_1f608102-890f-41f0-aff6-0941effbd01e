<?php

namespace Open\Izberg\Api;

use Generator;
use Open\Izberg\Exception\ApiException;
use Open\Izberg\Model\Cart;
use Open\Izberg\Model\CartItem;
use Open\Izberg\Model\IzbergResponseCartItems;
use Open\Izberg\DTO\Request;
use Open\Izberg\Model\ShippingSuggestionResponse;
use Symfony\Component\HttpClient\Exception\ClientException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

final class CartApi extends ApiAbstract implements CartApiInterface
{
    /**
     * @param string $currency
     * @return int
     * @throws ApiException
     */
    public function createCart(string $currency = 'EUR'): int
    {
        $content = $this->post(new Request('cart/'), [
            'currency' => $currency,
        ]);

        /** @var Cart $cart */
        $cart = $this->serializer->deserialize($content, Cart::class, 'json');

        return $cart->getId();
    }

    /**
     * @param int $cartId
     * @param int $offerId
     * @param int $quantity
     * @param float|null $price
     * @param int|null $variationId
     * @param float|null $tax_rate
     * @return CartItem The newly cartItem
     * @throws ApiException
     */
    public function addOfferToCart(
        int $cartId,
        int $offerId,
        int $quantity,
        ?float $price,
        ?int $variationId = null,
        ?float $tax_rate = null
    ): CartItem {
        $data = [
            'product_offer_id' => $offerId,
            'quantity' => $quantity,
        ];

        if ($variationId !== null) {
            $data ['product_variation_id'] = $variationId;
        }

        //overide for the price
        if ($price != null) {
            $data['unit_price'] = abs($price);
            $data['unit_vat'] = ($price * $tax_rate) / 100;
            $data['tax_rate'] = $tax_rate;
        }


        //override for the price need special header
        $timestamp = time();
        $toEncode = json_encode($data) . ':' . $timestamp;
        $message_auth = hash_hmac('sha1', $toEncode, $this->getSecretKey());

        $headers = [
            'Application-signature' => $message_auth,
            'Application-nonce' => $timestamp
        ];

        $content = $this->post(
            (new Request('cart/' . $cartId . '/items/'))
                ->setHeader($headers)
                ->setData($data)
        );

        /** @var CartItem $cartItem */
        $cartItem = $this->serializer->deserialize($content, CartItem::class, 'json');

        return $cartItem;
    }

    public function updateCartItemPaymentTerm(int $cartId, string $izbergPaymentTermId): void
    {

        $cartItems = $this->fetchCartItems($cartId);
        $data = ['selected_payment_term' => '/v1/payment_term/' . $izbergPaymentTermId . '/'];

        /** @var CartItem $cartItem */
        foreach ($cartItems as $cartItem) {
            $this->patch(new Request('cart_item/' . $cartItem->getId() . '/'), $data);
        }
    }

    /**
     * @param int $cartId
     * @return array
     * @throws ApiException
     */
    public function fetchCartItems(int $cartId): array
    {
        set_time_limit(150);
        return $this->fetchAllQuick(
            'cart/' . $cartId . '/items/',
            [],
            IzbergResponseCartItems::class
        );
    }

    /**
     * Retrieves in the list of cartItems of the cart a cartItem which corresponds
     * to the id sent ($cartItemId)
     *
     * @param int $cartItemId
     * @return CartItem|null
     * @throws ApiException
     */
    public function fetchCartItem(int $cartItemId): ?CartItem
    {
        $content = $this->get(new Request(sprintf('cart_item/%d', $cartItemId)));
        /** @var CartItem $cartItem */
        $cartItem = $this->serializer->deserialize($content, CartItem::class, 'json');
        return $cartItem;
    }

    public function updateCartItem(int $cartItemId, array $data): ?CartItem
    {
        $allowedKey = ["unit_price", "tax_rate", "quantity"];
        $dataFiltered = array_intersect_key($data, array_flip($allowedKey));

        $content = $this->patch(
            new Request(sprintf('cart_item/%d/', $cartItemId)),
            $dataFiltered
        );

        return $this->serializer->deserialize($content, CartItem::class, 'json');
    }


    /**
     * Retrieves in the list of cartItems of the cart a cartItem which corresponds
     * to the id sent ($cartItemId)
     *
     * @param int $cartId
     * @return Cart|null
     * @throws ApiException
     */
    public function fetchCart(int $cartId): ?Cart
    {
        $content = $this->get(new Request(sprintf('cart/%d', $cartId)));
        /** @var Cart $cart */
        $cart = $this->serializer->deserialize($content, Cart::class, 'json');
        return $cart;
    }

    public function fetchShippingSuggestion(int $cartId): ShippingSuggestionResponse
    {
        $content = $this->get(new Request(sprintf('cart/%d/shipping-suggestion', $cartId)));

        /** @var ShippingSuggestionResponse $shippingSuggestion */
        $shippingSuggestion = $this->serializer->deserialize(
            $content,
            ShippingSuggestionResponse::class,
            'json'
        );

        return $shippingSuggestion;
    }

    /**
     * Return true if the status of response is 201 (CREATED status)
     *
     * @param int $cartId
     * @param array $data
     * @return bool
     * @throws ApiException
     */
    public function createShipping(int $cartId, array $data): bool
    {
         $response = $this->postWithResponse(
             new Request(sprintf('cart/%d/create-shipping/', $cartId)),
             ['objects' => $data]
         );
        return $response->getCode() === Response::HTTP_CREATED;
    }

    /**
     * Return true if the response status code is 204 No Content, the cart item has been deleted
     *
     * @param int $cartItemId
     * @return bool
     * @throws ApiException
     */
    public function deleteCartItem(int $cartItemId): bool
    {
        $response = $this->deleteWithResponse(new Request(sprintf('/v1/cart_item/%d', $cartItemId)));
        return $response->getCode() === 204;
    }

    /**
     * @param int $cartId
     * @param string|null $paymentType
     * @param string|null $paymentMethod
     * @param int|null $shippingAddressId
     * @param int|null $billingAddressId
     * @throws TransportExceptionInterface
     */
    public function updateCart(
        int $cartId,
        ?string $paymentType = null,
        ?string $paymentMethod = null,
        ?int $shippingAddressId = null,
        ?int $billingAddressId = null,
        ?string $paymentTerm = null
    ) {
        $data = [];
        if ($paymentType !== null) {
            $data['selected_payment_type'] = $paymentType;
        }
        if ($paymentTerm !== null) {
            $data['selected_payment_term'] = '/v1/payment_term/' . $paymentTerm . '/';
        }

        if ($paymentMethod !== null) {
            $data['selected_payment_method'] = $paymentMethod;
        }

        if ($shippingAddressId !== null) {
            $data['shipping_address'] = '/v1/address/' . $shippingAddressId . '/';
        }

        if ($billingAddressId !== null) {
            $data['billing_address'] = '/v1/address/' . $billingAddressId . '/';
        }
        $this->patch(
            (new Request(
                sprintf('/v1/cart/%d/', $cartId)
            ))->setData($data)
        );
    }

    /**
     * @throws ApiException
     **/
    public function addDiscountCodeToCart(int $cartId, string $discountCode): void
    {
        $header = ['Accept' => 'application/json'];
        $url = sprintf('/v1/cart/%d/add_discount_code/', $cartId);
        $data = ["discount_code" => $discountCode];

        $this->post(
            (new Request($url))
                ->setData($data)
                ->setHeader($header)
        );
    }

    /**
     * @throws ApiException
     */
    public function overrideShipping(int $cartId, array $shipping): void
    {
        $header = ['Accept' => 'application/json'];
        $url = sprintf('/v1/cart/%d/create-shipping/', $cartId);
        $data = [];
        $objects = [];
        $objects[] = $shipping;
        $data["objects"] = $objects;

        $this->post(
            (new Request($url))
                ->setData($data)
                ->setHeader($header)
        );
    }
}
