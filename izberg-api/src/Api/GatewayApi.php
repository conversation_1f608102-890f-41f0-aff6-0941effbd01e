<?php

namespace Open\Izberg\Api;

use Open\Izberg\DTO\Request;
use Open\Izberg\Exception\ApiException;
use Open\Izberg\Model\Gateway;
use Open\Izberg\Model\IzbergResponseGateways;

class GatewayApi extends ApiAbstract
{
    private const GATEWAY_PATH = 'psp/gateway/';

    public const TYPE_TERM_PAYMENT = "TERM_PAYMENT";
    public const TYPE_PREPAYMENT = "PREPAYMENT";
    public const TYPE_REFUND = "REFUND";

    public const QUALIFIER_MERCHANT_ORDER = "MERCHANT_ORDER";
    public const QUALIFIER_INVOICE = "INVOICE";
    public const QUALIFIER_REFUND = "REFUND";

    public function getUri()
    {
        return null;
    }

    public function getItemClass()
    {
        return null;
    }

    /**
     * create a gateways
     * @param string $externalId external id of the transaction
     * @param string $gatewayType must be PREPAYMENT or TERM_PAYMENT or REFUND
     * @param string $qualifier must be INVOICE or MERCHANT_ORDER
     * @param int $value the value to save in the gateway
     * @throws ApiException
     */
    public function createGateway(
        string $externalId,
        string $gatewayType,
        string $qualifier,
        int $value
    ): void {
        $data = [];
        if ($qualifier === self::QUALIFIER_INVOICE) {
            $data["invoice"] = "/v1/customer_invoice/" . $value . "/";
        } else {
            if ($qualifier === self::QUALIFIER_MERCHANT_ORDER) {
                $data["merchant_order"] = "/v1/merchant_order/" . $value . "/";
            } else {
                if ($qualifier === self::QUALIFIER_REFUND) {
                    $data["refund"] = "/v1/refund/" . $value . "/";
                } else {
                    throw new ApiException(
                        "createGateway: Invalid value for qualifier: " . $qualifier,
                        400
                    );
                }
            }
        }

        $data["external_id"] = $externalId;
        $data["application"] = "/v1/application/" . $this->getApplicationId() . "/";
        $data["gateway_type"] = $gatewayType;

        $this->post(new Request(self::GATEWAY_PATH), $data);
    }

    public function getGatewayByMerchantOrderId(int $merchantOrderId): ?Gateway
    {
        $request = (new Request(self::GATEWAY_PATH))
            ->setQuery(['merchant_order' => $merchantOrderId]);

        $content = $this->get($request);

        /** @var IzbergResponseGateways $data */
        $data = $this->serializer->deserialize($content, IzbergResponseGateways::class, 'json');

        if (count($data) === 1) {
            return $data->getObjects()[0];
        }

        return null;
    }

    /**
     * @throws ApiException
     */
    public function payGateway(string $externalId): string
    {
        return $this->post(new Request(self::GATEWAY_PATH . $externalId . '/pay/'));
    }
}
