<?php

namespace Open\Izberg\Api;

use Open\Izberg\DTO\Request;
use Open\Izberg\Exception\ApiException;
use Open\Izberg\Model\IzbergMerchantReviewsResponse;
use Open\Izberg\Model\IzbergResponseMerchantOrderReview;
use Symfony\Component\HttpClient\Exception\ClientException;
use Open\Izberg\Model\MerchantOrderReview;

class ReviewApi extends ApiAbstract
{
    public function fetchMerchantOrderReview(int $merchantOrderId): ?MerchantOrderReview
    {
        $content = $this->get(new Request(sprintf('merchant-review/?merchant_order=%d', $merchantOrderId)));
        $reviewData = $this->serializer->deserialize($content, IzbergResponseMerchantOrderReview::class, 'json');
        if (empty($reviewData->getObjects())) {
            return null;
        }
        return ($reviewData->getObjects()[0]);
    }

    public function createMerchantOrderReview(MerchantOrderReview $review)
    {
        $data = [
            "merchant" => "/v1/merchant/" . $review->getMerchantId() . "/",
            "merchant_order" => "/v1/merchant_order/" . $review->getMerchantOrderId() . "/",
            "title" => "",
            "body" => $review->getBody(),
            "score" => $review->getScore(),
        ];
        $content = $this->post(new Request('merchant-review/'), $data);

        return $this->serializer->deserialize($content, IzbergResponseMerchantOrderReview::class, 'json');
    }

    public function fetchMerchantAverageScore(int $merchantId): ?float
    {
        $content = $this->get(new Request(sprintf('merchant-review/average-score/?merchant_id=%d', $merchantId)));
        $content = json_decode($content);
        return (float)$content->average;
    }

    public function fetchMerchantReviews(int $merchantId): ?array
    {

        $url = sprintf('merchant-review/?status=approved&merchant=%d', $merchantId);
        $content = $this->get(new Request($url), 1440);
        $reviews = $this->serializer->deserialize($content, IzbergMerchantReviewsResponse::class, 'json');
        if (empty($reviews->getObjects())) {
            return null;
        }
        return $reviews->getObjects();
    }
}
