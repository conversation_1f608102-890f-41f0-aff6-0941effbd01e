<?php

namespace Open\Izberg\Api;

use DateTime;
use Generator;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CacheServiceInterface;
use Open\Izberg\DTO\Request;
use Open\Izberg\Exception\ApiException;
use Open\Izberg\Exception\NotFoundException;
use Open\Izberg\Model\IzbergResponseOrders;
use Open\Izberg\Model\MerchantOrder;
use Open\Izberg\Model\Order;
use Open\Izberg\Model\ShippingOption;
use Open\Izberg\Service\AsyncActionIdExtractor;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;

class GlobalConfigApi extends ApiAbstract
{
    /**
     * @throws ApiException
     */
    public function fetchExtraFees(): array
    {
        $appConfigID = json_decode($this->get(
            (new Request(sprintf('application/%d/configurations/active/?only=id', $this->getApplicationId())))
                ->setHeader(['Accept' => 'application/json'])
        ))->id;

        $appConfigParams = json_decode($this->get(
            (new Request(sprintf('application_configuration/%d/settings/?key__in=enable_extra_fee_setting,extra_fee_amount,extra_fee_threshold&only=key,value', $appConfigID)))
                ->setHeader(['Accept' => 'application/json'])
        ))->objects;

        $result = [];
        foreach ($appConfigParams as $param) {
            /*if ($param->key == 'extra_fee_tax_rate') { // We will not use this param, but tax rate of the first product in the cart
                $param->value *= 100;
            }*/
            $result[$param->key] = $param->value;
        }

        return $result;
    }
}
