<?php

declare(strict_types=1);

namespace Open\Izberg\Api;

use Generator;
use Open\Izberg\Exception\ApiException;
use Open\Izberg\Model\Cart;
use Open\Izberg\Model\CartItem;
use Open\Izberg\Model\ShippingSuggestionResponse;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

/**
 * @TODO add all methods of CartApi in this interface
 */
interface CartApiInterface
{
    /**
     * @param string $currency
     * @return int
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function createCart(string $currency = 'EUR'): int;

    /**
     * @param int $cartId
     * @param int $offerId
     * @param int $quantity
     * @param float|null $price
     * @param int|null $variationId
     * @param float|null $tax_rate
     * @return CartItem The newly added cartItem
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function addOfferToCart(
        int $cartId,
        int $offerId,
        int $quantity,
        ?float $price,
        ?int $variationId = null,
        ?float $tax_rate = null
    ): CartItem;

    public function updateCartItemPaymentTerm(int $cartId, string $izbergPaymentTermId): void;

    /**
     * @param int $cartId
     * @return array
     * @throws ApiException
     */
    public function fetchCartItems(int $cartId): array;

    /**
     * Retrieves in the list of cartItems of the cart a cartItem which corresponds
     * to the id sent ($cartItemId)
     *
     * @param int $cartItemId
     * @return CartItem|null
     * @throws ApiException
     */
    public function fetchCartItem(int $cartItemId): ?CartItem;

    /**
     * update cart item
     *
     * @param int $cartItemId
     * @param array $data
     * @return CartItem|null
     * @throws ApiException
     */
    public function updateCartItem(int $cartItemId, array $data): ?CartItem;

    /**
     * Retrieves in the list of cartItems of the cart a cartItem which corresponds
     * to the id sent ($cartItemId)
     *
     * @param int $cartId
     * @return Cart|null
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function fetchCart(int $cartId): ?Cart;

    /**
     * Return true if the status of response is 201 (CREATED status)
     *
     * @param int $cartId
     * @param array $data
     * @return bool
     */
    public function createShipping(int $cartId, array $data): bool;

    public function fetchShippingSuggestion(int $cartId): ShippingSuggestionResponse;

    /**
     * Return true if the response status code is 204 No Content, the cart item has been deleted
     *
     * @param int $cartItemId
     * @return bool
     * @throws TransportExceptionInterface
     */
    public function deleteCartItem(int $cartItemId): bool;

    /**
     * @param int $cartId
     * @param string|null $paymentType
     * @param string|null $paymentMethod
     * @param int|null $shippingAddressId
     * @param int|null $billingAddressId
     * @throws TransportExceptionInterface
     */
    public function updateCart(
        int $cartId,
        ?string $paymentType = null,
        ?string $paymentMethod = null,
        ?int $shippingAddressId = null,
        ?int $billingAddressId = null
    );

    /**
     * @throws ApiException
     **/
    public function addDiscountCodeToCart(int $cartId, string $discountCode): void;

    /**
     * @throws ApiException
     */
    public function overrideShipping(int $cartId, array $shipping): void;
}
