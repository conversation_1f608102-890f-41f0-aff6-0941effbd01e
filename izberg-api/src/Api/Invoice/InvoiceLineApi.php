<?php

declare(strict_types=1);

namespace Open\Izberg\Api\Invoice;

use Open\Izberg\Api\ApiAbstract;
use Open\Izberg\DTO\Request;

final class InvoiceLineApi extends ApiAbstract implements InvoiceLineApiInterface
{
    private const URL = 'invoice_line';

    public function create(int $invoiceId, int $orderItemId): void
    {
        $data = [
            'invoice' => sprintf('/v1/customer_invoice/%d/', $invoiceId),
            'order_item' => sprintf('/v1/order_item/%d/', $orderItemId),
        ];

        $this->post(new Request(self::URL . '/'), $data);
    }

    public function createShippingInvoice(
        int $invoiceId,
        int $merchantOrderId,
        string $name,
        string $price,
        string $vat
    ): void {
        $data = [
            'invoice' => sprintf('/v1/customer_invoice/%d/', $invoiceId),
            'name' => $name,
            'quantity' => 1,
            'line_type' => "shipping",
            'unit_price' => $price,
            'merchant_order' => sprintf('/v1/merchant_order/%d/', $merchantOrderId),
            'tax_rate' => $vat,

        ];
        $this->post(new Request(self::URL . '/'), $data);
    }

    public function createExtraFees(int $invoiceId, int $merchantOrderId, float $price): void
    {
        $data = [
            'invoice' => sprintf('/v1/customer_invoice/%d/', $invoiceId),
            'merchant_order' => sprintf('/v1/merchant_order/%d/', $merchantOrderId),
            'line_type' => 'extra_fee',
            'order_item' => null,
            'unit_price' => $price,
            'quantity' => 1
        ];

        $this->post(new Request(self::URL . '/'), $data);
    }
}
