<?php

declare(strict_types=1);

namespace Open\Izberg\Api\Invoice;

interface InvoiceLineApiInterface
{
    public function create(int $invoiceId, int $orderItemId): void;

    public function createShippingInvoice(
        int $invoiceId,
        int $merchantOrderId,
        string $name,
        string $price,
        string $vat
    ): void;

    public function createExtraFees(int $invoiceId, int $merchantOrderId, float $price): void;
}
