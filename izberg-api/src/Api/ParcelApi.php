<?php

namespace Open\Izberg\Api;

use DateTime;
use Generator;
use Open\Izberg\DTO\Request;
use Open\Izberg\Model\IzbergResponseParcelItems;
use Open\Izberg\Model\IzbergResponseParcels;
use Open\Izberg\Model\Parcel;

class ParcelApi extends ApiAbstract
{
    public function fetchParcel(int $parcelId): Parcel
    {
        $content = $this->get(new Request(sprintf('parcel/%d', $parcelId)));

        /** @var Parcel $parcel */
        $parcel = $this->serializer->deserialize($content, Parcel::class, 'json');
        return $parcel;
    }

    public function create(array $request): Parcel
    {
        $data = [
            'application' => sprintf('/v1/application/%d/', $this->applicationId),
            'merchant_order' => sprintf('/v1/merchant_order/%d/', (int)$request['merchantOrderId']),
            'order_shipping_choice' => sprintf('/v1/order_shipping_choice/%d/', (int)$request['orderShippingChoiceId']),
            'tracking_number' => $request['trackingNumber'],
        ];

        if (isset($request['carrierId'])) {
            $data['carrier'] = sprintf('/v1/carrier/%d/', (int)$request['carrierId']);
        }

        $content = $this->post((new Request('parcel/'))->setData($data));

        /** @var Parcel $parcel */
        $parcel = $this->serializer->deserialize($content, Parcel::class, 'json');

        return $parcel;
    }

    public function deleteParcel(int $parcelId): void
    {
        $this->delete((new Request(sprintf('parcel/%d/', $parcelId))));
    }

    public function shipParcel(int $parcelId): void
    {
        $this->post((new Request(sprintf('parcel/%d/ship/', $parcelId))));
    }

    public function fetchMerchantOrderParcels(int $merchantOrderId): Generator
    {
        return $this->fetchAll(
            'parcel/',
            [
                'merchant_order' => $merchantOrderId

            ],
            IzbergResponseParcels::class
        );
    }

    public function fetchMerchantOrderParcelInitials(int $merchantOrderId): Generator
    {
        return $this->fetchAll(
            'parcel/',
            [
                'merchant_order' => $merchantOrderId,
                'status' => 'initial'
            ],
            IzbergResponseParcels::class
        );
    }

    public function getLastParcel(DateTime $lastModifiedStart): Generator
    {
        $date = $lastModifiedStart->format("Y-m-d");
        $request = sprintf("parcel/?last_modified__gt=%s&status__in=in_transit%%2Creceived", $date);

        return $this->fetchAll(
            $request,
            [
            ],
            IzbergResponseParcels::class
        );
    }


    public function fetchParcelItems(int $parcelId): Generator
    {
        return $this->fetchAll(
            sprintf('parcel/%d/assignments', $parcelId),
            [
            ],
            IzbergResponseParcelItems::class
        );
    }

    public function receiveParcel(int $parcelId, array $request): Parcel
    {
        $content = $this->post((new Request(sprintf('parcel/%d/receive/', $parcelId))));

        /** @var Parcel $parcel */
        $parcel = $this->serializer->deserialize($content, Parcel::class, 'json');
        return $parcel;
    }
}
