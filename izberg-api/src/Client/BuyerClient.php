<?php

declare(strict_types=1);

namespace Open\Izberg\Client;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CacheServiceInterface;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetLocaleInterface;
use Open\Izberg\Api\AddressApi;
use Open\Izberg\Api\CartApi;
use Open\Izberg\Api\CartApiInterface;
use Open\Izberg\Api\Invoice\InvoiceApi;
use Open\Izberg\Api\OfferApi;
use Open\Izberg\Api\PaymentApi;
use Open\Izberg\Api\ReviewApi;
use Open\Izberg\Api\MessageApi;
use Open\Izberg\Api\OrderApi;
use Open\Izberg\Authentication\AccessToken;
use Open\Izberg\Authentication\Authenticator;
use Open\Izberg\Event\CallIzbergApiEvent;
use Open\Izberg\Exception\IzbergClientAuthenticationException;
use Open\Izberg\IzbergConfiguration;
use Psr\EventDispatcher\EventDispatcherInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

final class BuyerClient extends ClientAbstract implements BuyerClientInterface
{
    private int $userId;
    protected AddressApi $addressApi;
    protected CartApi $cartApi;
    protected OfferApi $offerApi;
    protected OrderApi $orderApi;
    protected MessageApi $messageApi;
    protected ReviewApi $reviewApi;
    protected InvoiceApi $invoiceApi;
    protected PaymentApi $paymentApi;

    public function __construct(
        private readonly EventDispatcherInterface $eventDispatcher,
        IzbergConfiguration $izbergConfiguration,
        private readonly CacheServiceInterface $cacheService,
        protected GetLocaleInterface $localeService
    ) {
        parent::__construct($izbergConfiguration, $this->localeService);

        $this->addressApi = new AddressApi();
        $this->cartApi = new CartApi();
        $this->offerApi = new OfferApi();
        $this->orderApi = new OrderApi();
        $this->messageApi = new MessageApi();
        $this->reviewApi = new ReviewApi($this->cacheService);
        $this->invoiceApi = new InvoiceApi();
        $this->paymentApi = new PaymentApi();
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function setup(
        AccessToken $accessToken
    ): void {

        $authenticator = new Authenticator($this->izbergConfiguration);
        $authenticator->authenticate($accessToken);
    }

    public function setIzbergUserId(AccessToken $accessToken): void
    {
        $izbergUserId = $accessToken->getIzbergUserId();
        if ($izbergUserId === null) {
            throw new IzbergClientAuthenticationException();
        }
        $this->userId = $izbergUserId;
    }

    /**
     * Get izberg user id related to this client
     */
    public function getUserId(): int
    {
        $callIzbergApiEvent = new CallIzbergApiEvent($this);
        $this->eventDispatcher->dispatch($callIzbergApiEvent);
        return $this->userId;
    }

    public function addressApi(): AddressApi
    {
        /** @var AddressApi $api */
        $api = $this->callApi(AddressApi::class);
        return $api;
    }

    public function cartApi(): CartApiInterface
    {
        /** @var CartApi|CartApiInterface $api */
        $api = $this->callApi(CartApi::class);
        return $api;
    }

    public function offerApi(): OfferApi
    {
        /** @var OfferApi $api */
        $api = $this->callApi(OfferApi::class);
        return $api;
    }

    public function orderApi(): OrderApi
    {
        /** @var OrderApi $api */
        $api = $this->callApi(OrderApi::class);
        return $api;
    }

    public function messageApi(): MessageApi
    {
        /** @var MessageApi $api */
        $api = $this->callApi(MessageApi::class);
        return $api;
    }

    public function invoiceApi(): InvoiceApi
    {
        /** @var InvoiceApi $api */
        $api = $this->callApi(InvoiceApi::class);
        return $api;
    }

    public function reviewApi(): ReviewApi
    {
        /** @var ReviewApi $api */
        $api = $this->callApi(ReviewApi::class);
        return $api;
    }

    public function paymentApi(): PaymentApi
    {
        /** @var PaymentApi $api */
        $api = $this->callApi(PaymentApi::class);
        return $api;
    }

    public function callIzbergApiEvent(): void
    {
        $callIzbergApiEvent = new CallIzbergApiEvent($this);
        $this->eventDispatcher->dispatch($callIzbergApiEvent);
    }
}
