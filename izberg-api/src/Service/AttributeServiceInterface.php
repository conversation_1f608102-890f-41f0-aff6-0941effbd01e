<?php

namespace Open\Izberg\Service;

use Open\Izberg\DTO\AttributeDTO;
use Psr\Cache\InvalidArgumentException;

interface AttributeServiceInterface
{
    /**
     * @param string $attributeName
     * @param string $locale
     * @return AttributeDTO
     */
    public function getAttribute(string $attributeName, string $locale): AttributeDTO;

    /**
     * @param int $merchantId
     * @return AttributeDTO[]
     */
    public function getMerchantAttributes(int $merchantId): array;

    /**
     * @param bool $force
     * @return array
     */
    public function getCachedAttributes(bool $force = false): array;

    /**
     * @return array
     */
    public function getAttributesKeys(): array;

    /**
     * @param array $offerAttributes
     * @param int $merchantId
     * @return array<array<string, string>>
     * @throws InvalidArgumentException
     */
    public function findClickAndCollectAgencies(array $offerAttributes, int $merchantId): array;
}
