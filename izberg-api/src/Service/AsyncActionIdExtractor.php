<?php

namespace Open\Izberg\Service;

class AsyncActionIdExtractor
{
    /**
     * example of resource
     * https://api.sandbox.iceberg.technology/v1/async-action/1003979-global-f50a1fd6-8027-4b69-a3d5-6a61a3f37489/
     * @param string $resource
     * @return string|null
     */
    public static function extract(string $resource): ?string
    {
        $needle = '/async-action/';
        $needleSize = strlen($needle);
        $positionNeedle = strpos($resource, '/async-action/');
        if ($positionNeedle === false) {
            return null;
        }

        // take the rest after /async-action/
        $rest = substr($resource, $positionNeedle + $needleSize);

        // check if the rest has only one "/" after the asyncActionId
        $numberOfSlashes = substr_count($rest, '/');
        if ($numberOfSlashes !== 1) {
            return null;
        }

        return substr($rest, 0, strlen($rest) - 1);
    }
}
