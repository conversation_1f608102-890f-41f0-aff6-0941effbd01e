<?php

namespace Open\Izberg\Service;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CacheServiceInterface;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetLocaleInterface;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetRegionServiceInterface;
use Open\Izberg\Client\OperatorClient;
use Open\Izberg\Model\Category;

class CategoryService implements CategoryServiceInterface
{
    private const CACHE_KEY_ALL = 'IZBERG_ALL_CATEGORIES';
    private const CACHE_KEY_ALL_HIERARCHY = 'IZBERG_ALL_HIERARCHY';
    private const CACHE_KEY_ALL_HIERARCHY_FILTERED = 'IZBERG_ALL_HIERARCHY_FILTERED';

    private ?array $cachedCategories = null;

    public function __construct(
        private OperatorClient $izbergOperatorClient,
        private CacheServiceInterface $cacheService,
        private GetLocaleInterface $getLocaleService,
        private GetRegionServiceInterface $getRegionService,
        private string $categoryIconPath,
        private array $categoryIcons
    ) {
    }

    /**
     * @inheritDoc
     */
    public function getCategoryName(int $id): ?string
    {

        foreach ($this->getCachedCategories() as $category) {
            if ($category->getId() == $id) {
                return $category->getName();
            }
        }

        return null;
    }

    /**
     * @inheritDoc
     */
    public function getCategoryLevel(int $categoryId, int $level): int
    {
        $category = $this->find($categoryId);
        if ($category == null) {
            return -1;
        }

        if (count($category->getParents()) > 0) {
            $level++;
        }
        /** @var Category $parent */
        foreach ($category->getParents() as $parent) {
            $level = $this->getCategoryLevel($parent->getId(), $level);
        }
        return $level;
    }

    /**
     * @inheritDoc
     */
    public function getParent(?int $catId): ?string
    {
        if ($catId === null) {
            return null;
        }
        /** @var Category $category */
        foreach ($this->getCachedCategories() as $category) {
            if ($category->getId() === $catId) {
                $parents = $category->getParents();
                if (isset($parents[0])) {
                    return $parents[0]->getId();
                }
            }
        }

        return null;
    }

    public function getFullHierarchy(?string $locale = null): array
    {
        return $this->getFullHierarchyCached($locale);
    }


    /**
     * @return array
     */
    private function getCachedCategories(): array
    {
        if ($this->cachedCategories === null) {
            $this->cachedCategories = $this->getAllCategories();
        }
        return $this->cachedCategories;
    }

    /**
     * @param int $categoryId
     * @return Category|null
     */
    public function find(int $categoryId): ?Category
    {
        /** @var Category $category */
        foreach ($this->getCachedCategories() as $category) {
            if ($category->getId() === $categoryId) {
                return $category;
            }
        }
        return null;
    }

    /**
     * @return array
     */
    private function getAllCategories(?string $locale = null): array
    {
        if ($locale === null) {
            $locale = $this->getLocaleService->getLocale();
        }
        $categories = $this->cacheService->getItem(self::CACHE_KEY_ALL . '.' . $locale);
        if (null === $categories) {
            $categories = $this->izbergOperatorClient->categorieApi()->getAllCategories($locale);
            $this->cacheService->saveItem(self::CACHE_KEY_ALL . '.' . $locale, $categories);
        }

        return (null !== $categories ? $categories->getValues() : []);
    }

    /**
     * @return array
     */
    private function getFullHierarchyCached(?string $locale = null): array
    {
        if ($locale === null) {
            $locale = $this->getLocaleService->getLocale();
        }
        $categories = $this->cacheService->getItem(self::CACHE_KEY_ALL_HIERARCHY . '.' . $locale);
        if (null === $categories) {
            $categories = $this->buildFullHierarchy($locale);
            $this->cacheService->saveItem(self::CACHE_KEY_ALL_HIERARCHY . '.' . $locale, $categories);
        }

        return (null !== $categories ? $categories : []);
    }

    /**
     * @param array<int> $categoriesIds
     * @param string|null $locale
     * @return array<Category>
     */
    public function getFullHierarchyFiltered(array $categoriesIds, ?string $locale = null): array
    {
        if ($locale === null) {
            $locale = $this->getLocaleService->getLocale();
        }
        $region = $this->getRegionService->getRegion();
        $cacheKey = sprintf("%s.%s.%s", self::CACHE_KEY_ALL_HIERARCHY_FILTERED, $locale, $region);
        $categories = $this->cacheService->getItem($cacheKey);
        if (null === $categories) {
            $categories = $this->buildFullHierarchy($locale, true, $categoriesIds);
            $this->cacheService->saveItem($cacheKey, $categories);
        }

        return (null !== $categories ? $categories : []);
    }

    private function buildFullHierarchy(?string $locale = null, bool $filtered = false, array $categoriesIds = []): array
    {
        if ($locale === null) {
            $locale = $this->getLocaleService->getLocale();
        }
        $categories = $this->getAllCategories($locale);
        // category list, with no parent
        $levelZeroes = [];
        //category index by id
        $indexing = [];
        /** @var Category $category */
        foreach ($categories as $category) {
            $indexing [$category->getId()] = $category;
            if (count($category->getParents()) == 0) {
                $levelZeroes [] = $category;
            }
        }
        foreach ($levelZeroes as $categoryZero) {
            $this->setChildren($categoryZero, $indexing, $filtered, $categoriesIds, 2);
        }
        return $levelZeroes;
    }

    private function setChildren(Category &$parent, array $indexing, bool $filtered, array $categoriesIds, int $level): void
    {
        $hydratedChildren = array_map(function (Category $cat) use ($indexing) {
            if (isset($indexing[$cat->getId()])) {
                return $indexing[$cat->getId()];
            }
            return null;
        }, $parent->getChildren());

        $hydratedChildren = array_filter($hydratedChildren);

        // order alpha.
        usort($hydratedChildren, function (Category $a, Category $b) {
            return strcasecmp($a->getExternalId() ?? "ZZ", $b->getExternalId() ?? "ZZ");
        });

        if ($filtered) {
            $hydratedChildren = array_filter($hydratedChildren, function (Category $child) use ($categoriesIds) {
                return in_array($child->getId(), $categoriesIds);
            });
        }

        $parent->setHydratedChildren($hydratedChildren);
        $parent->setIconPath($this->getCategoryIconPath($parent->getId()));
        foreach ($hydratedChildren as $children) {
            $this->setChildren($children, $indexing, $filtered, $categoriesIds, $level + 1);
        }
    }

    /**
     * @param int $categoryId
     * @return string|null
     */
    private function getCategoryIconPath(int $categoryId): ?string
    {

        if (isset($this->categoryIcons[$categoryId])) {
            $ret = sprintf("/%s/%s", $this->categoryIconPath, $this->categoryIcons[$categoryId]);
            return str_replace('//', '/', $ret);
        }
            return null;
    }

    public function getSameLevelCategories(int $catId): array
    {
        //TODO extraire conversion des modeles dans un autre service
        $parentId = $this->getParent($catId);
        $parent = $this->find((int)$parentId);
        if ($parent === null) {
            return [];
        }
        $children = $parent->getChildren();
        return array_filter($children, function (Category $child) use ($catId) {
            return $child->getId() !== $catId;
        });
    }
}
