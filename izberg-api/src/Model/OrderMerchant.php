<?php

namespace Open\Izberg\Model;

class OrderMerchant
{
    private int $id = 0;
    private int $status;
    private float $amount;
    private float $amountVatIncluded;
    private float $amountVatIncludedBeforeDiscount;
    private float $appDiscountAmount;
    private float $appDiscountAmountOnItemsVatIncluded;
    private float $appDiscountAmountOnShippingVatIncluded;
    private float $appDiscountAmountVatIncluded;
    private float $discountAmount;
    private float $discountAmountVatIncluded;
    private float $ecoTax;
    private float $ecoTaxVatIncluded;
    private float $price;
    private float $priceVatIncluded;
    private float $vat;
    private float $vatOnEcoTax;
    private float $vatOnProducts;
    private float $vatOnShipping;
    private float $vatRateOnShipping;
    private string $idNumber;
    private string $statusLocalized;
    private string $confirmationDate;
    private string $createdOn;
    private string $paymentType;
    private array $items;
    private array $merchantOrders;
    private array $creditNotes;
    private array $attributes;
    private Address $billingAddress;
    private Address $shippingAddress;
    private OrderDetails $order;
    private User $user;
    private Merchant $merchant;
    private Currency $currency;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getStatus(): int
    {
        return $this->status;
    }

    public function setStatus(int $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;
        return $this;
    }

    public function getAmountVatIncluded(): float
    {
        return $this->amountVatIncluded;
    }

    public function setAmountVatIncluded(float $amountVatIncluded): self
    {
        $this->amountVatIncluded = $amountVatIncluded;
        return $this;
    }

    public function getAmountVatIncludedBeforeDiscount(): float
    {
        return $this->amountVatIncludedBeforeDiscount;
    }

    public function setAmountVatIncludedBeforeDiscount(float $amountVatIncludedBeforeDiscount): self
    {
        $this->amountVatIncludedBeforeDiscount = $amountVatIncludedBeforeDiscount;
        return $this;
    }

    public function getAppDiscountAmount(): float
    {
        return $this->appDiscountAmount;
    }

    public function setAppDiscountAmount(float $appDiscountAmount): self
    {
        $this->appDiscountAmount = $appDiscountAmount;
        return $this;
    }

    public function getAppDiscountAmountOnItemsVatIncluded(): float
    {
        return $this->appDiscountAmountOnItemsVatIncluded;
    }

    public function setAppDiscountAmountOnItemsVatIncluded(float $appDiscountAmountOnItemsVatIncluded): self
    {
        $this->appDiscountAmountOnItemsVatIncluded = $appDiscountAmountOnItemsVatIncluded;
        return $this;
    }

    public function getAppDiscountAmountOnShippingVatIncluded(): float
    {
        return $this->appDiscountAmountOnShippingVatIncluded;
    }

    public function setAppDiscountAmountOnShippingVatIncluded(float $appDiscountAmountOnShippingVatIncluded): self
    {
        $this->appDiscountAmountOnShippingVatIncluded = $appDiscountAmountOnShippingVatIncluded;
        return $this;
    }

    public function getAppDiscountAmountVatIncluded(): float
    {
        return $this->appDiscountAmountVatIncluded;
    }

    public function setAppDiscountAmountVatIncluded(float $appDiscountAmountVatIncluded): self
    {
        $this->appDiscountAmountVatIncluded = $appDiscountAmountVatIncluded;
        return $this;
    }

    public function getDiscountAmount(): float
    {
        return $this->discountAmount;
    }

    public function setDiscountAmount(float $discountAmount): self
    {
        $this->discountAmount = $discountAmount;
        return $this;
    }

    public function getDiscountAmountVatIncluded(): float
    {
        return $this->discountAmountVatIncluded;
    }

    public function setDiscountAmountVatIncluded(float $discountAmountVatIncluded): self
    {
        $this->discountAmountVatIncluded = $discountAmountVatIncluded;
        return $this;
    }

    public function getEcoTax(): float
    {
        return $this->ecoTax;
    }

    public function setEcoTax(float $ecoTax): self
    {
        $this->ecoTax = $ecoTax;
        return $this;
    }

    public function getEcoTaxVatIncluded(): float
    {
        return $this->ecoTaxVatIncluded;
    }

    public function setEcoTaxVatIncluded(float $ecoTaxVatIncluded): self
    {
        $this->ecoTaxVatIncluded = $ecoTaxVatIncluded;
        return $this;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function setPrice(float $price): self
    {
        $this->price = $price;
        return $this;
    }

    public function getPriceVatIncluded(): float
    {
        return $this->priceVatIncluded;
    }

    public function setPriceVatIncluded(float $priceVatIncluded): self
    {
        $this->priceVatIncluded = $priceVatIncluded;
        return $this;
    }

    public function getVat(): float
    {
        return $this->vat;
    }

    public function setVat(float $vat): self
    {
        $this->vat = $vat;
        return $this;
    }

    public function getVatOnEcoTax(): float
    {
        return $this->vatOnEcoTax;
    }

    public function setVatOnEcoTax(float $vatOnEcoTax): self
    {
        $this->vatOnEcoTax = $vatOnEcoTax;
        return $this;
    }

    public function getVatOnProducts(): float
    {
        return $this->vatOnProducts;
    }

    public function setVatOnProducts(float $vatOnProducts): self
    {
        $this->vatOnProducts = $vatOnProducts;
        return $this;
    }

    public function getVatOnShipping(): float
    {
        return $this->vatOnShipping;
    }

    public function setVatOnShipping(float $vatOnShipping): self
    {
        $this->vatOnShipping = $vatOnShipping;
        return $this;
    }

    public function getVatRateOnShipping(): float
    {
        return $this->vatRateOnShipping;
    }

    public function setVatRateOnShipping(float $vatRateOnShipping): self
    {
        $this->vatRateOnShipping = $vatRateOnShipping;
        return $this;
    }

    public function getIdNumber(): string
    {
        return $this->idNumber;
    }

    public function setIdNumber(string $idNumber): self
    {
        $this->idNumber = $idNumber;
        return $this;
    }

    public function getStatusLocalized(): string
    {
        return $this->statusLocalized;
    }

    public function setStatusLocalized(string $statusLocalized): self
    {
        $this->statusLocalized = $statusLocalized;
        return $this;
    }

    public function getConfirmationDate(): string
    {
        return $this->confirmationDate;
    }

    public function setConfirmationDate(string $confirmationDate): self
    {
        $this->confirmationDate = $confirmationDate;
        return $this;
    }

    public function getCreatedOn(): string
    {
        return $this->createdOn;
    }

    public function setCreatedOn(string $createdOn): self
    {
        $this->createdOn = $createdOn;
        return $this;
    }

    public function getPaymentType(): string
    {
        return $this->paymentType;
    }

    public function setPaymentType(string $paymentType): self
    {
        $this->paymentType = $paymentType;
        return $this;
    }

    public function getItems(): array
    {
        return $this->items;
    }

    public function setItems(array $items): self
    {
        $this->items = $items;
        return $this;
    }

    public function getMerchantOrders(): array
    {
        return $this->merchantOrders;
    }

    public function setMerchantOrders(array $merchantOrders): self
    {
        $this->merchantOrders = $merchantOrders;
        return $this;
    }

    public function getCreditNotes(): array
    {
        return $this->creditNotes;
    }

    public function setCreditNotes(array $creditNotes): self
    {
        $this->creditNotes = $creditNotes;
        return $this;
    }

    public function getAttributes(): array
    {
        return $this->attributes;
    }

    public function setAttributes(array $attributes): self
    {
        $this->attributes = $attributes;
        return $this;
    }

    public function getBillingAddress(): Address
    {
        return $this->billingAddress;
    }

    public function setBillingAddress(Address $billingAddress): self
    {
        $this->billingAddress = $billingAddress;
        return $this;
    }

    public function getShippingAddress(): Address
    {
        return $this->shippingAddress;
    }

    public function setShippingAddress(Address $shippingAddress): self
    {
        $this->shippingAddress = $shippingAddress;
        return $this;
    }

    public function getOrder(): OrderDetails
    {
        return $this->order;
    }

    public function setOrder(OrderDetails $order): self
    {
        $this->order = $order;
        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;
        return $this;
    }

    public function getMerchant(): Merchant
    {
        return $this->merchant;
    }

    public function setMerchant(Merchant $merchant): self
    {
        $this->merchant = $merchant;
        return $this;
    }

    public function getCurrency(): Currency
    {
        return $this->currency;
    }

    public function setCurrency(Currency $currency): self
    {
        $this->currency = $currency;
        return $this;
    }
}
