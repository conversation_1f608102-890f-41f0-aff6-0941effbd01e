<?php

namespace Open\Izberg\Model;

class OrderShippingChoice
{
    private Carrier $carrier;
    /**
     * @var ParcelOrderItem[]
     */
    private array $orderItems;

    /**
     * @return Carrier
     */
    public function getCarrier(): Carrier
    {
        return $this->carrier;
    }

    /**
     * @param Carrier $carrier
     * @return OrderShippingChoice
     */
    public function setCarrier(Carrier $carrier): OrderShippingChoice
    {
        $this->carrier = $carrier;
        return $this;
    }

    /**
     * @return ParcelOrderItem[]
     */
    public function getOrderItems(): array
    {
        return $this->orderItems;
    }

    /**
     * @param ParcelOrderItem[] $orderItems
     * @return OrderShippingChoice
     */
    public function setOrderItems(array $orderItems): OrderShippingChoice
    {
        $this->orderItems = $orderItems;
        return $this;
    }
}
