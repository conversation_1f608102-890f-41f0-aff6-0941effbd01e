<?php

namespace Open\Izberg\Model;

class Error
{
    private string $field;
    private array $msg;

    /**
     * @return string
     */
    public function getField(): string
    {
        return $this->field;
    }

    /**
     * @param string $field
     * @return Error
     */
    public function setField(string $field): Error
    {
        $this->field = $field;
        return $this;
    }

    /**
     * @return array
     */
    public function getMsg(): array
    {
        return $this->msg;
    }

    /**
     * @param array $msg
     * @return Error
     */
    public function setMsg(array $msg): Error
    {
        $this->msg = $msg;
        return $this;
    }

    public function __toString(): string
    {
        return sprintf("(%s:%s)", $this->field, implode(",", $this->getMsg()));
    }
}
