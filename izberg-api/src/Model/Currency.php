<?php

namespace Open\Izberg\Model;

class Currency
{
    private string $code;
    private string $pk;

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;
        return $this;
    }

    public function getPk(): string
    {
        return $this->pk;
    }

    public function setPk(string $pk): self
    {
        $this->pk = $pk;
        return $this;
    }

    public function __toString(): string
    {
        return $this->code;
    }
}
