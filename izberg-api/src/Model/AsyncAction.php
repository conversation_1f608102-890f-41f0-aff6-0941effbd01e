<?php

namespace Open\Izberg\Model;

class AsyncAction
{
    private const STATUS_IN_QUEUE = 'in_queue';
    private const STATUS_IN_PROGRESS = 'in_progress';
    private const STATUS_DONE = 'done';
    private const STATUS_ABORTED = 'aborted';
    private const STATUS_FAILED = 'failed';

    private string $id;
    private string $actionName;
    private string $status;
    private array $result;
    private array $context;

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): void
    {
        $this->id = $id;
    }

    public function getActionName(): string
    {
        return $this->actionName;
    }

    public function setActionName(string $actionName): void
    {
        $this->actionName = $actionName;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): void
    {
        $this->status = $status;
    }

    public function getResult(): array
    {
        return $this->result;
    }

    public function setResult(array $result): void
    {
        $this->result = $result;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public function setContext(array $context): void
    {
        $this->context = $context;
    }

    public function isInQueue(): bool
    {
        return $this->status === self::STATUS_IN_QUEUE;
    }

    public function isInProgress(): bool
    {
        return $this->status === self::STATUS_IN_PROGRESS;
    }

    public function isDone(): bool
    {
        return $this->status === self::STATUS_DONE;
    }

    public function isAborted(): bool
    {
        return $this->status === self::STATUS_ABORTED;
    }

    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }
}
