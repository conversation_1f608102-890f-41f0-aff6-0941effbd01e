<?php

namespace Open\Izberg\Model;

class CreditNote
{
    private int $id;
    private int $pk;
    private float $totalAmount;
    private float $totalAmountWithTaxes;
    private ?string $idNumber;
    private string $created_on;
    private ?string $pdfFile;
    private string $issuerName;
    private string $currency;
    private string $paymentStatus;
    private string $status;
    private array $creditNoteLines;
    private User $receiver;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getPk(): int
    {
        return $this->pk;
    }

    public function setPk(int $pk): self
    {
        $this->pk = $pk;
        return $this;
    }

    public function getTotalAmount(): float
    {
        return $this->totalAmount;
    }

    public function setTotalAmount(float $totalAmount): self
    {
        $this->totalAmount = $totalAmount;
        return $this;
    }

    public function getTotalAmountWithTaxes(): float
    {
        return $this->totalAmountWithTaxes;
    }

    public function setTotalAmountWithTaxes(float $totalAmountWithTaxes): self
    {
        $this->totalAmountWithTaxes = $totalAmountWithTaxes;
        return $this;
    }

    public function getIdNumber(): ?string
    {
        return $this->idNumber;
    }

    public function setIdNumber(?string $idNumber): self
    {
        $this->idNumber = $idNumber;
        return $this;
    }

    public function getCreatedOn(): string
    {
        return $this->created_on;
    }

    public function setCreatedOn(string $created_on): self
    {
        $this->created_on = $created_on;
        return $this;
    }

    public function getPdfFile(): ?string
    {
        return $this->pdfFile;
    }

    public function setPdfFile(?string $pdfFile): self
    {
        $this->pdfFile = $pdfFile;
        return $this;
    }

    public function getIssuerName(): string
    {
        return $this->issuerName;
    }

    public function setIssuerName(string $issuerName): self
    {
        $this->issuerName = $issuerName;
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getPaymentStatus(): string
    {
        return $this->paymentStatus;
    }

    public function setPaymentStatus(string $paymentStatus): self
    {
        $this->paymentStatus = $paymentStatus;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getCreditNoteLines(): array
    {
        return $this->creditNoteLines;
    }

    public function setCreditNoteLines(array $creditNoteLines): self
    {
        $this->creditNoteLines = $creditNoteLines;
        return $this;
    }

    /**
     * @return User
     */
    public function getReceiver(): User
    {
        return $this->receiver;
    }

    /**
     * @param User $receiver
     * @return CreditNote
     */
    public function setReceiver(User $receiver): CreditNote
    {
        $this->receiver = $receiver;
        return $this;
    }
}
