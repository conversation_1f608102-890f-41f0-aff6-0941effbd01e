<?php

namespace Open\Izberg\Model;

class ParcelOrderItem
{
    private int $id;
    private int $quantity;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return ParcelOrderItem
     */
    public function setId(int $id): ParcelOrderItem
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return int
     */
    public function getQuantity(): int
    {
        return $this->quantity;
    }

    /**
     * @param int $quantity
     * @return ParcelOrderItem
     */
    public function setQuantity(int $quantity): ParcelOrderItem
    {
        $this->quantity = $quantity;
        return $this;
    }
}
