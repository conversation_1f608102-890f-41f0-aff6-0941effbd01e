<?php

namespace Open\Izberg\Model;

class MessageAttachment
{
    private string $id;
    private string $fileName;

    /**
     * @return string
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * @param string $id
     * @return MessageAttachment
     */
    public function setId(string $id): MessageAttachment
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string
     */
    public function getFileName(): string
    {
        return $this->fileName;
    }

    /**
     * @param string $fileName
     * @return MessageAttachment
     */
    public function setFileName(string $fileName): MessageAttachment
    {
        $this->fileName = $fileName;
        return $this;
    }
}
