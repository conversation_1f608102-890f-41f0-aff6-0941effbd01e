<?php

namespace Open\Izberg\Model;

class Item
{
    private int $id = 0;
    private int $quantity;
    private int $offerId;
    private float $amount;
    private float $amountVatIncluded;
    private float $price;
    private float $vat;
    private string $name;
    private string $status;
    private string $itemImageUrl;
    private string $sku;
    private string $sellerRef;
    private string $manufacturerRef;
    private Currency $currency;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function getOfferId(): int
    {
        return $this->offerId;
    }

    public function setOfferId(int $offerId): self
    {
        $this->offerId = $offerId;
        return $this;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;
        return $this;
    }

    public function getAmountVatIncluded(): float
    {
        return $this->amountVatIncluded;
    }

    public function setAmountVatIncluded(float $amountVatIncluded): self
    {
        $this->amountVatIncluded = $amountVatIncluded;
        return $this;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function setPrice(float $price): self
    {
        $this->price = $price;
        return $this;
    }

    public function getVat(): float
    {
        return $this->vat;
    }

    public function setVat(float $vat): self
    {
        $this->vat = $vat;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getItemImageUrl(): string
    {
        return $this->itemImageUrl;
    }

    public function setItemImageUrl(string $itemImageUrl): self
    {
        $this->itemImageUrl = $itemImageUrl;
        return $this;
    }

    public function getSku(): string
    {
        return $this->sku;
    }

    public function setSku(string $sku): self
    {
        $this->sku = $sku;
        return $this;
    }

    public function getSellerRef(): string
    {
        return $this->sellerRef;
    }

    public function setSellerRef(string $sellerRef): self
    {
        $this->sellerRef = $sellerRef;
        return $this;
    }

    public function getManufacturerRef(): string
    {
        return $this->manufacturerRef;
    }

    public function setManufacturerRef(string $manufacturerRef): self
    {
        $this->manufacturerRef = $manufacturerRef;
        return $this;
    }

    public function getCurrency(): Currency
    {
        return $this->currency;
    }

    public function setCurrency(Currency $currency): self
    {
        $this->currency = $currency;
        return $this;
    }
}
