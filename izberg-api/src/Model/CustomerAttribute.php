<?php

namespace Open\Izberg\Model;

// NOT an izberg mapped class!
class CustomerAttribute
{
    private ?int $id = null;

    public function __construct(
        private string $key,
        private string $value
    ) {
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return CustomerAttribute
     */
    public function setId(?int $id): CustomerAttribute
    {
        $this->id = $id;
        return $this;
    }


    /**
     * @return string
     */
    public function getKey(): string
    {
        return $this->key;
    }

    /**
     * @param string $key
     * @return CustomerAttribute
     */
    public function setKey(string $key): CustomerAttribute
    {
        $this->key = $key;
        return $this;
    }

    /**
     * @return string
     */
    public function getValue(): string
    {
        return $this->value;
    }

    /**
     * @param string $value
     * @return CustomerAttribute
     */
    public function setValue(string $value): CustomerAttribute
    {
        $this->value = $value;
        return $this;
    }
}
