<?php

namespace Open\Izberg\Model;

class OrderItem
{
    public const STATUS_CANCELLED = 2000;
    public const STATUS_CONFIRMED = 80;
    public const STATUS_PROCESSED = 85;
    public const STATUS_FINALIZED = 110;

    private int $id;
    private int $status;
    private int $offerId;
    private int $productId;
    private float $amount = 0.0;
    private float $amountVatIncluded = 0.0;
    private float $price;
    private float $vat;
    private string $lastModified;
    private string $itemImageUrl;
    private string $name;
    private string $delayDelivery;
    private string $incoterm;
    private string $sellerRef;
    private string $buyerRef;
    private string $merchantOrder;
    private Currency $currency;
    private Product $product;
    private CartItemExtraInfo $extraInfo;
    private int $invoicedQuantity = 0;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getStatus(): int
    {
        return $this->status;
    }

    public function setStatus(int $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getOfferId(): int
    {
        return $this->offerId;
    }

    public function setOfferId(int $offerId): self
    {
        $this->offerId = $offerId;
        return $this;
    }

    public function getProductId(): int
    {
        return $this->productId;
    }

    public function setProductId(int $productId): self
    {
        $this->productId = $productId;
        return $this;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;
        return $this;
    }

    public function getAmountVatIncluded(): float
    {
        return $this->amountVatIncluded;
    }

    public function setAmountVatIncluded(float $amountVatIncluded): self
    {
        $this->amountVatIncluded = $amountVatIncluded;
        return $this;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function setPrice(float $price): self
    {
        $this->price = $price;
        return $this;
    }

    public function getVat(): float
    {
        return $this->vat;
    }

    public function setVat(float $vat): self
    {
        $this->vat = $vat;
        return $this;
    }

    public function getLastModified(): string
    {
        return $this->lastModified;
    }

    public function setLastModified(string $lastModified): self
    {
        $this->lastModified = $lastModified;
        return $this;
    }

    public function getItemImageUrl(): string
    {
        return $this->itemImageUrl;
    }

    public function setItemImageUrl(string $itemImageUrl): self
    {
        $this->itemImageUrl = $itemImageUrl;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getDelayDelivery(): string
    {
        return $this->delayDelivery;
    }

    public function setDelayDelivery(string $delayDelivery): self
    {
        $this->delayDelivery = $delayDelivery;
        return $this;
    }

    public function getIncoterm(): string
    {
        return $this->incoterm;
    }

    public function setIncoterm(string $incoterm): self
    {
        $this->incoterm = $incoterm;
        return $this;
    }

    public function getSellerRef(): string
    {
        return $this->sellerRef;
    }

    public function setSellerRef(string $sellerRef): self
    {
        $this->sellerRef = $sellerRef;
        return $this;
    }

    public function getBuyerRef(): string
    {
        return $this->buyerRef;
    }

    public function setBuyerRef(string $buyerRef): self
    {
        $this->buyerRef = $buyerRef;
        return $this;
    }

    public function getMerchantOrder(): string
    {
        return $this->merchantOrder;
    }

    public function setMerchantOrder(string $merchantOrder): self
    {
        $this->merchantOrder = $merchantOrder;
        return $this;
    }

    public function getCurrency(): Currency
    {
        return $this->currency;
    }

    public function setCurrency(Currency $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getProduct(): Product
    {
        return $this->product;
    }

    public function setProduct(Product $product): self
    {
        $this->product = $product;
        return $this;
    }

    public function getExtraInfo(): CartItemExtraInfo
    {
        return $this->extraInfo;
    }

    public function setExtraInfo(CartItemExtraInfo $extraInfo): self
    {
        $this->extraInfo = $extraInfo;
        return $this;
    }

    public function getInvoicedQuantity(): int
    {
        return $this->invoicedQuantity;
    }

    public function setInvoicedQuantity(int $invoicedQuantity): self
    {
        $this->invoicedQuantity = $invoicedQuantity;
        return $this;
    }
}
