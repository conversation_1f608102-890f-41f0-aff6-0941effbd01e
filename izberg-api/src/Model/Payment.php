<?php

namespace Open\Izberg\Model;

class Payment
{
    private string $id;
    private string $createdOn;
    private float $toCollectAmount;
    private int $status;
    private PaymentMethod $paymentMethod;
    private IzbergResource $order;

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getCreatedOn(): string
    {
        return $this->createdOn;
    }

    public function setCreatedOn(string $createdOn): self
    {
        $this->createdOn = $createdOn;
        return $this;
    }

    public function getToCollectAmount(): float
    {
        return $this->toCollectAmount;
    }

    public function setToCollectAmount(float $toCollectAmount): self
    {
        $this->toCollectAmount = $toCollectAmount;
        return $this;
    }

    public function getStatus(): int
    {
        return $this->status;
    }

    public function setStatus(int $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getPaymentMethod(): PaymentMethod
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod(PaymentMethod $paymentMethod): self
    {
        $this->paymentMethod = $paymentMethod;
        return $this;
    }

    /**
     * @return IzbergResource
     */
    public function getOrder(): IzbergResource
    {
        return $this->order;
    }

    /**
     * @param IzbergResource $order
     * @return Payment
     */
    public function setOrder(IzbergResource $order): Payment
    {
        $this->order = $order;
        return $this;
    }
}
