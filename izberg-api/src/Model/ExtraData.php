<?php

namespace Open\Izberg\Model;

class ExtraData
{
    private float $extraFee;
    private float $extraFeeTaxRate;
    private float $extraFeeVatAmount;
    private float $extraFeeWithVat;

    public function getExtraFee(): float
    {
        return $this->extraFee;
    }
    public function setExtraFee(float $value): self
    {
        $this->extraFee = $value;
        return $this;
    }

    public function getExtraFeeTaxRate(): float
    {
        return $this->extraFeeTaxRate;
    }
    public function setExtraFeeTaxRate(float $value): self
    {
        $this->extraFeeTaxRate = $value;
        return $this;
    }

    public function getExtraFeeVatAmount(): float
    {
        return $this->extraFeeVatAmount;
    }
    public function setExtraFeeVatAmount(float $value): self
    {
        $this->extraFeeVatAmount = $value;
        return $this;
    }

    public function getExtraFeeWithVat(): float
    {
        return $this->extraFeeWithVat;
    }
    public function setExtraFeeWithVat(float $value): self
    {
        $this->extraFeeWithVat = $value;
        return $this;
    }
}
