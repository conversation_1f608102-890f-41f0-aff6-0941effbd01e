analyse:
	phpcs && psalm --no-cache

.PHONY: test
test: vendor/autoload.php
	php ./vendor/bin/phpunit --colors --testdox

.PHONY: testc
testc:
	php -dxdebug.mode=coverage ./vendor/bin/phpunit --colors --testdox --coverage-html=./coverage

.PHONY: functional_test
functional_test:
	php -dxdebug.mode=coverage ./vendor/bin/phpunit --colors --testdox --testsuite=functional

vendor/autoload.php: composer.lock
	composer install
	touch ./vendor/autoload.php
