<?php

declare(strict_types=1);

namespace Open\Izberg\Tests\Functional;

use Open\Izberg\Api\ParcelItemApi;

/**
 * @coversDefaultClass \Open\Izberg\Api\ParcelItemApi
 */
final class ParcelItemApiTest extends OperatorApiTestCase
{
    /**
     * @covers \Open\Izberg\Client\OperatorClient::parcelItemApi
     */
    public function testInitializeParcelApiApiApiInOperatorClient(): void
    {
        $merchantOrder = $this->operatorClient->parcelItemApi();

        $this->assertInstanceOf(ParcelItemApi::class, $merchantOrder);
    }
}
