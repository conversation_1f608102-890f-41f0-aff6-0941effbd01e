<?php

declare(strict_types=1);

namespace Open\Izberg\Tests\Functional;

use Marketplace\Component\CleanArchiCore\Domain\Port\Service\CacheServiceInterface;
use Marketplace\Component\CleanArchiCore\Domain\Port\Service\GetLocaleInterface;
use Open\Izberg\Client\OperatorClient;
use Open\Izberg\Factory\OperatorClientFactory;
use Open\Izberg\IzbergConfiguration;
use PHPUnit\Framework\TestCase;
use Prophecy\Prophet;
use Psr\Log\LoggerInterface;

abstract class OperatorApiTestCase extends TestCase
{
    protected OperatorClient $operatorClient;

    protected function setUp(): void
    {
        $operatorUsername = 'open_service38';
        $operatorAccessToken = '1731b2c834eeed67844867c8ec2d3d843921582f';
        $izbergSecretKey = '17306f97-7147-41d4-9623-8f103efa694b';
        $izbergApplicationNamespace = 'dev-telenco';
        $apiUrl = 'https://api.sandbox.iceberg.technology/v1/';
        $applicationId = "1003979";
        $audience = "https://api.sandbox.iceberg.technology";
        $identityUrl = "https://api.izberg.me/v1/";
        $clientId = "m2m-505d70d457845d5654fa2983b4fa556a";
        $clientSecret = "fHP4gK6ZyDQ9yMM8UWf1nvlC7OQfjcSin1uj-DO-DH0";
        $domainId = "0E2B97CFDD";


        $izbergConfiguration = new IzbergConfiguration(
            $izbergSecretKey,
            $izbergApplicationNamespace,
            $apiUrl,
            $operatorUsername,
            $operatorAccessToken,
            $applicationId,
            $audience,
            $identityUrl,
            $clientId,
            $clientSecret,
            $domainId
        );

        $prophet = new Prophet();
        $logger = $prophet->prophesize(LoggerInterface::class);

        $operatorClientFactory = new OperatorClientFactory(
            $logger->reveal(),
            $izbergConfiguration,
            $this->createMock(CacheServiceInterface::class),
            $this->createMock(GetLocaleInterface::class)
        );
        $this->operatorClient = $operatorClientFactory->buildOperatorClient();
    }
}
