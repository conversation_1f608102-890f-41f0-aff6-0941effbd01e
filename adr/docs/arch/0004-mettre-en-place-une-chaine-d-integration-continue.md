# 4. Mettre en place une chaîne d'intégration continue

Date: 2021-06-17

## Status

Accepted

## Context

Il nous faut un outil permettant de contrôler de manière automatisées la qualité
du code développé sur le projet.

## Decision

Le gitlab de boost https://gitlab.boost.open.global/ propose une chaîne
d'intégration continue.

Configurer la CI sur le projet pour lancer plusieurs outils de contrôle de qualité
pour Php:
* psalm
* phpcs
* phpunit
* symfony security:check

## Consequences

Chaque application, composant ou librairie développé sur le projet devra être
configurer sur la CI.

Le fichier .gitlab-ci.yml à la racine du projet permet de configurer la CI.
