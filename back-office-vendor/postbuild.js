const fs = require("fs-extra");
const src = "./build/";
const assetsDest = "../front-marketplace/public/back-office-vendor/";
const viewDest =
  "../front-marketplace/templates/BackOfficeVendor/";
const ncp = require("ncp").ncp;

fs.removeSync(assetsDest);

if (!fs.existsSync(assetsDest)) {
  fs.mkdirSync(assetsDest);
}

ncp(src, assetsDest, (err) => {
  if (err) {
    return console.error(err);
  }

  fs.readFile(assetsDest + "index.html", "utf-8", (err, html) => {
    html = html.replace(
      /\/front\/vendor/gi,
      "/back-office-vendor"
    );
    html = html.replace("__NAME__", "{{ user.name }}");
    html = html.replace("__ID__", "{{ user.id }}");
    html = html.replace("__LANG__", "{{ user.preferedLanguage }}");
    html = html.replace("__SLUG__", "{{ user.slug }}");

    fs.writeFile(viewDest + "index.html.twig", html, (err) => {
      if (err) return console.error(err);
      console.log("Successfully Written to index.html.twig.");
    });
  });

  console.log("Assets copy done");
});
