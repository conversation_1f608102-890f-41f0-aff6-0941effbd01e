import i18n from "./../i18n/init";
const locale = i18n.language;

export const priceFormatter = (price, currency) => {
  return new Intl.NumberFormat(locale, { style: 'currency', currency: currency }).format(price);
}

export const iconFromCurrency = (currency) => {
  switch (currency) {
    case 'EUR': return '€';
    case 'GBP': return '£';
    default: return '€';
  }
}

export const formatPrice = (price) => {
  return new Intl.NumberFormat(locale, { minimumFractionDigits: 2, maximumFractionDigits: 2}).format(price);
}
