import {useDispatch} from "react-redux";
import React, {useEffect, useState} from "react";
import {updateQuoteItem} from "../../../../store/actions/quote-detail-actions";
import * as QuoteDetailService from "../quote-detail-service";
import * as NumberUtils from "../../../../utils/NumberUtils";
import {TableCell, TableRow} from "@material-ui/core";
import {
    QuoteItemsTableCellComment,
    QuoteItemsTableCellDropdown,
    QuoteItemsTableCellInput
} from "../common/quote-items-table";

export default function CatalogQuoteItemsTableRow({index, quoteItem, editableFields, currency, quoteId}) {
    const [quotesVat, setQuotesVat] = useState([]);
    const dispatch = useDispatch();

    const handleChange = (event, propertyName) => {
        dispatch(updateQuoteItem(index, {...quoteItem, [propertyName]: event.target.value}));
    }

    useEffect(() => {
        QuoteDetailService.fetchQuotesVat(quoteId).then(({data}) => {
            setQuotesVat(QuoteDetailService.mapCatalogQuoteRateVatDropdownItems(data));
        });
    }, []);

    const {reference, designation, quantity, unitPrice, vatRate, comment} = quoteItem;

    const roundedPrice = quantity * unitPrice;
    const roundedVatPrice = (quantity * unitPrice) + (quantity * unitPrice * vatRate / 100);

    const totalPrice = NumberUtils.priceFormatter(roundedPrice, currency);
    const ttcPrice = NumberUtils.priceFormatter(roundedVatPrice, currency);

    return (
        <>
            <TableRow className="border-bottom-0">
                <QuoteItemsTableCellInput
                    value={reference}
                    disabled={editableFields.reference}
                />
                <QuoteItemsTableCellInput
                    value={designation}
                    disabled={editableFields.designation}
                />
                <QuoteItemsTableCellInput
                    align={"center"}
                    value={quantity}
                    disabled={editableFields.quantity}
                />
                <QuoteItemsTableCellInput
                    align={"center"}
                    value={NumberUtils.formatPrice(unitPrice.toFixed(2))}
                    unit={NumberUtils.iconFromCurrency(currency)}
                    disabled={editableFields.unitPrice}
                />
                <QuoteItemsTableCellDropdown
                    items={quotesVat}
                    value={vatRate}
                    disabled={editableFields.vatRate}
                />
                <TableCell align={"center"}>{totalPrice}</TableCell>
                <TableCell align={"center"}>{ttcPrice}</TableCell>
            </TableRow>

            <TableRow>
                <QuoteItemsTableCellComment
                    value={comment}
                    handleChange={(event) => handleChange(event, 'comment')}
                    disabled={editableFields.comment}
                />
            </TableRow>
        </>
    );
}
