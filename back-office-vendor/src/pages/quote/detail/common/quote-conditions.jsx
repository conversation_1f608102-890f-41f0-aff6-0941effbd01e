import React from "react";
import {TextField, Typography} from "@material-ui/core";
import i18n from "../../../../i18n/init";
import {useDispatch, useSelector} from "react-redux";
import {quoteDetailSelector} from "../../../../store/selectors/quote-detail-selector";
import {updateQuoteDetail} from "../../../../store/actions/quote-detail-actions";

export default function QuoteConditions() {
    const dispatch = useDispatch();
    const {conditions, status} = useSelector(quoteDetailSelector);
    const isDisabled = !(
        status === i18n.t('quotes.statuses.sent') || status === i18n.t('quotes.statuses.redraft')
    );

    const handleChange = (event) => {
        dispatch(updateQuoteDetail({conditions: event.target.value}));
    }

    return (<div className="p-25">
        <Typography style={{fontWeight: 'bold'}}>
            {`${i18n.t('quotes.detail.conditions')}`}
        </Typography>

        <TextField
            fullWidth
            multiline
            rows={8}
            variant="outlined"
            margin="normal"
            value={conditions}
            onChange={handleChange}
            disabled={isDisabled}
        />
    </div>);
}
