.d-flex {
    display: flex !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.justify-content-center {
    justify-content: center !important;
}

.align-items-center {
    align-items: center !important;
}

.w-fit-content {
    width: fit-content !important;
}

.p-25 {
    padding: 25px;
}

.m-10 {
    margin: 10px !important;
}

.border-bottom-0 {
    border-bottom: none !important;
}

.border-bottom-0 th, .border-bottom-0 td {
    border-bottom: none !important;
}

.error-message {
    color: red;
}

/**
    Quote item designation
 */
.quote-item-designation {
    min-width: 350px !important;
}

@media (max-width: 1400px) {
    .quote-item-designation {
        min-width: 200px !important;
    }
}

@media (max-width: 1200px) {
    .quote-item-designation {
        min-width: auto !important;
    }
}

/**
    Quote detail messenger
 */

.messenger-container {
    position: fixed;
    bottom: 20px;
    right: 10px;
    width: 100%;
}

