export const servicesConfig = {
  BASE: "/",
  IZBERGBASE: "https://telenco.izberg-marketplace.com/", // DEV: https://dev-telenco.merchant.izberg-marketplace.com/
  // react routes
  QUOTES_LIST_PAGE: "front/vendor/quotes",
  QUOTE_DETAIL_PAGE: "front/vendor/quote/:id",
  SPECIFIC_PRICES_PAGE: "front/vendor/specific-prices",
  SAV_LIST: "front/vendor/sav/list",
  SAV_DETAIL: "front/vendor/sav/detail/:id",
  // api routes
  SPECIFIC_PRICES: "api/vendor/specific-prices/show",
  SPECIFIC_PRICES_UPDATE: "api/vendor/specific-prices-update-request",
  SPECIFIC_PRICES_EXPORT: "api/vendor/specific-prices/export",
  SPECIFIC_PRICES_DELETE_CAT: "api/vendor/category-price/delete",
  SAV_LIST_API: "api/vendor/aftersales/show/vendor",
  SAV_DETAIL_API: "api/vendor/aftersales/detail/vendor/"
};

export function uri(name) {
  return servicesConfig.BASE + servicesConfig[name];
}

export function menuUri(url) {
  return servicesConfig.IZBERGBASE + window.slug + url;
}

export function formatDate(date) {
  return new Date(date).toLocaleDateString(window.lang);
}
