import {state as initialState} from "../initial-states/snackbar";

export const SHOW_SNACKBAR_ACTION = 'SHOW_SNACKBAR_ACTION';
export const HIDE_SNACKBAR_ACTION = 'HIDE_SNACKBAR_ACTION';


export function snackbarReducer(state = initialState, {type, payload}) {
    switch (type) {
        case SHOW_SNACKBAR_ACTION:
            const {variant, message} = payload;
            return {...state, show: true, variant, message};
        case HIDE_SNACKBAR_ACTION:
            return {...state, show: false};
        default:
            return state;
    }
}
