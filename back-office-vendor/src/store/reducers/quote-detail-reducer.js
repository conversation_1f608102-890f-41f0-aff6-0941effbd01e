import {state as initialState} from "../initial-states/quote-detail";

export const UPDATE_QUOTE_DETAIL_ACTION = 'UPDATE_QUOTE_DETAIL_ACTION';
export const UPDATE_QUOTE_ITEM_ACTION = 'UPDATE_QUOTE_ITEM_ACTION';
export const REMOVE_QUOTE_ITEM_ACTION = 'REMOVE_QUOTE_ITEM_ACTION';

export function quoteDetailReducer(state = initialState, {type, payload}) {
    const {items} = state;

    switch (type) {
        case UPDATE_QUOTE_DETAIL_ACTION:
            return {...state, ...payload};
        case UPDATE_QUOTE_ITEM_ACTION:
            return {
                ...state, items: items.map((quoteItem, quoteIndex) => {
                    if (quoteIndex !== payload.index) {
                        return quoteItem;
                    }

                    return {...quoteItem, ...payload.quoteItem}
                })
            };
        case REMOVE_QUOTE_ITEM_ACTION:
            return {...state, items: items.filter((quoteItem, quoteIndex) => quoteIndex !== payload.index)};
        default:
            return state;
    }
}
