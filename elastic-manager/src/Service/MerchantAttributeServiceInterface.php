<?php

namespace App\Service;

use Open\Izberg\DTO\AttributeDTO;

interface MerchantAttributeServiceInterface
{
    public function translateDeliveryAreasIntoRegions(array $attributes): array;
    /**
     * @param int $merchantId
     * @return AttributeDTO[]
     */
    public function getMerchantAttributes(int $merchantId): array;
    public function getAvailabilityKeyAntilles(int $merchantId): ?string;
    public function getAvailabilityKeyReunion(int $merchantId): ?string;

    public function getLongDescription(int $merchantId): string;
}
