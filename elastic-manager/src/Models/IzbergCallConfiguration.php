<?php

namespace App\Models;

class IzbergCallConfiguration
{
    private string $configurationName;
    private string $izbergUrl;
    private string $izbergChannelUrl;
    private string $izbergToken;
    private string $izbergApiId;
    private string $izbergChannelId;
    private int $callTimeout;
    private string $locale;
    private string $index;

    public function __construct(
        string $izbergUrl,
        string $izbergToken,
        string $izbergApiId,
        string $izbergChannelId,
        int $callTimeout,
        string $izbergChannelUrl,
        string $locale,
        string $index,
        string $name = "offer"
    ) {
        $this->izbergUrl = $izbergUrl;
        $this->izbergToken = $izbergToken;
        $this->izbergApiId = $izbergApiId;
        $this->izbergChannelId = $izbergChannelId;
        $this->callTimeout = $callTimeout;
        $this->configurationName = $name;
        $this->locale = $locale;
        $this->izbergChannelUrl = $izbergChannelUrl;
        $this->index = $index;
    }

    /**
     * @return string
     */
    public function getIzbergUrl(): string
    {
        return $this->izbergUrl;
    }

    /**
     * @param string $izbergUrl
     */
    public function setIzbergUrl(string $izbergUrl): void
    {
        $this->izbergUrl = $izbergUrl;
    }

    /**
     * @return string
     */
    public function getIzbergToken(): string
    {
        return $this->izbergToken;
    }

    /**
     * @param string $izbergToken
     */
    public function setIzbergToken(string $izbergToken): void
    {
        $this->izbergToken = $izbergToken;
    }

    /**
     * @return string
     */
    public function getIzbergApiId(): string
    {
        return $this->izbergApiId;
    }

    /**
     * @param string $izbergApiId
     */
    public function setIzbergApiId(string $izbergApiId): void
    {
        $this->izbergApiId = $izbergApiId;
    }

    /**
     * @return string
     */
    public function getIzbergChannelId(): string
    {
        return $this->izbergChannelId;
    }

    /**
     * @param string $izbergChannelId
     */
    public function setIzbergChannelId(string $izbergChannelId): void
    {
        $this->izbergChannelId = $izbergChannelId;
    }

    /**
     * @return int
     */
    public function getCallTimeout(): int
    {
        return $this->callTimeout;
    }

    /**
     * @param int $callTimeout
     */
    public function setCallTimeout(int $callTimeout): void
    {
        $this->callTimeout = $callTimeout;
    }

    public function getHeaderParameters(): array
    {

        return [
            "x-application-id" => $this->izbergApiId,
            "Authorization" => $this->izbergToken
        ];
    }

    /**
     * @return string
     */
    public function getConfigurationName(): string
    {
        return $this->configurationName;
    }

    /**
     * @param string $configurationName
     */
    public function setConfigurationName(string $configurationName): void
    {
        $this->configurationName = $configurationName;
    }

    /**
     * @return string
     */
    public function getIzbergChannelUrl(): string
    {
        return $this->izbergChannelUrl;
    }

    /**
     * @param string $izbergChannelUrl
     * @return IzbergCallConfiguration
     */
    public function setIzbergChannelUrl(string $izbergChannelUrl): IzbergCallConfiguration
    {
        $this->izbergChannelUrl = $izbergChannelUrl;
        return $this;
    }

    /**
     * @return string
     */
    public function getLocale(): string
    {
        return $this->locale;
    }

    /**
     * @param string $locale
     * @return IzbergCallConfiguration
     */
    public function setLocale(string $locale): IzbergCallConfiguration
    {
        $this->locale = $locale;
        return $this;
    }

    /**
     * @return string
     */
    public function getIndex(): string
    {
        return $this->index;
    }

    /**
     * @param string $index
     * @return IzbergCallConfiguration
     */
    public function setIndex(string $index): IzbergCallConfiguration
    {
        $this->index = $index;
        return $this;
    }
}
