<?php

namespace App\IzbergApi;

use App\Models\IzbergCallConfiguration;
use DateTime;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class CategoryIzbergApi extends Api
{
    public const DATE_FORMAT = DateTime::ISO8601;
    private const APPLICATION_CATEGORY_PATH = "application_category/";

    public function __construct(
        HttpClientInterface $izbergClient,
        LoggerInterface $logger,
        private IzbergCallConfiguration $callConfiguration
    ) {
        parent::__construct($izbergClient, $logger);
    }

    public function getCategories(
        string $locale
    ): ?array {
        $url = $this->callConfiguration->getIzbergUrl() . self::APPLICATION_CATEGORY_PATH;
        $headers = [
            Api::HEADER_ACCEPT_LANGUAGE => $locale,
        ];
        $categories = [];
        /** @var array $category */
        foreach (
            $this->fetchAll(
                $url,
                [],
                array_merge(
                    $this->callConfiguration->getHeaderParameters(),
                    $headers
                ),
                100
            ) as $category
        ) {
            $categories[] = $category;
        }
        return $categories;
    }
}
