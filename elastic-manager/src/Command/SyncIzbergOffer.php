<?php

namespace App\Command;

use App\Models\IzbergOfferViewer;
use App\Repository\SpecificPriceRepositoryInterface;
use App\Service\SynchronizationService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Synchronize izberg offer with marketplace catalogue
 */
class SyncIzbergOffer extends Command
{
    protected static $defaultName = 'open:sync_izberg_offer';

    private SynchronizationService $syncService;


    public function __construct(SynchronizationService $syncService, private SpecificPriceRepositoryInterface $specificPriceRepository)
    {
        $this->syncService = $syncService;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setDescription('Sync Izberg offer.')
            ->addOption(
                'bulkSize',
                null,
                InputOption::VALUE_OPTIONAL,
                'Specify the number of offer to fetch.',
                20
            )
            ->addOption(
                'local',
                null,
                InputOption::VALUE_OPTIONAL,
                "Specify the offer's locale.",
                IzbergOfferViewer::SLUG_EN
            )
            ->addOption(
                'since',
                null,
                InputOption::VALUE_OPTIONAL,
                'The date from where you want to fetch izberg offer.',
                null
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $local = $input->getOption('local');

        if (!in_array($local, IzbergOfferViewer::LOCALISATION_SLUGS)) {
            $output->writeln(sprintf('<comment>wrong locale ---</comment>'));
            return 2;
        }

        $output->writeln(
            sprintf(
                '<comment>Start sync izberg offer: %s ---</comment>',
                (new \DateTime())->format(\DATE_ATOM)
            )
        );

        $this->syncService->synchronize();
        return 0;
    }
}
