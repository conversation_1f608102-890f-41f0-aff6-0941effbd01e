<?php

return [
    Symfony\Bundle\FrameworkBundle\FrameworkBundle::class => ['all' => true],
    Symfony\Bundle\MonologBundle\MonologBundle::class => ['all' => true],
    Open\Izberg\IzbergApiBundle::class => ['all' => true],
    SymfonyCasts\Bundle\ResetPassword\SymfonyCastsResetPasswordBundle::class => ['all' => true],
    Symfony\Bundle\SecurityBundle\SecurityBundle::class => ['all' => true],
    Marketplace\Component\User\MarketplaceUserBundle::class => ['all' => true],
    Doctrine\Bundle\DoctrineBundle\DoctrineBundle::class => ['all' => true],
    SymfonyCasts\Bundle\VerifyEmail\SymfonyCastsVerifyEmailBundle::class => ['all' => true],
    Symfony\Bundle\TwigBundle\TwigBundle::class => ['all' => true],
    Marketplace\Component\Mail\MarketplaceMailBundle::class => ['all' => true],
    Marketplace\Component\CleanArchiCore\MarketplaceCleanArchiCoreBundle::class => ['all' => true],
    Knp\Bundle\PaginatorBundle\KnpPaginatorBundle::class => ['all' => true],
    Marketplace\Component\Order\MarketplaceOrderBundle::class => ['all' => true],
    Marketplace\Component\Sso\MarketplaceSsoBundle::class => ['all' => true],
    Marketplace\Component\Offer\MarketplaceOfferBundle::class => ['all' => true],
    Marketplace\Component\Invoice\MarketplaceInvoiceBundle::class => ['all' => true],
];
