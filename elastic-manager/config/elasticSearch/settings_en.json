{"index.mapping.total_fields.limit": 3000, "analysis": {"filter": {"english_stop": {"type": "stop", "stopwords": "_english_"}, "english_stemmer": {"type": "stemmer", "language": "english"}, "english_possessive_stemmer": {"type": "stemmer", "language": "possessive_english"}, "autocomplete_filter": {"type": "edge_ngram", "min_gram": 1, "max_gram": 20}}, "analyzer": {"autocomplete": {"type": "custom", "tokenizer": "standard", "char_filter": ["html_strip"], "filter": ["lowercase", "asciifolding", "autocomplete_filter"]}, "autocomplete_english": {"type": "custom", "tokenizer": "standard", "char_filter": ["html_strip"], "filter": ["lowercase", "asciifolding", "english_possessive_stemmer", "english_stop", "english_stemmer", "autocomplete_filter"]}, "custom_analyzer": {"type": "custom", "tokenizer": "standard", "char_filter": ["html_strip"], "filter": ["lowercase", "asciifolding"]}, "custom_analyzer_with_min": {"filter": ["lowercase", "asciifolding", "english_stemmer"], "char_filter": ["html_strip"], "type": "custom", "tokenizer": "standard"}}}}