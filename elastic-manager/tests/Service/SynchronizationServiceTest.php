<?php

namespace App\Tests\Service;

use App\Models\SyncInfo;
use App\Service\OfferService;
use App\Service\SyncDateService;
use App\Service\SynchronizationService;
use App\Utils\TotalChangeValidator;
use PHPUnit\Framework\TestCase;
use Prophecy\Argument;
use Prophecy\Prophet;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class SynchronizationServiceTest extends TestCase
{
    private SynchronizationService $syncService;


    /**
     *
     */
    public function testSynchronize()
    {
        $this->markTestSkipped('must be revisited.');
        $prophet = new Prophet();
        $mockedLog = $prophet->prophesize(LoggerInterface::class);
        $mockedLog->warning(Argument::any())->willReturn();

        $messageBusMock = $this->createMock(MessageBusInterface::class);
        $offerServiceMock = $this->createMock(OfferService::class);

        $this->syncService = new SynchronizationService(
            $messageBusMock,
            $offerServiceMock,
            new TotalChangeValidator(),
            $mockedLog->reveal(),
            7601,
            10
        );
        $result = $this->syncService->synchronize();
        $this->assertTrue($result);
    }
}
