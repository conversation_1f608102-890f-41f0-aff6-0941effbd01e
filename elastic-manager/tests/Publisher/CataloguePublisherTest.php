<?php

namespace App\Tests\Publisher;

use PHPUnit\Framework\TestCase;

class CataloguePublisherTest extends TestCase
{

    /**
     *
     */
    public function testPublishNewOffer()
    {
        // todo implements
        // todo implements dataprovider
        $this->markTestSkipped('must be revisited.');
        $catalogue = new Catalogue();
        $totalOffersInCatalogue = $catalogue->count();

        $cataloguePublisher = new CataloguePublisher($catalogue);

        $newOffer = new CatalogueOffer();

        $cataloguePublisher->publish($newOffer);

        $this->assertEquals($totalOffersInCatalogue + 1, $catalogue->count());
    }

    public function testUpdateExistingEnglishOfferWithFrenchTranslation()
    {
        // todo implements
        $this->markTestSkipped('must be revisited.');
    }

    public function testUpdateExistingEnglishOfferWithSpanishTranslation()
    {
        // todo implements
        $this->markTestSkipped('must be revisited.');
    }

    public function testUpdateExistingEnglishOfferWithGermanTranslation()
    {
        // todo implements
        $this->markTestSkipped('must be revisited.');
    }

    public function testUpdateExistingEnglishOfferWithItalianTranslation()
    {
        $this->markTestSkipped('must be revisited.');
    }
}
